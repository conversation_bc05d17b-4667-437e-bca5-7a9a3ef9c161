## Configuration
### Database Setup
1. Copy database.yml.sample to database.yml (Do not delete or overwrite database.yml.sample)
2. Navigate inside of new database.yml
3. Under default -> password fill in your password for root access to mysql
4. If there are issues with the socket path, run `mysql_config --socket` to obtain the proper path

### Specdb Setup
1. Copy specdb.yml.sample to specdb.yml (Do not delete or overwrite specdb.yml.sample)

### Python Setup
1. Follow instructions located in python_paths.yml