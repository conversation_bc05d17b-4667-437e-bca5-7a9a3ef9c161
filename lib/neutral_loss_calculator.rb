class NeutralLossCalculator

  NEUTRAL_LOSS = {
    	1.008	=>	'H',																						
    	2.016	=>	'H2',																						
    	15.023	=>	'CH3',																						
    	15.995	=>	'O',																						
    	16.019	=>	'NH2',																						
    	16.031	=>	'CH4',																						
    	17.003	=>	'OH',																						
    	17.027	=>	'NH3',																						
    	18.011	=>	'H2O',																						
    	20.006	=>	'HF',																						
    	26.003	=>	'CN',																						
    	27.011	=>	'HCN',																						
    	27.995	=>	'CO',																						
    	28.019	=>	'H2CN',																						
    	28.031	=>	'C2H4',																						
    	29.003	=>	'HCO',																						
    	29.039	=>	'C2H5',																						
    	29.998	=>	'NO',																						
    	30.011	=>	'formaldehyde',																						
    	31.006	=>	'HNO',																						
    	31.018	=>	'CH2OH',																						
    	31.042	=>	'CH3NH2',																						
    	31.972	=>	'S',																						
    	32.026	=>	'CH3OH',																						
    	33.034	=>	'CH3+H2O',																						
    	33.988	=>	'H2S',																						
    	34.969	=>	'Cl	',																						
    	35.977	=>	'HCl',																						
    	36.021	=>	'H2O+H2O',																						
    	42.011	=>	'CH2CO',																						
    	42.022	=>	'CH2N2',																						
    	42.047	=>	'C3H6',																						
    	43.006	=>	'HNCO',																						
    	43.055	=>	'C3H7',																						
    	43.99	=>	'CO2',																						
    	44.037	=>	'NH3+HCN',																						
    	44.05	=>	'N(CH3)2',																						
    	44.998	=>	'COOH',																						
    	45.021	=>	'NH3+CO or HCN+H2O',																						
    	45.058	=>	'C2H7N',																						
    	45.993	=>	'NO2',																						
    	46.005	=>	'HCOOH(H2+CO2 or H2O+CO)',																						
    	47.001	=>	'NO+OH',																						
    	48.003	=>	'CH4S',																						
    	48.009	=>	'H2O+NO',																						
    	48.988	=>	'H3PO4 from 2+ charge state',																						
    	49.992	=>	'CH3Cl',																						
    	55.006	=>	'HCN+CO',																						
    	55.99	=>	'CO+CO',																						
    	56.998	=>	'CO+CO+H',																						
    	57.021	=>	'Methyl isocyanate',																						
    	57.993	=>	'NO+CO',																						
    	58.005	=>	'C2H2O2',																						
    	59.073	=>	'trimethylamine',																						
    	59.996	=>	'NO+NO',																						
    	60.021	=>	'CH3COOH',																						
    	60.021	=>	'HCOO+CH3',																						
    	61.016	=>	'CO2+NH3',																						
    	62	=>	'H2O+CO2',																						
    	62.996	=>	'HNO3',																						
    	63.962	=>	'SO2',																						
    	63.972	=>	'HCl+CO',																						
    	63.998	=>	'methylsulfenic acid',																						
    	68.995	=>	'CF3',																						
    	71.037	=>	'dehydroalanine',																						
    	71.985	=>	'CO2+CO',																						
    	73.004	=>	'NO2+HCN',																						
    	73.988	=>	'NO2+CO',																						
    	74	=>	'H2O+CO+CO',																						
    	74.019	=>	'C3H6S',																						
    	75.032	=>	'Glycine',																						
    	75.991	=>	'NO+NO2',																						
    	76.031	=>	'benzyne',																						
    	77.039	=>	'phenyl radical',																						
    	77.998	=>	'CH2N2+HCl',																						
    	78.047	=>	'benzene',																						
    	78.918	=>	'Br',																						
    	79.926	=>	'HBr',																						
    	79.957	=>	'SO3',																						
    	79.966	=>	'HPO3',																						
    	80.965	=>	'HSO3',																						
    	81.045	=>	'Histidine side chain',																						
    	81.972	=>	'H2SO3',																						
    	83.985	=>	'CO+CO+CO',																						
    	84.094	=>	'C3H6+C3H6',																						
    	85.028	=>	'HCNO+CH2N2',																						
    	85.988	=>	'NO+CO+CO',																						
    	87.032	=>	'C3H5NO2',																						
    	87.991	=>	'NO+NO+CO',																						
    	90.032	=>	'C3H6O3',																						
    	91.967	=>	'CO+CO+HCl',																						
    	91.986	=>	'NO2+NO2',																						
    	93.96	=>	'SO2+NO',																						
    	97.977	=>	'H3PO4',																						
    	99.98	=>	'CO2+CO+CO',																						
    	102.068	=>	'HC(=O)OC4H9',																						
    	103.986	=>	'NO+NO2+CO',																						
    	105.025	=>	'C3H7NOS',																						
    	107.05	=>	'tyrosine side-chain',																						
    	108.058	=>	'tyrosine side-chain+hydrogen',																						
    	109.011	=>	'phenylthio radical',																						
    	111.98	=>	'4CO',																						
    	119.981	=>	'NO2+NO2+CO',																						
    	120.042	=>	'C4H8O4',																						
    	121.02	=>	'Cystein',																						
    	123.958	=>	'SO2+NO+NO',																						
    	127.912	=>	'HI',																						
    	129.043	=>	'Pyroglutamic acid',																						
    	129.043	=>	'C5H7NO3',																						
    	130.063	=>	'Anhydro-dideoxyhexose',																						
    	132.042	=>	'Anhydro-pentose',																						
    	133.984	=>	'NO2+NO+NO+CO',																						
    	141.019	=>	'C2H8O4NP',																						
    	146.058	=>	'Anhydro-deoxyhexose',																						
    	146.069	=>	'Glutamine',																						
    	154.003	=>	'C3H7O5P',																						
    	156.115	=>	'HNE',																						
    	161.069	=>	'Anhydro-aminodeoxyhexose',																						
    	162.053	=>	'Anhydrohexose',																						
    	163.03	=>	'acetylcysteine',																						
    	164.068	=>	'Rhamnose',																						
    	171.09	=>	'C8H13NO3',																						
    	172.014	=>	'C3H9O6P',																						
    	176.026	=>	'CysGly(neg)',																						
    	176.032	=>	'Anhydroglucuronic-acid',																						
    	178.041	=>	'CysGly(pos)',																						
    	179.079	=>	'C6H13NO5',																						
    	185.009	=>	'C3H8NO6P',																						
    	189.04	=>	'C3H12NO6P',																						
    	194.043	=>	'Glucuronic-acid',																						
    	197.045	=>	'C5H12NO5P',																						
    	203.079	=>	'Anhydro-N-acetylglucosamine',																						
    	221.09	=>	'N-acetylglucosamine',																						
    	225.077	=>	'C7H16NO5P',																						
    	228.04	=>	'C6H13O7P',																						
    	248.053	=>	'AnhydromalonylGlc',																						
    	250.062	=>	'gamma-GluCys',																						
    	260.03	=>	'C6H13O9P',																						
    	261.028	=>	'C6H13O9S',																						
    	266.064	=>	'MalonylGlc',																						
    	273.096	=>	'γ-GluAlaGly-2H',																						
    	275.112	=>	'γ-GluAlaGly(pos)',																						
    	277.056	=>	'C6H16NO9P',																						
    	306.076	=>	'Glutathione(neg)',																						
    	307.084	=>	'Glutathione',																						
    	316.056	=>	'C9H17O10P',																						
    	341.132	=>	'C12H23NO10',
  }

  def self.find_neutral_mass_formula(mass)
    neutral_loss = nil
    mass = mass.to_f
    mass = sprintf("%.3f",(mass)).to_f
    potential_masses = [mass - 0.001, mass, mass + 0.001]
    potential_masses.each do |m|
      neutral_loss = NEUTRAL_LOSS[m] 
      break if !neutral_loss.nil?
    end
    neutral_loss
  end


end
