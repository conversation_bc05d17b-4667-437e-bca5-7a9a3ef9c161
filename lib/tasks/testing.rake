require "#{Rails.root}/app/helpers/application_helper"
include ApplicationHelper
require 'csv'
#require 'jchem'

namespace :testing do
  desc "Check CASMI compounds have classifications from classyfire"
  task :check_classifications => :environment do
    missing = []
    # Format: http://classyfire.wishartlab.com/entities/NFBLWTRRMVFTHW-UHFFFAOYSA-N.json
    DatabaseMolecule.where(source: "CASMI2016").find_each(batch_size: 100) do |dm|
      if dm.classification.blank?
        puts dm.id
        if !classify(dm)
          missing << dm.inchi_key
        end
      end
    end

    File.open("data/casmi2016/missing_casmi_classifications.txt", "w+") do |f|
      f.puts(missing.compact.uniq)
    end
  end

  desc "Check that the CASMI 2016 challenge compounds can find themselves"
  task :full_casmi => :environment do |t, args|
    dir = "data/testing/casmi_full"
    unless File.directory?(dir)
      FileUtils.mkdir_p(dir)
    end

    overall_file = File.open(dir + "/overall-results-" + Time.now.to_i.to_s + ".txt", "w")
    overall_file.puts [ "Coefficients", "Rank 1", "Rank 3", "Rank 10", "Medal Score", "Average Query Rank" ].join("\t")
    list = []
    CSV.foreach("data/casmi2016/casmi-2016-testing.csv", headers: true) do |row|
      # ID, ionization_mode, challenge ID, precursor mass
      list << [row[0], row[3].downcase.strip, row[1].strip, row[2], row[13]]
    end

    run_casmi_test(IdentifyQuery::ESI_SIMILARITY_WEIGHT,
                   IdentifyQuery::ESI_REFERENCE_WEIGHT,
                   IdentifyQuery::ESI_ANCESTOR_WEIGHT,
                   dir, overall_file, list)
  end

  task :omitted_casmi => :environment do |t, args|
    dir = "data/testing/casmi_omitted"
    unless File.directory?(dir)
      FileUtils.mkdir_p(dir)
    end

    overall_file = File.open(dir + "/overall-results-" + Time.now.to_i.to_s + ".txt", "w")
    overall_file.puts [ "Coefficients", "Rank 1", "Rank 3", "Rank 10", "Medal Score", "Average Query Rank" ].join("\t")
    list = []
    CSV.foreach("data/casmi2016/casmi-2016-testing.csv", headers: true) do |row|
      # ID, ionization_mode, challenge ID, precursor mass, energy_level
      list << [row[0], row[3].downcase.strip, row[1].strip, row[2], row[13]]
    end

    run_casmi_test(IdentifyQuery::ESI_SIMILARITY_WEIGHT,
                   IdentifyQuery::ESI_REFERENCE_WEIGHT,
                   IdentifyQuery::ESI_ANCESTOR_WEIGHT,
                   dir, overall_file, list,
                   omit_sources: ["CASMI2016"])
  end

  desc "Test provided query spectra against spectra library"
  def run_casmi_test(s_weight, r_weight, a_weight, dir, overall_file, list, omit_sources: nil)
    result_file = File.open(dir + "/challenge-results-" + Time.now.to_i.to_s + ".txt", "w")
    result_file.puts ["Query_Rank", "Original_Rank", "Score", "CFMID_Score", "Ref_Score", "Class_Score", "Query_Source", "Query_ID", "Predicted_Class", "Direct_Parent", "Alternative_Parents", "Ancestors", "Top_Ranked_Source", "Top_Ranked_Source_ID", "Top_Ranked_Original_Rank", "Score", "CFMID_Score", "Ref_Score", "Class_Score", "Num_Candidates", "Type"].join("\t")
    ranks = []
    original_ranks = []

    list.each do |row|

      molecule = DatabaseMolecule.find_by(source_id: row[0])
      if !molecule.present?
        next
      end

      type = row[1]
      puts molecule.source_id

      if type == "positive"
        databases = IdentifyQuery::ESI_DATABASES.values
        ion_mode = "positive"
        spectra_type = "ESI"
        adduct_type = "[M+H]+"
        mass = AdductCalculator.neutral_mass_to_adduct_mass("M+H", molecule.neutral_mass)
      elsif type == "negative"
        databases = IdentifyQuery::ESI_DATABASES.values
        ion_mode = "negative"
        spectra_type = "ESI"
        adduct_type = "[M-H]-"
        mass = AdductCalculator.neutral_mass_to_adduct_mass("M-H", molecule.neutral_mass)
      end

      test_file = "data/casmi2016/challenge_" + type + "/" + row[2] + ".txt"
      test_spectra = File.read(test_file)

      if test_spectra.present?
        if !omit_sources.present?
          computed_spectra = molecule.spectra.computed.esi.send(ion_mode).where(adduct_type: "Neutral").first&.get_spectra_as_hash(false, adduct_type)

          merged_spectra = {}
          merged_spectra[row[4].to_i] = test_spectra
          if merged_spectra[10].blank?
            if computed_spectra.present? && computed_spectra["energy0"].present?
              merged_spectra[10] = computed_spectra["energy0"].map{ |l| l.gsub(/\s/, "\t") }.join("\n")
            else
              merged_spectra[10] = test_spectra
            end
          end
          if merged_spectra[20].blank?
            if computed_spectra.present? && computed_spectra["energy1"].present?
              merged_spectra[20] = computed_spectra["energy1"].map{ |l| l.gsub(/\s/, "\t") }.join("\n")
            else
              merged_spectra[20] = test_spectra
            end
          end
          if merged_spectra[40].blank?
            if computed_spectra.present? && computed_spectra["energy2"].present?
              merged_spectra[40] = computed_spectra["energy2"].map{ |l| l.gsub(/\s/, "\t") }.join("\n")
            else
              merged_spectra[40] = test_spectra
            end
          end

          spectra = "energy0\n" + merged_spectra[10].strip + "\nenergy1\n" + merged_spectra[20].strip + "\nenergy2\n" + merged_spectra[40].strip
        else
          spectra = "energy0\n" + test_spectra
        end

        # Create a query with the compound spectra as the input spectra
        query = Query.new(
          type: "IdentifyQuery",
          spectra: spectra,
          database: databases,
          num_results: -1,
          threshold: 0.001,
          ppm_mass_tol: 10.0,
          abs_mass_tol: 0.01,
          scoring_function: "DotProduct + Metadata",
          candidate_limit: 100,
          neutral_mass: nil,
          candidate_ppm_mass_tol: 50.0,
          candidate_abs_mass_tol: 0.5,
          parent_ion_mass: mass,
          parent_ion_mass_type: "Original",
          ion_mode: ion_mode,
          adduct_type: adduct_type,
          spectra_type: spectra_type,
          param_file: get_param_file(spectra_type, ion_mode),
          config_file: get_config_file(spectra_type, ion_mode),
          omit_sources: omit_sources
        )

        if query.valid? && query.convert_spectra && query.save
          output_file = "output.txt"

          query_string = "cfm-id-precomputed #{query.input_file.path} \"#{query.print_spectra_id}\" #{query.candidates_file.path} #{query.num_results} #{query.ppm_mass_tol} #{query.abs_mass_tol} #{query.cfm_id_scoring_function} #{output_file}"

          if CFMID.config[:extra_params]
            query_string = CFMID.config[:extra_params] + " " + query_string
          end
          stdin, stdout, stderr = Open3.popen3(query_string)

          stderr.gets(nil)
          stdout.gets(nil)

          stdin.close
          stdout.close
          stderr.close

          query.update_attributes(output_file: File.new(output_file))
          query_results = query.get_results(similarity_weight: s_weight,
                                            reference_weight: r_weight,
                                            ancestor_weight: a_weight,
                                            similarity_threshold: 3 * 0.9)
          results = query_results[:results]

          result = results.find { |r|
            r[:inchi_key] == molecule.inchi_key
          }

          if result.blank?
            result = {}
            rank = 0
          else
            rank = result[:adjusted_rank]
          end

          ranks << rank
          original_ranks << result[:original_rank].to_i

          single_result_file = File.open(dir + "/results-" + row[0].to_s + "-S" + s_weight.round(2).to_s.gsub(".", "") + "-R" + r_weight.round(2).to_s.gsub(".", "") + "-C" + a_weight.round(2).to_s.gsub(".", "") + ".txt", "w")
          single_result_file.puts results.first.keys.map(&:to_s).join("\t")
          results.each do |r|
            single_result_file.puts r.values.map(&:to_s).join("\t")
          end

          result_file.puts [rank, result[:original_rank], result[:combined_score], result[:cfm_id_score], result[:reference_score], result[:ancestor_score], molecule.source, molecule.source_id, query_results[:predicted_class].join("; "), result[:direct_parent], result[:alternative_parents].try(:join, "; "), result[:ancestors].try(:join, "; "), results.first[:database], results.first[:database_id], results.first[:original_rank], results.first[:combined_score], results.first[:cfm_id_score], results.first[:reference_score], results.first[:ancestor_score], results.length, type].join("\t")
          query.destroy
        else
          puts "INVALID"
        end
      else
        puts "MISSING SPECTRA"
      end
    end

    counts = Hash.new 0

    ranks.each do |rank|
      counts[rank] += 1
    end

    result_file.puts counts
    result_file.puts "Rank 1: " + ranks.count { |r| r <= 1 && r > 0}.to_s
    result_file.puts "Rank 3: " + ranks.count { |r| r <= 3 && r > 0}.to_s
    result_file.puts "Rank 10: " + ranks.count { |r| r <= 10 && r > 0}.to_s
    result_file.puts "Medal Score: " + (ranks.count { |r| r <= 1 && r > 0} * 5 + ranks.count { |r| r <= 2 && r > 1} * 3 + ranks.count { |r| r <= 3 && r > 2}).to_s
    result_file.puts "Average Rank: " + (ranks.sum / ranks.length.to_f).to_s
    result_file.puts "\n"
    result_file.puts "Original Rank 1: " + original_ranks.count { |r| r <= 1 && r > 0}.to_s
    result_file.puts "Original Rank 3: " + original_ranks.count { |r| r <= 3 && r > 0}.to_s
    result_file.puts "Original Rank 10: " + original_ranks.count { |r| r <= 10 && r > 0}.to_s
    result_file.puts "Original Medal Score: " + (original_ranks.count { |r| r <= 1 && r > 0} * 5 + ranks.count { |r| r <= 2 && r > 1} * 3 + ranks.count { |r| r <= 3 && r > 2}).to_s
    result_file.puts "Average Original Rank: " + (original_ranks.sum / original_ranks.length.to_f).to_s
    result_file.close

    if overall_file.present?
      overall_file.puts [ "S" + s_weight.round(2).to_s.gsub(".", "") + "-R" + r_weight.round(2).to_s.gsub(".", "") + "-C" + a_weight.round(2).to_s.gsub(".", ""),
                          ranks.count { |r| r <= 1 && r > 0}.to_s,
                          ranks.count { |r| r <= 3 && r > 0}.to_s,
                          ranks.count { |r| r <= 10 && r > 0}.to_s,
                          (ranks.count { |r| r <= 1 && r > 0} * 5 + ranks.count { |r| r <= 2 && r > 1} * 3 + ranks.count { |r| r <= 3 && r > 2}).to_s,
                          (ranks.sum / ranks.length.to_f).to_s
                        ].join("\t")
    end
  end
end
