require 'zip'
require 'csv'
require 'builder'
require 'admin_mailer'
require 'wishart/task_support/zipper'

namespace :npmrd do
  namespace :export do
    include Wishart::TaskSupport::Zipper

    DOWNLOAD_PATH = 'public/system/downloads/current'.freeze
    

    desc "Ensure the correct paths exist for export"
    task paths: [:environment] do
      [DOWNLOAD_PATH].each do |path|
        FileUtils.makedirs(path) unless File.exists?(path)
      end
    end

    desc "Run all export tasks"
    task all: [:paths, :structures_smiles, :structures] # not including :xml for now

    desc 'Export all compounds and proteins to xml format'
    task xml: ['xml:natural_products']

    namespace :xml do     
      desc 'Export natural products to xml'
      task natural_products: [:environment] do
        metabolites_scope = NaturalProduct.exported ##.includes(
        #   :accession_numbers,
        #   :articles, :textbooks, :external_links
        # )
        begin
          export_models_as_xml metabolites_scope, "npmrd_natural_products", batch_size: 20
        rescue
        end
      end
    end


    desc "Export structures (SDF)"
    task structures: [:environment] do
      mailer = AdminMailer.build "np-mrd",
        subject: "SDF structures export",
        message: "SDF structures export from rake task."

      begin
        outfile = Tempfile.new('structures', encoding: 'UTF-8')

        puts "Building SDF file"
        progress = ProgressBar.new(NaturalProduct.exported.count) if Rails.env.development?
        puts "Total number of compounds = #{NaturalProduct.exported.count}"

        # Will give the compounds in order of id asc (which is what we want)
        NaturalProduct.exported.each do |compound|
          progress.increment! if Rails.env.development?
          next unless compound.has_structure?

          # Wrap the helper due to network connection
          # issues
          retries = [3, 5]
          begin
            structure = compound.to_sdf
          # When the host is unreachable, retry with a backoff strategy
          rescue Errno::EHOSTUNREACH => e
            if delay = retries.shift # will be nil if the list is empty
              sleep delay
              retry # backs up to just after the "begin"
            else
              raise # with no args re-raises original error
            end
          # When the file is not found, skip it
          rescue OpenURI::HTTPError => e
            next
          end
          outfile.write(structure) unless structure.blank?
        end

        write_zip_file("#{DOWNLOAD_PATH}/structures.zip",
                       "structures.sdf",
                       outfile)
        mailer.notify_success!
      rescue => e
        raise if Rails.env.development?
        mailer.notify_error! e
      end
    end

    desc "Export structures (SDF)"
    task structures_smiles: [:environment] do
      mailer = AdminMailer.build "np-mrd",
        subject: "Smiles structures export",
        message: "Smiles structures export from rake task."

      begin
        # outfile = Tempfile.new('structures', encoding: 'UTF-8')
        outfile = "#{DOWNLOAD_PATH}/smiles.csv"
        puts "Building Smiles file"
        progress = ProgressBar.new(NaturalProduct.exported.count) if Rails.env.development?
        puts "Total number of compounds = #{NaturalProduct.exported.count}"
        HEADER = ["Natural_Products_Name","NP_MRD_ID","Smiles"]
        # Will give the compounds in order of id asc (which is what we want)
        CSV.open(outfile, "w") do |csv|
          csv << HEADER
          NaturalProduct.exported.each do |compound|
            if compound.has_structure?
              puts("#{compound.np_mrd_id}")
              # if compound.np_mrd_id == "NP0000271"
              #   break
              # else
              row = ["#{compound.name}","#{compound.np_mrd_id}","#{compound.structure_resource.smiles}"]
              csv << row
              # end
            end
          end
        end
        system("gzip #{outfile}")
      rescue => e
        raise if Rails.env.development?
        mailer.notify_error! e
      end
    end            
  end
end

def export_models_as_xml(scope, filename, batch_size: 100)
  puts "Packing #{scope.name}"
  mailer = AdminMailer.build "np-mrd",
    subject: "XML export: #{scope.name}",
    message: "XML export of #{scope.name} from rake task."

  outfile = Tempfile.new(filename, encoding: 'UTF-8')

  begin
    puts "\tExporting"
    outfile.write('<?xml version="1.0" encoding="UTF-8"?>')
    outfile.write("\n");
    outfile.write('<np-mrd xmlns="http://www.np-mrd.org">')
    outfile.write("\n");
    scope.find_each(batch_size: batch_size) do |item|
      puts item.to_param

      # Wrap the fetch due to network connection issues
      retries = [3, 5]
      begin
        # Strip XML header before outputting
        if m = (/^(<\?xml [^>]+> *\n?)(.*)/m).match(item.to_xml)
          outfile.write(m[2])
        end
      rescue Errno::EHOSTUNREACH => e
        if delay = retries.shift # will be nil if the list is empty
          sleep delay
          retry # backs up to just after the "begin"
        else
          raise # with no args re-raises original error
        end
      end
    end

    outfile.write('</npmrd>');
    outfile.write("\n");

    puts "\tZipping"
    write_zip_file("#{DOWNLOAD_PATH}/#{filename}.zip",
                   "#{filename}.xml",
                   outfile)
    mailer.notify_success!
  rescue => e
    mailer.notify_error! e
    raise unless Rails.env.production?
  end
end

