namespace :refactor do

  desc "Change all HMDB IDs to NP-MRD IDs"
  task change_hmdb_ids: [:environment] do
    NaturalProduct.update_all("np_mrd_id = REPLACE(np_mrd_id, 'HMDB', 'NP')")
    puts("FINISHED NATURAL PRODUCTS")

    AccessionNumber.delete_all
    puts("FINISHED ACCESSION NUMBERS")
  end

  desc "Remove all natural products that do not have NMR"
  task remove_non_nmr_natural_products: [:environment] do
    nps_no_nmr = ["NP0000001", "NP0000002", "NP0000005", "NP0000008", "NP0000010", "NP0000011", "NP0000012", "NP0000014", "NP0000015", "NP0000016", "NP0000017", "NP0000019", "NP0000020", "NP0000021", "NP0000022", "NP0000023", "NP0000026", "NP0000030", "NP0000032", "NP0000033", "NP0000034", "NP0000036", "NP0000037", "NP0000038", "NP0000039", "NP0000042", "NP0000043", "NP0000045", "NP0000048", "NP0000050", "NP0000052", "NP0000053", "NP0000054", "NP0000055", "NP0000056", "NP0000058", "NP0000060", "NP0000061", "NP0000062", "NP0000064", "NP0000067", "NP0000068", "NP0000070", "NP0000071", "NP0000072", "NP0000073", "NP0000076", "NP0000077", "NP0000079", "NP0000082", "NP0000085", "NP0000086", "NP0000087", "NP0000089", "NP0000092", "NP0000094", "NP0000095", "NP0000097", "NP0000098", "NP0000099", "NP0000101", "NP0000107", "NP0000108", "NP0000112", "NP0000115", "NP0000118", "NP0000119", "NP0000121", "NP0000122", "NP0000123", "NP0000124", "NP0000125", "NP0000126", "NP0000127", "NP0000128", "NP0000130", "NP0000131", "NP0000132", "NP0000133", "NP0000134", "NP0000138", "NP0000139", "NP0000142", "NP0000143", "NP0000145", "NP0000148", "NP0000149", "NP0000150", "NP0000151", "NP0000152", "NP0000153", "NP0000156", "NP0000157", "NP0000158", "NP0000159", "NP0000161", "NP0000162", "NP0000163", "NP0000164", "NP0000167", "NP0000168", "NP0000169", "NP0000172", "NP0000174", "NP0000175", "NP0000176", "NP0000177", "NP0000181", "NP0000182", "NP0000186", "NP0000187", "NP0000189", "NP0000190", "NP0000191", "NP0000192", "NP0000193", "NP0000194", "NP0000195", "NP0000197", "NP0000201", "NP0000202", "NP0000205", "NP0000206", "NP0000207", "NP0000208", "NP0000209", "NP0000210", "NP0000211", "NP0000212", "NP0000214", "NP0000216", "NP0000217", "NP0000220", "NP0000221", "NP0000222", "NP0000223", "NP0000224", "NP0000225", "NP0000226", "NP0000227", "NP0000228", "NP0000229", "NP0000230", "NP0000232", "NP0000234", "NP0000235", "NP0000237", "NP0000238", "NP0000239", "NP0000243", "NP0000244", "NP0000246", "NP0000247", "NP0000248", "NP0000251", "NP0000252", "NP0000253", "NP0000254", "NP0000256", "NP0000258", "NP0000259", "NP0000262", "NP0000263", "NP0000265", "NP0000267", "NP0000269", "NP0000271", "NP0000272", "NP0000273", "NP0000279", "NP0000283", "NP0000285", "NP0000286", "NP0000288", "NP0000289", "NP0000290", "NP0000291", "NP0000292", "NP0000294", "NP0000295", "NP0000296", "NP0000299", "NP0000300", "NP0000301", "NP0000302", "NP0000303", "NP0000305", "NP0000306", "NP0000308", "NP0000310", "NP0000315", "NP0000318", "NP0000321", "NP0000325", "NP0000329", "NP0000335", "NP0000336", "NP0000338", "NP0000339", "NP0000341", "NP0000343", "NP0000346", "NP0000347", "NP0000350", "NP0000355", "NP0000356", "NP0000357", "NP0000363", "NP0000364", "NP0000365", "NP0000370", "NP0000374", "NP0000378", "NP0000387", "NP0000392", "NP0000393", "NP0000394", "NP0000397", "NP0000398", "NP0000402", "NP0000405", "NP0000407", "NP0000408", "NP0000413", "NP0000415", "NP0000421", "NP0000422", "NP0000423", "NP0000424", "NP0000426", "NP0000428", "NP0000429", "NP0000434", "NP0000439", "NP0000440", "NP0000442", "NP0000444", "NP0000446", "NP0000448", "NP0000450", "NP0000452", "NP0000453", "NP0000455", "NP0000459", "NP0000462", "NP0000466", "NP0000467", "NP0000468", "NP0000469", "NP0000472", "NP0000473", "NP0000474", "NP0000479", "NP0000482", "NP0000484", "NP0000488", "NP0000490", "NP0000491", "NP0000493", "NP0000500", "NP0000501", "NP0000508", "NP0000509", "NP0000510", "NP0000511", "NP0000512", "NP0000517", "NP0000518", "NP0000528", "NP0000529", "NP0000532", "NP0000535", "NP0000536", "NP0000538", "NP0000543", "NP0000544", "NP0000546", "NP0000548", "NP0000549", "NP0000554", "NP0000555", "NP0000557", "NP0000561", "NP0000562", "NP0000564", "NP0000565", "NP0000567", "NP0000568", "NP0000573", "NP0000574", "NP0000575", "NP0000576", "NP0000577", "NP0000582", "NP0000590", "NP0000592", "NP0000598", "NP0000609", "NP0000617", "NP0000619", "NP0000620", "NP0000622", "NP0000623", "NP0000625", "NP0000626", "NP0000628", "NP0000630", "NP0000631", "NP0000633", "NP0000634", "NP0000635", "NP0000638", "NP0000639", "NP0000640", "NP0000641", "NP0000645", "NP0000646", "NP0000650", "NP0000651", "NP0000653", "NP0000656", "NP0000660", "NP0000661", "NP0000663", "NP0000665", "NP0000666", "NP0000667", "NP0000669", "NP0000670", "NP0000671", "NP0000672", "NP0000673", "NP0000674", "NP0000676", "NP0000678", "NP0000679", "NP0000682", "NP0000684", "NP0000687", "NP0000688", "NP0000689", "NP0000691", "NP0000693", "NP0000694", "NP0000695", "NP0000696", "NP0000699", "NP0000700", "NP0000701", "NP0000703", "NP0000704", "NP0000705", "NP0000706", "NP0000707", "NP0000711", "NP0000714", "NP0000715", "NP0000716", "NP0000718", "NP0000719", "NP0000720", "NP0000721", "NP0000725", "NP0000726", "NP0000729", "NP0000730", "NP0000731", "NP0000732", "NP0000733", "NP0000734", "NP0000735", "NP0000736", "NP0000738", "NP0000740", "NP0000742", "NP0000744", "NP0000745", "NP0000746", "NP0000747", "NP0000748", "NP0000749", "NP0000750", "NP0000752", "NP0000753", "NP0000754", "NP0000755", "NP0000756", "NP0000757", "NP0000759", "NP0000761", "NP0000763", "NP0000764", "NP0000765", "NP0000766", "NP0000767", "NP0000772", "NP0000774", "NP0000779", "NP0000781", "NP0000782", "NP0000783", "NP0000784", "NP0000786", "NP0000788", "NP0000791", "NP0000792", "NP0000798", "NP0000802", "NP0000803", "NP0000806", "NP0000807", "NP0000808", "NP0000812", "NP0000819", "NP0000820", "NP0000821", "NP0000826", "NP0000827", "NP0000828", "NP0000829", "NP0000832", "NP0000840", "NP0000842", "NP0000845", "NP0000847", "NP0000848", "NP0000849", "NP0000852", "NP0000857", "NP0000858", "NP0000859", "NP0000860", "NP0000863", "NP0000866", "NP0000870", "NP0000871", "NP0000872", "NP0000873", "NP0000875", "NP0000876", "NP0000881", "NP0000883", "NP0000884", "NP0000885", "NP0000888", "NP0000892", "NP0000893", "NP0000894", "NP0000895", "NP0000896", "NP0000898", "NP0000899", "NP0000900", "NP0000902", "NP0000903", "NP0000904", "NP0000905", "NP0000906", "NP0000907", "NP0000908", "NP0000910", "NP0000913", "NP0000920", "NP0000921", "NP0000925", "NP0000926", "NP0000927", "NP0000929", "NP0000930", "NP0000933", "NP0000935", "NP0000937", "NP0000939", "NP0000943", "NP0000944", "NP0000946", "NP0000947", "NP0000953", "NP0000954", "NP0000955", "NP0000956", "NP0000957", "NP0000958", "NP0000959", "NP0000962", "NP0000965", "NP0000975", "NP0000982", "NP0000990", "NP0000991", "NP0000995", "NP0001003", "NP0001015", "NP0001020", "NP0001044", "NP0001046", "NP0001047", "NP0001049", "NP0001051", "NP0001065", "NP0001067", "NP0001069", "NP0001078", "NP0001101", "NP0001107", "NP0001120", "NP0001123", "NP0001129", "NP0001138", "NP0001140", "NP0001149", "NP0001151", "NP0001160", "NP0001169", "NP0001170", "NP0001173", "NP0001183", "NP0001184", "NP0001186", "NP0001197", "NP0001201", "NP0001202", "NP0001209", "NP0001218", "NP0001220", "NP0001227", "NP0001232", "NP0001238", "NP0001248", "NP0001254", "NP0001256", "NP0001257", "NP0001259", "NP0001262", "NP0001264", "NP0001266", "NP0001273", "NP0001294", "NP0001296", "NP0001310", "NP0001311", "NP0001316", "NP0001336", "NP0001341", "NP0001342", "NP0001348", "NP0001355", "NP0001358", "NP0001366", "NP0001368", "NP0001370", "NP0001372", "NP0001388", "NP0001389", "NP0001392", "NP0001394", "NP0001397", "NP0001398", "NP0001401", "NP0001403", "NP0001406", "NP0001409", "NP0001413", "NP0001414", "NP0001423", "NP0001426", "NP0001431", "NP0001432", "NP0001434", "NP0001440", "NP0001451", "NP0001460", "NP0001470", "NP0001476", "NP0001485", "NP0001487", "NP0001488", "NP0001490", "NP0001491", "NP0001494", "NP0001505", "NP0001511", "NP0001514", "NP0001520", "NP0001522", "NP0001525", "NP0001532", "NP0001536", "NP0001537", "NP0001538", "NP0001542", "NP0001544", "NP0001545", "NP0001546", "NP0001547", "NP0001548", "NP0001555", "NP0001563", "NP0001565", "NP0001568", "NP0001569", "NP0001570", "NP0001586", "NP0001587", "NP0001624", "NP0001644", "NP0001645", "NP0001659", "NP0001713", "NP0001721", "NP0001786", "NP0001830", "NP0001833", "NP0001843", "NP0001844", "NP0001846", "NP0001847", "NP0001848", "NP0001849", "NP0001850", "NP0001851", "NP0001852", "NP0001855", "NP0001856", "NP0001857", "NP0001858", "NP0001859", "NP0001860", "NP0001861", "NP0001862", "NP0001864", "NP0001866", "NP0001867", "NP0001868", "NP0001870", "NP0001871", "NP0001873", "NP0001874", "NP0001875", "NP0001877", "NP0001878", "NP0001879", "NP0001881", "NP0001882", "NP0001885", "NP0001886", "NP0001888", "NP0001889", "NP0001890", "NP0001891", "NP0001892", "NP0001893", "NP0001894", "NP0001895", "NP0001896", "NP0001900", "NP0001901", "NP0001902", "NP0001904", "NP0001906", "NP0001913", "NP0001919", "NP0001920", "NP0001921", "NP0001922", "NP0001923", "NP0001924", "NP0001925", "NP0001926", "NP0001927", "NP0001928", "NP0001929", "NP0001930", "NP0001932", "NP0001933", "NP0001934", "NP0001935", "NP0001936", "NP0001937", "NP0001938", "NP0001939", "NP0001940", "NP0001941", "NP0001942", "NP0001943", "NP0001944", "NP0001955", "NP0001964", "NP0001972", "NP0001973", "NP0001975", "NP0001982", "NP0001987", "NP0001991", "NP0002001", "NP0002003", "NP0002004", "NP0002005", "NP0002006", "NP0002024", "NP0002026", "NP0002035", "NP0002039", "NP0002040", "NP0002043", "NP0002048", "NP0002055", "NP0002059", "NP0002064", "NP0002068", "NP0002072", "NP0002074", "NP0002080", "NP0002085", "NP0002092", "NP0002095", "NP0002096", "NP0002097", "NP0002099", "NP0002107", "NP0002108", "NP0002111", "NP0002120", "NP0002123", "NP0002128", "NP0002141", "NP0002144", "NP0002151", "NP0002152", "NP0002176", "NP0002182", "NP0002199", "NP0002205", "NP0002210", "NP0002212", "NP0002214", "NP0002222", "NP0002229", "NP0002243", "NP0002259", "NP0002262", "NP0002302", "NP0002303", "NP0002308", "NP0002322", "NP0002327", "NP0002339", "NP0002345", "NP0002348", "NP0002349", "NP0002350", "NP0002356", "NP0002359", "NP0002361", "NP0002362", "NP0002364", "NP0002368", "NP0002390", "NP0002391", "NP0002393", "NP0002404", "NP0002428", "NP0002432", "NP0002434", "NP0002455", "NP0002466", "NP0002467", "NP0002511", "NP0002545", "NP0002641", "NP0002643", "NP0002649", "NP0002658", "NP0002670", "NP0002706", "NP0002712", "NP0002714", "NP0002721", "NP0002755", "NP0002780", "NP0002802", "NP0002820", "NP0002825", "NP0002835", "NP0002873", "NP0002899", "NP0002917", "NP0002923", "NP0002925", "NP0002927", "NP0002928", "NP0002931", "NP0002935", "NP0002939", "NP0002961", "NP0002991", "NP0002994", "NP0003011", "NP0003012", "NP0003033", "NP0003066", "NP0003070", "NP0003072", "NP0003075", "NP0003099", "NP0003119", "NP0003134", "NP0003152", "NP0003154", "NP0003156", "NP0003157", "NP0003164", "NP0003213", "NP0003217", "NP0003219", "NP0003227", "NP0003229", "NP0003231", "NP0003243", "NP0003249", "NP0003254", "NP0003265", "NP0003269", "NP0003276", "NP0003282", "NP0003290", "NP0003306", "NP0003312", "NP0003315", "NP0003320", "NP0003331", "NP0003332", "NP0003334", "NP0003337", "NP0003344", "NP0003345", "NP0003349", "NP0003352", "NP0003355", "NP0003357", "NP0003361", "NP0003364", "NP0003366", "NP0003375", "NP0003402", "NP0003403", "NP0003404", "NP0003405", "NP0003406", "NP0003407", "NP0003409", "NP0003416", "NP0003417", "NP0003418", "NP0003423", "NP0003424", "NP0003431", "NP0003441", "NP0003447", "NP0003464", "NP0003466", "NP0003474", "NP0003498", "NP0003501", "NP0003514", "NP0003543", "NP0003546", "NP0003553", "NP0003555", "NP0003573", "NP0003581", "NP0003634", "NP0003640", "NP0003646", "NP0003671", "NP0003681", "NP0003828", "NP0003843", "NP0003911", "NP0003966", "NP0004041", "NP0004043", "NP0004094", "NP0004095", "NP0004101", "NP0004110", "NP0004122", "NP0004136", "NP0004230", "NP0004284", "NP0004296", "NP0004321", "NP0004327", "NP0004350", "NP0004437", "NP0004472", "NP0004476", "NP0004487", "NP0004586", "NP0004811", "NP0004812", "NP0004814", "NP0004815", "NP0004816", "NP0004982", "NP0004983", "NP0004987", "NP0004992", "NP0004998", "NP0004999", "NP0005000", "NP0005005", "NP0005008", "NP0005015", "NP0005016", "NP0005018", "NP0005035", "NP0005038", "NP0005356", "NP0005393", "NP0005453", "NP0005782", "NP0005785", "NP0005792", "NP0005794", "NP0005800", "NP0005801", "NP0005802", "NP0005805", "NP0005806", "NP0005807", "NP0005809", "NP0005812", "NP0005842", "NP0005843", "NP0005846", "NP0005994", "NP0006006", "NP0006007", "NP0006024", "NP0006029", "NP0006050", "NP0006115", "NP0006216", "NP0006217", "NP0006218", "NP0006219", "NP0006220", "NP0006221", "NP0006331", "NP0006355", "NP0006478", "NP0006483", "NP0006524", "NP0006792", "NP0006944", "NP0006961", "NP0010720", "NP0010726", "NP0011188", "NP0011469", "NP0011599", "NP0011600", "NP0011603", "NP0011614", "NP0011619", "NP0011623", "NP0011624", "NP0011625", "NP0011626", "NP0011628", "NP0011632", "NP0011634", "NP0011635", "NP0011653", "NP0011718", "NP0011723", "NP0011733", "NP0011745", "NP0011749", "NP0011751", "NP0011757", "NP0012127", "NP0012138", "NP0012157", "NP0012204", "NP0012275", "NP0012283", "NP0012308", "NP0012322", "NP0012971", "NP0013036", "NP0013113", "NP0013231", "NP0013592", "NP0013716", "NP0013733", "NP0013742", "NP0013785", "NP0013821", "NP0013824", "NP0013825", "NP0013835", "NP0013860", "NP0014312", "NP0014325", "NP0014337", "NP0014352", "NP0014395", "NP0014397", "NP0014426", "NP0014473", "NP0014483", "NP0014511", "NP0014562", "NP0014568", "NP0014607", "NP0014638", "NP0014641", "NP0014644", "NP0014645", "NP0014659", "NP0014672", "NP0014684", "NP0014690", "NP0014693", "NP0014703", "NP0014704", "NP0014758", "NP0014770", "NP0014786", "NP0014831", "NP0014850", "NP0014855", "NP0014859", "NP0014868", "NP0014910", "NP0014911", "NP0014956", "NP0014960", "NP0014972", "NP0015051", "NP0015052", "NP0015063", "NP0015086", "NP0015122", "NP0015143", "NP0015253", "NP0015290", "NP0015305", "NP0015309", "NP0015312", "NP0015329", "NP0015333", "NP0015371", "NP0015440", "NP0015444", "NP0015446", "NP0015468", "NP0015489", "NP0015497", "NP0015534", "NP0015655", "NP0029188", "NP0029306", "NP0029377", "NP0029419", "NP0029571", "NP0029572", "NP0029573", "NP0029579", "NP0029593", "NP0029596", "NP0029597", "NP0029598", "NP0029599", "NP0029600", "NP0029603", "NP0029608", "NP0029637", "NP0029638", "NP0029642", "NP0029643", "NP0029648", "NP0029665", "NP0029686", "NP0029718", "NP0029737", "NP0029739", "NP0029750", "NP0029751", "NP0029758", "NP0029762", "NP0029811", "NP0029817", "NP0029846", "NP0029862", "NP0029880", "NP0029881", "NP0029902", "NP0029927", "NP0029980", "NP0030003", "NP0030028", "NP0030029", "NP0030058", "NP0030089", "NP0030396", "NP0030637", "NP0030776", "NP0030819", "NP0030941", "NP0030942", "NP0030952", "NP0030998", "NP0031019", "NP0031082", "NP0031193", "NP0031206", "NP0031207", "NP0031209", "NP0031213", "NP0031217", "NP0031219", "NP0031220", "NP0031229", "NP0031230", "NP0031231", "NP0031243", "NP0031246", "NP0031249", "NP0031264", "NP0031265", "NP0031266", "NP0031294", "NP0031295", "NP0031299", "NP0031310", "NP0031313", "NP0031317", "NP0031320", "NP0031321", "NP0031322", "NP0031325", "NP0031330", "NP0031332", "NP0031352", "NP0031407", "NP0031409", "NP0031413", "NP0031417", "NP0031418", "NP0031419", "NP0031445", "NP0031447", "NP0031450", "NP0031472", "NP0031474", "NP0031475", "NP0031476", "NP0031479", "NP0031480", "NP0031493", "NP0031503", "NP0031523", "NP0031524", "NP0031526", "NP0031527", "NP0031528", "NP0031542", "NP0031548", "NP0031549", "NP0031558", "NP0031563", "NP0031583", "NP0031598", "NP0031599", "NP0031605", "NP0031607", "NP0031630", "NP0031634", "NP0031642", "NP0031645", "NP0031659", "NP0031666", "NP0031669", "NP0031716", "NP0031766", "NP0031778", "NP0031782", "NP0031791", "NP0031792", "NP0031794", "NP0031795", "NP0031797", "NP0031802", "NP0031804", "NP0031805", "NP0031806", "NP0031811", "NP0031817", "NP0031844", "NP0031849", "NP0031857", "NP0031864", "NP0032012", "NP0032018", "NP0032029", "NP0032037", "NP0032044", "NP0032049", "NP0032051", "NP0032063", "NP0032072", "NP0032076", "NP0032078", "NP0032127", "NP0032133", "NP0032136", "NP0032137", "NP0032139", "NP0032150", "NP0032151", "NP0032231", "NP0032412", "NP0032425", "NP0032431", "NP0032538", "NP0032562", "NP0032566", "NP0032567", "NP0032568", "NP0032570", "NP0032572", "NP0032573", "NP0032574", "NP0032575", "NP0032582", "NP0032608", "NP0032616", "NP0032617", "NP0032618", "NP0032782", "NP0032860", "NP0032914", "NP0032915", "NP0032916", "NP0032917", "NP0032929", "NP0032930", "NP0032943", "NP0032971", "NP0032985", "NP0032993", "NP0033002", "NP0033116", "NP0033118", "NP0033121", "NP0033127", "NP0033133", "NP0033155", "NP0033182", "NP0033205", "NP0033209", "NP0033244", "NP0033375", "NP0033532", "NP0033547", "NP0033584", "NP0033591", "NP0033618", "NP0033619", "NP0033676", "NP0033704", "NP0033716", "NP0033721", "NP0033724", "NP0033731", "NP0033751", "NP0033772", "NP0033777", "NP0033780", "NP0033788", "NP0033792", "NP0033826", "NP0033827", "NP0033835", "NP0033837", "NP0033838", "NP0033840", "NP0033848", "NP0033854", "NP0033871", "NP0033889", "NP0033890", "NP0033895", "NP0033908", "NP0033910", "NP0033916", "NP0033923", "NP0033944", "NP0033945", "NP0033951", "NP0033956", "NP0033966", "NP0033967", "NP0033968", "NP0033978", "NP0034006", "NP0034029", "NP0034072", "NP0034106", "NP0034121", "NP0034136", "NP0034153", "NP0034156", "NP0034158", "NP0034159", "NP0034160", "NP0034166", "NP0034168", "NP0034170", "NP0034172", "NP0034174", "NP0034232", "NP0034235", "NP0034237", "NP0034238", "NP0034240", "NP0034261", "NP0034263", "NP0034272", "NP0034282", "NP0034284", "NP0034288", "NP0034289", "NP0034301", "NP0034355", "NP0034377", "NP0034380", "NP0034381", "NP0034413", "NP0034436", "NP0034437", "NP0034462", "NP0034666", "NP0034853", "NP0034859", "NP0034893", "NP0035078", "NP0035089", "NP0035092", "NP0035093", "NP0035094", "NP0035155", "NP0035162", "NP0035238", "NP0035246", "NP0035248", "NP0035289", "NP0035474", "NP0035604", "NP0035762", "NP0035770", "NP0035833", "NP0035882", "NP0035915", "NP0035924", "NP0036079", "NP0036086", "NP0036100", "NP0036174", "NP0036179", "NP0036491", "NP0036565", "NP0036574", "NP0036584", "NP0036626", "NP0036634", "NP0036994", "NP0036995", "NP0037116", "NP0037156", "NP0037217", "NP0037617", "NP0037685", "NP0037790", "NP0037953", "NP0037954", "NP0038036", "NP0038169", "NP0038310", "NP0038445", "NP0038556", "NP0038852", "NP0038962", "NP0039522", "NP0039531", "NP0039618", "NP0039620", "NP0039815", "NP0040054", "NP0040148", "NP0040179", "NP0040193", "NP0040195", "NP0040199", "NP0040201", "NP0040209", "NP0040210", "NP0040215", "NP0040221", "NP0040240", "NP0040251", "NP0040254", "NP0040292", "NP0040297", "NP0040327", "NP0040411", "NP0040575", "NP0040587", "NP0040733", "NP0040735", "NP0040937", "NP0041194", "NP0041220", "NP0041314", "NP0041326", "NP0041345", "NP0041606", "NP0041607", "NP0041791", "NP0041792", "NP0041797", "NP0041799", "NP0041800", "NP0041802", "NP0041808", "NP0041822", "NP0041830", "NP0041855", "NP0041856", "NP0041866", "NP0041878", "NP0041922", "NP0041950", "NP0041951", "NP0041965", "NP0041971", "NP0041980", "NP0041985", "NP0042002", "NP0042061", "NP0059629", "NP0059737", "NP0059797", "NP0059799", "NP0059800", "NP0059803", "NP0059810", "NP0059812", "NP0059819", "NP0059823", "NP0059830", "NP0059831", "NP0059835", "NP0059837", "NP0059839", "NP0059846", "NP0059851", "NP0059864", "NP0059873", "NP0059877", "NP0059882", "NP0059883", "NP0059886", "NP0059889", "NP0059899", "NP0059900", "NP0059901", "NP0059903", "NP0059905", "NP0059907", "NP0059909", "NP0059924", "NP0059933", "NP0060079", "NP0060180", "NP0060254", "NP0060263", "NP0060268", "NP0060272", "NP0060276", "NP0060292", "NP0060334", "NP0060427", "NP0060475", "NP0060484", "NP0060677", "NP0061166", "NP0061185", "NP0061732", "NP0061736", "NP0061749", "NP0061782", "NP0061788", "NP0061865", "NP0061867", "NP0061868", "NP0061869", "NP0061883", "NP0061884", "NP0061885", "NP0061886", "NP0061888", "NP0061915", "NP0061917", "NP0062089", "NP0062186", "NP0062188", "NP0062203", "NP0062251", "NP0062270", "NP0062326", "NP0062469", "NP0062501", "NP0062511", "NP0062513", "NP0062538", "NP0062546", "NP0062573", "NP0062590", "NP0062627", "NP0062642", "NP0062698", "NP0062767", "NP0062769", "NP0062806", "NP0094660", "NP0094708", "NP0094715", "NP0125600", "NP0130150", "NP0133305", "NP0142137", "NP0142894", "NP0144295", "NP0240219", "NP0240266"]

    NaturalProduct.where.not(:np_mrd_id => nps_no_nmr).delete_all
  end

  desc "Remove unlinked references and change relevant names"
  task refactor_references: [:environment] do
    natural_product_ids = NaturalProduct.ids

    # Articles
    CiteThis::ArticleReferencing.where.not(referencer_type: 'Metabolite').delete_all
    CiteThis::ArticleReferencing.where.not(:referencer_id => natural_product_ids).delete_all
    CiteThis::ArticleReferencing.update_all(referencer_type: 'NaturalProduct')

    article_ids = CiteThis::ArticleReferencing.pluck(:article_id)
    CiteThis::Article.where.not(:id => article_ids).delete_all
    puts("FINISHED ARTICLES")


    # External Links
    CiteThis::ExternalLinkReferencing.where.not(referencer_type: 'Metabolite').delete_all
    CiteThis::ExternalLinkReferencing.where.not(:referencer_id => natural_product_ids).delete_all
    CiteThis::ExternalLinkReferencing.update_all(referencer_type: 'NaturalProduct')

    external_link_ids = CiteThis::ExternalLinkReferencing.pluck(:external_link_id)
    CiteThis::ExternalLink.where.not(:id => external_link_ids).delete_all
    puts("FINISHED EXTERNAL LINKS")


    # Textbooks
    CiteThis::TextbookReferencing.where.not(referencer_type: 'Metabolite').delete_all
    CiteThis::TextbookReferencing.where.not(:referencer_id => natural_product_ids).delete_all
    CiteThis::TextbookReferencing.update_all(referencer_type: 'NaturalProduct')

    textbook_ids = CiteThis::TextbookReferencing.pluck(:textbook_id)
    CiteThis::Textbook.where.not(:id => textbook_ids).delete_all
    puts("FINISHED TEXTBOOKS")
  end

  desc "Shift ids and np_mrd_ids in order to redo the order of natural products"
  task shift_natural_products: [:environment] do
    NaturalProduct.update_all("np_mrd_id = REPLACE(np_mrd_id, 'NP', 'NP0')")
    NaturalProduct.update_all("id = id + 250000")
    CiteThis::ArticleReferencing.update_all("referencer_id = referencer_id + 250000")
    CiteThis::ExternalLinkReferencing.update_all("referencer_id = referencer_id + 250000")
    CiteThis::TextbookReferencing.update_all("referencer_id = referencer_id + 250000")
    ExperimentalPropertySet.update_all("natural_product_id = natural_product_id + 250000")
  end

  desc "Re-ID natural products given a CSV with ids and HMDB IDs"
  task re_id_natural_products: [:environment] do
    input_file = "data/input.csv"

    CSV.foreach(input_file, :headers => true) do |row|
      primary_id = row[0]
      hmdb_id = row[1]
      natural_product = NaturalProduct.find_by(np_mrd_id: hmdb_id.gsub("HMDB", "NP0"))

      # Remap dependencies
      CiteThis::ArticleReferencing.where(referencer_id: natural_product.id).update_all(referencer_id: primary_id)
      CiteThis::ExternalLinkReferencing.where(referencer_id: natural_product.id).update_all(referencer_id: primary_id)
      CiteThis::TextbookReferencing.where(referencer_id: natural_product.id).update_all(referencer_id: primary_id)
      ExperimentalPropertySet.where(natural_product_id: natural_product.id).update_all(natural_product_id: primary_id)

      # Re-ID natural product
      natural_product.id = primary_id
      natural_product.np_mrd_id = generate_np_mrd_id(primary_id)
      natural_product.structure = open("http://moldb.wishartlab.com/structures/#{hmdb_id}.mol").read
      natural_product.save!
      clear_cache(natural_product)
      puts(natural_product.np_mrd_id)
    end
  end

  desc "Re-ID references to reduce the ID number"
  task re_id_references: [:environment] do
    # Create a temporary article to reassign all references to
    temp_article = CiteThis::Article.create(pubmed_id: "18537291")

    # Articles
    CiteThis::Article.all.each.with_index(1) do |article, i|
      old_primary_id = article.id

      # Assign references to temporary article to avoid foreign key errors
      CiteThis::ArticleReferencing.where(article_id: old_primary_id).update_all(article_id: temp_article.id)
      
      # Change article primary key to match the index
      CiteThis::Article.where(id: old_primary_id).update_all(id: i)
      
      # Reassign references to article
      CiteThis::ArticleReferencing.where(article_id: temp_article.id).update_all(article_id: i)
    end

    # Article References
    CiteThis::ArticleReferencing.all.each.with_index(1) do |reference, i|
      CiteThis::ArticleReferencing.where(id: reference.id).update_all(id: i)
    end

    # Destroy temporary article
    temp_article = CiteThis::Article.find_by(pubmed_id: "18537291")
    temp_article.destroy!
    puts("FINISHED ARTICLES")



    # Create a temporary external link to reassign all references to
    temp_external_link = CiteThis::ExternalLink.create(url: "http://www.temp.com", name: "Temp")

    # External Links
    CiteThis::ExternalLink.all.each.with_index(1) do |external_link, i|
      old_primary_id = external_link.id

      # Assign references to temporary external link to avoid foreign key errors
      CiteThis::ExternalLinkReferencing.where(external_link_id: old_primary_id).update_all(external_link_id: temp_external_link.id)
      
      # Change external link primary key to match the index
      CiteThis::ExternalLink.where(id: old_primary_id).update_all(id: i)
      
      # Reassign references to external_link
      CiteThis::ExternalLinkReferencing.where(external_link_id: temp_external_link.id).update_all(external_link_id: i)
    end

    # External Link References
    CiteThis::ExternalLinkReferencing.all.each.with_index(1) do |reference, i|
      CiteThis::ExternalLinkReferencing.where(id: reference.id).update_all(id: i)
    end

    # Destroy temporary external link
    temp_external_link = CiteThis::ExternalLink.find_by(url: "http://www.temp.com", name: "Temp")
    temp_external_link.destroy!
    puts("FINISHED EXTERNAL LINKS")



    # Create a temporary textbook to reassign all references to
    temp_textbook = CiteThis::Textbook.create(title: "Temp", authors: "Temp", publisher: "Temp", year: 1990)

    # Textbooks
    CiteThis::Textbook.all.each.with_index(1) do |textbook, i|
      old_primary_id = textbook.id

      # Assign references to temporary textbook to avoid foreign key errors
      CiteThis::TextbookReferencing.where(textbook_id: old_primary_id).update_all(textbook_id: temp_textbook.id)
      
      # Change textbook primary key to match the index
      CiteThis::Textbook.where(id: old_primary_id).update_all(id: i)
      
      # Reassign references to textbook
      CiteThis::TextbookReferencing.where(textbook_id: temp_textbook.id).update_all(textbook_id: i)
    end

    # Textbook References
    CiteThis::TextbookReferencing.all.each.with_index(1) do |reference, i|
      CiteThis::TextbookReferencing.where(id: reference.id).update_all(id: i)
    end

    # Destroy temporary textbook
    temp_textbook = CiteThis::Textbook.find_by(title: "Temp", authors: "Temp", publisher: "Temp", year: 1990)
    temp_textbook.destroy!
    puts("FINISHED TEXTBOOKS")
  end

  desc "Re-ID experimental property sets to reduce the ID number"
  task re_id_properties: [:environment] do
    ExperimentalPropertySet.all.each.with_index(2) do |property_set, i|
      ExperimentalPropertySet.where(id: property_set.id).update_all(id: i)
    end
  end

  desc "Reset auto increment in SQL tables"
  task reset_auto_increment: [:environment] do
    ActiveRecord::Base.connection.tables.each do |table|
      ActiveRecord::Base.connection.execute("ALTER TABLE #{table} AUTO_INCREMENT = 1;")
    end
  end

  # Generate an NP-MRD ID given a primary ID
  def generate_np_mrd_id(primary_id)
    number_of_zeroes = 7 - primary_id.to_i.digits.count

    return "NP" + ("0" * number_of_zeroes) + primary_id
  end

  def clear_cache(natural_product)
    natural_product.load_thumb_from_url "http://moldb.wishartlab.com/molecules/#{natural_product.np_mrd_id}/thumb.png"
    natural_product.update_cached_attributes
    natural_product.save!

    Rails.cache.delete_matched("moldbi\/structure-resources\/#{natural_product.np_mrd_id}")
    puts("MolDB structure resource")
    Rails.cache.delete_matched("moldbi\/curation-resources\/#{natural_product.np_mrd_id}")
    puts("MolDB curation resource")
    Rails.cache.delete_matched("natural_products\/#{natural_product.id}-")
  end

  # Prints task completion percentage
  def percent_complete(total, percentage_displayed, current_index)
    percent_done = (current_index.to_f / total.to_f) * 100.0

    if percent_done.round > percentage_displayed
      puts("#{percent_done.round}% completed")
      return percent_done.round
    else
      return percentage_displayed
    end
  end
end

