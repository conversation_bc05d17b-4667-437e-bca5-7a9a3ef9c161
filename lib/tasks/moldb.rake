require 'progress_bar'
require 'zip'
require 'builder'
require 'admin_mailer'
require 'wishart/task_support/zipper'
require 'progress_bar'

namespace :moldb do
    

    desc "Copy spectra files from moldb to NP-MRD"
    task :copy_spectra_files_over => [:environment] do
        mailer = AdminMailer.build "NP-MRD",
            subject: "Spectra files",
            message: "Copy over spectra files from moldb to NP-MRD."

        begin
            # system "scp -r moldb@************:project/current/public/downloads/exports/hmdb/*/NP-MRD* ~/zach/np-mrd/public/system/downloads/current/spectral_data/"
            system "scp -r moldb@************:project/current/public/downloads/exports/hmdb/*/NP-MRD* ~/project/current/public/system/downloads/current/spectral_data/"
            # system "scp -r moldb@*************:project/current/public/downloads/exports/hmdb/*/NP-MRD* ~/project/current/public/system/downloads/current/spectral_data/"
            mailer.notify_success!
        rescue => e
            raise if Rails.env.development?
            mailer.notify_error! e
        end
    end
end