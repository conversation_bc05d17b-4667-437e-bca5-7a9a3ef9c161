require 'zip'
require 'csv'
require 'builder'
require 'admin_mailer'
require 'wishart/task_support/zipper'

namespace :retention_index_compound_update_db do

	desc 'Update the retention_index_table with new entries'
	task update_retention_index_table_with_new_entries: [:flag1, :flag2] do
		puts RetentionIndexCompound.all.count
        
		ActiveRecord::Base.transaction do
		  CSV.read("public/python/output_test/dersemistdnp_transformer/run_0/results/train_results.csv", headers: true, header_converters: :symbol).each do |row|
		    #puts row[:ri_compound_name]
		    #puts row[:ri_compound_smiles]
		    #puts row[:retention_index_value]
            
            #ADD a value: for derivatized type, 
			#sc = RetentionIndexCompound.create!(ri_compound_name: row[:Name].nil? ? nil : row[:Name].strip, 
			#					ri_compound_smiles: row[:smiles].nil? ? nil : row[:smiles].strip, 
			#					retention_index_value: row[:Predicted_RI].nil? ? nil : row[:Predicted_RI].strip),
            #                   compound_stationary_phase: row[:Stationary_Phase].nil? ? nil : row[:Stationary_Phase].strip)
		  end
		end
		puts RetentionIndexCompound.all.count
	end

	task reset_retention_index_table: [:environment] do
		RetentionIndexCompound.all.delete_all
		ActiveRecord::Base.connection.execute('ALTER TABLE retention_index_compounds AUTO_INCREMENT = 1')
	end
end



