namespace :import_anchi do

  desc "import new compounds with mol file"
  task :new_compound_mol => [:environment] do
    #NaturalProduct.transaction do
      CSV.open("data/NPA_compounds_to_add_test.tsv", "r", :col_sep => "\t", quote_char: "|").each do |row|
        npa_id, name, formula, mw, mass, inchi, inchikey, smiles, origin, origin_mod, genus, origin_species, pubmed_id = row
        puts "#{name}  #{npa_id}.  #{inchikey}"

        n_len = name.length
        print "#{n_len}"

        if name.length<4
          next
        end

        m = NaturalProduct.find_by_name(name)
        if !m.nil?
          puts "#{npa_id}.  #{name} is in NP-MRD #{m.np_mrd_id} as #{m.name}. #{m.moldb_inchikey}"
          begin
            file = "data/mol_files/#{npa_id}.mol"
            mol_file = File.read(file)
            #puts "#{mol_file}"
            m.structure = mol_file
            m.save!
            puts "#{name} structure Saved"
            #puts "#{m.name}   #{m.id}   #{m.np_mrd_id}"
          rescue Exception => e
            # puts "#{name} NOT Saved " + e.message
          end  
          puts "Here"
        else
          puts "new compound to add"
          m = NaturalProduct.new
          if origin == "Bacterium"
            origin_mod = "Eubacteria"
            puts "#{origin_mod}"
          end
          m.name = name
          m.origin = origin_mod
          m.export = 1
          article = CiteThis::Article.where(pubmed_id: pubmed_id).first_or_create!  #add the references
          m.articles << article
          m.save!
          puts "#{name} Saved"
          begin
            file = "data/mol_files/#{npa_id}.mol"
            mol_file = File.read(file)
            #puts "#{mol_file}"
            m.structure = mol_file
            m.save!
            puts "#{name} structure Saved"
            #puts "#{m.name}   #{m.id}   #{m.np_mrd_id}"
          rescue Exception => e
            puts "#{name} NOT Saved " + e.message
          end
        end
      end
    #end
  end


  desc "import new compounds with mol file"
  task :new_compound_mol_bk => [:environment] do
    #NaturalProduct.transaction do
      CSV.open("data/NPA_compounds_to_add_test.tsv", "r", :col_sep => "\t", quote_char: "|").each do |row|
        npa_id, name, formula, mw, mass, inchi, inchikey, smiles, origin, origin_mod, genus, origin_species, pubmed_id = row
        puts "#{name}  #{npa_id}.  #{inchikey}"

        n_len = name.length
        print "#{n_len}"

        if name.length<4
          next
        end

        m = NaturalProduct.find_by_moldb_inchi(inchi)
        if !m.nil?
          puts "#{npa_id}.  #{name} iis in NP-MRD #{m.np_mrd_id} as #{m.name}. #{m.moldb_inchikey}"
          
          if m.export == 1
              puts "#{npa_id}.  #{name} is in MRD #{m.np_mrd_id} as #{m.name}"
          end
          puts "Here"

        else
          mm = NaturalProduct.find_by_name(name)
          if !mm.nil?
            puts " #{name} is found in NP-MRD #{mm.np_mrd_id} as #{mm.name}"
          else
            m = NaturalProduct.new
            if origin == "Bacterium"
              origin_mod = "Eubacteria"
              puts "#{origin_mod}"
            end
            m.name = name
            m.origin = origin_mod
            m.export = 1
            puts "There"
            article = CiteThis::Article.where(pubmed_id: pubmed_id).first_or_create!  #add the references
            m.articles << article
            #m.save!
            puts "#{name} Saved"
            begin
              file = "data/mol_files/#{npa_id}.mol"
              mol_file = File.read(file)
              #puts "#{mol_file}"
              m.structure = mol_file
              #m.save!
              puts "#{name} structure Saved"
              #puts "#{m.name}   #{m.id}   #{m.np_mrd_id}"
            rescue Exception => e
              # puts "#{name} NOT Saved " + e.message
            end
          end
        end
      end
    #end
  end

  desc "Delete natural products"
  task :delete_natural_products => [:environment] do
    NaturalProduct.transaction do
      CSV.open("data/compounds_to_delete.tsv", "r", :col_sep => "\t", quote_char: "|").each do |row|
        npmrd_id = row
        natural_product = NaturalProduct.find_by(np_mrd_id: npmrd_id)
        natural_product.destroy!
        puts "Delete #{npmrd_id}"
      end
    end
  end

  desc "Unexport natural products"
    task :unexport_natural_products => [:environment] do
      NaturalProduct.transaction do
        CSV.open("data/to_unexport.tsv", "r", :col_sep => "\t", quote_char: "|").each do |row|
          npmrd_id, name, origin = row
          natural_product = NaturalProduct.find_by(np_mrd_id: npmrd_id)
          if !natural_product.nil?
            puts "#{natural_product.id}"
            ChemicalShiftSubmission.where(natural_product_id: natural_product.id).each do |submission|
            submission.destroy!
            puts "DDelete #{npmrd_id}.  #{submission.id}"
          end
          #natural_product.destroy!
          natural_product.export=0
          natural_product.comment = "Not a natural product, unexported by An Chi on Sep. 13, 2020"
          natural_product.save!
          puts "Delete #{npmrd_id}"
        end
      end
    end
  end


  desc "update compounds"
  task :update_compounds => [:environment] do
    NaturalProduct.transaction do
      #CSV.open("data/compound_origin_pathbank.tsv", "r", :col_sep => "\t", quote_char: "|").each do |row|
      #CSV.open("data/compound_origin_HMDB.tsv", "r", :col_sep => "\t", quote_char: "|").each do |row|
      #CSV.open("data/compound_origin.tsv", "r", :col_sep => "\t", quote_char: "|").each do |row|
      CSV.open("data/NP0001675_update.tsv", "r", :col_sep => "\t", quote_char: "|").each do |row|
        npmrd_id, formula, inchi, inchikey, smiles, mw_avg, mw_mono = row
        puts "#{npmrd_id}"
        g = NaturalProduct.find_by(np_mrd_id: npmrd_id)

        if !g.nil?
          #g.origin = origin

          # article = CiteThis::Article.where(:pubmed_id => pubmed_id).first_or_create!  #add the references
          # g.articles << article
          g.moldb_formula = formula
          g.moldb_inchi = inchi
          g.moldb_inchikey = inchikey
          g.moldb_smiles = smiles
          g.moldb_average_mass = mw_avg
          g.moldb_mono_mass = mw_mono
          begin
            g.save!
            puts "Updated!"
          rescue Exception => e
            puts "#{npmrd_id} NOT Updated " + e.message
          end
        else
          puts "#{npmrd_id} not found"
        end

      end
    end
  end


  desc "Import species"
  task import_species: [:environment] do
    CSV.open("data/species_to_add.tsv", "r", :col_sep => "\t", quote_char: "|").each do |row|
      sci_name, genus, family, order, classs, phylum, kingdom = row

      species = Species.find_by(scientific_name: sci_name)
      if species.nil?
        s = Species.new
        s.kingdom = kingdom
        s.phylum = phylum
        s.class = classs
        s.order = order
        s.family = family
        s.scientific_name = sci_name
        s.save!
      else 
        puts "#{sci_name} laready exists"
      end
    end
  end



  desc "Import a single species to natural product mapping"
  task single_species_mapping: [:environment] do
    mapping = SpeciesMapping.new
    mapping.species_id = Species.find_by(scientific_name: "Strychnos nux-vomica L.").id
    mapping.species_mappable_id = NaturalProduct.find_by(np_mrd_id: "NP0000001").id
    mapping.species_mappable_type = "NaturalProduct"
    mapping.source = "Seed" # e.g. Whole Plant, Leaf, Root
    mapping.save!

    # Reference
    mapping.articles << CiteThis::Article.find_or_create_by(pubmed_id: "7912074")
  end

  desc "Import species to natural product mapping with pubmed reference"
  task import_species_mapping_pubmed: [:environment] do
  	CSV.open("data/species_mapping_pubmed.tsv", "r", :col_sep => "\t", quote_char: "|").each do |row|
  	  npmrd_id, name, source, sci_name, genus, family, order, classs, phylum, kingdom, pubmed_id = row

      mapping = SpeciesMapping.new
      species = Species.find_by(scientific_name: sci_name)
      puts "#{npmrd_id}"
      puts "#{species.kingdom}"
      if !species.nil?
        m = NaturalProduct.find_by(np_mrd_id: npmrd_id)
        puts "#{npmrd_id}   #{m.id}"

        if !m.nil?
          mapping = SpeciesMapping.new
          mapping.species_id = species.id
          mapping.species_mappable_id = m.id
          mapping.species_mappable_type = "NaturalProduct"
          if !source.nil?
            mapping.source = source
          end
        else
          puts "#{npmrd_id} not found"
        end
      else 
    	  puts "#{sci_name} not found"
      end

      # Reference
      #mapping.articles << CiteThis::Article.find_or_create_by(pubmed_id: pubmed_id)
      article = CiteThis::Article.where(:pubmed_id => pubmed_id).first_or_create!  #add the references
      mapping.articles << article
      mapping.save!
    end
  end

    desc "Import species to natural product mapping with textbook reference"
  task import_species_mapping_textbook: [:environment] do
    CSV.open("data/species_mapping_textbook.tsv", "r", :col_sep => "\t", quote_char: "|").each do |row|
      npmrd_id, name, source, sci_name, genus, family, order, classs, phylum, kingdom, reference, author, year, publisher, title = row

      mapping = SpeciesMapping.new
      species = Species.find_by(scientific_name: sci_name)
      puts "#{npmrd_id}.   #{sci_name}"
      puts "#{species.kingdom}"
      if !species.nil?
        m = NaturalProduct.find_by(np_mrd_id: npmrd_id)
        puts "#{npmrd_id}   #{m.id}"

        if !m.nil?
          mapping = SpeciesMapping.new
          mapping.species_id = species.id
          mapping.species_mappable_id = m.id
          mapping.species_mappable_type = "NaturalProduct"
          if !source.nil?
            mapping.source = source
          end
        else
          puts "#{npmrd_id} not found"
        end
      else 
        puts "#{sci_name} not found"
      end

      # Reference
      #mapping.articles << CiteThis::Article.find_or_create_by(pubmed_id: pubmed_id)
      textbook = CiteThis::Textbook.where(:authors => author, :year => year, :publisher => publisher, :title => title).first_or_create!  #add the references
      mapping.textbooks << textbook
      mapping.save!
    end
  end

end