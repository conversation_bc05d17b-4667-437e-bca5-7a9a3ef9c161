namespace :update do

  desc "Sync natural product structures from HMDB using the hmdb_id column"
  task structures_to_match_hmdb: [:environment] do
    errored_hmdb_ids = []

    NaturalProduct.where.not(hmdb_id: nil).each do |natural_product|
      begin
        # Check what HMDB's InChIKey is
        inchikey = nil
        sdf_file_lines = open("http://moldb.wishartlab.com/structures/#{natural_product.hmdb_id}.sdf").readlines
        
        # Parse SDF file for the InChIKey
        flagged_line = false
        sdf_file_lines.each do |line|
          if flagged_line
            inchikey = line.strip.chomp
            break
          elsif line.include? "<INCHI_KEY>"
            flagged_line = true
          end
        end

        # If the InChIKeys don't match, replace the current structure with HMDB's
        if natural_product.moldb_inchikey != inchikey
          natural_product.structure = open("http://moldb.wishartlab.com/structures/#{natural_product.hmdb_id}.mol").read
          natural_product.save!
          puts("FIXED: #{natural_product.np_mrd_id}")
        end
      rescue
        errored_hmdb_ids.append(natural_product.hmdb_id)
      end
    end

    puts("ERRORED: #{errored_hmdb_ids.inspect}")
  end


  desc "Generate threeD mols"
  task generate_threeDmol: [:environment] do
    NaturalProduct.find_each do |np|
      begin
        puts np.load_threeDmol
      rescue Exception => e 
        puts e.message
      end
    end
  end
end
