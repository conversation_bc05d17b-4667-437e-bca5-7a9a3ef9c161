require "#{Rails.root}/app/helpers/application_helper"
include ApplicationHelper
require 'csv'
#require 'jchem'

namespace :casmi do
  desc "Make a random training set"
  task :get_training_set => :environment do
    # We don't want compounds with these classes
    excluded_classes = File.read("data/training/excluded_classes2.txt").split("\n")
    # 1/3 ESI positive, 1/3 ESI negative, and 1/3 EI
    types = ["positive", "positive", "negative", "negative", "ei"]
    type = types[0]
    min_ref = 10
    num_compounds = 1200 # Number of compounds to use
    File.open("data/training/training_compounds-" + num_compounds.to_s + "computed.tsv", "wb") do |file|
      test_compounds = []
      file.puts ["ID", "Type", "Name", "Source", "InChIKey", "InChI", "SMILES", "Computed Spectra", "Experimental Spectra", "# References", "Direct Parent", "Ancestors"].join("\t")
      while test_compounds.length < num_compounds
        random = rand(1...DatabaseMolecule.count)
        molecule = DatabaseMolecule.find_by(id: random)
        if molecule.present?
          if type == "positive"
            spectra = molecule.spectra.where(spectra_type: "ESI", ion_mode: "positive", method: "Computed")
            spectra_exp = molecule.spectra.where(spectra_type: "ESI", ion_mode: "positive", method: "Experimental")
          elsif type == "negative"
            spectra = molecule.spectra.where(spectra_type: "ESI", ion_mode: "negative", method: "Computed")
            spectra_exp = molecule.spectra.where(spectra_type: "ESI", ion_mode: "negative", method: "Experimental")
          else # EI
            spectra = molecule.spectra.where(spectra_type: "EI", ion_mode: "positive", method: "Computed")
            spectra_exp = molecule.spectra.where(spectra_type: "EI", ion_mode: "positive", method: "Experimental")
          end
          if molecule.classification.present? && # has classification
            (molecule.classification.ancestors & excluded_classes).blank? && # classification is allowed
            (spectra.count > 0) && # has spectra
            !(test_compounds.any? { |c| c[0].inchi_key == molecule.inchi_key}) # inchi is not already in test set

            if (test_compounds.length < (num_compounds / 2)  && (molecule.reference_count.try(:count) || 0) >= min_ref)
              test_compounds << [molecule, type, spectra.count, spectra_exp.count]
              type = types[test_compounds.length % types.length]
            elsif (test_compounds.length >= (num_compounds / 2))
              test_compounds << [molecule, type, spectra.count, spectra_exp.count]
              type = types[test_compounds.length % types.length]
            end
          end
        end
      end

      test_compounds.each do |tc|
        file.puts [tc[0].id, tc[1], tc[0].name, tc[0].source, tc[0].inchi_key, tc[0].inchi, tc[0].smiles, tc[2], tc[3], (tc[0].reference_count.try(:count) || 0), tc[0].classification.try(:direct_parent), tc[0].classification.try(:ancestors).try(:join, "; ")].join("\t")
      end
    end
  end

  desc "Make a random training set where query compounds have computed and experimental spectra"
  task :get_experimental_training_set => :environment do
    # We don't want compounds with these classes
    excluded_classes = File.read("data/training/excluded_classes.txt").split("\n")
    # 1/3 ESI positive, 1/3 ESI negative, and 1/3 EI
    test_esi_compounds = []
    # Get ALL potential ESI candidates
    File.open("data/training/experimental_training_compounds_esi.tsv", "wb") do |file|
      file.puts ["ID", "Type", "Name", "Source", "InChIKey", "InChI", "SMILES", "Computed Spectra", "Experimental Spectra", "# References", "Direct Parent", "Ancestors"].join("\t")
      DatabaseMolecule.joins(:spectra).where(spectra: {spectra_type: "ESI", method: "Experimental"}).where("lower(spectra.collision_energy) REGEXP ?", '^[1,2,4]0(\.0)?[[:blank:]]?(e?v|cid)?$').each do |molecule|

        experimental = molecule.spectra.where(spectra: {spectra_type: "ESI", method: "Experimental", spectra_source: molecule.source}).where("lower(spectra.collision_energy) REGEXP ?", '^[1,2,4]0(\.0)?[[:blank:]]?(e?v|cid)?$')
        experimental.each do |s|
          computed = molecule.spectra.where(spectra: {spectra_type: "ESI", method: "Computed", ion_mode: s.ion_mode})

          if computed.present? && # has computed
            molecule.classification.present? && # has classification
            (molecule.classification.ancestors & excluded_classes).blank? && # classification is allowed
            !(test_esi_compounds.any? { |c| c[0].inchi_key == molecule.inchi_key && c[1] == s.ion_mode }) # inchi is not already in test set
            test_esi_compounds << [molecule, s.ion_mode, computed.count, experimental.count]
          end
        end
      end

      test_esi_compounds.each do |tc|
        file.puts [tc[0].id, tc[1], tc[0].name, tc[0].source, tc[0].inchi_key, tc[0].inchi, tc[0].smiles, tc[2], tc[3], (tc[0].reference_count.try(:count) || 0), tc[0].classification.try(:direct_parent), tc[0].classification.try(:ancestors).try(:join, "; ")].join("\t")
      end
    end

    test_ei_compounds = []
    # Get ALL potential EI candidates
    File.open("data/training/experimental_training_compounds_ei.tsv", "wb") do |file|
      file.puts ["ID", "Type", "Name", "Source", "InChIKey", "InChI", "SMILES", "Computed Spectra", "Experimental Spectra", "# References", "Direct Parent", "Ancestors"].join("\t")
      DatabaseMolecule.joins(:spectra).where(spectra: {spectra_type: "EI", method: "Experimental"}).where("lower(spectra.collision_energy) REGEXP ?", '^\-?70(\.0)?[[:blank:]]?(e?v)?$').each do |molecule|

        experimental = molecule.spectra.where(spectra: {spectra_type: "EI", method: "Experimental", spectra_source: molecule.source}).where("lower(spectra.collision_energy) REGEXP ?", '^\-?70(\.0)?[[:blank:]]?(e?v)?$')
        experimental.each do |s|
          computed = molecule.spectra.where(spectra: {spectra_type: "EI", method: "Computed"})

          if computed.present? && # has computed
            molecule.classification.present? && # has classification
            (molecule.classification.ancestors & excluded_classes).blank? && # classification is allowed
            !(test_ei_compounds.any? { |c| c[0].inchi_key == molecule.inchi_key }) # inchi is not already in test set
            test_ei_compounds << [molecule, "ei", computed.count, experimental.count]
          end
        end
      end

      test_ei_compounds.each do |tc|
        file.puts [tc[0].id, tc[1], tc[0].name, tc[0].source, tc[0].inchi_key, tc[0].inchi, tc[0].smiles, tc[2], tc[3], (tc[0].reference_count.try(:count) || 0), tc[0].classification.try(:direct_parent), tc[0].classification.try(:ancestors).try(:join, "; ")].join("\t")
      end
    end

    # Get a random selection of ESI and EI candidates
    File.open("data/training/experimental_training_compounds.tsv", "wb") do |file|
      num_esi = 316
      num_ei = 284
      i = 0
      while i < num_esi
        random = rand(1...test_esi_compounds.length)
        tc = test_esi_compounds[random - 1]

        file.puts [tc[0].id, tc[1], tc[0].name, tc[0].source, tc[0].inchi_key, tc[0].inchi, tc[0].smiles, tc[2], tc[3], (tc[0].reference_count.try(:count) || 0), tc[0].classification.try(:direct_parent), tc[0].classification.try(:ancestors).try(:join, "; ")].join("\t")
        i += 1
      end

      i = 0
      while i < num_ei
        random = rand(1...test_ei_compounds.length)
        tc = test_ei_compounds[random - 1]

        file.puts [tc[0].id, tc[1], tc[0].name, tc[0].source, tc[0].inchi_key, tc[0].inchi, tc[0].smiles, tc[2], tc[3], (tc[0].reference_count.try(:count) || 0), tc[0].classification.try(:direct_parent), tc[0].classification.try(:ancestors).try(:join, "; ")].join("\t")
        i += 1
      end
    end
  end

  desc "List compounds that have experimental spectra"
  task :get_experimental_only_set => :environment do
    # We don't want compounds with these classes
    excluded_classes = File.read("data/training/excluded_classes.txt").split("\n")
    # 1/3 ESI positive, 1/3 ESI negative, and 1/3 EI
    File.open("data/training/experimental_only_compounds_esi.tsv", "wb") do |file|
      test_compounds = []
      file.puts ["ID", "Type", "Name", "Source", "InChIKey", "InChI", "SMILES", "Computed Spectra", "Experimental Spectra", "# References", "Direct Parent", "Ancestors"].join("\t")
      DatabaseMolecule.joins(:spectra).where(spectra: {spectra_type: "ESI", method: "Experimental"}).where("lower(spectra.collision_energy) REGEXP ?", '^[1,2,4]0(\.0)?[[:blank:]]?(e?v|cid)?$').each do |molecule|

        experimental = molecule.spectra.where(spectra: {spectra_type: "ESI", method: "Experimental"}).where("lower(spectra.collision_energy) REGEXP ?", '^[1,2,4]0(\.0)?[[:blank:]]?(e?v|cid)?$')
        experimental.each do |s|
          computed = molecule.spectra.where(spectra: {spectra_type: "ESI", method: "Computed", ion_mode: s.ion_mode})

          if !(test_compounds.any? { |c| c[0].inchi_key == molecule.inchi_key && c[1] == s.ion_mode }) # inchi is not already in test set
            test_compounds << [molecule, s.ion_mode, computed.count, experimental.count]
          end
        end
      end

      test_compounds.each do |tc|
        file.puts [tc[0].id, tc[1], tc[0].name, tc[0].source, tc[0].inchi_key, tc[0].inchi, tc[0].smiles, tc[2], tc[3], (tc[0].reference_count.try(:count) || 0), tc[0].classification.try(:direct_parent), tc[0].classification.try(:ancestors).try(:join, "; ")].join("\t")
      end
    end

    File.open("data/training/experimental_only_compounds_ei.tsv", "wb") do |file|
      test_compounds = []
      file.puts ["ID", "Type", "Name", "Source", "InChIKey", "InChI", "SMILES", "Computed Spectra", "Experimental Spectra", "# References", "Direct Parent", "Ancestors"].join("\t")
      DatabaseMolecule.joins(:spectra).where(spectra: {spectra_type: "EI", method: "Experimental"}).where("lower(spectra.collision_energy) REGEXP ?", '^\-?70(\.0)?[[:blank:]]?(e?v)?$').each do |molecule|

        experimental = molecule.spectra.where(spectra: {spectra_type: "EI", method: "Experimental"}).where("lower(spectra.collision_energy) REGEXP ?", '^\-?70(\.0)?[[:blank:]]?(e?v)?$')
        experimental.each do |s|
          computed = molecule.spectra.where(spectra: {spectra_type: "EI", method: "Computed"})

          if !(test_compounds.any? { |c| c[0].inchi_key == molecule.inchi_key }) # inchi is not already in test set
            test_compounds << [molecule, "ei", computed.count, experimental.count]
          end
        end
      end

      test_compounds.each do |tc|
        file.puts [tc[0].id, tc[1], tc[0].name, tc[0].source, tc[0].inchi_key, tc[0].inchi, tc[0].smiles, tc[2], tc[3], (tc[0].reference_count.try(:count) || 0), tc[0].classification.try(:direct_parent), tc[0].classification.try(:ancestors).try(:join, "; ")].join("\t")
      end
    end
  end

  desc "Get all compounds with at least two spectra, for training"
  task :get_full_training_set_old => :environment do
    # We don't want compounds with these classes
    excluded_classes = File.read("data/training/excluded_classes.txt").split("\n")
    # 1/3 ESI positive, 1/3 ESI negative, and 1/3 EI
    test_esi_compounds = []
    # Get ALL potential ESI candidates
    File.open("data/training/results-allexp/experimental_training_compounds_esi.tsv", "wb") do |file|
      file.puts ["ID", "Type", "Name", "Source", "InChIKey", "InChI", "SMILES", "Computed Spectra", "Experimental Spectra", "QTOF Experimental Spectra", "# References", "Direct Parent", "Ancestors"].join("\t")
      # Molecules with experimental
      DatabaseMolecule.joins(:spectra).where(spectra: {spectra_type: "ESI", method: "Experimental"}).each do |molecule|

        # All experimental
        experimental = molecule.spectra.where(spectra: {spectra_type: "ESI", method: "Experimental", spectra_source: molecule.source})

        # Check for each ion mode (pos/neg)
        experimental.each do |s|
          computed = molecule.spectra.where(spectra: {spectra_type: "ESI", method: "Computed", ion_mode: s.ion_mode})
          # Experimental at 10, 20, 40 minus the current spectra
          qtof_experimental = molecule.spectra.where(spectra: {spectra_type: "ESI", method: "Experimental", ion_mode: s.ion_mode, spectra_source: molecule.source}).where("lower(spectra.collision_energy) REGEXP ?", '^[1,2,4]0(\.0)?[[:blank:]]?(e?v|cid)?$')

          if (computed.present? || (qtof_experimental - [s]).present?) && # has computed or >= 1 other q-tof experimental
            molecule.classification.present? && # has classification
            (molecule.classification.ancestors & excluded_classes).blank? && # classification is allowed
            !(test_esi_compounds.any? { |c| c[0].id == molecule.id && c[1] == s.ion_mode }) # molecule is not already in test set
            test_esi_compounds << [molecule, s.ion_mode, computed.count, experimental.where(ion_mode: s.ion_mode).count, qtof_experimental.count]
          end
        end
      end

      test_esi_compounds.each do |tc|
        file.puts [tc[0].id, tc[1], tc[0].name, tc[0].source, tc[0].inchi_key, tc[0].inchi, tc[0].smiles, tc[2], tc[3], (tc[0].reference_count.try(:count) || 0), tc[0].classification.try(:direct_parent), tc[0].classification.try(:ancestors).try(:join, "; ")].join("\t")
      end
    end

    test_ei_compounds = []
    # Get ALL potential EI candidates
    File.open("data/training/results-allexp/experimental_training_compounds_ei.tsv", "wb") do |file|
      file.puts ["ID", "Type", "Name", "Source", "InChIKey", "InChI", "SMILES", "Computed Spectra", "Experimental Spectra", "QTOF Experimental Spectra", "# References", "Direct Parent", "Ancestors"].join("\t")
      DatabaseMolecule.joins(:spectra).where(spectra: {spectra_type: "EI", method: "Experimental"}).each do |molecule|

        experimental = molecule.spectra.where(spectra: {spectra_type: "EI", method: "Experimental", spectra_source: molecule.source})

        experimental.each do |s|
          computed = molecule.spectra.where(spectra: {spectra_type: "EI", method: "Computed"})
          # Experimental at 70 minus the current spectra
          qtof_experimental = molecule.spectra.where(spectra: {spectra_type: "EI", method: "Experimental", spectra_source: molecule.source}).where("lower(spectra.collision_energy) REGEXP ?", '^\-?70(\.0)?[[:blank:]]?(e?v)?$')

          if (computed.present? || (qtof_experimental - [s]).present?) && # has computed
            molecule.classification.present? && # has classification
            (molecule.classification.ancestors & excluded_classes).blank? && # classification is allowed
            !(test_ei_compounds.any? { |c| c[0].id == molecule.id }) # molecule is not already in test set
            test_ei_compounds << [molecule, "ei", computed.count, experimental.where(ion_mode: s.ion_mode).count, qtof_experimental.count]
          end
        end
      end

      test_ei_compounds.each do |tc|
        file.puts [tc[0].id, tc[1], tc[0].name, tc[0].source, tc[0].inchi_key, tc[0].inchi, tc[0].smiles, tc[2], tc[3], tc[4], (tc[0].reference_count.try(:count) || 0), tc[0].classification.try(:direct_parent), tc[0].classification.try(:ancestors).try(:join, "; ")].join("\t")
      end
    end

    # Get a random selection of ESI and EI candidates
    File.open("data/training/results-allexp/experimental_training_compounds.tsv", "wb") do |file|
      test_esi_compounds.concat(test_ei_compounds).each do |tc|
        file.puts [tc[0].id, tc[1], tc[0].name, tc[0].source, tc[0].inchi_key, tc[0].inchi, tc[0].smiles, tc[2], tc[3], tc[4], (tc[0].reference_count.try(:count) || 0), tc[0].classification.try(:direct_parent), tc[0].classification.try(:ancestors).try(:join, "; ")].join("\t")
      end
    end
  end

  # **THIS IS THE ONE WE ARE USING AS OF FEB 1 2018
  desc "Get all compounds with at least two spectra, for training"
  task :get_full_training_set => :environment do
    file = "data/training/results-sept14/experimental_training_compounds_all.tsv"
    if !File.exists?(file)
      # We don't want compounds with these classes
      excluded_classes = File.read("data/training/excluded_classes.txt").split("\n")
      test_compounds = []
      # Get ALL potential ESI candidates
      File.open(file, "wb") do |file|
        file.puts ["ID", "Type", "Name", "Source", "InChIKey", "InChI", "SMILES", "Computed Spectra", "Experimental Spectra", "# References", "Direct Parent", "Ancestors"].join("\t")
        # Molecules with experimental
        DatabaseMolecule.joins(:spectra).where(spectra: {method: "Experimental"}).each do |molecule|

          ["positive", "negative", "ei"].each do |type|
            if type == "ei"
              test, computed, exp, exp_annotated = molecule.remove_experimental_test_spectra("EI", "positive")
              c = molecule.spectra.where(method: "Computed", spectra_type: "EI", ion_mode: "positive")
              e = molecule.spectra.where(method: "Experimental", spectra_type: "EI", ion_mode: "positive")
            else
              test, computed, exp, exp_annotated = molecule.remove_experimental_test_spectra("ESI", type)
              c = molecule.spectra.where(method: "Computed", spectra_type: "ESI", ion_mode: type)
              e = molecule.spectra.where(method: "Experimental", spectra_type: "ESI", ion_mode: type)
            end

            if test["energy0"].present? && (test["energy0"].length < 10000) &&
              (molecule.inchi.present? && (Query.new.inchi_to_size(molecule.inchi) < Query::MAX_COMPOUND_SIZE)) &&
              (computed.values.compact.present? || exp.values.compact.present?) && # has computed or >= 1 other q-tof experimental
              molecule.classification.present? && # has classification
              (molecule.classification.ancestors & excluded_classes).blank? && # classification is allowed
              !(test_compounds.any? { |c| c[0].id == molecule.id && c[1] == type }) # molecule is not already in test set
              test_compounds << [molecule, type, c.count, e.count]
            end
          end
        end

        test_compounds.each do |tc|
          file.puts [tc[0].id, tc[1], tc[0].name, tc[0].source, tc[0].inchi_key, tc[0].inchi, tc[0].smiles, tc[2], tc[3], (tc[0].reference_count.try(:count) || 0), tc[0].classification.try(:direct_parent), tc[0].classification.try(:ancestors).try(:join, "; ")].join("\t")
        end
      end
    end

    ei_candidates = []
    esi_candidates = []
    first = true
    File.readlines(file).each do |line|
      if first
        first = false
        next
      end

      row = line.split("\t")

      if row[1] == "ei"
        ei_candidates << line
      else
        esi_candidates << line
      end
    end

    # TO DO: Increase set size to 5000 when we have enough compounds
    num_candidates = 5000
    File.open(file = "data/training/results-sept14/experimental_training_compounds_ei.tsv", "wb") do |file|
      file.puts ei_candidates.shuffle[0, num_candidates]
    end

    File.open(file = "data/training/results-sept14/experimental_training_compounds_esi.tsv", "wb") do |file|
      file.puts esi_candidates.shuffle[0, num_candidates]
    end
  end

  desc "200 ei compounds for testing"
  task :get_ei_testing_set => :environment do
    file = "data/training/results-sept14/experimental_training_compounds_all.tsv"
    ei_file = "data/training/results-sept14/experimental_training_compounds_ei.tsv"

    ei_training = []
    first = true
    File.readlines(ei_file).each do |line|
      if first
        first = false
        next
      end
      ei_training << line.split("\t")[0]
    end

    ei_testing = []
    first = true
    File.readlines(file).each do |line|
      if first
        first = false
        next
      end

      row = line.split("\t")

      if row[1] == "ei" && !ei_training.include?(row[0])
        ei_testing << line
      end
    end

    num_candidates = 200

    File.open(file = "data/casmi/ei_testing_compounds.tsv", "wb") do |file|
      file.puts ei_testing.shuffle[0, num_candidates]
    end

  end

  desc "Train scoring"
  task :train_scoring => :environment do

    overall_file = File.open("data/training/results-allexp/all_results-" + Time.now.to_i.to_s + ".txt", "w")
    overall_file.puts [ "Coefficients", "Rank 1", "Rank 3", "Rank 10", "Medal Score", "Average Query Rank" ].join("\t")
    count = 0

    anc_co = 0.0
    while anc_co <= 1.0
      ref_co = 0.0
      while ref_co <= (1.0 - anc_co)
        cfm_co = (1.0 - ref_co - anc_co)
        puts "S: " + cfm_co.to_s + " R: " + ref_co.to_s + " C: " + anc_co.to_s

        # casmi_train(cfm_co, ref_co, anc_co, overall_file)
        casmi_exp_train(cfm_co, ref_co, anc_co, "data/training/results-allexp", overall_file)

        ref_co += 0.1
        count += 1
      end
      anc_co += 0.1
    end

    puts "COUNT: " + count.to_s

    overall_file.close
  end

  # ***CROSS VALIDATION TRAINING, THIS IS THE ONE WE ARE USING AS OF FEB 1 2018
  desc "Train scoring"
  task :train_restricted_scoring => :environment do

    ["esi", "ei"].each do |ion_mode|
      file = "experimental_training_compounds_" + ion_mode + ".tsv"
      compounds = File.readlines("data/training/results-sept14/" + file)
      # TO DO: Use 10 groups instead
      num_groups = 5
      size = compounds.length / num_groups
      subsets = []
      while compounds.length > 0
        subsets << compounds.shift(size)
      end

      groups = subsets.combination(num_groups - 1).to_a
      i = 1
      groups.each do |training|
        testing = (subsets - training).first
        ["train", "test"].each do |iteration|
          dir = "data/training/results-sept14/" + i.to_s + "-" + ion_mode + "-" + iteration
          if iteration == "test"
            list = testing
          else
            list = training.flatten
          end

          unless File.directory?(dir)
            FileUtils.mkdir_p(dir)
          end

          # Print lists of compounds for each iteration
          File.open(dir + "/training-compounds-" + Time.now.to_i.to_s + ".txt", "w") do |file|
            file.puts training
          end

          File.open(dir + "/testing-compounds-" + Time.now.to_i.to_s + ".txt", "w") do |file|
            file.puts testing
          end

          overall_file = File.open(dir + "/all_results-" + Time.now.to_i.to_s + ".txt", "w")
          overall_file.puts [ "Coefficients", "Rank 1", "Rank 3", "Rank 10", "Medal Score", "Average Query Rank" ].join("\t")
          count = 0

          anc_co = 0.0
          while anc_co <= 0.6
            ref_co = 0.0
            while ref_co <= [(1.0 - anc_co), 0.7].max
              cfm_co = (1.0 - ref_co - anc_co)
              if cfm_co >= 0.3 && cfm_co <= 1.0
                puts "S: " + cfm_co.to_s + " R: " + ref_co.to_s + " C: " + anc_co.to_s
                # casmi_train(cfm_co, ref_co, anc_co, overall_file)
                casmi_exp_train(cfm_co, ref_co, anc_co, dir, overall_file, list)
                count += 1
              end
              ref_co += 0.1
            end
            anc_co += 0.1
          end

          puts "COUNT: " + count.to_s

          overall_file.close
        end

        i += 1
      end
    end
  end

  desc "Train scoring"
  task :train_restricted_scoring_single, [:cfm_co, :ref_co, :anc_co] => :environment do |t, args|

    ["esi", "ei"].each do |ion_mode|
      file = "experimental_training_compounds_" + ion_mode + ".tsv"
      compounds = File.readlines("data/training/results-allexp/" + file)
      # TO DO: Use 10 groups instead
      num_groups = 5
      size = compounds.length / num_groups
      subsets = []
      while compounds.length > 0
        subsets << compounds.shift(size)
      end

      groups = subsets.combination(num_groups - 1).to_a
      i = 1
      groups.each do |training|
        testing = (subsets - training).first
        ["train", "test"].each do |iteration|
          dir = "data/training/results-allexp/" + i.to_s + "-" + ion_mode + "-" + iteration
          if iteration == "test"
            list = testing
          else
            list = training.flatten
          end

          unless File.directory?(dir)
            FileUtils.mkdir_p(dir)
          end

          # Print lists of compounds for each iteration
          File.open(dir + "/training-compounds-" + Time.now.to_i.to_s + ".txt", "w") do |file|
            file.puts training
          end

          File.open(dir + "/testing-compounds-" + Time.now.to_i.to_s + ".txt", "w") do |file|
            file.puts testing
          end

          overall_file = File.open(dir + "/all_results-" + Time.now.to_i.to_s + ".txt", "w")
          overall_file.puts [ "Coefficients", "Rank 1", "Rank 3", "Rank 10", "Medal Score", "Average Query Rank" ].join("\t")

          puts "S: " + args[:cfm_co].to_s + " R: " + args[:ref_co].to_s + " C: " + args[:anc_co].to_s
          casmi_exp_train(args[:cfm_co].to_f, args[:ref_co].to_f, args[:anc_co].to_f, dir, overall_file, list)

          overall_file.close
        end

        i += 1
      end
    end
  end

  desc "Train scoring for a single model"
  task :score_single, [:cfm_co, :ref_co, :anc_co] => :environment do |t, args|
    casmi_exp_train(args[:cfm_co].to_f, args[:ref_co].to_f, args[:anc_co].to_f, "data/training/results15")
  end

  desc "Test scoring on a single model"
  task :esi_test, [:cfm_co, :ref_co, :anc_co] => :environment do |t, args|
    dir = "data/testing/esi/#{args[:cfm_co]}-#{args[:ref_co]}-#{args[:anc_co]}"
    unless File.directory?(dir)
      FileUtils.mkdir_p(dir)
    end

    overall_file = File.open(dir + "/all_results-" + Time.now.to_i.to_s + ".txt", "w")
    overall_file.puts [ "Coefficients", "Rank 1", "Rank 3", "Rank 10", "Medal Score", "Average Query Rank" ].join("\t")
    list = []
    CSV.foreach("data/casmi/challenge_compounds.csv", headers: true) do |row|
      dm = DatabaseMolecule.find_by(source_id: row[0])

      list << [dm.id, row[3].downcase.strip, row[1].strip]
    end

    casmi_test(args[:cfm_co].to_f, args[:ref_co].to_f, args[:anc_co].to_f, dir, overall_file, list)
  end

  desc "Test scoring on a single ei model"
  task :ei_test, [:cfm_co, :ref_co, :anc_co] => :environment do |t, args|
    dir = "data/testing/ei/#{args[:cfm_co]}-#{args[:ref_co]}-#{args[:anc_co]}"
    unless File.directory?(dir)
      FileUtils.mkdir_p(dir)
    end

    overall_file = File.open(dir + "/all_results-" + Time.now.to_i.to_s + ".txt", "w")
    overall_file.puts [ "Coefficients", "Rank 1", "Rank 3", "Rank 10", "Medal Score", "Average Query Rank" ].join("\t")
    list = []
    File.readlines("data/casmi/ei_testing_compounds.tsv").each do |line|
      row = line.split("\t")
      dm = DatabaseMolecule.find_by(id: row[0])

      list << [dm.id, "ei", row[1].strip].join("\t")
    end

    casmi_exp_train(args[:cfm_co].to_f, args[:ref_co].to_f, args[:anc_co].to_f, dir, overall_file, list)
  end

  desc "METHOD 1: Test experimental spectra against computed spectra"
  def casmi_exp_train(s_weight, r_weight, a_weight, dir, overall_file = nil, list = nil)
    if list.blank?
      list = File.readlines(dir + "/experimental_training_compounds.tsv")
    end

    result_file = File.open(dir + "/results-S" + s_weight.round(2).to_s.gsub(".", "") + "-R" + r_weight.round(2).to_s.gsub(".", "") + "-C" + a_weight.round(2).to_s.gsub(".", "") + ".txt", "w")
    result_file.puts ["Query_Rank", "Score", "CFMID_Score", "Ref_Score", "Class_Score", "Query_Source", "Query_ID", "Predicted_Class", "Direct_Parent", "Alternative_Parents", "Ancestors", "Top_Ranked_Source", "Top_Ranked_Source_ID", "Top_Ranked_Original_Rank", "Score", "CFMID_Score", "Ref_Score", "Class_Score", "Num_Candidates", "Type"].join("\t")
    ranks = []

    first = true
    list.each do |line|
      if first
        first = false
        next
      end
      row = line.split("\t")

      molecule = DatabaseMolecule.find_by(id: row[0])
      if !molecule.present?
        next
      end

      type = row[1]
      puts molecule.id

      if type == "positive"
        databases = IdentifyQuery::ESI_DATABASES.values
        ion_mode = "positive"
        spectra_type = "ESI"
        adduct_type = "Neutral"
        mass = molecule.neutral_mass
      elsif type == "negative"
        databases = IdentifyQuery::ESI_DATABASES.values
        ion_mode = "negative"
        spectra_type = "ESI"
        adduct_type = "Neutral"
        mass = molecule.neutral_mass
      else # EI
        databases = IdentifyQuery::EI_DATABASES.values
        ion_mode = "positive"
        spectra_type = "EI"
        adduct_type = "M+"
        mass = molecule.neutral_mass + AdductCalculator::MASS_OF_ELECTRON
      end

      # test_spectra = molecule.make_experimental_spectra(spectra_type, ion_mode)
      test_spectra = molecule.remove_experimental_test_spectra(spectra_type, ion_mode)[0].except("annotations")

      if test_spectra["energy0"].present? && (test_spectra["energy0"].length < 100000)
        # Create a query with the compound spectra as the input spectra
        query = Query.new(
          type: "IdentifyQuery",
          spectra: test_spectra.flatten.join("\n"),
          database: databases,
          num_results: -1,
          threshold: 0.001,
          ppm_mass_tol: 10.0,
          abs_mass_tol: 0.01,
          scoring_function: "DotProduct",
          candidate_limit: 100,
          neutral_mass: nil,
          candidate_ppm_mass_tol: 50.0,
          candidate_abs_mass_tol: 0.5,
          parent_ion_mass: mass,
          parent_ion_mass_type: "Original",
          ion_mode: ion_mode,
          adduct_type: adduct_type,
          spectra_type: spectra_type,
          param_file: get_param_file(spectra_type, ion_mode),
          config_file: get_config_file(spectra_type, ion_mode),
          train: true # Need this flag to tell the query to make candidate spectra using the special training way (i.e. omitting certain spectra)
        )

        if query.valid? && query.convert_spectra && query.save
          output_file = "output.txt"

          query_string = "cfm-id-precomputed #{query.input_file.path} \"#{query.print_spectra_id}\" #{query.candidates_file.path} #{query.num_results} #{query.ppm_mass_tol} #{query.abs_mass_tol} #{query.scoring_function} #{output_file}"

          if CFMID.config[:extra_params]
            query_string = CFMID.config[:extra_params] + " " + query_string
          end
          stdin, stdout, stderr = Open3.popen3(query_string)

          stderr.gets(nil)
          stdout.gets(nil)

          stdin.close
          stdout.close
          stderr.close

          query.update_attributes(output_file: File.new(output_file))
          query_results = query.get_results(similarity_weight: s_weight, reference_weight: r_weight, ancestor_weight: a_weight)
          results = query_results[:results]

          result = results.find { |r|
            r[:inchi_key] == molecule.inchi_key
            # r[:database] == molecule.source && r[:database_id] == molecule.source_id
          }

          if result.blank?
            result = {}
            rank = 0
          else
            rank = result[:adjusted_rank]
          end

          ranks << rank

          # single_result_file = File.open(dir + "/results-" + row[0].to_s + "-S" + s_weight.round(2).to_s.gsub(".", "") + "-R" + r_weight.round(2).to_s.gsub(".", "") + "-C" + a_weight.round(2).to_s.gsub(".", "") + ".txt", "w")
          # single_result_file.puts results.first.keys.map(&:to_s).join("\t")
          # results.each do |r|
          #   single_result_file.puts r.values.map(&:to_s).join("\t")
          # end

          result_file.puts [rank, result[:combined_score], result[:cfm_id_score], result[:reference_score], result[:ancestor_score], molecule.source, molecule.source_id, query_results[:predicted_class].join("; "), result[:direct_parent], result[:alternative_parents].try(:join, "; "), result[:ancestors].try(:join, "; "), results.first[:database], results.first[:database_id], results.first[:original_rank], results.first[:combined_score], results.first[:cfm_id_score], results.first[:reference_score], results.first[:ancestor_score], results.length, type].join("\t")
          query.destroy
        else
          puts "INVALID"
        end
      else
        puts "MISSING SPECTRA"
      end
    end

    counts = Hash.new 0

    ranks.each do |rank|
      counts[rank] += 1
    end

    result_file.puts counts
    result_file.puts "Rank 1: " + ranks.count { |r| r <= 1 && r > 0}.to_s
    result_file.puts "Rank 3: " + ranks.count { |r| r <= 3 && r > 0}.to_s
    result_file.puts "Rank 10: " + ranks.count { |r| r <= 10 && r > 0}.to_s
    result_file.puts "Medal Score: " + (ranks.count { |r| r <= 1 && r > 0} * 5 + ranks.count { |r| r <= 2 && r > 1} * 3 + ranks.count { |r| r <= 3 && r > 2}).to_s
    result_file.puts "Average Rank: " + (ranks.sum / ranks.length.to_f).to_s
    result_file.close

    if overall_file.present?
      overall_file.puts [ "S" + s_weight.round(2).to_s.gsub(".", "") + "-R" + r_weight.round(2).to_s.gsub(".", "") + "-C" + a_weight.round(2).to_s.gsub(".", ""),
                          ranks.count { |r| r <= 1 && r > 0}.to_s,
                          ranks.count { |r| r <= 3 && r > 0}.to_s,
                          ranks.count { |r| r <= 10 && r > 0}.to_s,
                          (ranks.count { |r| r <= 1 && r > 0} * 5 + ranks.count { |r| r <= 2 && r > 1} * 3 + ranks.count { |r| r <= 3 && r > 2}).to_s,
                          (ranks.sum / ranks.length.to_f).to_s
                        ].join("\t")
    end
  end

  desc "METHOD 2: Test provided query spectra against computed spectra"
  def casmi_test(s_weight, r_weight, a_weight, dir, overall_file = nil, list = nil)
    if list.blank?
      list = File.readlines(dir + "/experimental_training_compounds.tsv")
    end

    result_file = File.open(dir + "/results-S" + s_weight.round(2).to_s.gsub(".", "") + "-R" + r_weight.round(2).to_s.gsub(".", "") + "-C" + a_weight.round(2).to_s.gsub(".", "") + ".txt", "w")
    result_file.puts ["Query_Rank", "Score", "CFMID_Score", "Ref_Score", "Class_Score", "Query_Source", "Query_ID", "Predicted_Class", "Direct_Parent", "Alternative_Parents", "Ancestors", "Top_Ranked_Source", "Top_Ranked_Source_ID", "Top_Ranked_Original_Rank", "Score", "CFMID_Score", "Ref_Score", "Class_Score", "Num_Candidates", "Type"].join("\t")
    ranks = []

    list.each do |row|
      molecule = DatabaseMolecule.find_by(id: row[0])
      if !molecule.present?
        next
      end

      type = row[1]
      puts molecule.id

      if type == "positive"
        databases = IdentifyQuery::ESI_DATABASES.values
        ion_mode = "positive"
        spectra_type = "ESI"
        adduct_type = "Neutral"
        mass = molecule.neutral_mass
      elsif type == "negative"
        databases = IdentifyQuery::ESI_DATABASES.values
        ion_mode = "negative"
        spectra_type = "ESI"
        adduct_type = "Neutral"
        mass = molecule.neutral_mass
      else # EI
        databases = IdentifyQuery::EI_DATABASES.values
        ion_mode = "positive"
        spectra_type = "EI"
        adduct_type = "M+"
        mass = molecule.neutral_mass + AdductCalculator::MASS_OF_ELECTRON
      end

      puts "data/casmi/challenge_" + type + "/" + row[2] + ".txt"
      test_spectra = File.read("data/casmi/challenge_" + type + "/" + row[2] + ".txt")

      if test_spectra.present?
        # Create a query with the compound spectra as the input spectra
        query = Query.new(
          type: "IdentifyQuery",
          spectra: "energy0\n" + test_spectra,
          database: databases,
          num_results: -1,
          threshold: 0.001,
          ppm_mass_tol: 10.0,
          abs_mass_tol: 0.01,
          scoring_function: "DotProduct",
          candidate_limit: 100,
          neutral_mass: nil,
          candidate_ppm_mass_tol: 50.0,
          candidate_abs_mass_tol: 0.5,
          parent_ion_mass: mass,
          parent_ion_mass_type: "Original",
          ion_mode: ion_mode,
          adduct_type: adduct_type,
          spectra_type: spectra_type,
          param_file: get_param_file(spectra_type, ion_mode),
          config_file: get_config_file(spectra_type, ion_mode)
        )

        if query.valid? && query.convert_spectra && query.save
          output_file = "output.txt"

          query_string = "cfm-id-precomputed #{query.input_file.path} \"#{query.print_spectra_id}\" #{query.candidates_file.path} #{query.num_results} #{query.ppm_mass_tol} #{query.abs_mass_tol} #{query.scoring_function} #{output_file}"

          if CFMID.config[:extra_params]
            query_string = CFMID.config[:extra_params] + " " + query_string
          end
          stdin, stdout, stderr = Open3.popen3(query_string)

          stderr.gets(nil)
          stdout.gets(nil)

          stdin.close
          stdout.close
          stderr.close

          query.update_attributes(output_file: File.new(output_file))
          query_results = query.get_results(similarity_weight: s_weight, reference_weight: r_weight, ancestor_weight: a_weight)
          results = query_results[:results]

          result = results.find { |r|
            r[:inchi_key] == molecule.inchi_key
            # r[:database] == molecule.source && r[:database_id] == molecule.source_id
          }

          if result.blank?
            result = {}
            rank = 0
          else
            rank = result[:adjusted_rank]
          end

          ranks << rank

          single_result_file = File.open(dir + "/results-" + row[0].to_s + "-S" + s_weight.round(2).to_s.gsub(".", "") + "-R" + r_weight.round(2).to_s.gsub(".", "") + "-C" + a_weight.round(2).to_s.gsub(".", "") + ".txt", "w")
          single_result_file.puts results.first.keys.map(&:to_s).join("\t")
          results.each do |r|
            single_result_file.puts r.values.map(&:to_s).join("\t")
          end

          result_file.puts [rank, result[:combined_score], result[:cfm_id_score], result[:reference_score], result[:ancestor_score], molecule.source, molecule.source_id, query_results[:predicted_class].join("; "), result[:direct_parent], result[:alternative_parents].try(:join, "; "), result[:ancestors].try(:join, "; "), results.first[:database], results.first[:database_id], results.first[:original_rank], results.first[:combined_score], results.first[:cfm_id_score], results.first[:reference_score], results.first[:ancestor_score], results.length, type].join("\t")
          query.destroy
        else
          puts "INVALID"
        end
      else
        puts "MISSING SPECTRA"
      end
    end

    counts = Hash.new 0

    ranks.each do |rank|
      counts[rank] += 1
    end

    result_file.puts counts
    result_file.puts "Rank 1: " + ranks.count { |r| r <= 1 && r > 0}.to_s
    result_file.puts "Rank 3: " + ranks.count { |r| r <= 3 && r > 0}.to_s
    result_file.puts "Rank 10: " + ranks.count { |r| r <= 10 && r > 0}.to_s
    result_file.puts "Medal Score: " + (ranks.count { |r| r <= 1 && r > 0} * 5 + ranks.count { |r| r <= 2 && r > 1} * 3 + ranks.count { |r| r <= 3 && r > 2}).to_s
    result_file.puts "Average Rank: " + (ranks.sum / ranks.length.to_f).to_s
    result_file.close

    if overall_file.present?
      overall_file.puts [ "S" + s_weight.round(2).to_s.gsub(".", "") + "-R" + r_weight.round(2).to_s.gsub(".", "") + "-C" + a_weight.round(2).to_s.gsub(".", ""),
                          ranks.count { |r| r <= 1 && r > 0}.to_s,
                          ranks.count { |r| r <= 3 && r > 0}.to_s,
                          ranks.count { |r| r <= 10 && r > 0}.to_s,
                          (ranks.count { |r| r <= 1 && r > 0} * 5 + ranks.count { |r| r <= 2 && r > 1} * 3 + ranks.count { |r| r <= 3 && r > 2}).to_s,
                          (ranks.sum / ranks.length.to_f).to_s
                        ].join("\t")
    end
  end

  desc "METHOD 3: Test merged spectra against merged spectra (experimental + computed)"
  def casmi_train(s_weight, r_weight, a_weight, overall_file)

    result_file = File.open("data/training/results1000/results-S" + s_weight.round(2).to_s.gsub(".", "") + "-R" + r_weight.round(2).to_s.gsub(".", "") + "-C" + a_weight.round(2).to_s.gsub(".", "") + ".txt", "w")
    result_file.puts  "Query_Rank\tScore\tCFMID_Score\tRef_Score\tClass_Score\tQuery_Source\tQuery_ID\tTop_Ranked_Source\tTop_Ranked_Source_ID\tTop_Ranked_Original_Rank\tScore\tCFMID_Score\tRef_Score\tClass_Score\tNum_Candidates\tType"
    ranks = []

    first = true
    File.readlines("data/training/results1000/training_compounds-1000computed-Jul12.tsv").each do |line|
      if first
        first = false
        next
      end
      row = line.split("\t")

      molecule = DatabaseMolecule.find_by(id: row[0])
      if !molecule.present?
        next
      end

      type = row[1]
      puts molecule.id

      if type == "positive"
        databases = IdentifyQuery::ESI_DATABASES.values
        ion_mode = "positive"
        spectra_type = "ESI"
        adduct_type = "Neutral"
        mass = molecule.neutral_mass
      elsif type == "negative"
        databases = IdentifyQuery::ESI_DATABASES.values
        ion_mode = "negative"
        spectra_type = "ESI"
        adduct_type = "Neutral"
        mass = molecule.neutral_mass
      else # EI
        databases = IdentifyQuery::EI_DATABASES.values
        ion_mode = "positive"
        spectra_type = "EI"
        adduct_type = "M+"
        mass = molecule.neutral_mass + AdductCalculator::MASS_OF_ELECTRON
      end

      test_spectra = molecule.make_spectra(spectra_type, ion_mode)

      if test_spectra["energy0"].present?
        # Create a query with the compound spectra as the input spectra
        query = Query.new(
          type: "IdentifyQuery",
          spectra: test_spectra.flatten.join("\n"),
          database: databases,
          num_results: -1,
          threshold: 0.001,
          ppm_mass_tol: 10.0,
          abs_mass_tol: 0.01,
          scoring_function: "DotProduct",
          candidate_limit: 100,
          neutral_mass: nil,
          candidate_ppm_mass_tol: 50.0,
          candidate_abs_mass_tol: 0.5,
          parent_ion_mass: mass,
          parent_ion_mass_type: "Original",
          ion_mode: ion_mode,
          adduct_type: adduct_type,
          spectra_type: spectra_type,
          param_file: get_param_file(spectra_type, ion_mode),
          config_file: get_config_file(spectra_type, ion_mode)
        )

        if query.valid? && query.convert_spectra && query.save
          output_file = "output.txt"

          query_string = "cfm-id-precomputed #{query.input_file.path} \"#{query.print_spectra_id}\" #{query.candidates_file.path} #{query.num_results} #{query.ppm_mass_tol} #{query.abs_mass_tol} #{query.scoring_function} #{output_file}"

          if CFMID.config[:extra_params]
            query_string = CFMID.config[:extra_params] + " " + query_string
          end
          stdin, stdout, stderr = Open3.popen3(query_string)

          stderr.gets(nil)
          stdout.gets(nil)

          stdin.close
          stdout.close
          stderr.close

          query.update_attributes(output_file: File.new(output_file))
          query_results = query.get_results(similarity_weight: s_weight, reference_weight: r_weight, ancestor_weight: a_weight)
          results = query_results[:results]

          result = results.find { |r|
            r[:inchi_key] == molecule.inchi_key
            # r[:database] == molecule.source && r[:database_id] == molecule.source_id
          }
          if result.blank?
            result = {}
            rank = 0
          else
            rank = result[:adjusted_rank]
          end

          ranks << rank
          result_file.puts rank.to_s + "\t" + result[:combined_score].to_s + "\t" + result[:cfm_id_score].to_s + "\t" + result[:reference_score].to_s + "\t" + result[:ancestor_score].to_s + "\t" + molecule.source + "\t" + molecule.source_id + "\t" + results.first[:database] + "\t" + results.first[:database_id] + "\t" + results.first[:original_rank].to_s + "\t"+ results.first[:combined_score].to_s + "\t" + results.first[:cfm_id_score].to_s + "\t" + results.first[:reference_score].to_s + "\t" + results.first[:ancestor_score].to_s + "\t" + results.length.to_s + "\t" + type
          query.destroy
        end
      end
    end

    counts = Hash.new 0

    ranks.each do |rank|
      counts[rank] += 1
    end

    result_file.puts counts
    result_file.puts "Rank 1: " + ranks.count { |r| r <= 1 && r > 0}.to_s
    result_file.puts "Rank 3: " + ranks.count { |r| r <= 3 && r > 0}.to_s
    result_file.puts "Rank 10: " + ranks.count { |r| r <= 10 && r > 0}.to_s
    result_file.puts "Medal Score: " + (ranks.count { |r| r <= 1 && r > 0} * 5 + ranks.count { |r| r <= 2 && r > 1} * 3 + ranks.count { |r| r <= 3 && r > 2}).to_s

    result_file.close

    overall_file.puts [ "S" + s_weight.round(2).to_s.gsub(".", "") + "-R" + r_weight.round(2).to_s.gsub(".", "") + "-C" + a_weight.round(2).to_s.gsub(".", ""),
                        ranks.count { |r| r <= 1 && r > 0}.to_s,
                        ranks.count { |r| r <= 3 && r > 0}.to_s,
                        ranks.count { |r| r <= 10 && r > 0}.to_s,
                        (ranks.count { |r| r <= 1 && r > 0} * 5 + ranks.count { |r| r <= 2 && r > 1} * 3 + ranks.count { |r| r <= 3 && r > 2}).to_s
                      ].join("\t")
  end

  task :make_test_set => :environment do
    CSV.open("data/testing/casmi-15.csv", "wb") do |csv|
      CSV.foreach("data/testing/casmi-list.csv", headers: false) do |row|
        name = row[0].capitalize
        smiles = row[1]
        mass = row[2]
        references = row[3]
        source = row[4]
        id = row[5]
        challenge = row[6]
        ion_mode = row[7]
        inchi_key = Jchem.structure_to_inchikey(smiles)
        inchi = Jchem.structure_to_inchi(smiles)

        csv << [name, smiles, inchi_key, inchi, mass, source, id, challenge, ion_mode, references]
      end
    end
  end

  desc "CASMI test compounds"
  task :import_casmi_test_15 => :environment do

    positive_dir = File.join("data", "esi", "positive", "casmi")
    negative_dir = File.join("data", "esi", "negative", "casmi")
    positive_annotated_dir = File.join("data", "esi", "positive", "casmi_annotated")
    negative_annotated_dir = File.join("data", "esi", "negative", "casmi_annotated")
    unless File.directory?(positive_dir)
      FileUtils.mkdir_p(positive_dir)
    end
    unless File.directory?(negative_dir)
      FileUtils.mkdir_p(negative_dir)
    end
    unless File.directory?(positive_annotated_dir)
      FileUtils.mkdir_p(positive_annotated_dir)
    end
    unless File.directory?(negative_annotated_dir)
      FileUtils.mkdir_p(negative_annotated_dir)
    end

    CSV.foreach("data/testing/casmi-15.csv", headers: false) do |row|
      dm = DatabaseMolecule.find_by(source: row[5],
                                    source_id: row[6])

      if dm.blank?
        dm = DatabaseMolecule.create!(source: row[5],
                                      source_id: row[6],
                                      name: row[0],
                                      smiles: row[1],
                                      inchi_key: row[2],
                                      inchi: row[3],
                                      neutral_mass: row[4]
                                      )
      end

      rc = ReferenceCount.find_or_create_by(inchi_key: row[2])
      rc.count = row[9]
      rc.import_id = row[6]
      rc.save!

      classify(dm)

      Spectrum.find_or_create_by!(spectra_source: "CFMID",
                                  inchi_key: dm.inchi_key,
                                  method: "Computed",
                                  spectra_type: "ESI",
                                  ion_mode: row[8],
                                  collision_energy: "all"
                                  )

      spectra_file = "Challenge-" + row[7] + "-" + row[0] + ".txt"
      spectra_path = "data/testing/spectra/" + spectra_file
      new_file = row[6] + ".txt"
      if row[8] == "positive"
        FileUtils.cp(spectra_path, positive_annotated_dir)
        File.rename(positive_annotated_dir + "/" + spectra_file, positive_annotated_dir + "/" + new_file)

        annotated_peak_list = File.read(positive_annotated_dir + "/" + new_file).split("\n\n")[0].split("\n")
        peak_list = annotated_peak_list.map { |p| p.scan(/energy\d|\d+\.?\d+ \d+\.?\d+/).first }
        File.open(positive_dir + "/" + new_file, "w+") do |f|
          f.puts(peak_list)
        end

        FileUtils.copy(positive_dir + "/" + new_file, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "positive", "peaks", dm.inchi_key + ".txt"))
        FileUtils.copy(positive_annotated_dir + "/" + new_file, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "positive", "annotated", dm.inchi_key + ".txt"))
      else
        FileUtils.cp(spectra_path, negative_annotated_dir)
        File.rename(negative_annotated_dir + "/" + spectra_file, negative_annotated_dir + "/" + new_file)

        annotated_peak_list = File.read(negative_annotated_dir + "/" + new_file).split("\n\n")[0].split("\n")
        peak_list = annotated_peak_list.map { |p| p.scan(/energy\d|\d+\.?\d+ \d+\.?\d+/).first }
        File.open(negative_dir + "/" + new_file, "w+") do |f|
          f.puts(peak_list)
        end

        FileUtils.copy(negative_dir + "/" + new_file, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "negative", "peaks", dm.inchi_key + ".txt"))
        FileUtils.copy(negative_annotated_dir + "/" + new_file, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "negative", "annotated", dm.inchi_key + ".txt"))
      end
    end
  end

  desc "CASMI compounds"
  task :import_casmi => :environment do
    type = "testing"

    positive_dir = File.join("data", "esi", "positive", "casmi")
    negative_dir = File.join("data", "esi", "negative", "casmi")
    positive_annotated_dir = File.join("data", "esi", "positive", "casmi_annotated")
    negative_annotated_dir = File.join("data", "esi", "negative", "casmi_annotated")
    unless File.directory?(positive_dir)
      FileUtils.mkdir_p(positive_dir)
    end
    unless File.directory?(negative_dir)
      FileUtils.mkdir_p(negative_dir)
    end
    unless File.directory?(positive_annotated_dir)
      FileUtils.mkdir_p(positive_annotated_dir)
    end
    unless File.directory?(negative_annotated_dir)
      FileUtils.mkdir_p(negative_annotated_dir)
    end

    masses = {}
    File.readlines("data/import/casmi-2016-#{type}-masses.tsv").each do |line|
      items = line.split("\t")
      masses[items[0].strip] = items[2].strip.to_f
    end

    CSV.foreach("data/import/casmi-2016-#{type}.csv", headers: true) do |row|
      dm = DatabaseMolecule.find_by(source: "CASMI2016",
                                    source_id: row[0])

      if dm.blank?
        dm = DatabaseMolecule.create!(source: "CASMI2016",
                                      source_id: row[0],
                                      name: row[6],
                                      smiles: row[7],
                                      inchi_key: "InChIKey=" + row[9],
                                      inchi: row[8],
                                      neutral_mass: row[2]
                                      )
      end

      # Fix mass
      dm.update_attribute(:neutral_mass, masses[row[1]])

      # rc = ReferenceCount.find_or_create_by(inchi_key: row[2])
      # rc.count = row[9]
      # rc.import_id = row[6]
      # rc.save!

      classify(dm)

      # Add positive
      spectra_file = row[1] + ".log"
      spectra_path = "data/import/casmi-2016-cat3-#{type}/positive/" + spectra_file
      new_file = row[0] + ".txt"

      if File.exists?(spectra_path)

        Spectrum.find_or_create_by!(spectra_source: "CFMID",
                                  inchi_key: dm.inchi_key,
                                  method: "Computed",
                                  spectra_type: "ESI",
                                  ion_mode: "positive",
                                  collision_energy: "all"
                                  )

        FileUtils.cp(spectra_path, positive_annotated_dir)
        File.rename(positive_annotated_dir + "/" + spectra_file, positive_annotated_dir + "/" + new_file)

        annotated_peak_list = File.read(positive_annotated_dir + "/" + new_file).split("\n\n")[0].split("\n")
        peak_list = annotated_peak_list.map { |p| p.scan(/energy\d|\d+\.?\d+ \d+\.?\d+/).first }
        File.open(positive_dir + "/" + new_file, "w+") do |f|
          f.puts(peak_list)
        end

        FileUtils.copy(positive_dir + "/" + new_file, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "positive", "peaks", dm.inchi_key + ".txt"))
        FileUtils.copy(positive_annotated_dir + "/" + new_file, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "positive", "annotated", dm.inchi_key + ".txt"))

      end

        # Then add negative
      spectra_path = "data/import/casmi-2016-cat3-#{type}/negative/" + spectra_file

      if File.exists?(spectra_path)
        Spectrum.find_or_create_by!(spectra_source: "CFMID",
                                    inchi_key: dm.inchi_key,
                                    method: "Computed",
                                    spectra_type: "ESI",
                                    ion_mode: "negative",
                                    collision_energy: "all"
                                    )

        FileUtils.cp(spectra_path, negative_annotated_dir)
        File.rename(negative_annotated_dir + "/" + spectra_file, negative_annotated_dir + "/" + new_file)

        annotated_peak_list = File.read(negative_annotated_dir + "/" + new_file).split("\n\n")[0].split("\n")
        peak_list = annotated_peak_list.map { |p| p.scan(/energy\d|\d+\.?\d+ \d+\.?\d+/).first }
        File.open(negative_dir + "/" + new_file, "w+") do |f|
          f.puts(peak_list)
        end

        FileUtils.copy(negative_dir + "/" + new_file, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "negative", "peaks", dm.inchi_key + ".txt"))
        FileUtils.copy(negative_annotated_dir + "/" + new_file, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "negative", "annotated", dm.inchi_key + ".txt"))
      end
    end
  end

  desc "CASMI compounds"
  task :import_casmi_refs => :environment do
    count = 0
    count_not_zero = 0
    File.readlines("data/import/casmi-dw-refs_2.tsv").each do |line|
      items = line.split(/\t/)
      if (existing = ReferenceCount.find_by(inchi_key: "InChIKey=" + items[1])).present?
        if existing.count == 0 && items[4].to_i > 0
          count_not_zero += 1
        end
        count += 1
      end
      rc = ReferenceCount.find_or_create_by(inchi_key: "InChIKey=" + items[1])
      rc.count = items[4].to_i
      rc.save!
    end

    puts "COUNT: #{count}"
    puts "BETTER COUNT: #{count_not_zero}"
  end

  desc "CASMI compounds"
  task :check_casmi_refs => :environment do
    count = 0
    File.readlines("data/import/casmi-dw-refs_2.tsv").each do |line|
      items = line.split(/\t/)
      if (existing = DatabaseMolecule.where(inchi_key: "InChIKey=" + items[1])).present?
        count += 1
        puts existing.map(&:source_id).join(", ")
      else
        puts "MISSING"
      end
    end

    puts "COUNT: #{count}"
  end

  task :transfer_casmi_15 => :environment do
    File.open("data/training/results15/experimental_training_compounds.tsv", "wb") do |file|
      CSV.foreach("data/testing/casmi-15.csv", headers: false) do |row|
        dm = DatabaseMolecule.find_by(source: row[5],
                                      source_id: row[6])

        file.puts [dm.id, row[8], dm.name, dm.source, dm.inchi_key, dm.inchi, dm.smiles].join("\t")
      end
    end
  end

  desc "Check casmi compound masses"
  task :fix_casmi_masses => :environment do
    File.open("missing_masses.txt", "wb") do |file|
      DatabaseMolecule.where(source: "CASMI2016").each do |dm|
        masses = DatabaseMolecule.where(inchi_key: dm.inchi_key).where("source != ?", "CASMI2016").map(&:neutral_mass)
        mass = masses.sort_by {|m| m.to_s.length - 1 }.reverse.first

        if mass.blank?
          file.puts dm.inchi_key
        end

        dm.update_attribute(:neutral_mass, mass)
      end
    end
  end

  # Get compounds with experimental spectra that don't have computed spectra
  desc "Get missing computed"
  task :get_missing_computed => :environment do
    file = "data/training/missing_computed.tsv"
    # We don't want compounds with these classes
    excluded_classes = File.read("data/training/excluded_classes.txt").split("\n")
    # Get ALL potential ESI candidates
    File.open(file, "wb") do |file|
      file.puts ["Type", "Name", "InChIKey", "InChI", "SMILES"].join("\t")
      # Molecules with experimental ei
      DatabaseMolecule.includes(:spectra).where(spectra: {method: "Experimental", spectra_type: "EI", ion_mode: "positive"}).to_a.uniq(&:inchi_key).each do |molecule|
        if molecule.classification.present? && (molecule.classification.ancestors & excluded_classes).blank? && molecule.spectra.where(method: "Computed", spectra_type: "EI", ion_mode: "positive").empty?
          file.puts ["ei", molecule.name, molecule.inchi_key, molecule.inchi, molecule.smiles].join("\t")
        end
      end
      # Molecules with experimental positive esi
      DatabaseMolecule.includes(:spectra).where(spectra: {method: "Experimental", spectra_type: "ESI", ion_mode: "positive"}).to_a.uniq(&:inchi_key).each do |molecule|
        if molecule.classification.present? && (molecule.classification.ancestors & excluded_classes).blank? && molecule.spectra.where(method: "Computed", spectra_type: "ESI", ion_mode: "positive").empty?
          file.puts ["positive", molecule.name, molecule.inchi_key, molecule.inchi, molecule.smiles].join("\t")
        end
      end
      # Molecules with experimental negative esi
      DatabaseMolecule.includes(:spectra).where(spectra: {method: "Experimental", spectra_type: "ESI", ion_mode: "negative"}).to_a.uniq(&:inchi_key).each do |molecule|
        if molecule.classification.present? && (molecule.classification.ancestors & excluded_classes).blank? && molecule.spectra.where(method: "Computed", spectra_type: "ESI", ion_mode: "negative").empty?
          file.puts ["negative", molecule.name, molecule.inchi_key, molecule.inchi, molecule.smiles].join("\t")
        end
      end
    end
  end

  desc "CASMI compounds"
  task :check_missing_computed => :environment do
    count = 0
    missing_count = 0
    File.readlines("data/training/missing_computed.tsv").each do |line|
      items = line.split(/\t/)
      type = items[0]
      inchi_key = items[2]
      if type != "ei"
        if File.exists?("data/import/CompoundsMissingComputedESIspectra/#{type}/#{inchi_key}.log")
          count += 1
        else
          missing_count += 1
          puts "MISSING"
        end
      end
    end

    puts "FOUND: #{count}"
    puts "MISSING: #{missing_count}"
  end
end
