require 'zip'
require 'csv'
require 'builder'
require 'admin_mailer'
require 'wishart/task_support/zipper'

namespace :retention_index_compound do

	desc 'Update retention_index_table'
	task update_retention_index_table: [:environment] do
		puts RetentionIndexCompound.all.count
		ActiveRecord::Base.transaction do
		  CSV.read("public/rails_app_test.csv", headers: true, header_converters: :symbol).each do |row|
		    #puts row[:ri_compound_name]
		    #puts row[:ri_compound_smiles]
		    #puts row[:retention_index_value]			
			sc = RetentionIndexCompound.create!(ri_compound_name: row[:ri_compound_name].nil? ? nil : row[:ri_compound_name].strip, 
								ri_compound_smiles: row[:ri_compound_smiles].nil? ? nil : row[:ri_compound_smiles].strip,
								compound_stationary_phase: row[:compound_stationary_phase].nil? ? nil : row[:compound_stationary_phase].strip,
								compound_derivative_type: row[:compound_derivative_type].nil? ? nil : row[:compound_derivative_type].strip, 
								ri_compound_average_mass: row[:ri_compound_average_mass].nil? ? nil : row[:ri_compound_average_mass].strip, 
								retention_index_value: row[:retention_index_value].nil? ? nil : row[:retention_index_value].strip)
		  end
		end
		puts RetentionIndexCompound.all.count
	end

	task reset_retention_index_table: [:environment] do
		RetentionIndexCompound.all.delete_all
		ActiveRecord::Base.connection.execute('ALTER TABLE retention_index_compounds AUTO_INCREMENT = 1')
	end
end



