namespace :query do

  namespace :destroy do

    desc "Destroy all queries"
    task :all => :environment do
      Query.all.each do |query|
        filedir = "public/queries/" + query.secret_id
        if File.directory?(filedir)
          FileUtils.rm_rf(filedir)
        end
        query.destroy
      end
    end

    desc "Destroy all expired queries (older than 1 month)"
    task :expired => :environment do
      Query.where('updated_at < ?', 1.month.ago).each do |query|
        filedir = "public/queries/" + query.secret_id
        if File.directory?(filedir)
          FileUtils.rm_rf(filedir)
        end
        query.destroy
      end
    end

  end

end
