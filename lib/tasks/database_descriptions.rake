namespace :database_descriptions do

  task :add_descriptions => :environment do

    DatabaseDescription.create!(name: "HMD<PERSON>", 
                                abbreviated_name: "hmdb", 
                                description: "The Human Metabolome Database (HMDB) is a freely available database containing detailed information about small molecule metabolites found in the human body. It is intended to be used for applications in metabolomics, clinical chemistry, biomarker discovery and general education. It contains more than 253243 metabolites entries including both water-soluble and lipid soluble metabolites",
                                # data_downloaded: DateTime.Today, 
                                url_link: "https://hmdb.ca/", 
                                number_of_compounds: 253243, 
                                version_number: '5.0')
     DatabaseDescription.create!(name: "FooDB", 
                                  abbreviated_name: "foodb", 
                                  description: "FooDB is the world’s largest and most comprehensive resource on food constituents, chemistry and biology. It provides information on both macronutrients and micronutrients, including many of the constituents that give foods their flavor, color, taste, texture and aroma. It currently has data on over 26,500  food compounds and food associations.",
                                  # data_downloaded: DateTime.today, 
                                  url_link: "https://foodb.ca/", 
                                  number_of_compounds: 26500, 
                                  version_number: '1.0')
      DatabaseDescription.create!(name: "KEG<PERSON>", 
                                    abbreviated_name: "kegg", 
                                    description: "KEGG is a database resource for understanding high-level functions and utilities of the biological system, such as the cell, the organism and the ecosystem, from molecular-level information.",
                                    # data_downloaded: DateTime.today, 
                                    url_link: "https://www.genome.jp/kegg/pathway.html", 
                                    number_of_compounds: 0.0, 
                                    version_number: '')
      DatabaseDescription.create!(name: "MassBankJP/MassBankEU", 
                                  abbreviated_name: "MassBank", 
                                  description: "MassBank is an ecosystem of databases and tools for mass spectrometry reference spectra. It is provided as open source. MassBank is a metadata-centric, auto-curating repository designed for efficient storage and querying of mass spectral records. MassBank currently contains 695478 mass spectral records from experimental and in-silico libraries as well as from user contributions.",
                                  # data_downloaded: DateTime.today, 
                                  url_link: "https://mona.fiehnlab.ucdavis.edu/", 
                                  number_of_compounds: 695478, 
                                  version_number: '')
      DatabaseDescription.create!(name: "DrugBank", 
                                  abbreviated_name: "drugbank", 
                                  description: "The DrugBank database is a unique bioinformatics and cheminformatics resource that combines detailed and comprehensive drug target information. The database contains over 7,800 drug entries nearly 2,200 FDA-approved small molecule drugs, 340 FDA-approved biotech (protein/peptide) drugs, 93 nutraceuticals and >5,000 experimental drugs.",
                                  # data_downloaded: DateTime.today, 
                                  url_link: "https://go.drugbank.com/", 
                                  number_of_compounds: 7800, 
                                  version_number: '')
      DatabaseDescription.create!(name: "ContaminantDB", 
                                  abbreviated_name: "ContaminantDB", 
                                  description: "The ContaminantDB is a unique bioinformatics resource that combines detailed contaminant data from different online references and databases on contaminants. The database currently houses 54,249 compounds. This database serves as a consortium of resources used to gather contaminant data includes Carcinogens, drugs and metabolites, disinfection byproducts.",
                                  # data_downloaded: DateTime.today, 
                                  url_link: "https://contaminantdb.ca/", 
                                  number_of_compounds: 54249, 
                                  version_number: '')
      DatabaseDescription.create!(name: "ECMDB", 
                                  abbreviated_name: "ecmdb", 
                                  description: "The ECMDB is an expertly curated database containing extensive metabolomic data and metabolic pathway diagrams. ECMDB currently contains 3755 small molecules with 1402 associated enzymes and 387 associated transporters. It also has 1542 metabolic pathways that are linked to 3011 metabolites. A total of 19,294 NMR and MS spectra (experimental and predicted) for 3098 different E. coli metabolites are also contained in the database.",
                                  # data_downloaded: DateTime.today, 
                                  url_link: "https://ecmdb.ca/", 
                                  number_of_compounds: 3755, 
                                  version_number: '')
      DatabaseDescription.create!(name: "YMDB", 
                                  abbreviated_name: "ymdb", 
                                  description: "The Yeast Metabolome Database (YMDB) is a manually curated database of small molecule metabolites found in or produced by Saccharomyces cerevisiae. YMDB currently contains 16042 small molecules with 909 associated enzymes and 149 associated transporters. Each small molecule has 48 data fields describing the metabolite, its chemical properties and links to spectral and chemical databases.",
                                  # data_downloaded: DateTime.today, 
                                  url_link: "http://www.ymdb.ca/", 
                                  number_of_compounds: 16042, 
                                  version_number: '')
      DatabaseDescription.create!(name: "NP-MRD", 
                                  abbreviated_name: "np-mrd", 
                                  description: "The NP-MRD (Natural Products Magnetic Resonance Database) is a freely available cloud-based, user-friendly, FAIR electronic database. NP-MRD accepts NMR data and associated metadata from newly undertaken NP studies ranging from purified substances to crude extracts, in all major solvents. It currently contains more than 40,000 natural compounds and their spectra.",
                                  # data_downloaded: DateTime.today, 
                                  url_link: "https://np-mrd.org/", 
                                  number_of_compounds: 40000, 
                                  version_number: '')
      DatabaseDescription.create!(name: "LIPID MAPS", 
                                  abbreviated_name: "lipid-maps", 
                                  description: "The LIPID MAPS® Structure Database (LMSD) is a relational database encompassing structures and annotations of biologically relevant lipids. As of today, LMSD contains 47433 unique lipid structures, making it the largest public lipid-only database in the world.",
                                  # data_downloaded: DateTime.today, 
                                  url_link: "https://www.lipidmaps.org/databases/lmsd/browse", 
                                  number_of_compounds: 47433, 
                                  version_number: '')  
      DatabaseDescription.create!(name: "ChEBI Complete", 
                                  abbreviated_name: "chebi", 
                                  description: "Chemical Entities of Biological Interest, also known as ChEBI, is a chemical database and ontology of molecular entities focused on 'small' chemical compounds, that is part of the Open Biomedical Ontologies effort at the European Bioinformatics Institute.",
                                  # data_downloaded: DateTime.today, 
                                  url_link: "https://www.ebi.ac.uk/chebi/init.do", 
                                  number_of_compounds: 0, 
                                  version_number: '') 
      DatabaseDescription.create!(name: "STOFF-IDENT", 
                                  abbreviated_name: "STOFF-IDENT", 
                                  description: "STOFF-IDENT is a database of water relevant substances collated from various sources within the STOFF-IDENT and FOR-IDENT projects. The database have enhanced usability and functionality, enabling the search for exact masses from target or unknown lists and the automatic use of a Retention Time Index. It contains 8885 compounds.",
                                  # data_downloaded: DateTime.today, 
                                  url_link: "https://comptox.epa.gov/dashboard/chemical-lists/STOFFIDENT", 
                                  number_of_compounds: 8885, 
                                  version_number: '')
      DatabaseDescription.create!(name: "DSSTox", 
                                  abbreviated_name: "dsstox", 
                                  description: "DSSTox provides a high quality public chemistry resource for supporting improved predictive toxicology. The DSSTox Database incorporates state-of-the-art cheminformatics workflows, provides the chemical infrastructure for EPA’s Safer Chemicals Research, including the ToxCast and Tox21 high-throughput toxicology efforts.",
                                  # data_downloaded: DateTime.today, 
                                  url_link: "https://www.epa.gov/chemical-research/distributed-structure-searchable-toxicity-dsstox-database", 
                                  number_of_compounds: 0.0, 
                                  version_number: '')
      DatabaseDescription.create!(name: "MoNA", 
                                  abbreviated_name: "mona", 
                                  description: "MassBank of North America (MoNA) is a metadata-centric, auto-curating repository designed for efficient storage and querying of mass spectral records.MoNA currently contains 695464 mass spectral records from experimental and in-silico libraries as well as from user contributions.",
                                  # data_downloaded: DateTime.today, 
                                  url_link: "https://mona.fiehnlab.ucdavis.edu/", 
                                  number_of_compounds: 695478, 
                                  version_number: '')
      DatabaseDescription.create!(name: "TMIC", 
                                  abbreviated_name: "tmic", 
                                  description: "The TMIC dataset contains in-house collected experimental spectra data.",
                                  # data_downloaded: DateTime.today, 
                                  url_link: "", 
                                  number_of_compounds: 0.0, 
                                  version_number: '')
      # DatabaseDescription.create!(name: "", 
      #                             abbreviated_name: "", 
      #                             description: "",
      #                             data_downloaded: DateTime.today, 
      #                             url_link: "", 
      #                             number_of_compounds: , 
      #                             version_number: '')  
  
    end
end