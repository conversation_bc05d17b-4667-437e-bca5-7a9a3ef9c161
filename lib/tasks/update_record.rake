namespace :update_record do

  desc "delete chemical_shift submission record"
  task :delete_chemical_shift_submission_record => [:environment] do
    ChemicalShiftSubmission.transaction do
      CSV.open("data/chemical_shift_submissions_to_be_deleted.tsv", "r", :col_sep => "\t").each do |row|
      #CSV.open("data/chemical_shift_submission_anchi.tsv", "r", :col_sep => "\t").each do |row|
        puts "#{row}"
        # submission_id, user_id, email, natural_product_id = row
        submission_id = row
        puts "#{submission_id}"
        cs = ChemicalShiftSubmission.find_by_id(submission_id)
        #c.destroy
        puts "#{submission_id}  #{cs.user_id} #{cs.natural_product_id}"

        unless cs.nil?
          csmd = ChemicalShiftSubmissionMetaData.find_by(chemical_shift_submission_id: submission_id)
          

          if csmd.nil?
            puts "Chemcial shift Sumission #{submission_id} doesn't have meta_data!"
          else
            puts "Found Chemcial shift Sumission #{submission_id} meta_data"
            csmd.destroy!
            puts "meta data deleted!"
          end

          cscs = ChemicalShift.find_by(chemical_shift_submission_id: submission_id)
          if cscs.nil?
            puts "Chemcial shift Sumission #{submission_id} doesn't have chemical shift data!"
          else
            puts "Found Chemcial shift Sumission #{submission_id} chemical shift data!"
            cscs.destroy!
            puts "chemical shift data deleted!"
          end 

          cs.destroy!
          puts "Chemical shift submission #{submission_id} deleted!"        

        end
      end
    end
  end

  desc "delete nmr submission record"
  task :delete_nmr_submission_record => [:environment] do
    Submission.transaction do
      CSV.open("data/submissions_view.tsv", "r", :col_sep => "\t").each do |row|
      #CSV.open("data/chemical_shift_submission_anchi.tsv", "r", :col_sep => "\t").each do |row|
        puts "#{row}"
        submission_id = row[0]
        puts "#{submission_id}"
        cs = Submission.find_by_id(submission_id)
        #c.destroy
        puts "s_id = #{submission_id}, user_id = #{cs.user_id}"

        unless cs.nil?
          csmd = SubmissionMetaData.find_by(submission_id: submission_id)
          

          if csmd.nil?
            puts "Sumission #{submission_id} doesn't have meta_data!"
          else
            puts "Found Submission #{submission_id} meta_data"
            csmd.destroy!
            puts "meta data deleted!"
          end

          cscs = NmrSubmission.find_by(submission_id: submission_id)
          if cscs.nil?
            puts "Nmr Sumission #{submission_id} doesn't have chemical shift data!"
          else
            puts "Found Nmr Sumission #{submission_id} chemical shift data!"
            cscs.destroy!
            puts "Nmr Submission data deleted!"
          end 

          cs.destroy!
          puts "submission #{submission_id} deleted!"        

        end
      end
    end
  end



  desc "make valid false"
  task :valid_nmr_submission_false => [:environment] do
    Submission.transaction do
      all_submission = Submission.all
      all_submission.each do |sub|
        puts "is = #{sub.id}, user_id = #{sub.user_id}, before_valid = #{sub.valid}"
        sub.valid = false
        sub.save!
        puts "after change, valid = #{sub.valid}"
      end  
    end
  end

  desc "make valid false for chemical shift"
  task :valid_chemical_shift_submission_false => [:environment] do
    ChemicalShiftSubmission.transaction do
      all_submission = ChemicalShiftSubmission.all
      all_submission.each do |sub|
        puts "is = #{sub.id}, user_id = #{sub.user_id}, before_valid = #{sub.valid}"
        sub.valid = false
        sub.save!
        puts "after change, valid = #{sub.valid}"
      end  
    end
  end







end
