require 'open-uri'
#require 'jchem'

namespace :molecules do

  desc "Importing and updating all new databases"
  task :import_molecules_mar_23_2022, [:dir, :spectra_file] => :environment do |t, args|
    spectra_path = args.dir + "/" + args.spectra_file
    # puts spectra_path
    CSV.foreach(spectra_path, headers: true, header_converters: :symbol) do |row|
      dm = DatabaseMolecule.find_by(source_id: row[:database_id])
      if dm.present?
        puts dm.source
        # dm.update_attribute(:source, row[:source])
      else
        puts row[:database_id]
        next if row[:neutral_mass] == 'nan'
        DatabaseMolecule.create!(source: row[:source],
                                source_id: row[:database_id],
                                name: row[:name],
                                smiles: row[:smiles],
                                inchi_key: "InChIKey=" + row[:inchi_key],
                                inchi: row[:inchi],
                                neutral_mass: row[:neutral_mass] # Exact mass
                                )
      end
    end
  end

  desc "Make sure all inchikeys have the prefix"
  task :fix_inchi_keys => :environment do
    DatabaseMolecule.where("inchi_key NOT LIKE ?", "InChIKey=%").each do |dm|
      dm.update_attribute(:inchi_key, "InChIKey=" + dm.inchi_key)
    end

    Spectrum.where("inchi_key NOT LIKE ?", "InChIKey=%").each do |s|
      s.update_attribute(:inchi_key, "InChIKey=" + s.inchi_key)
    end
  end

  desc "Delete all cached images"
  task :delete_images_links => :environment do
    Structure.find_each do |structure|
      begin
        structure.update_attributes(image: nil)
        structure.save!
      rescue StandardError => e
        #query.update_attributes( error: "Error generating structure images, please try again or contact an administrator." )
        puts e.message
      end
    end
  end
  desc "Create/Cache all images"
  task :create_images => :environment do 
    Structure.find_each do |structure|
      begin
        structure.update_attributes(image: nil)
        filename = "public/#{structure.id}.png"
        Jchem.structure_to_png_file(structure.smiles, filename)
        structure.update_attributes(image: File.new(filename))
      rescue StandardError => e
        query.update_attributes( error: "Error generating structure images" )
      ensure
        if File.exists?(filename)
          File.delete(filename)
        end
      end
    end
  end

  desc "Add references"
  task :import_references => :environment do
    files = Dir.glob("data/references/*")
    files.each do |input_file|
      puts input_file
      File.readlines(input_file).each do |line|
        items = line.split(/\t/)
        if items.length > 6
          inchi_key = items[2]
          count = items[5]
          import_id = nil
        elsif items.length > 2
          inchi_key = items[1]
          count = items[2]
          import_id = items[0]
        else
          inchi_key = items[0]
          count = items[1]
          import_id = nil
        end

        if inchi_key !~ /^InChIKey=/
          inchi_key = "InChIKey=" + inchi_key
        end

        if DatabaseMolecule.where(inchi_key: inchi_key).present?
          rc = ReferenceCount.find_or_create_by(inchi_key: inchi_key)
          rc.count = count
          rc.import_id = import_id
          rc.save!
        end
      end
    end
  end

  desc "Add classifications from classyfire"
  task :import_classifications => :environment do
    # Format: http://classyfire.wishartlab.com/entities/NFBLWTRRMVFTHW-UHFFFAOYSA-N.json
    DatabaseMolecule.all.each do |dm|
      if dm.classification.blank?
        puts dm.id
        classify(dm)
      end
    end
  end

  desc "Add 1222 Phytohub compounds"
  task :import_phytohub => :environment do
    first = true
    File.readlines("data/import/PhytoHub.tsv").each do |line|
      if first
        first = false
        next
      end
      items = line.split(/\t/)
      # Add to table
      dm = DatabaseMolecule.find_by(source: "PhytoHub",
                                    source_id: items[0])
      if dm.blank?
        begin
          inchi = Jchem.structure_to_inchi(items[2])
        rescue
          puts "Jchem error: " + items[2]
          inchi = nil
        end
        dm = DatabaseMolecule.create!(source: "PhytoHub",
                                      source_id: items[0],
                                      name: (items[1] == "null" || items[1].blank?) ? items[0] : items[1],
                                      smiles: items[2],
                                      inchi_key: items[3],
                                      inchi: inchi,
                                      neutral_mass: items[5] # Exact mass
                                      )
      end
    end
  end

  task :parse_phytohub_spectra => :environment do
    positive_dir = File.join("data", "esi", "positive", "phytohub")
    negative_dir = File.join("data", "esi", "negative", "phytohub")
    positive_annotated_dir = File.join("data", "esi", "positive", "phytohub_annotated")
    negative_annotated_dir = File.join("data", "esi", "negative", "phytohub_annotated")
    unless File.directory?(positive_dir)
      FileUtils.mkdir_p(positive_dir)
    end
    unless File.directory?(negative_dir)
      FileUtils.mkdir_p(negative_dir)
    end
    unless File.directory?(positive_annotated_dir)
      FileUtils.mkdir_p(positive_annotated_dir)
    end
    unless File.directory?(negative_annotated_dir)
      FileUtils.mkdir_p(negative_annotated_dir)
    end
    files = Dir.glob("data/import/PhytohubOutput/positive/*")
    files.each do |input_file|
      puts input_file
      existing_name = input_file.split("/").last
      new_filename = existing_name.gsub(".log", ".txt")
      identifier = existing_name.gsub(".log", "")
      FileUtils.cp(input_file, positive_annotated_dir)
      File.rename(positive_annotated_dir + "/" + existing_name, positive_annotated_dir + "/" + new_filename)
      annotated_peak_list = File.read(positive_annotated_dir + "/" + new_filename).split("\n\n")[0].split("\n")
      peak_list = annotated_peak_list.map { |p| p.scan(/energy\d|\d+\.?\d+ \d+\.?\d+/).first }
      File.open(positive_dir + "/" + new_filename.split("/").last, "w+") do |f|
        f.puts(peak_list)
      end

      dm = DatabaseMolecule.find_by(source: "PhytoHub",
                                    source_id: identifier)

      Spectrum.find_or_create_by!(spectra_source: "CFMID",
                                  inchi_key: dm.inchi_key,
                                  method: "Computed",
                                  spectra_type: "ESI",
                                  ion_mode: "positive",
                                  collision_energy: "all"
                                )
    end
    files = Dir.glob("data/import/PhytohubOutput/negative/*")
    files.each do |input_file|
      puts input_file
      existing_name = input_file.split("/").last
      new_filename = existing_name.gsub(".log", ".txt")
      identifier = existing_name.gsub(".log", "")
      FileUtils.cp(input_file, negative_annotated_dir)
      File.rename(negative_annotated_dir + "/" + existing_name, negative_annotated_dir + "/" + new_filename)
      annotated_peak_list = File.read(negative_annotated_dir + "/" + new_filename).split("\n\n")[0].split("\n")
      peak_list = annotated_peak_list.map { |p| p.scan(/energy\d|\d+\.?\d+ \d+\.?\d+/).first }
      File.open(negative_dir + "/" + new_filename.split("/").last, "w+") do |f|
        f.puts(peak_list)
      end

      dm = DatabaseMolecule.find_by(source: "PhytoHub",
                                    source_id: identifier)

      Spectrum.find_or_create_by!(spectra_source: "CFMID",
                                  inchi_key: dm.inchi_key,
                                  method: "Computed",
                                  spectra_type: "ESI",
                                  ion_mode: "negative",
                                  collision_energy: "all"
                                )
    end
  end

  task :move_phytohub_spectra => :environment do
    positive_dir = File.join("data", "esi", "positive", "phytohub")
    negative_dir = File.join("data", "esi", "negative", "phytohub")
    positive_annotated_dir = File.join("data", "esi", "positive", "phytohub_annotated")
    negative_annotated_dir = File.join("data", "esi", "negative", "phytohub_annotated")

    Dir.glob(positive_dir + "/*").each do |positive_file|
      identifier = positive_file.split("/").last.gsub(/\.txt/, "")
      molecule = DatabaseMolecule.find_by(source: "PhytoHub", source_id: identifier)
      FileUtils.copy(positive_file, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "positive", "peaks", molecule.inchi_key + ".txt"))
    end

    Dir.glob(negative_dir + "/*").each do |negative_file|
      identifier = negative_file.split("/").last.gsub(/\.txt/, "")
      molecule = DatabaseMolecule.find_by(source: "PhytoHub", source_id: identifier)
      FileUtils.copy(negative_file, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "negative", "peaks", molecule.inchi_key + ".txt"))
    end

    Dir.glob(positive_annotated_dir + "/*").each do |positive_file|
      identifier = positive_file.split("/").last.gsub(/\.txt/, "")
      molecule = DatabaseMolecule.find_by(source: "PhytoHub", source_id: identifier)
      FileUtils.copy(positive_file, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "positive", "annotated", molecule.inchi_key + ".txt"))
    end

    Dir.glob(negative_annotated_dir + "/*").each do |negative_file|
      identifier = negative_file.split("/").last.gsub(/\.txt/, "")
      molecule = DatabaseMolecule.find_by(source: "PhytoHub", source_id: identifier)
      FileUtils.copy(negative_file, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "negative", "annotated", molecule.inchi_key + ".txt"))
    end
  end

  desc "DrugBank compounds"
  task :import_drugbank => :environment do
    drugs = {}
    CSV.foreach("data/import/cfmid_drugs.csv", headers: false) do |row|
      drugs[row[0]] = row
    end

    positive_dir = File.join("data", "esi", "positive", "drugbank")
    negative_dir = File.join("data", "esi", "negative", "drugbank")
    positive_annotated_dir = File.join("data", "esi", "positive", "drugbank_annotated")
    negative_annotated_dir = File.join("data", "esi", "negative", "drugbank_annotated")
    unless File.directory?(positive_dir)
      FileUtils.mkdir_p(positive_dir)
    end
    unless File.directory?(negative_dir)
      FileUtils.mkdir_p(negative_dir)
    end
    unless File.directory?(positive_annotated_dir)
      FileUtils.mkdir_p(positive_annotated_dir)
    end
    unless File.directory?(negative_annotated_dir)
      FileUtils.mkdir_p(negative_annotated_dir)
    end
    files = Dir.glob("data/import/drugbankoutput/positive/*")
    files.each do |input_file|
      puts input_file
      existing_name = input_file.split("/").last
      new_filename = existing_name.gsub(".log", ".txt")
      identifier = existing_name.gsub(".log", "")
      FileUtils.cp(input_file, positive_annotated_dir)
      File.rename(positive_annotated_dir + "/" + existing_name, positive_annotated_dir + "/" + new_filename)

      annotated_peak_list = File.read(positive_annotated_dir + "/" + new_filename).split("\n\n")[0].split("\n")
      peak_list = annotated_peak_list.map { |p| p.scan(/energy\d|\d+\.?\d+ \d+\.?\d+/).first }
      File.open(positive_dir + "/" + new_filename.split("/").last, "w+") do |f|
        f.puts(peak_list)
      end

      dm = DatabaseMolecule.find_by(source: "DrugBank",
                                    source_id: identifier)

      if dm.blank?
        begin
          drug = drugs[identifier]
          name = drug[1]
          smiles = drug[2]
          inchi_key = drug[4]
          inchi = drug[3]
          neutral_mass = drug[5] # Exact mass

          if smiles.blank? || inchi_key.blank? || inchi.blank? || neutral_mass.blank?
          # Fetch DrugBank XML data
            doc =  Nokogiri::XML(open("https://www.drugbank.ca/drugs/" + identifier + ".xml"))
            name = doc.css("drugbank > drug > name").text || name
            smiles = doc.css("calculated-properties > property > kind:contains('SMILES')").first.parent.css("value").text || smiles
            inchi = doc.css("calculated-properties > property > kind:contains('InChI')").first.parent.css("value").text || inchi
            inchi_key = doc.css("calculated-properties > property > kind:contains('InChIKey')").first.parent.css("value").text || inchi_key
            neutral_mass = doc.css("drugbank > drug > monoisotopic-mass").text || neutral_mass
          end

          dm = DatabaseMolecule.create!(source: "DrugBank",
                                      source_id: identifier,
                                      name: name,
                                      smiles: smiles,
                                      inchi_key: "InChIKey=" + inchi_key,
                                      inchi: inchi,
                                      neutral_mass: neutral_mass # Exact mass
                                      )
        rescue
          puts "Error: " + identifier
        end
      end

      if dm.present?
        Spectrum.find_or_create_by!(spectra_source: "CFMID",
                                    inchi_key: dm.inchi_key,
                                    method: "Computed",
                                    spectra_type: "ESI",
                                    ion_mode: "positive",
                                    collision_energy: "all"
                                  )

        FileUtils.copy(positive_dir + "/" + identifier + ".txt", File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "positive", "peaks", dm.inchi_key + ".txt"))
        FileUtils.copy(positive_annotated_dir + "/" + identifier + ".txt", File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "positive", "annotated", dm.inchi_key + ".txt"))
      end
    end

    files = Dir.glob("data/import/drugbankoutput/negative/*")
    files.each do |input_file|
      puts input_file
      existing_name = input_file.split("/").last
      new_filename = existing_name.gsub(".log", ".txt")
      identifier = existing_name.gsub(".log", "")
      FileUtils.cp(input_file, negative_annotated_dir)
      File.rename(negative_annotated_dir + "/" + existing_name, negative_annotated_dir + "/" + new_filename)
      annotated_peak_list = File.read(negative_annotated_dir + "/" + new_filename).split("\n\n")[0].split("\n")
      peak_list = annotated_peak_list.map { |p| p.scan(/energy\d|\d+\.?\d+ \d+\.?\d+/).first }
      File.open(negative_dir + "/" + new_filename.split("/").last, "w+") do |f|
        f.puts(peak_list)
      end

      dm = DatabaseMolecule.find_by(source: "DrugBank",
                                    source_id: identifier)

      if dm.blank?
        begin
          drug = drugs[identifier]
          name = drug[1]
          smiles = drug[2]
          inchi_key = drug[4]
          inchi = drug[3]
          neutral_mass = drug[5] # Exact mass

          if smiles.blank? || inchi_key.blank? || inchi.blank? || neutral_mass.blank?
          # Fetch DrugBank XML data
            doc =  Nokogiri::XML(open("https://www.drugbank.ca/drugs/" + identifier + ".xml"))
            name = doc.css("drugbank > drug > name").text || name
            smiles = doc.css("calculated-properties > property > kind:contains('SMILES')").first.parent.css("value").text || smiles
            inchi = doc.css("calculated-properties > property > kind:contains('InChI')").first.parent.css("value").text || inchi
            inchi_key = doc.css("calculated-properties > property > kind:contains('InChIKey')").first.parent.css("value").text || inchi_key
            neutral_mass = doc.css("drugbank > drug > monoisotopic-mass").text || neutral_mass
          end

          dm = DatabaseMolecule.create!(source: "DrugBank",
                                      source_id: identifier,
                                      name: name,
                                      smiles: smiles,
                                      inchi_key: "InChIKey=" + inchi_key,
                                      inchi: inchi,
                                      neutral_mass: neutral_mass # Exact mass
                                      )
        rescue
          puts "Error: " + identifier
        end
      end

      if dm.present?
        Spectrum.find_or_create_by!(spectra_source: "CFMID",
                                    inchi_key: dm.inchi_key,
                                    method: "Computed",
                                    spectra_type: "ESI",
                                    ion_mode: "negative",
                                    collision_energy: "all"
                                  )

        FileUtils.copy(negative_dir + "/" + identifier + ".txt", File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "negative", "peaks", dm.inchi_key + ".txt"))
        FileUtils.copy(negative_annotated_dir + "/" + identifier + ".txt", File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "negative", "annotated", dm.inchi_key + ".txt"))
      end
    end
  end

  task :remove_whitespace => :environment do
    DatabaseMolecule.all.each do |dm|
      dm.update_attribute(:name, dm.name.strip.gsub(/\p{Z}+$/, ""))
    end
  end

  desc "Additional computed spectra"
  task :import_additional_computed => :environment do

    positive_dir = File.join("data", "esi", "positive", "other")
    negative_dir = File.join("data", "esi", "negative", "other")
    positive_annotated_dir = File.join("data", "esi", "positive", "other_annotated")
    negative_annotated_dir = File.join("data", "esi", "negative", "other_annotated")
    unless File.directory?(positive_dir)
      FileUtils.mkdir_p(positive_dir)
    end
    unless File.directory?(negative_dir)
      FileUtils.mkdir_p(negative_dir)
    end
    unless File.directory?(positive_annotated_dir)
      FileUtils.mkdir_p(positive_annotated_dir)
    end
    unless File.directory?(negative_annotated_dir)
      FileUtils.mkdir_p(negative_annotated_dir)
    end
    files = Dir.glob("data/import/expoutput/positive/*")
    files.each do |input_file|
      puts input_file
      existing_name = input_file.split("/").last
      new_filename = existing_name.gsub(".log", ".txt")
      compound_name = existing_name.gsub(".log", "")
      FileUtils.cp(input_file, positive_annotated_dir)
      File.rename(positive_annotated_dir + "/" + existing_name, positive_annotated_dir + "/" + new_filename)

      annotated_peak_list = File.read(positive_annotated_dir + "/" + new_filename).split("\n\n")[0].split("\n")
      peak_list = annotated_peak_list.map { |p| p.scan(/energy\d|\d+\.?\d+ \d+\.?\d+/).first }
      File.open(positive_dir + "/" + new_filename, "w+") do |f|
        f.puts(peak_list)
      end

      dm = DatabaseMolecule.find_by(name: compound_name)

      if dm.blank?
        dm = DatabaseMolecule.where('replace(name, "\'", "") = ?', compound_name).first
      end

      if dm.blank?
        dm = DatabaseMolecule.where('replace(name, "\'", "") LIKE ?', compound_name + "%").first
      end

      if dm.present?
        Spectrum.find_or_create_by!(spectra_source: "CFMID",
                                    inchi_key: dm.inchi_key,
                                    method: "Computed",
                                    spectra_type: "ESI",
                                    ion_mode: "positive",
                                    collision_energy: "all"
                                  )

        FileUtils.copy(positive_dir + "/" + new_filename, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "positive", "peaks", dm.inchi_key + ".txt"))
        FileUtils.copy(positive_annotated_dir + "/" + new_filename, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "positive", "annotated", dm.inchi_key + ".txt"))
      else
        puts "MISSING " + compound_name
      end
    end

    files = Dir.glob("data/import/expoutput/negative/*")
    files.each do |input_file|
      puts input_file
      existing_name = input_file.split("/").last
      new_filename = existing_name.gsub(".log", ".txt")
      compound_name = existing_name.gsub(".log", "")
      FileUtils.cp(input_file, negative_annotated_dir)
      File.rename(negative_annotated_dir + "/" + existing_name, negative_annotated_dir + "/" + new_filename)

      annotated_peak_list = File.read(negative_annotated_dir + "/" + new_filename).split("\n\n")[0].split("\n")
      peak_list = annotated_peak_list.map { |p| p.scan(/energy\d|\d+\.?\d+ \d+\.?\d+/).first }
      File.open(negative_dir + "/" + new_filename, "w+") do |f|
        f.puts(peak_list)
      end

      dm = DatabaseMolecule.find_by(name: compound_name)

      if dm.blank?
        dm = DatabaseMolecule.where('replace(name, "\'", "") = ?', compound_name).first
      end

      if dm.blank?
        dm = DatabaseMolecule.where('replace(name, "\'", "") LIKE ?', compound_name + "%").first
      end

      if dm.present?
        Spectrum.find_or_create_by!(spectra_source: "CFMID",
                                    inchi_key: dm.inchi_key,
                                    method: "Computed",
                                    spectra_type: "ESI",
                                    ion_mode: "negative",
                                    collision_energy: "all"
                                  )

        FileUtils.copy(negative_dir + "/" + new_filename, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "negative", "peaks", dm.inchi_key + ".txt"))
        FileUtils.copy(negative_annotated_dir + "/" + new_filename, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "negative", "annotated", dm.inchi_key + ".txt"))
      else
        puts "MISSING " + compound_name
      end
    end
  end


  desc "Remove duplicated computed spectra"
  task :remove_dup_computed => :environment do
    count = 0
    DatabaseMolecule.where(source: ["PhytoHub", "DrugBank"]).each do |dm|
      positive  = dm.spectra.where(method: "Computed", spectra_type: "ESI", ion_mode: "positive")
      negative  = dm.spectra.where(method: "Computed", spectra_type: "ESI", ion_mode: "negative")
      if (negative.count > 1)
        positive.last.destroy
        count += 1
      end
      if (negative.count > 1)
        negative.last.destroy
        count += 1
      end
    end
    puts "Removed: " + count.to_s
  end

  task :fix_moldb_compound => :environment do 
    DatabaseMolecule.find_each(batch_size: 100) do |dm|
      if ((dm.source == "HMDB") || (dm.source == "DrugBank") || (dm.source == "ContaminantDB")) && (dm.source_id.match(/[A-Z]{14}-[A-Z]{10}-[A-Z]{1}/))
        conn = Faraday.new(url: "http://moldb.wishartlab.com") do |c|
          c.response :json, :content_type => /\bjson$/
          c.adapter Faraday.default_adapter
        end
        inchi_key = dm.inchi_key.gsub("InChIKey=","")
        api_response = conn.get "/structures/#{inchi_key}.json"
        while api_response.status == 502
          sleep(120)
          api_response = conn.get "/structures/#{inchi_key}.json"
        end
        if api_response.status == 200
          r = api_response.body
          if dm.source == "HMDB"
            dm.source_id = r["database_registrations"].select{|val| val["resource"] == "HMDB"}["id"]
          elsif dm.source == "DrugBank"
            dm.source_id = r["database_registrations"].select{|val| val["resource"] == "DrugBank"}["id"]
          elsif dm.source == "ContaminantDB"
            dm.source_id = r["database_registrations"].select{|val| val["resource"] == "ContaminantDB"}["id"]
          end
        end
        dm.save!
      end
    end
  end

  task :refactor_derivatization_of => :environment do
    DatabaseMolecule.where("derivatization_of_source_id <> ''").each do |dm|
      parent = DatabaseMolecule.where(source: dm.source.split("_").first, source_id: dm.derivatization_of_source_id).first
      dm.update_attribute(:derivatization_of_id, parent.try(:id))
    end
  end

  desc "Remove MoNA duplicates"
  task :remove_mona_dups => :environment do
    count = 0
    DatabaseMolecule.where("source_id REGEXP ?", 'HMDB[0-9]{5,5}_').each do |hm|
      if DatabaseMolecule.where("source_id REGEXP ?", '^HMDB[0-9]{5,5}$').find_by(inchi_key: hm.inchi_key)
        count += 1
        hm.destroy
      end
    end
    puts count
  end


  desc "Computed spectra from moldb"
  task :import_moldb2019 => :environment do
    errors = []

    positive_dir = File.join("data", "esi", "positive", "moldb")
    negative_dir = File.join("data", "esi", "negative", "moldb")
    positive_annotated_dir = File.join("data", "esi", "positive", "moldb_annotated")
    negative_annotated_dir = File.join("data", "esi", "negative", "moldb_annotated")
    unless File.directory?(positive_dir)
      FileUtils.mkdir_p(positive_dir)
    end
    unless File.directory?(negative_dir)
      FileUtils.mkdir_p(negative_dir)
    end
    unless File.directory?(positive_annotated_dir)
      FileUtils.mkdir_p(positive_annotated_dir)
    end
    unless File.directory?(negative_annotated_dir)
      FileUtils.mkdir_p(negative_annotated_dir)
    end

    # Load structure id to inchikey mappings
    inchikeys = {}
    CSV.foreach("data/moldb2019/structure_inchikey_names.csv").each do |row|
      inchikeys[row[0]] = [row[1], row[2]]
    end

    folder = "data/moldb2019"
    ["positive-1", "negative-1", "positive-2", "negative-2", "plus-1", "plus-2"].each do |subfolder|
      puts "Importing #{subfolder}"
      original_num_molecules = DatabaseMolecule.count
      original_num_spectra = Spectrum.count

      files = Dir.glob("#{folder}/#{subfolder}/*")
      files.each do |input_file|
        next if !input_file.end_with?(".log")
        matches = input_file.split("/").last.match(/\A(\d+)(?:_([^\.]+))?/).captures
        structure_id = matches.first
        adduct_type = matches.second
        inchi_key = inchikeys[structure_id].first
        name = inchikeys[structure_id].second

        if adduct_type.present?
          adduct_type = AdductCalculator.normalized(adduct_type.gsub(/\([^\(\)]+\)/, ""))
          if AdductCalculator.positive_adduct_type?(adduct_type)
            ionization_mode = "positive"
          elsif AdductCalculator.negative_adduct_type?(adduct_type)
            ionization_mode = "negative"
          else
            puts "BAD ADDUCT"
            next
          end
          final_filename = "InChIKey=" + inchi_key + "_" + adduct_type + ".txt"
        else
          ionization_mode = subfolder.split("-").first
          final_filename = "InChIKey=" + inchi_key + ".txt"
        end

        # puts "#{input_file} -> #{name}: #{inchi_key} -> #{final_filename}"

        dir = eval("#{ionization_mode}_dir")
        annotated_dir = eval("#{ionization_mode}_annotated_dir")

        new_filename = "#{structure_id}.txt"
        FileUtils.cp(input_file, annotated_dir + "/" + new_filename)

        annotated_peak_list = File.read(annotated_dir + "/" + new_filename).split("\n\n")[0].split("\n")
        peak_list = annotated_peak_list.map { |p| p.scan(/energy\d|\d+\.?\d+ \d+\.?\d+/).first }
        File.open(dir + "/" + new_filename, "w+") do |f|
          f.puts(peak_list)
        end

        # Find or make database molecules for drugbank, hmdb, and contaminentdb
        conn = Faraday.new(url: "http://moldb.wishartlab.com") do |c|
          c.response :json, :content_type => /\bjson$/
          c.adapter Faraday.default_adapter
        end

        api_response = conn.get "/structures/#{inchi_key}.json"

        if api_response.status == 200
          r = api_response.body
          ["DrugBank", "HMDB", "ContaminantDB"].each do |source|
            db = (source == "ContaminantDB" ? "chemdb" : source.downcase)
            registration = r["database_registrations"].find { |dr| dr["resource"] == db }
            next if registration.blank?

            dm = DatabaseMolecule.find_by(source: source,
                                          source_id: registration["id"])
            if dm.blank?
              # puts "New mol for #{registration["id"]}"

              dm = DatabaseMolecule.create(source: source,
                                          source_id: registration["id"],
                                          name: name || r["traditional_iupac"],
                                          smiles: r["smiles"],
                                          inchi_key: "InChIKey=" + inchi_key,
                                          inchi: r["inchi"],
                                          neutral_mass: r["mono_mass"] # Exact mass
                                          )
              if !dm.persisted?
                puts "Error: Could not create #{inchi_key}"
              end
            end
          end
        else
          errors << "#{structure_id}\t#{inchi_key}"
        end

        Spectrum.find_or_create_by!(spectra_source: "CFMID",
                                    inchi_key: "InChIKey=" + inchi_key,
                                    method: "Computed",
                                    spectra_type: "ESI",
                                    ion_mode: ionization_mode,
                                    collision_energy: "all",
                                    adduct_type: adduct_type || "Neutral"
                                  )

        FileUtils.copy(dir + "/" + new_filename, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", ionization_mode, "peaks", final_filename))
        FileUtils.copy(annotated_dir + "/" + new_filename, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", ionization_mode, "annotated", final_filename))
      end

      puts "New Molecules: #{DatabaseMolecule.count - original_num_molecules}"
      puts "New Spectra: #{Spectrum.count - original_num_spectra}"
    end

    if errors.present?
      File.open("data/moldb2019/moldb_import_fails.txt", "w+") do |f|
        f.puts(errors.compact.uniq)
      end
    end
  end

  desc "Add classifications from classyfire"
  task :import_and_check_classifications => :environment do
    missing = []
    count = 0
    # Format: http://classyfire.wishartlab.com/entities/NFBLWTRRMVFTHW-UHFFFAOYSA-N.json
    DatabaseMolecule.find_each(batch_size: 100) do |dm|
      if dm.classification.blank?
        puts dm.id
        count += 1
        puts "Trying to classify for the #{count}th time"
        if !classify_by_inchikey(dm)
          puts "Could not classify #{dm.id} with inchikey #{dm.inchi_key} + smiles #{dm.smiles}"
          puts "Could not classify for the #{missing.length + 1} time"
          missing << [dm.inchi_key, dm.inchi, dm.smiles]
        end
      end
    end
    CSV.open("data/missing_classifications.tsv", "w+", col_sep: "\t") do |f|
      missing.each do |row|
        f << row
      end
    end
  end

  def classify_via_smiles(dm)
      return false if dm.smiles.nil?
      begin
        uri = URI.parse("http://classyfire.wishartlab.com/queries.json")
        headers = {"Content-Type" => "application/json"}
        data = {"label" => "curl_test", "query_input" => dm.smiles, "query_type" => "STRUCTURE"}
        http = Net::HTTP.new(uri.host, uri.port)
        response = http.post(uri.path, data.to_json, headers)
        data = JSON.load(response.body)
        puts "data = #{data}" if response.code == "201"
        return nil if response.code == "201"
        puts "data = #{data}" if data["direct_parent"].nil?
        return nil if data["direct_parent"].nil?
        classification = Classification.find_or_create_by(inchi_key: dm.inchi_key)
        classification.kingdom = data["kingdom"].present? ? data["kingdom"]["name"] : nil
        classification.superklass = data["superclass"].present? ? data["superclass"]["name"] : nil
        classification.klass = data["class"].present? ? data["class"]["name"] : nil
        classification.subklass = data["subclass"].present? ? data["subclass"]["name"] : nil
        classification.intermediate_nodes = data["intermediate_nodes"] # Array
        classification.direct_parent = data["direct_parent"].present? ? data["direct_parent"]["name"] : nil
        classification.alternative_parents = data["alternative_parents"].present? ? data["alternative_parents"].map { |ap| ap["name"] } : nil
        classification.molecular_framework = data["molecular_framework"]
        classification.substituents = data["substituents"] # Array
        classification.ancestors = data["ancestors"] # Array
        classification.save! if classification.direct_parent.present? 
        puts "CLASSIFICATION MISSING: " + dm.id.to_s if classification.direct_parent.nil?
        return false if classification.direct_parent.nil?
        return true
      rescue Exception => e
        $stderr.puts "WARNING #{SOURCE}.post_json #{e.message} #{e.backtrace}"
        puts "CLASSIFICATION MISSING: " + dm.id.to_s
        return false
      end

  end

  def classify_via_inchi(dm)
      return false if dm.inchi.nil?
      begin
        uri = URI.parse("http://classyfire.wishartlab.com/queries.json")
        headers = {"Content-Type" => "application/json"}
        data = {"label" => "curl_test", "query_input" => dm.inchi, "query_type" => "STRUCTURE"}
        http = Net::HTTP.new(uri.host, uri.port)
        response = http.post(uri.path, data.to_json, headers)
        data = JSON.load(response.body)
        puts "data = #{data}" if response.code == "201"
        return nil if response.code == "201"
        puts "data = #{data}" if data["direct_parent"].nil?
	      return nil if data["direct_parent"].nil?
        classification = Classification.find_or_create_by(inchi_key: dm.inchi_key)
        classification.kingdom = data["kingdom"].present? ? data["kingdom"]["name"] : nil
        classification.superklass = data["superclass"].present? ? data["superclass"]["name"] : nil
        classification.klass = data["class"].present? ? data["class"]["name"] : nil
        classification.subklass = data["subclass"].present? ? data["subclass"]["name"] : nil
        classification.intermediate_nodes = data["intermediate_nodes"] # Array
        classification.direct_parent = data["direct_parent"].present? ? data["direct_parent"]["name"] : nil
        classification.alternative_parents = data["alternative_parents"].present? ? data["alternative_parents"].map { |ap| ap["name"] } : nil
        classification.molecular_framework = data["molecular_framework"]
        classification.substituents = data["substituents"] # Array
        classification.ancestors = data["ancestors"] # Array
        classification.save! if classification.direct_parent.present? 
        puts "CLASSIFICATION MISSING: " + dm.id.to_s if classification.direct_parent.nil?
        return false if classification.direct_parent.nil?
        return true
      rescue Exception => e
        $stderr.puts "WARNING #{SOURCE}.post_json #{e.message} #{e.backtrace}"
        puts "CLASSIFICATION MISSING: " + dm.id.to_s
        return false
      end

  end


  def classify_by_inchikey(dm)
    return false if dm.inchi_key.blank?
    inchi_key = dm.inchi_key.gsub(/^InChIKey=/i, "")
    puts inchi_key
    m = inchi_key.match( /^(InChIKey=[A-Z]{14}-[A-Z]{8})[A-Z]{2}-[A-Z]$/)
    if !m.nil?
      # Retrieve classification using standard InChIKey with neutral charge.
      inchi_key = m[1] + 'SA-N'
    end
    puts inchi_key
    url = "http://classyfire.wishartlab.com/entities/#{inchi_key}.json"
    begin
      data = JSON.load(open(url))
      raise "No data" if data["kingdom"].nil?
      classification = Classification.find_or_create_by(inchi_key: dm.inchi_key)
      classification.kingdom = data["kingdom"].present? ? data["kingdom"]["name"] : nil
      classification.superklass = data["superclass"].present? ? data["superclass"]["name"] : nil
      classification.klass = data["class"].present? ? data["class"]["name"] : nil
      classification.subklass = data["subclass"].present? ? data["subclass"]["name"] : nil
      classification.intermediate_nodes = data["intermediate_nodes"] # Array
      classification.direct_parent = data["direct_parent"].present? ? data["direct_parent"]["name"] : ""
      classification.alternative_parents = data["alternative_parents"].present? ? data["alternative_parents"].map { |ap| ap["name"] } : nil
      classification.molecular_framework = data["molecular_framework"]
      classification.substituents = data["substituents"] # Array
      classification.ancestors = data["ancestors"] # Array
      classification.save!

      return true
    rescue
      puts "CLASSIFICATION MISSING: #{dm.id.to_s} #{inchi_key} "
      return false
    end
  end

  desc "Import HMDB SMILES to fix defective ones for existing DatabaseMolecules records"
  task :import_fixed_hmdb_smiles, [:infile] => :environment  do |t, args|
    CSV.foreach(args.infile, col_sep: "\t", headers: true) do |row|
      hmdb_id = row[0]
      smiles = row[2]

      dm = DatabaseMolecule.where(:source_id => hmdb_id).first
      dm.smiles = smiles
      dm.save!
    end
  end

  desc "Import HMDB InChIs to fix defective ones for existing DatabaseMolecules records"
  task :import_fixed_hmdb_inchis, [:infile] => :environment  do |t, args|
    CSV.foreach(args.infile, col_sep: "\t", headers: true) do |row|
      hmdb_id = row[0]
      inchi = row[2]

      dm = DatabaseMolecule.where(:source_id => hmdb_id).first
      dm.inchi = inchi
      dm.save!
    end
  end


  desc "Standardize InChIs and InChIKeys. Also attempt to fix SMILES that cannot be converted into other formats."
  # This task was run in Nov 2019 when the database contained a large number of non-standard
  # InChIs and InChIKeys, and was missing SMILES for numerous KEGG compounds.
  task :standardize_structures => :environment do

    DatabaseMolecule.find_each do |dm|
      puts dm.id

      begin

        # Check whether the existing SMILES is usable as a basis for generating the InChI and InChIKey.
        # If not, try to update the SMILES based on the InChIKey, if the InChIKey is a standard InChIKey.
        usable_smiles = true
        if dm.smiles.nil?
          usable_smiles = false
        else
          begin
            inchi_key = Jchem.structure_to_inchikey(dm.smiles)
          rescue Exception => e
            usable_smiles = false
          end
        end

        try_to_fix_smiles = (dm.inchi_key.nil? or !dm.inchi_key.match(/SA-[A-Z]$/) or dm.inchi.nil? or !dm.inchi.match(/^InChI=1S\//))
          # Whether we want to try to fix the SMILES string.

        if !usable_smiles and try_to_fix_smiles
          base_url = 'http://cts.fiehnlab.ucdavis.edu/service/convert/InChIKey/InChI%20Code/'

          if !dm.inchi_key.nil? and dm.inchi_key.match(/SA-[A-Z]$/)
            response = Typhoeus.get base_url + dm.inchi_key.gsub("InChIKey=","")
            if response.success?
              inchi = JSON.parse(response.response_body)[0]['result'].first
              if !inchi.blank? and (dm.inchi.nil? or !dm.inchi.match(/^InChI=1S\//))
                dm.orig_inchi = dm.inchi
                dm.inchi = inchi
              end

              if !inchi.blank?
                smiles = Jchem.structure_to_smiles(inchi)
                puts 'Updated SMILES: ' + smiles
                if !smiles.nil? and (dm.smiles.nil? or smiles != dm.smiles)
                  dm.orig_smiles = dm.smiles
                  dm.smiles = smiles
                  usable_smiles = true
                end
              end
            elsif response.timed_out?
              puts 'Timed out when using CTS'
            else
              puts 'Unknown error when using CTS'
              puts response.status_message
            end
          else
            # Non-standard InChIKey
            puts 'Problematic SMILES and non-standard InChIKey'
          end
        end


        # Generate standard structure formats where possible.
        if !usable_smiles
          if dm.source == 'KEGG' and !dm.source_id.nil?
            # Generate structure info from KEGG molfile.

            molfile = Net::HTTP.get(URI.parse('https://www.genome.jp/dbget-bin/www_bget?-f+m+compound+' + dm.source_id))
            sleep(3)

            inchi = Jchem.structure_to_inchi(molfile)
            if inchi != dm.inchi
              dm.orig_inchi = dm.inchi
              dm.inchi = inchi
            end

            smiles = Jchem.structure_to_smiles(dm.inchi)
            if smiles != dm.smiles
              dm.orig_smiles = dm.smiles
              dm.smiles = smiles
              usable_smiles = true
            end

            inchi_key = Jchem.structure_to_inchikey(dm.smiles)
            if inchi_key != dm.inchi_key
              dm.orig_inchi_key = dm.inchi_key
              dm.inchi_key = inchi_key
            end

            dm.save!
          else
            next
          end
        else
          # SMILES exists and can be used to generate InChI and InChIKey.

          if (dm.inchi_key.nil? or !dm.inchi_key.match(/SA-[A-Z]$/))
            inchi_key = Jchem.structure_to_inchikey(dm.smiles)
            if inchi_key.nil?
              puts "WARNING: Could not obtain standard InChIKey for DatabaseMolecule #{dm.id} using SMILES #{dm.smiles}"
              next
            elsif !inchi_key.match(/SA-[A-Z]$/)
              puts "ERROR: Jchem returned non-standard InChIKey for DatabaseMolecule #{dm.id} using SMILES #{dm.smiles}: #{inchi_key}"
              exit! 1
            else
              dm.orig_inchi_key = dm.inchi_key
              dm.inchi_key = inchi_key
            end
          end

          if (dm.inchi.nil? or !dm.inchi.match(/^InChI=1S\//))
            inchi = Jchem.structure_to_inchi(dm.smiles)
            if !inchi.nil?
              dm.orig_inchi = dm.inchi
              dm.inchi = inchi
            end
          end

          dm.save!
        end


        # If the InChIKey was updated, update the InChIKey used in the classifications and
        # spectra tables and in predicted spectra file names.
        if dm.inchi_key != dm.orig_inchi_key

          # classifications table
          classification = Classification.find_by(:inchi_key => dm.orig_inchi_key)
          if !classification.nil?
            classification.inchi_key = dm.inchi_key
            classification.save!
          end

          # spectra table
          spectra = Spectrum.where(:inchi_key => dm.orig_inchi_key)
          spectra.each do |spectrum|
            spectrum.inchi_key = dm.inchi_key
            spectrum.save!

            # predicted spectra file names
            if spectrum.method == 'Computed'
              ion_mode_str = ''
              if spectrum.spectra_type == 'ESI'
                ion_mode_str = spectrum.ion_mode.downcase
              end
              orig_peaks_filenames = Dir.glob(File.join(Spectrum::PRECOMPUTED_DATA_DIR, spectrum.spectra_type.downcase, ion_mode_str, "peaks", dm.orig_inchi_key + "*"))
              orig_annotated_filenames = Dir.glob(File.join(Spectrum::PRECOMPUTED_DATA_DIR, spectrum.spectra_type.downcase, ion_mode_str, "annotated", dm.orig_inchi_key + "*"))
              orig_peaks_filenames.each do |orig_peaks_filename|
                begin
                  if File.file?(orig_peaks_filename)
                    if orig_peaks_filename.include?(dm.orig_inchi_key)
                      new_peaks_filename = orig_peaks_filename.gsub(dm.orig_inchi_key, dm.inchi_key)
                    else
                      puts "Unexpected error when handling #{new_peaks_filename}"
                      exit! 1
                    end

                    puts "Moving: #{orig_peaks_filename} #{new_peaks_filename}"
                    FileUtils.mv(orig_peaks_filename, new_peaks_filename)
                  end
                rescue Exception => e
                  puts e.message
                  exit! 1
                end
              end
              orig_annotated_filenames.each do |orig_annotated_filename|
                begin
                  if File.file?(orig_annotated_filename)
                    if orig_annotated_filename.include?(dm.orig_inchi_key)
                      new_annotated_filename = orig_annotated_filename.gsub(dm.orig_inchi_key, dm.inchi_key)
                    else
                      puts "Unexpected error when handling #{new_annotated_filename}"
                      exit! 1
                    end

                    puts "Moving: #{orig_annotated_filename} #{new_annotated_filename}"
                    FileUtils.mv(orig_annotated_filename, new_annotated_filename)
                  end
                rescue Exception => e
                  puts e.message
                  exit! 1
                end
              end
            end

          end
        end

      rescue Exception => e
        puts "ERROR:" + e.message
        puts e.backtrace
      end

    end
  end

end
