namespace :import do

  desc "Import a single species to natural product mapping"
  task single_species_mapping: [:environment] do
    mapping = SpeciesMapping.new
    mapping.species_id = Species.find_by(scientific_name: "Strychnos nux-vomica L.").id
    mapping.species_mappable_id = NaturalProduct.find_by(np_mrd_id: "NP0000001").id
    mapping.species_mappable_type = "NaturalProduct"
    mapping.source = "Seed" # e.g. Whole Plant, Leaf, Root
    mapping.save!

    # Reference
    mapping.articles << CiteThis::Article.find_or_create_by(pubmed_id: "7912074")
  end


  task test_create: [:environment] do
    error = false
    mol = nil
    message = nil
    begin
      NaturalProduct.find_by_name("Test_Compound_rake").destroy!
    rescue Exception => e
      puts e.message
    end
    smiles = "C[C@@H](C(=O)O)N.C1=CC(=CC=C1C[C@@H](C(=O)O)N)O.C(CCN)C[C@@H](C(=O)O)N.C(CC(=O)O)[C@@H](C(=O)O)N"
    begin
      #puts "#{PYTHON_ENV["#{Rails.env}"]['python_path']} #{Rails.root}/public/python/rdkit_smiles_to_mol.py '#{smiles}'"
      stdin,stdout,stderr = Open3.popen3("#{PYTHON_ENV["#{Rails.env}"]['python_path']} #{Rails.root}/public/python/rdkit_smiles_to_mol.py '#{smiles}'")
      #puts stderr.gets(nil).to_s
      mol = stdout.gets(nil).to_s
      #puts mol
      if !mol.empty?
        puts "Making NP"
        np = NaturalProduct.new(:name => "Test_Compound_rake")
        np.save!
        np.structure = smiles
        np.save!
        puts np.structure_resource
        #puts np.structure
      end
    rescue Exception => e
      message = e.message
      error = true
      puts message
    end
  end

  task test_update: [:environment] do
    np = NaturalProduct.find_by_name("Erucic acid")
    puts np.structure_resource.inspect
    np.structure = np.moldb_smiles
    np.save!
    puts np.structure_resource.inspect
  end



  task test_delete_exported: [:environment] do
    NaturalProduect.where(exported: 0).each do |np|
      ChemicalShiftSubmission.where(natural_product_id: np.id).each do |css|
        ChemicalShiftSubmissionMetaData.where(chemical_shift_submission_id: css.id).each do |css_md|
          css_md.destroy!
        end
        css.destroy!
      end
      Submission.where(natural_product_id: np.id).each do |s|
        SubmissionMetaData.where(chemical_shift_submission_id: css.id).each do |s_md|
          s_md.destroy!
        end
        s.destroy!
      end
      np.destroy!
    end
  end




  desc "delete nautral product"
  task delete_natural_product: [:environment] do
    NaturalProduct.where(moldb_inchikey: nil).each do |np|
      ChemicalShiftSubmission.where(natural_product_id: np.id).each do |sub|
        ChemicalShiftSubmissionMetaData.where(chemical_shift_submission_id: sub.id).delete_all
        sub.destroy!
      end
      Submission.where(natural_product_id: np.id).each do |sub|
        SubmissionMetaData.where(submission_id: sub.id).delete_all
        sub.destroy!
      end
      np.destroy!
    end

  end

  task import_retention_indices: [:environment] do
    progress = ProgressBar.new(1722895)
    CSV.foreach('./retention_indices_for_Afia.tsv', headers: true, col_sep: "\t") do |row|
      begin
        RetentionIndexCompound.create!(
          ri_compound_name: row["derivative_name"],
          ri_compound_smiles: row["smiles"],
          ri_compound_formula: row["moldb_smiles"],
          ri_base_compound_inchikey: row["moldb_inchikey"],
          ri_compound_average_mass: row["average_mass"],
          hmdb_id: row["hmdb_id"],
          compound_stationary_phase: row["stationary_phase"],
          compound_derivative_type: row["derivative_type"],
          retention_index_value: row["retention_index"]
        )
        # puts "#{row}"
      rescue Errno::ENOENT
        File.open("import_retention_indices_errors.txt", "a") {|line| line.write "#{row}\n\n"}
      rescue CSV::MalformedCSVError
        File.open("import_retention_indices_errors.txt", "a") {|line| line.write "#{row}\n\n"}
      end
      progress.increment!
    end
  end

  task update_average_mass: [:environment] do
    python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
    script_path = Rails.root.join("public", "python", "smiles_to_mol_wt.py")
        RetentionIndexCompound.find_each do | retention_index_compound |
          script_smile = "#{Regexp.escape(retention_index_compound.ri_compound_smiles)}"
          #script_smile = mol_smile
          #script_smile = "C"
          script_command = ""
          script_command += "#{python_path} "
          script_command += "#{script_path} "
          script_command += "#{script_smile}"
          stdin,stdout,stderr = Open3.popen3(script_command)
          mass=stdout.gets(nil).to_s
          puts mass
          retention_index_compound.ri_compound_average_mass = mass
          retention_index_compound.save!
        end
  end

  task update_tms_and_tbdms_numbers: [:environment] do
    python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
    script_path = Rails.root.join("public", "python", "smiles_to_tms_tbdms_numbers.py")
        RetentionIndexCompound.find_each do | retention_index_compound |
          script_smile = "#{Regexp.escape(retention_index_compound.ri_compound_smiles)}"
          #script_smile = mol_smile
          #script_smile = "C"
          script_command = ""
          script_command += "#{python_path} "
          script_command += "#{script_path} "
          script_command += "#{script_smile}"
          stdin,stdout,stderr = Open3.popen3(script_command)
          der_num=stdout.gets(nil).to_s
          puts der_num
          retention_index_compound.derivative_number = der_num
          retention_index_compound.save!
        end
  end


  task update_derivatized: [:environment] do
    python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
    script_path = Rails.root.join("public", "python", "smiles_to_derivatized.py")
        RetentionIndexCompound.find_each do | retention_index_compound |
          script_smile = "#{Regexp.escape(retention_index_compound.ri_compound_smiles)}"
          #script_smile = mol_smile
          #script_smile = "C"
          script_command = ""
          script_command += "#{python_path} "
          script_command += "#{script_path} "
          script_command += "#{script_smile}"
          stdin,stdout,stderr = Open3.popen3(script_command)
          der_type=stdout.gets(nil).to_s
          puts der_type
          retention_index_compound.compound_derivatized = der_type
          retention_index_compound.save!
        end
  end
end
