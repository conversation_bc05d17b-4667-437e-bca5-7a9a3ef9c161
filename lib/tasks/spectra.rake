require 'fileutils'
require 'adduct_calculator'
#require 'jchem'
require 'open3'
require "#{Rails.root}/app/helpers/application_helper"
include ApplicationHelper

namespace :spectra do

  # Parse the spectra by database, the /mona spectra are in the CFMID-3.0/Spectra 3.0/Mona Google Drive
  desc "Parse MoNA spectra JSON files into smaller, more readable JSON with only the info we need"
  task :parse => :environment do
    # libraries = {"MassBank" => 44549, "ReSpect" => 5308, "HMDB" => 3799,
    #   "GNPS" => 5926, "FAHFA" => 4290, "iTree" => 4081,
    #   "RTX5_Fiehnlib" => 1195, "MetaboBASE" => 1254, "LipidBlast" => 135456}
    libraries = {"FAHFA" => 4290, "LipidBlast" => 135456}
    libraries.each do |library, count|
      puts "Library: " + library
      input_file = "data/mona/spectra_#{library}.json"
      output_file = "data/mona/parsed/filtered_spectra_#{library}.json"
      file = open(input_file)

      if library == "LipidBlast"
        # Do this instead to avoid errors due to newlines?
        input_json = ""
        File.open(input_file).each do |line|
          input_json << line
        end
      else
        input_json = file.read
      end

      # This will be an array of spectra data objects
      parsed_json = JSON.parse(input_json)

      # object keys are:
      # ["compound", "id", "lastUpdated", "metaData", "annotations", "spectrum", "splash", "submitter", "tags", "library"]

      # ["compound"][0].keys:
      # ["inchi", "inchiKey", "metaData", "molFile", "names", "tags", "computed", "classification"]

      # Data to store (and accessor):
      # => Source (from file name or object["library"]["tag"]["text"])
      # => Source ID (object["id"])
      # => Compound name (["compound"][0]["names"][0]["name"] or check score?)
      # => Neutral Mass (["compound"][0]["metaData"] find ["value"] where ["name"] == "total exact mass")
      # => InChI (object["compound"][0]["inchi"])
      # => InChI Key (object["compound"][0]["inchiKey"])
      # => SMILES (["compound"][0]["metaData"] find ["value"] where ["name"] == "SMILES")
      # => Splash key (object["splash"])
      # => Spectrum - this will later go in it's own file (object["spectrum"])
      # => Instrument (["metaData"] find ["value"] where ["name"] == "instrument")
      # => Instrument Type (["metaData"] find ["value"] where ["name"] == "instrument type")
      #     => or "ion source" in GNPS
      # => Ionization - ESI vs EI (["metaData"] find ["value"] where ["name"] == "ionization")
      # => Collision Energy (["metaData"] find ["value"] where ["name"] == "collision energy")
      #     => or "collision energy voltage" (eg: 40) or "collision energy level" (eg: high) ??? in HMDB
      #     => or "ionization energy" in RTX5_Fiehnlib
      # => Adduct Type (["metaData"] find ["value"] where ["name"] == "precursor type")
      #     => or "adduct" in GNPS
      # => Ionization Mode (["metaData"] find ["value"] where ["name"] == "ionization mode")
      # => Derivatization Type - HMDB Only? (["metaData"] find ["value"] where ["name"] == "derivatization type")

      # => MoNA Data - raw JSON to store just in case?

      collision_energy_types = []
      libraries_with_spectra = []
      # Make a hash first so we can combine all the spectra for each compound
      compounds = {}
      parsed_json.each do |object|
        inchi_key = compound_inchi(object)
        if compounds[inchi_key].blank?
          new_compound = {}
          new_compound["source"] = library
          new_compound["source_id"] = object["id"]
          new_compound["compound_name"] = object["compound"][0]["names"][0]["name"]
          new_compound["neutral_mass"] = compound_metadata(object, ["total exact mass"])
          new_compound["inchi"] = object["compound"][0]["inchi"]
          new_compound["inchi_key"] = inchi_key
          new_compound["smiles"] = compound_metadata(object, ["SMILES"])
          new_compound["classification"] = object["compound"][0]["classification"]
          new_compound["spectra"] = []

          compounds[inchi_key] = new_compound
        end

        new_spectra = {}
        new_spectra["splash_key"] = object["splash"].present? ? object["splash"]["splash"] : object["splash"]
        new_spectra["spectrum"] = object["spectrum"]
        new_spectra["instrument"] = metadata(object, ["instrument"])
        new_spectra["instrument_type"] = metadata(object, ["instrument type", "ion source"])
        new_spectra["ionization"] = metadata(object, ["ionization"])
        new_spectra["collision_energy"] = metadata(object, ["collision energy", "collision energy voltage", "ionization energy"]).to_s
        new_spectra["adduct_type"] = metadata(object, ["precursor type", "adduct"])
        new_spectra["ionization_mode"] = metadata(object, ["ionization mode"])
        new_spectra["exact_mass"] = metadata(object, ["exact mass"])
        # Predict ionization from the adduct, if we know that
        if new_spectra["ionization_mode"].empty?
          if new_spectra["adduct_type"].ends_with? "-"
            new_spectra["ionization_mode"] = "negative"
          elsif new_spectra["adduct_type"].ends_with? "+"
            new_spectra["ionization_mode"] = "positive"
          end
        end
        new_spectra["derivatization_type"] = metadata(object, ["derivatization type"])

        # Only keep the spectra if we know the ionization mode
        # NOTE that this may omit GC-MS spectra
        if new_spectra["ionization_mode"].present?
          compounds[inchi_key]["spectra"] << new_spectra
        end

        collision_energy_types.concat(compounds[inchi_key]["spectra"].map { |s| s["collision_energy"] })
        collision_energy_types.uniq!
      end

      # Then turn the hash into an array to make the JSON
      output = []
      spectra_count = 0
      compounds.each do |id, compound|
        # Only add compounds that we have spectra for
        if compound["spectra"].present? && compound["neutral_mass"].present?
          output << compound
          spectra_count += compound["spectra"].length

          libraries_with_spectra << compound["source"]
        end
      end

      collision_energy_types.uniq!
      libraries_with_spectra.uniq!

      puts "Compounds: " + output.length.to_s + " of " + compounds.length.to_s
      puts "Spectra: " + spectra_count.to_s + " of " + count.to_s
      puts "Collision Energies Found: " + collision_energy_types.compact.join(", ")

      output_json = JSON.pretty_generate(output)

      File.open(output_file, 'w') { |file| file.write(output_json) }
    end
  end

  # Parse the spectra by GC-MS vs LC-MS (not including computed)
  desc "Parse MoNA spectra JSON files into smaller, more readable JSON with only the info we need"
  task :parse_by_type => :environment do
    # libraries = {"GC-MS" => 14879, "LC-MS" => 44237}
    # libraries = {"GC-MS" => 14924, "LC-MS-positive" => 10493, "LC-MS-negative" => 3718}
    # libraries = {"GC-MS" => 14924, "LC-MS" => 50144}
    # libraries = {"GC-MS" => 14847, "LC-MS" => 51135}
    libraries = {"GC-MS" => 14967, "LC-MS" => 96173}
    libraries.each do |library, count|
      puts "Library: " + library
      input_file = "data/mona2019/spectra_all_#{library}.json"
      output_file = "data/mona2019/parsed/filtered_spectra_all_#{library}.json"
      file = open(input_file)

      input_json = file.read

      # This will be an array of spectra data objects
      parsed_json = JSON.parse(input_json)

      collision_energy_types = []
      libraries_with_spectra = []
      # Make a hash first so we can combine all the spectra for each compound
      compounds = {}
      parsed_json.each do |object|
        inchi_key = compound_inchi(object)
        if compounds[inchi_key].blank?
          new_compound = {}
          # TO DO: Some spectra don't have a source...So juse say MoNA?
          new_compound["source"] = object.dig("library", "library") || "MoNA"
          new_compound["source_id"] = object["id"]
          new_compound["compound_name"] = (object["compound"][0]["names"][0] || {})["name"]
          new_compound["neutral_mass"] = compound_metadata(object, ["total exact mass"])
          new_compound["inchi"] = object["compound"][0]["inchi"]
          new_compound["inchi_key"] = inchi_key
          new_compound["smiles"] = compound_metadata(object, ["SMILES"])
          new_compound["classification"] = object["compound"][0]["classification"]
          new_compound["spectra"] = []

          compounds[inchi_key] = new_compound
        end

        new_spectra = {}
        new_spectra["splash_key"] = object["splash"].present? ? object["splash"]["splash"] : object["splash"]
        new_spectra["spectrum"] = object["spectrum"]
        new_spectra["instrument"] = metadata(object, ["instrument"])
        new_spectra["instrument_type"] = metadata(object, ["instrument type", "ion source"])
        new_spectra["ionization"] = metadata(object, ["ionization"])
        new_spectra["collision_energy"] = metadata(object, ["collision energy", "collision energy voltage", "ionization energy"]).to_s
        new_spectra["adduct_type"] = metadata(object, ["precursor type", "adduct"])
        new_spectra["ionization_mode"] = metadata(object, ["ionization mode"])
        new_spectra["exact_mass"] = metadata(object, ["exact mass"])
        # Predict ionization from the adduct, if we know that
        if new_spectra["ionization_mode"].empty?
          if new_spectra["adduct_type"].ends_with? "-"
            new_spectra["ionization_mode"] = "negative"
          elsif new_spectra["adduct_type"].ends_with? "+"
            new_spectra["ionization_mode"] = "positive"
          end
        end
        new_spectra["derivatization_type"] = metadata(object, ["derivatization type"])

        # For LC-MS, only take spectra with QTOF/q-tof instruments/instrument_types
        # Assume all MetaboBASE spectra are QTOF
        if library == "LC-MS"
          # if (new_spectra["instrument"] =~ /QTOF/i) || (new_spectra["instrument_type"] =~ /QTOF/i) ||
          #   (new_spectra["instrument"] =~ /Q\-TOF/i) || (new_spectra["instrument_type"] =~ /Q\-TOF/i) ||
          #   compounds[inchi_key]["source"] == "MetaboBASE"
          # Only keep the spectra if we know the ionization mode
          # if new_spectra["ionization_mode"].present? && (new_spectra["exact_mass"].to_s =~ /\.(\d){3,}$/)
          if new_spectra["ionization_mode"].present? && (
              !["LC-ESI-QQ", "Acquity UPLC and a Xevo TQ (Waters)", "Quattro_QQQ", "4000Q TRAP, Applied Biosystems", "Xevo TQ, Waters"].include?(new_spectra["instrument_type"]) &&
              !["LC-ESI-QQ", "Acquity UPLC and a Xevo TQ (Waters)", "Quattro_QQQ", "4000Q TRAP, Applied Biosystems", "Xevo TQ, Waters"].include?(new_spectra["instrument"])
            ) && (new_spectra["spectrum"].split(/\s/).any? { |i| i.split(":")[0].to_s =~ /\.(\d){3,}$/ })
            compounds[inchi_key]["spectra"] << new_spectra
          end
          # end
        else
          # For GC-MS we don't need the ionization mode
          compounds[inchi_key]["spectra"] << new_spectra
        end

        # Don't filter the spectra!
        # compounds[inchi_key]["spectra"] << new_spectra

        collision_energy_types.concat(compounds[inchi_key]["spectra"].map { |s| s["collision_energy"] })
        collision_energy_types.uniq!
      end

      # Then turn the hash into an array to make the JSON
      output = []
      spectra_count = 0
      compounds.each do |id, compound|
        # Only add compounds that we have spectra for
        if compound["spectra"].present? && compound["neutral_mass"].present?
          output << compound
          spectra_count += compound["spectra"].length

          libraries_with_spectra << compound["source"]
        end
      end

      collision_energy_types.uniq!
      libraries_with_spectra.uniq!

      puts "Compounds: " + output.length.to_s + " of " + compounds.length.to_s
      puts "Spectra: " + spectra_count.to_s + " of " + count.to_s
      puts "Collision Energies Found: " + collision_energy_types.compact.join(", ")
      puts "Libraries Used (# of compounds): " + libraries_with_spectra.compact.map { |l|
          l + "(" + output.select {|c| c["source"] == l }.length.to_s + ")"
        }.join(", ")

      output_json = JSON.pretty_generate(output)

      File.open(output_file, 'w') { |file| file.write(output_json) }
    end
  end

  # Parse the spectra by GC-MS vs LC-MS (not including computed)
  # Filter to only include 10, 20, 40 and 70 eV spectra
  desc "Filter spectra by collision energy"
  task :filter_by_energy => :environment do
    files = Dir.glob("data/mona/parsed/filtered*")
    files.each do |input_file|
      puts "File: " + input_file

      # Compounds with at least one of the desired energy levels
      output_file = input_file.gsub("parsed/filtered", "parsed/energies-filtered")
      # Compounds with all 3 desired energy levels
      all_output_file = input_file.gsub("parsed/filtered", "parsed/all-energies-filtered")
      file = open(input_file)

      input_json = file.read

      # This will be an array of spectra data objects
      parsed_json = JSON.parse(input_json)

      spectra_count = 0
      output = []
      all_output = []
      libraries_with_spectra = []
      instruments = { "NONE" => 0 }
      parsed_json.each do |compound|
        new_spectra = []
        compound["spectra"].each do |spectra|
          # Only take spectra with 10, 20, or 40 eV collision energy
          if input_file.ends_with? "GC-MS.json"
            if ["70 eV"].include? spectra["collision_energy"]
              new_spectra << spectra
            end
          else
            if spectra["collision_energy"] =~ /^[1,2,4]0(\.0)?\s?(e?v|CID)?$/i
              new_spectra << spectra
            end
          end
        end
        compound["spectra"] = new_spectra

        if compound["spectra"].present?
          output << compound
          spectra_count += compound["spectra"].length
          libraries_with_spectra << compound["source"]
          positive = compound["spectra"].select { |s| s["ionization_mode"] == "positive" }
          negative = compound["spectra"].select { |s| s["ionization_mode"] == "negative" }
          if (positive.any? { |s| s["collision_energy"] =~ /10/} &&
              positive.any? { |s| s["collision_energy"] =~ /20/} &&
              positive.any? { |s| s["collision_energy"] =~ /40/}) ||
             (negative.any? { |s| s["collision_energy"] =~ /10/} &&
              negative.any? { |s| s["collision_energy"] =~ /20/} &&
              negative.any? { |s| s["collision_energy"] =~ /40/})
            all_output << compound
          end

          compound["spectra"].each do |s|
            if s["instrument"].blank? && s["instrument_type"].blank?
              instruments["NONE"] += 1
            else
              if s["instrument"].present?
                if instruments[s["instrument"]].blank?
                  instruments[s["instrument"]] = 0
                end
                instruments[s["instrument"]] += 1
              end
              if s["instrument_type"].present?
                if instruments[s["instrument_type"]].blank?
                  instruments[s["instrument_type"]] = 0
                end

                instruments[s["instrument_type"]] += 1
              end
            end
          end
        end
      end

      libraries_with_spectra.uniq!

      puts "Compounds: " + output.length.to_s
      puts "Compounds with 3: " + all_output.length.to_s
      puts "Spectra: " + spectra_count.to_s
      puts "Instruments"
      instruments.each do |k, v|
        puts k + ": " + v.to_s
      end
      puts "Libraries Used (# of compounds): " + libraries_with_spectra.compact.map { |l|
        l + "(" + output.select {|c| c["source"] == l }.length.to_s + ")"
      }.join(", ")

      output_json = JSON.pretty_generate(output)
      File.open(output_file, 'w') { |file| file.write(output_json) }

      all_output_json = JSON.pretty_generate(all_output)
      File.open(all_output_file, 'w') { |file| file.write(all_output_json) }
    end
  end

  # Parse the spectra by GC-MS vs LC-MS (not including computed)
  desc "Get compounds missing classification"
  task :missing_classification => :environment do
    files = Dir.glob("data/mona2019/parsed/filtered*")
    output_file = "data/mona2019/parsed/missing_classification.txt"
    missing_by_library = {}
    files.each do |input_file|
      puts "File: " + input_file
      missing_by_library[input_file] = []

      file = open(input_file)

      input_json = file.read

      # This will be an array of spectra data objects
      parsed_json = JSON.parse(input_json)

      parsed_json.each do |compound|
        if compound["classification"].blank?
          missing_by_library[input_file] << compound["inchi_key"] + "\t" + compound["smiles"]
        end
      end

      missing_by_library[input_file] = missing_by_library[input_file].compact.uniq
    end

    missing_by_library.each do |k, v|
      puts k + ": " + v.length.to_s
    end

    all_missing = missing_by_library.values.flatten

    File.open(output_file, "w+") do |f|
      f.puts(all_missing.compact.uniq)
    end
  end

  desc "Find lipid spectra at 40eV"
  task :find_lipids => :environment do

    input_file = "data/mona/parsed/energies-filtered_spectra_all_LC-MS.json"
    output_file = "data/mona/parsed/lipids.txt"
    file = open(input_file)

    input_json = file.read

    parsed_json = JSON.parse(input_json)

    output = []
    parsed_json.each do |compound|
      if compound["classification"].any? { |c| c["value"] == "Lipids and lipid-like molecules" }
        compound["spectra"].each do |s|
          if s["collision_energy"] =~ /40/
            output << compound["compound_name"] + "\t" + compound["source_id"].gsub("-", "_") + "\t" + compound["inchi_key"] + "\t" + compound["smiles"] + "\t" + s["collision_energy"] + "\t" + s["adduct_type"]
          end
        end
      end
    end

    puts "Lipids: " + output.length.to_s

    File.open(output_file, "w+") do |f|
      f.puts(output)
    end
  end


  # Import experimental spectra
  desc "Import MoNA spectra"
  task :import_mona => :environment do
    files = Dir.glob("data/mona/parsed/energies*")
    files.each do |input_file|
      puts "File: " + input_file
      spectra_type = input_file.match(/_([^_]+)\.json/).captures[0]
      puts spectra_type
      file = open(input_file)

      input_json = file.read

      # This will be an array of spectra data objects
      parsed_json = JSON.parse(input_json)

      # num_spectra = []
      # energy_levels = []
      # adducts = []

      parsed_json.each do |compound|

        # Skip HMDB spectra
        if compound["source"] == "HMDB"
          next
        end
        # Add to table
        if !DatabaseMolecule.find_by(source_id: nillify(compound["source_id"].gsub("-", "_")))
          DatabaseMolecule.find_or_create_by!( source: nillify(compound["source"]),
                                    source_id: nillify(compound["source_id"].gsub("-", "_")),
                                    name: nillify(compound["compound_name"]),
                                    smiles: nillify(compound["smiles"]),
                                    inchi_key: nillify(compound["inchi_key"]),
                                    inchi: nillify(compound["inchi"]),
                                    neutral_mass: nillify(compound["neutral_mass"].to_f.round(9))
                                  )
        end

        positive = compound["spectra"].select {|s| s["ionization_mode"] == "positive"}
        negative = compound["spectra"].select {|s| s["ionization_mode"] == "negative"}
        if spectra_type =~ /GC-MS/
          spectra = {
            positive: {
              energy0: positive.select {|s| s["collision_energy"] =~ /70/}
            },
            negative: {
              energy0: negative.select {|s| s["collision_energy"] =~ /70/}
            },
          }
        else
          spectra = {
            positive: {
              energy0: positive.select {|s| s["collision_energy"] =~ /10/},
              energy1: positive.select {|s| s["collision_energy"] =~ /20/},
              energy2: positive.select {|s| s["collision_energy"] =~ /40/}
            },
            negative: {
              energy0: negative.select {|s| s["collision_energy"] =~ /10/},
              energy1: negative.select {|s| s["collision_energy"] =~ /20/},
              energy2: negative.select {|s| s["collision_energy"] =~ /40/}
            },
          }
          spectra = fill_energy_levels(spectra)
        end
        spectra.each do |ionization, levels|
          spectra_text = []
          levels.each do |level, spectra_list|
            # if v.length > 1 && v.map { |s| s["adduct_type"] }.uniq.length > 1
              # puts "MULTIPLE"
              # puts compound["neutral_mass"]
              # puts v

            # If there are multiple spectra for a certain energy level
            # Take the one with the mass closest to the given compound mass
            new_spectra = spectra_list[0]
            if spectra_list.length > 1
              min = nil
              spectra_list.each do |spectrum|
                diff = (compound["neutral_mass"].to_f - spectrum["exact_mass"].to_f).abs
                if (min == nil) || (diff < min)
                  min = diff
                  new_spectra = spectrum
                end
              end
            end

            # Create the spectra text
            spectra_text << level.to_s
            if new_spectra
              spectra_text = spectra_text.concat(new_spectra["spectrum"].split(/\s/).map { |l| l.gsub(":", "\t") })
            end
          end

          # Make a directory for our spectra, if necessary
          if spectra_type =~ /GC-MS/
            dir_name = "data/ei/" + compound["source"].parameterize
            write_ei_spectra_file(dir_name, compound, spectra_text)
          else
            dir_name = "data/esi/" + ionization.to_s + "/" + compound["source"].parameterize
            write_esi_spectra_file(dir_name, compound, spectra_text)
          end

        end
        # energy_levels << positive.map {|s| s["collision_energy"]}.uniq
        # energy_levels << negative.map {|s| s["collision_energy"]}.uniq
        # adducts << positive.map {|s| s["adduct_type"]}.uniq
        # adducts << negative.map {|s| s["adduct_type"]}.uniq
        # num_spectra << positive.length
        # num_spectra << negative.length
      end

      # puts adducts.uniq.to_s
      # puts energy_levels.uniq.to_s
      # puts num_spectra.uniq.to_s
    end
  end

  # Import predicted spectra by InChIKey.
  #
  # WARNING: Existing predicted spectra whose source does not match program_version will
  # be deleted.
  #
  # Usage:
  #     bundle exec rake spectra:import_predicted_spectra_by_inchikey[dir,spectra_type,program_version]
  #   where
  #     dir = relative path to directory containing input files, e.g. data/import
  #     spectra_type = EI or ESI
  #     program_version = string with prediction program name and version, e.g. "MSRB-Fragmenter 1.0.8"
  #
  # Input files should contain CFM-ID 2.0, MSRB-Fragmenter, or CFM-ID 4.0 predicted spectra output and
  # consist of files names like:
  #   [InChIKey].txt
  # or
  #   [InChIKey].log
  # or
  #   [InChIKey]_[adduct].txt
  # or
  #   [InChIKey]_[adduct].log
  # e.g.
  #   InChIKey=AAPGXBRKBIUCMQ-UNIWNIHSSA-N_[M+Na]+.txt
  desc "Import Predicted Spectra by InChIKey"
  task :import_predicted_spectra_by_inchikey, [:dir, :spectra_type, :adduct_type, :program_version] => :environment  do |t, args|
    usage = "Usage:\n    bundle exec rake spectra:import_predicted_spectra_by_inchikey[dir,spectra_type,adduct_type,program_version]"
    usage += "\n  where"
    usage += "\n    dir = relative path to directory containing input files, e.g. data/import"
    usage += "\n    spectra_type = EI or ESI"
    usage += "\n    program_version = string with prediction program name and version, e.g. \"MSRB-Fragmenter 1.0.8\""
    usage += "\n    adduct_type = default adduct type to use, if it cannot be parsed from input filename, e.g. \"[M+H]+\", \"[M-H]-\"."
    usage += "\n      Provide \"NA\" to skip all input files without an adduct type in the filename."

    # Check arguments
    if args.dir.nil?
      STDERR.puts "ERROR: dir not specified. Exiting."
      STDERR.puts usage
      exit(1)
    end
    if args.spectra_type.nil?
      STDERR.puts "ERROR: spectra_type not specified. Exiting."
      STDERR.puts usage
      exit(1)
    elsif not ['EI', 'ESI'].include?(args.spectra_type)
      STDERR.puts "ERROR: unrecognized spectra_type value '#{args.spectra_type}'. Exiting."
      STDERR.puts usage
      exit(1)
    end
    if args.adduct_type.nil?
      STDERR.puts "ERROR: adduct_type not specified. Exiting."
      STDERR.puts usage
      exit(1)
    end
    if args.program_version.nil? # Also will be nil if string is empty or all whitespace.
      STDERR.puts "ERROR: program_version not specified. Exiting."
      STDERR.puts usage
      exit(1)
    end

    if args.adduct_type != 'NA'
      default_adduct_type = AdductCalculator.normalized(args.adduct_type)
      if AdductCalculator.positive_adduct_type?(default_adduct_type)
        default_ionization_mode = "positive"
      elsif AdductCalculator.negative_adduct_type?(default_adduct_type)
        default_ionization_mode = "negative"
      else
        puts "ERROR: Invalid default adduct #{default_adduct_type} give as argument"
        exit(1)
      end
    end

    errors = []
    original_num_spectra = Spectrum.count
    files = Dir.glob(args.dir + '/*')
    files.each do |input_file|
      next if !(input_file.end_with?(".log") or input_file.end_with?(".txt"))
      m = File.basename(input_file).match(/^InChIKey=[A-Z]{14}-[A-Z]{10}-[A-Z]{1}(?:_([^\.]+))?/)
      next if m.nil?
      matches = m[0].split("_")
      inchi_key = matches.first
#       puts inchi_key.inspect
      adduct_type = matches.second
      database_molecules = DatabaseMolecule.where(:inchi_key => inchi_key)
      if adduct_type.present?
        adduct_type = AdductCalculator.normalized(adduct_type)
        if AdductCalculator.positive_adduct_type?(adduct_type)
          ionization_mode = "positive"
        elsif AdductCalculator.negative_adduct_type?(adduct_type)
          ionization_mode = "negative"
        else
          err_msg = "Invalid adduct #{adduct_type} in #{input_file}"
          puts "ERROR: " + err_msg
          errors << err_msg
          next
        end
      else
        if args.adduct_type == 'NA'
          # Skip the input file.
          err_msg = "Skipping: no adduct type in filename #{input_file}"
          puts "ERROR: " + err_msg
          errors << err_msg
          next
        else
          # Default adduct type was specified, so use it.
          ionization_mode = default_ionization_mode
          adduct_type = default_adduct_type
        end
      end
      final_filename = inchi_key + "_" + adduct_type + ".txt"

      # Create temporary directories within input directory
      dir = "#{Dir.pwd}/#{args.dir}/#{ionization_mode}/peaks"
      annotated_dir = "#{Dir.pwd}/#{args.dir}/#{ionization_mode}/annotated"
      FileUtils.mkdir_p dir
      FileUtils.mkdir_p annotated_dir

      new_filename = File.basename(input_file)
      new_filename = new_filename.gsub(/.log$/, ".txt")
      if !new_filename.match(/\.txt$/)
        new_filename += '.txt'
      end

      # Copy annotated peak list file to temporary directory, and create unannotated peak
      # list file there as well.
      FileUtils.cp(input_file, File.join(annotated_dir, new_filename))
      annotated_peak_list = File.read(File.join(annotated_dir, new_filename)).split("\n\n")[0].split("\n")
      peak_list = annotated_peak_list.map { |p| p.scan(/#.*|energy\d|\d+\.?\d+ \d+\.?\d+/).first }
      File.open(File.join(dir, new_filename), "w+") do |f|
        f.puts(peak_list)
      end

      begin
        # Find spectra matching attributes other than spectra_source.
        spectra = Spectrum.where( inchi_key: inchi_key,
                                  method: "Computed",
                                  spectra_type: args.spectra_type,
                                  ion_mode: ionization_mode,
                                  collision_energy: "all", # Assume predictions made for all collision energy levels.
                                  adduct_type: adduct_type || "Neutral"
                                )

        create = false

        if spectra.count == 0
          create = true
        else
          # Delete any spectra with a different spectra_source, but which otherwise have
          # the same attributes as the spectrum we are importing.
          spectrum_same_program_version = nil
          spectra.each do |spectrum|
            if spectrum.spectra_source != args.program_version
              puts "Deleting spectrum with source #{spectrum.spectra_source} for #{spectrum.inchi_key} #{spectrum.adduct_type}"
              spectrum.destroy!
            else
              if spectrum_same_program_version.nil?
                puts "Found existing spectrum with source #{spectrum.spectra_source} for #{spectrum.inchi_key} #{spectrum.adduct_type}"
                spectrum_same_program_version = spectrum
              else
                # Duplicate entry in database with same program version!
                puts "Deleting duplicate spectrum with source #{spectrum.spectra_source} for #{spectrum.inchi_key} #{spectrum.adduct_type}"
                spectrum.destroy!
              end
            end
          end

          if spectrum_same_program_version.nil?
            create = true
          else
            # We could run spectrum_same_program_version.update_attributes, but we know the
            # spectrum already has all the attributes we need.
          end
        end

        if create
          # Spectrum does not exist already, so create it.
          puts "Creating spectrum with source #{args.program_version} for #{inchi_key} #{adduct_type}"
          Spectrum.create(spectra_source: args.program_version,
                          inchi_key: inchi_key,
                          method: "Computed",
                          spectra_type: args.spectra_type,
                          ion_mode: ionization_mode,
                          collision_energy: "all", # Assume predictions made for all collision energy levels.
                          adduct_type: adduct_type
                        )
          peaks_dest_dir = File.join(Spectrum::PRECOMPUTED_DATA_DIR, args.spectra_type.downcase, ionization_mode, "peaks")
          annotated_dest_dir = File.join(Spectrum::PRECOMPUTED_DATA_DIR, args.spectra_type.downcase, ionization_mode, "annotated")
          FileUtils.mkdir_p peaks_dest_dir
          FileUtils.mkdir_p annotated_dest_dir
          FileUtils.copy(File.join(dir, new_filename), File.join(peaks_dest_dir, final_filename))
          FileUtils.copy(File.join(annotated_dir, new_filename), File.join(annotated_dest_dir, final_filename))
        end

      rescue Exception => e
        errors << "Problem when processing #{inchi_key}"
        puts e.message
        puts e.backtrace
      end

    end
    puts "Change in spectra count: #{Spectrum.count - original_num_spectra}"
    puts "Errors: #{errors.length}"
    if errors.present?
      File.open("#{args.dir}/import_fails.txt", "w+") do |f|
        f.puts(errors.compact.uniq)
      end
      puts "Errors written to #{args.dir}/import_fails.txt"
    end
  end

  desc 'Output predicted spectra flat files and a TSV file indicating prediction program name and version for each spectrum'
  task :export_predicted_spectra, [:spectra_type, :outdir, :outfile] => :environment  do |t, args|
    usage = "Usage:\n    bundle exec rake spectra:export_predicted_spectra[outdir,outfile]"
    usage += "\n  where"
    usage += "\n    spectra_type = 'ESI' or 'EI'"
    usage += "\n    outdir = path where spectra files should be written"
    usage += "\n    outfile = output file containing InChIKey values and prediction program name and version"

    # Check arguments
    if args.spectra_type.nil?
      STDERR.puts "ERROR: spectra_type not specified. Exiting."
      STDERR.puts usage
      exit(1)
    elsif not ['esi', 'ei'].include?(args.spectra_type.downcase)
      STDERR.puts "ERROR: unrecognized spectra_type value '#{args.spectra_type}'. Exiting."
      STDERR.puts usage
      exit(1)
    end
    if args.outdir.nil?
      STDERR.puts "ERROR: outdir not specified. Exiting."
      STDERR.puts usage
      exit(1)
    end
    if args.outfile.nil?
      STDERR.puts "ERROR: outfile not specified. Exiting."
      STDERR.puts usage
      exit(1)
    end


    begin
      # Create output directories
      if !File.directory?(args.outdir)
        FileUtils.mkdir_p(args.outdir)
        if !File.directory?(args.outdir)
          STDERR.puts "ERROR: could not create #{args.outdir}. Exiting."
          exit(1)
        end
      end

      outdir_positive = File.join(args.outdir, 'positive')
      outdir_negative = File.join(args.outdir, 'negative')
      if !File.directory?(outdir_positive) or !File.directory?(outdir_negative)
        FileUtils.mkdir_p([outdir_positive, outdir_negative])
        if !File.directory?(outdir_positive)
          STDERR.puts "ERROR: could not create #{outdir_positive}. Exiting."
          exit(1)
        end
        if !File.directory?(outdir_negative)
          STDERR.puts "ERROR: could not create #{outdir_negative}. Exiting."
          exit(1)
        end
      end

      CSV.open(args.outfile, "w", col_sep: "\t") do |csv|
        Spectrum.where(:method => 'Computed', :spectra_type => args.spectra_type).each do |spectrum|

          # puts spectrum.adduct_type
          adduct_str = AdductCalculator.normalized(spectrum["adduct_type"])
          annotated_basename = spectrum.inchi_key + '_' + adduct_str + '.txt'
          orig_annotated_filename = File.join(Spectrum::PRECOMPUTED_DATA_DIR, spectrum.spectra_type.downcase, spectrum.ion_mode.downcase, "annotated", annotated_basename)
          new_annotated_filename = File.join(args.outdir, spectrum.ion_mode.downcase, annotated_basename)
          if File.file?(orig_annotated_filename)
            FileUtils.cp(orig_annotated_filename, new_annotated_filename)
          else
            puts "Does not exist: " + orig_annotated_filename
          end

          csv << [spectrum.inchi_key, spectrum.adduct_type, spectrum.spectra_type, spectrum.ion_mode, spectrum.spectra_source]
        end
      end
    rescue Exception => e
      puts e.message
      puts e.backtrace
      exit(1)
    end

  end

  # Imports predicted spectra of MolDB compounds, using InChIKey.
  # Obtains information about the structure from MolDB, and creates corresponding
  # DatabaseMolecule objects if needed. 
  #
  # Input files consist of files names like:
  #   [InChIKey]_[adduct].txt
  # and contain CFM-ID 2.0 or MSRB-Fragmenter predicted spectra output.
=begin
  desc "Import Generic Spectra"
  task :import_generic_spectra => :environment do
    generic_names = {}
    errors = []
    original_num_molecules = DatabaseMolecule.count
    original_num_spectra = Spectrum.count
    files = Dir.glob('data/generic/*')
    files.each do |input_file|
      next if !input_file.end_with?(".log")
      matches = input_file.split("/").last.match(/InChIKey=[A-Z]{14}-[A-Z]{10}-[A-Z]{1}(?:_([^\.]+))?/)[0].split("_")
      inchi_key = matches.first
      puts inchi_key.inspect
      adduct_type = matches.second
      puts adduct_type
      database_molecules = DatabaseMolecule.where(:inchi_key => inchi_key)
      if adduct_type.present?
        adduct_type = AdductCalculator.normalized(adduct_type.gsub(/\([^\(\)]+\)/, ""))
        if AdductCalculator.positive_adduct_type?(adduct_type)
          ionization_mode = "positive"
        elsif AdductCalculator.negative_adduct_type?(adduct_type)
          ionization_mode = "negative"
        else
          puts "BAD ADDUCT"
          next
        end
        final_filename = inchi_key + "_" + adduct_type + ".txt"
      else
        final_filename = inchi_key + ".txt"
      end
      
      dir = "#{Dir.pwd}/data/generic/#{ionization_mode}/peaks"
      annotated_dir = "#{Dir.pwd}/data/generic/#{ionization_mode}/annotated"
      new_filename = "#{input_file.split("/").last.gsub(".log","")}.txt"
      FileUtils.cp(input_file, annotated_dir + "/" + new_filename)
      annotated_peak_list = File.read(annotated_dir + "/" + new_filename).split("\n\n")[0].split("\n")
      peak_list = annotated_peak_list.map { |p| p.scan(/energy\d|\d+\.?\d+ \d+\.?\d+/).first }
      File.open(dir + "/" + new_filename, "w+") do |f|
        f.puts(peak_list)
      end

      if database_molecules.empty?
        conn = Faraday.new(url: "http://moldb.wishartlab.com") do |c|
          c.response :json, :content_type => /\bjson$/
          c.adapter Faraday.default_adapter
        end
        api_response = conn.get "/structures/#{inchi_key.gsub("InChIKey=","")}.json"
        while api_response.status == 502
          sleep(120)
          api_response = conn.get "/structures/#{inchi_key.gsub("InChIKey=","")}.json"
        end
        if api_response.status == 200
          r = api_response.body
          ["DrugBank", "HMDB", "ContaminantDB"].each do |source|
            db = (source == "ContaminantDB" ? "chemdb" : source.downcase)
            registration = r["database_registrations"].find { |dr| dr["resource"] == db }
            next if registration.blank?
            puts registration
            begin
              dm = DatabaseMolecule.create(source: source,
                                            source_id: r["id"],
                                            name: r["traditional_iupac"],
                                            smiles: r["smiles"],
                                            inchi_key: inchi_key,
                                            inchi: r["inchi"],
                                            neutral_mass: r["mono_mass"] # Exact mass
                                            )
            rescue Exception => e
              puts e
            end
            if dm.classification.blank?
              puts dm.id
              errors << "#{inchi_key} could not be classified" if !classify(dm)
            end
            if !dm.persisted?
              puts "Error: Could not create #{inchi_key}"
              errors << "#{inchi_key} could not be created"
            end
          end
        end
      end

      Spectrum.find_or_create_by!(spectra_source: "MSRB-Fragmenter 1.0.8",
                                    inchi_key: inchi_key,
                                    method: "Computed",
                                    spectra_type: "ESI",
                                    ion_mode: ionization_mode,
                                    collision_energy: "all",
                                    adduct_type: adduct_type || "Neutral"
                                )

      FileUtils.copy(dir + "/" + new_filename, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", ionization_mode, "peaks", final_filename))
      FileUtils.copy(annotated_dir + "/" + new_filename, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", ionization_mode, "annotated", final_filename))
    end
    puts "New Molecules: #{DatabaseMolecule.count - original_num_molecules}"
    puts "New Spectra: #{Spectrum.count - original_num_spectra}"
    puts "Errors: #{errors.length}"
    if errors.present?
      File.open("data/generic/import_fails.txt", "w+") do |f|
        f.puts(errors.compact.uniq)
      end
    end
  end
=end

  def classify(dm)
    if dm.inchi_key.blank?
      if dm.inchi.present?
        inchi_key = Jchem.structure_to_inchikey(dm.inchi)
      else
        inchi_key = Jchem.structure_to_inchikey(dm.smiles)
      end
      inchi_key = inchi_key.gsub(/^InChIKey=/i, "")
    else
      inchi_key = dm.inchi_key.gsub(/^InChIKey=/i, "")
    end
    url = "http://classyfire.wishartlab.com/entities/#{inchi_key}.json"
    begin
      data = JSON.load(open(url))
      classification = Classification.find_or_create_by(inchi_key: dm.inchi_key)
      classification.kingdom = data["kingdom"].present? ? data["kingdom"]["name"] : nil
      classification.superklass = data["superclass"].present? ? data["superclass"]["name"] : nil
      classification.klass = data["class"].present? ? data["class"]["name"] : nil
      classification.subklass = data["subclass"].present? ? data["subclass"]["name"] : nil
      classification.intermediate_nodes = data["intermediate_nodes"] # Array
      classification.direct_parent = data["direct_parent"].present? ? data["direct_parent"]["name"] : ""
      classification.alternative_parents = data["alternative_parents"].present? ? data["alternative_parents"].map { |ap| ap["name"] } : nil
      classification.molecular_framework = data["molecular_framework"]
      classification.substituents = data["substituents"] # Array
      classification.ancestors = data["ancestors"] # Array
      classification.save!

      return true
    rescue
      puts "CLASSIFICATION MISSING: " + dm.id.to_s
      return false
    end
  end


  # Import experimental spectra
  desc "Import HMDB spectra"
  task :import_hmdb => :environment do
    hmdb_names = {}

    files = Dir.glob("data/hmdb/*")
    files.each do |input_file|
      puts "File: " + input_file

      file = open(input_file)

      input_json = file.read

      # This will be an array of spectra data objects
      parsed_json = JSON.parse(input_json)

      parsed_json.each do |compound|
        source_id = compound["source_id"].gsub("-", "_")

        # Add to table, we need to check this way
        # because for some reason the HMDB molecules don't
        # always match on name for id
        if !DatabaseMolecule.find_by(source: "HMDB_Exp", source_id: source_id)

          DatabaseMolecule.create!( source: "HMDB_Exp",
                                  source_id: source_id,
                                  name: compound["compound_name"],
                                  smiles: compound["smiles"],
                                  inchi_key: compound["inchi_key"],
                                  inchi: compound["inchi"],
                                  neutral_mass: compound["neutral_mass"].to_f.round(9)
                                )
        end

        positive = compound["spectra"].select {|s| s["ionization_mode"].downcase == "positive"}
        negative = compound["spectra"].select {|s| s["ionization_mode"].downcase == "negative"}
        if input_file =~ /hmdb_gc.json/
          # HMDB GC-MS spectra have no collision energies but we assume they are all OK
          spectra = {
            positive: {
              energy0: positive
            },
            negative: {
              energy0: negative
            },
          }
        else
          spectra = {
            positive: {
              energy0: positive.select {|s| s["collision_energy"] =~ /10/},
              energy1: positive.select {|s| s["collision_energy"] =~ /20/},
              energy2: positive.select {|s| s["collision_energy"] =~ /40/}
            },
            negative: {
              energy0: negative.select {|s| s["collision_energy"] =~ /10/},
              energy1: negative.select {|s| s["collision_energy"] =~ /20/},
              energy2: negative.select {|s| s["collision_energy"] =~ /40/}
            },
          }
          spectra = fill_energy_levels(spectra)
        end
        spectra.each do |ionization, levels|
          spectra_text = []
          levels.each do |level, spectra_list|
            # If there are multiple spectra for a certain energy level
            # Take the one with the mass closest to the given compound mass
            new_spectra = spectra_list[0]
            if spectra_list.length > 1
              min = nil
              spectra_list.each do |spectrum|
                diff = (compound["neutral_mass"].to_f - spectrum["exact_mass"].to_f).abs
                if (min == nil) || (diff < min)
                  min = diff
                  new_spectra = spectrum
                end
              end
            end

            # Create the spectra text
            spectra_text << level.to_s
            if new_spectra
              spectra_text.concat(new_spectra["spectrum"].split(/\s/).map { |l| l.gsub(":", "\t") })
            end
          end

          # Make a directory for our spectra, if necessary
          if input_file =~ /hmdb_gc.json/
            dir_name = "data/ei/hmdb_exp"
            write_ei_spectra_file(dir_name, compound, spectra_text)
          else
            dir_name = "data/esi/" + ionization.to_s + "/hmdb_exp"
            write_esi_spectra_file(dir_name, compound, spectra_text)
          end
        end
      end
    end
  end

  desc "Transfer existing compounds to database molecules and spectra "
  task :transfer_existing => :environment do
    unless File.directory?(File.join(Rails.root, "data", "ei", "peaks"))
      FileUtils.mkdir_p(File.join(Rails.root, "data", "ei", "peaks"))
    end
    unless File.directory?(File.join(Rails.root, "data", "ei", "annotated"))
      FileUtils.mkdir_p(File.join(Rails.root, "data", "ei", "annotated"))
    end
    unless File.directory?(File.join(Rails.root, "data", "esi", "positive", "peaks"))
      FileUtils.mkdir_p(File.join(Rails.root, "data", "esi", "positive", "peaks"))
    end
    unless File.directory?(File.join(Rails.root, "data", "esi", "negative", "peaks"))
      FileUtils.mkdir_p(File.join(Rails.root, "data", "esi", "negative", "peaks"))
    end
    unless File.directory?(File.join(Rails.root, "data", "esi", "positive", "annotated"))
      FileUtils.mkdir_p(File.join(Rails.root, "data", "esi", "positive", "annotated"))
    end
    unless File.directory?(File.join(Rails.root, "data", "esi", "negative", "annotated"))
      FileUtils.mkdir_p(File.join(Rails.root, "data", "esi", "negative", "annotated"))
    end

    # List of HMDB names to fill in missing HMDB_Deriv names
    hmdb_names = {}
    CSV.foreach("data/hmdb_id_names.csv") do |row|
      hmdb_names[row[0]] = row[1]
    end

    HmdbMolecule.all.each do |m|
      # Add to table
      dm = DatabaseMolecule.find_or_create_by!( source: "HMDB",
                                    source_id: m.hmdb_id,
                                    name: m.name,
                                    smiles: m.smiles,
                                    inchi_key: m.inchi_key,
                                    inchi: m.inchi,
                                    neutral_mass: m.neutral_mass
                                  )
      check_structures(dm)
      create_existing_spectra("HMDB", dm, m.hmdb_id)
    end

    HmdbDerivative.all.each do |m|
      d_name = HmdbMolecule.find_by_hmdb_id(m.hmdb_id).try(:name)
      if d_name.blank?
        d_name = hmdb_names[m.hmdb_id]
      end
      # Add to table
      dm = DatabaseMolecule.find_or_create_by!( source: "HMDB_Deriv",
                                    source_id: m.hmdb_deriv_id,
                                    name: d_name,
                                    smiles: m.smiles,
                                    inchi_key: Jchem.structure_to_inchikey(m.inchi), 
                                    inchi: m.inchi,
                                    neutral_mass: m.neutral_mass,
                                    derivatization_type: m.deriv_type,
                                    derivatization_of_source_id: m.hmdb_id
                                  )
      check_structures(dm)
      create_existing_spectra("HMDB_Deriv", dm, m.hmdb_deriv_id, m.deriv_type)
    end

    KeggMolecule.all.each do |m|
      # Add to table
      dm = DatabaseMolecule.find_or_create_by!( source: "KEGG",
                                    source_id: m.kegg_id,
                                    name: m.name,
                                    inchi_key: m.inchi_key,
                                    inchi: m.inchi,
                                    neutral_mass: m.neutral_mass
                                  )
      check_structures(dm)
      create_existing_spectra("KEGG", dm, m.kegg_id)
    end
  end

  desc "Split MoNa file into smaller files"
  task :split_mona => :environment do
    input_json = File.read("data/mona2019/parsed/filtered_spectra_all_LC-MS.json")
    parsed_json = JSON.parse(input_json)

    i = 1
    parsed_json.in_groups_of(2000) do |group|
      File.open("data/mona2019/parsed/filtered_spectra_#{i}_LC-MS.json", "w+") do |f|
        f.write(group.compact.to_json)
      end
      i += 1
    end
  end

  desc "Import experimental spectra (all collision energy levels)"
  task :import => :environment do
    files = Dir.glob("data/mona2019/parsed/filtered*")
    files.each do |input_file|
      original_num_molecules = DatabaseMolecule.count
      original_num_spectra = Spectrum.count
      puts "File: " + input_file
      spectra_type = input_file.match(/_([^_]+)\.json/).captures[0]
      puts spectra_type

      input_json = File.read(input_file)

      # This will be an array of spectra data objects
      parsed_json = JSON.parse(input_json)

      # We just need to add all possible adducts here to create unique file names
      postive_adducts = AdductCalculator::MASS_TO_POSITIVE_ION_MASS.keys
      # postive_adducts = postive_adducts.concat(["M-H2O+H", "M-2H2O+H", "M+H-H2O", "M+H-C12H20O9", "M-H2O", "M+15"])
      negative_adducts = AdductCalculator::MASS_TO_NEGATIVE_ION_MASS.keys
      # negative_adducts = negative_adducts.concat(["M+CH3COOH-H", "M-H2O-H", "M-1", "M-C2H3O", "M-2H2-"])

      parsed_json.each do |compound|
        next if compound["source"] == "MoNA" # Skip if we don't really know the source
        next if compound["inchi_key"].length > 255 # Skip if the inchi_key is too long
        source_id = compound["source_id"].gsub("-", "_")

        # Add to table
        dm = DatabaseMolecule.find_by(source: compound["source"],
                                      source_id: source_id)
        if dm.blank?
          dm = DatabaseMolecule.create!(source: compound["source"],
                                        source_id: source_id,
                                        name: compound["compound_name"],
                                        smiles: compound["smiles"],
                                        inchi_key: compound["inchi_key"],
                                        inchi: compound["inchi"],
                                        neutral_mass: compound["neutral_mass"].to_f.round(9)
                                       )
        end

        check_structures(dm)

        compound["spectra"].each do |spectrum|
          if spectra_type =~ /GC-MS/
            identifier = compound["source_id"]
            ionization = "positive"
            type = "EI"
            adduct_type = AdductCalculator.normalized(spectrum["adduct_type"])
          else
            if spectrum["adduct_type"].present? && spectrum["adduct_type"] != "?"
              adduct_type = AdductCalculator.normalized(spectrum["adduct_type"])
              if spectrum["ionization_mode"].downcase == "positive"
                adduct_num = postive_adducts.index(adduct_type)
              elsif spectrum["ionization_mode"].downcase == "negative"
                adduct_num = negative_adducts.index(adduct_type)
              end
              if adduct_num != nil && adduct_num > -1
                identifier = source_id + "_" + adduct_num.to_s
              else
                # Skip the spectra if we don't support the adduct type
                next
              end
            else
              adduct_type = nil
              identifier = source_id
            end
            ionization = nillify(spectrum["ionization_mode"].downcase)
            type = "ESI"
          end
          new_spectrum = Spectrum.find_or_create_by!(spectra_source: nillify(compound["source"]),
                                      inchi_key: dm.inchi_key,
                                      method: "Experimental",
                                      spectra_type: type,
                                      ion_mode: ionization,
                                      collision_energy: nillify(spectrum["collision_energy"]),
                                      adduct_type: nillify(adduct_type),
                                      instrument_type: nillify(spectrum["instrument_type"]),
                                      derivatization_type: nillify(spectrum["derivatization_type"]),
                                      splash_key: nillify(spectrum["splash_key"])
                                    )

          # Create the spectra text
          spectra_text = ["energy0"]
          spectra_text = spectra_text.concat(spectrum["spectrum"].split(/\s/).map { |l| l.gsub(":", "\t") })

          write_spectra_file(spectra_text, new_spectrum)
        end
      end

      puts "New Molecules: #{DatabaseMolecule.count - original_num_molecules}"
      puts "New Spectra: #{Spectrum.count - original_num_spectra}"
    end
  end

  desc "Import NIST spectra"
  task :import_nist => :environment do
    input_file = "data/mona/parsed/nist_smiles.msp"
    puts "File: " + input_file
    file = open(input_file)

    input_data = file.read

    # This will be an array of spectra data objects
    parsed_data = input_data.split(/\r\n\r/)

    # We just need to add all possible adducts here to create unique file names
    postive_adducts = AdductCalculator::MASS_TO_POSITIVE_ION_MASS.keys
    # postive_adducts = postive_adducts.concat(["M-H2O+H", "M-2H2O+H", "M+H-H2O", "M+H-C12H20O9", "M-H2O", "M+15"])
    negative_adducts = AdductCalculator::MASS_TO_NEGATIVE_ION_MASS.keys
    # negative_adducts = negative_adducts.concat(["M+CH3COOH-H", "M-H2O-H", "M-1", "M-C2H3O", "M-2H2-"])

    parsed_data.each do |spectra_data|
      lines = spectra_data.split("\n").map(&:strip).reject { |c| c.empty? }

      data_hash = { "peak_list" => [] }

      lines.each do |line|
        if line =~ /^([\S ]+):\s(\$:\d+)?(.+)$/
          data_hash[($1.to_s + " " + $2.to_s).strip] = $3
        else
          data_hash["peak_list"] << line
        end
      end

      # Add to table
      dm = DatabaseMolecule.find_by(source: "NIST",
                                    source_id: data_hash["CASNO"])
      begin
        inchi = Jchem.structure_to_inchi(data_hash["SMILES"])
      rescue
        puts "Jchem error: " + data_hash["SMILES"]
        inchi = nil
      end

      if dm.blank?
        dm = DatabaseMolecule.create!(source: "NIST",
                                      source_id: data_hash["CASNO"],
                                      name: data_hash["Name"],
                                      smiles: data_hash["SMILES"],
                                      inchi_key: data_hash["Synon $:28"],
                                      inchi: inchi,
                                      neutral_mass: data_hash["MW"].to_f.round(9)
                                      )
      end

      check_structures(dm)

      ion_mode = nil
      if data_hash["Synon $:03"]&.ends_with? "-"
        ion_mode = "negative"
      elsif data_hash["Synon $:03"]&.ends_with? "+"
       ion_mode = "positive"
      end

      new_spectrum = Spectrum.find_or_create_by!(spectra_source: "NIST",
                                  inchi_key: dm.inchi_key,
                                  method: "Experimental",
                                  spectra_type: nillify(data_hash["Synon $:10"]),
                                  ion_mode: ion_mode,
                                  adduct_type: nillify(AdductCalculator.normalized(data_hash["Synon $:03"])),
                                  instrument_type: nillify(data_hash["Synon $:06"])
                                )

      # Create the spectra text
      spectra_text = ["energy0"]
      spectra_text = spectra_text.concat(data_hash["peak_list"])

      write_spectra_file(spectra_text, new_spectrum)

      if !File.exists?(new_spectrum.peak_list.path)
        puts "MISSING " + new_spectrum.id.to_s
      end
    end
  end

  desc "Makes sure all database molecules have smiles, inchis and inchikeys"
  task :complete_structures => :environment do
    DatabaseMolecule.where(inchi: [nil, ""]).each do |dm|
      begin
        dm.update_attribute(:inchi, Jchem.structure_to_inchi(dm.smiles))
      rescue
        puts "Jchem error smiles (#{dm.source_id}): " + dm.smiles
      end
    end

    DatabaseMolecule.where(smiles: [nil, ""]).each do |dm|
      begin
        dm.update_attribute(:smiles, Jchem.structure_to_smiles(dm.inchi))
      rescue
        puts "Jchem error inchi (#{dm.source_id}): " + dm.inchi
      end
    end
    DatabaseMolecule.where(inchi_key: [nil, ""]).each do |dm|
      begin
        dm.update_attribute(:inchi_key, Jchem.structure_to_inchikey(dm.inchi))
      rescue
        puts "Jchem error inchi (#{dm.source_id}): " + dm.inchi
      end
    end
    DatabaseMolecule.where(inchi_key: [nil, ""]).each do |dm|
      begin
        dm.update_attribute(:inchi_key, Jchem.structure_to_inchikey(dm.smiles))
      rescue
        puts "Jchem error smiles (#{dm.source_id}): " + dm.inchi
      end
    end
  end

  desc "For counting spectra files"
  task :count => :environment do
    files = Dir.glob("data/ei/hmdb_deriv/*")
    missing = []
    files.each do |f|
      id = f.match(/([^\/]+)\.txt/).captures[0]
      puts id
      c = HmdbDerivative.find_by_hmdb_deriv_id(id)
      if c.blank?
        missing << id
      end
    end
    puts missing.length
  end

  desc "To compute lipid spectra with the fragmenter"
  task :compute_lipids => :environment do
    csv_text = File.read('data/mona/parsed/lipids.txt')
    csv = CSV.parse(csv_text, { :col_sep => "\t" })
    count = 0;
    csv.each do |row|
      id = row[1]
      smiles = row[3]
      adduct_type = row[5]
      output_file = id
      puts query = "java -classpath lib/fragmenter/fragmenter-1_1.jar wishartlab.cfmid_plus.fragmentation.Fragmenter \"#{smiles}\" \"#{adduct_type}\" \"#{output_file}\""
      stdin, stdout, stderr = Open3.popen3(query)
      # puts stderr.gets(nil).to_s
      puts stdout.gets(nil).to_s

      count += 1;

      stdin.close
      stdout.close
      stderr.close
    end
    puts "Total: " + count.to_s
  end

  desc "Run cfm-annotate on the experimental spectra"
  task :annotate => :environment do
    Spectrum.where(method: "Experimental").order(id: :desc).each do |spectrum|
      if spectrum.annotated_peak_list.blank?
        puts spectrum.id
        if !spectrum.database_molecule.present?
          puts "MISSING MOLECULE!"
          next
        end
        # if spectrum.database_molecule.source == "NIST"
        #   output_file = "public/nist/#{spectrum.inchi_key}_output.txt"
        # else
        output_file = "public/#{spectrum.id}_output.txt"
        # end
        input_file = "public/peak_list_file.txt"
        param_file = get_param_file(spectrum.spectra_type, spectrum.ion_mode)
        config_file = get_config_file(spectrum.spectra_type, spectrum.ion_mode)

        if spectrum.spectra_type == "EI"
          peak_list_file = spectrum.peak_list.path
        else
          # Since the experimental ESI spectra have only one energy level
          # but CFM is trained on 3, we need to replicate the spectra
          peak_list = File.read(spectrum.peak_list.path)
          File.open(input_file, "w+") do |f|
            f.puts(peak_list)
            f.puts(peak_list)
            f.puts(peak_list)
          end
          peak_list_file = input_file
        end

        if spectrum.database_molecule.inchi.present?
          compound = spectrum.database_molecule.inchi
        else
          compound = spectrum.database_molecule.smiles
        end

        query_string = "cfm-annotate \"#{compound}\" #{peak_list_file} #{spectrum.id} 10.0 0.01 #{param_file} #{config_file} #{output_file}"
        if CFMID.config[:extra_params]
          query_string = CFMID.config[:extra_params] + " " + query_string
        end
        stdin, stdout, stderr = Open3.popen3(query_string)
        runtime = stdout.gets(nil)
        error = stderr.gets(nil)

        if error.blank? && File.file?(output_file)
          if spectrum.spectra_type == "ESI"
            # Since we made 3 identical energy levels, just take the
            # first annotated one
            annotated_content = File.read(output_file).split("\n\n")
            # Get the annotated peak list
            annotated_peak_list = annotated_content[0]
            # Take the first energy level
            first_annotated = annotated_peak_list.split(/\nenergy[0-2]/)[0].strip
            File.open(output_file, "w+") do |f|
              f.puts(first_annotated)
              f.puts("\n")
              f.puts(annotated_content[1]) # Re-add the fragments
              f.puts("\n")
              f.puts(annotated_content[2]) # And the transitions
            end
          end
          new_file = File.new(output_file)
          spectrum.update_attributes( annotated_peak_list: new_file )
          new_file.close
        else
          puts "ERROR: " + spectrum.id.to_s
          puts error
          puts runtime
        end

        # Remove temp files
        # if spectrum.database_molecule.source != "NIST"
        if File.exists?(output_file)
          File.delete(output_file)
        end
        # end
        if File.exists?(input_file)
          File.delete(input_file)
        end

        if !File.exists?(spectrum.annotated_peak_list.try(:path) || "")
          puts "MISSING " + spectrum.id.to_s
        end
      end
    end
  end

  desc "Transfer existing compounds to database molecules and spectra "
  task :fix_ei => :environment do
    unless File.directory?(File.join(Rails.root, "data", "ei", "peaks"))
      FileUtils.mkdir_p(File.join(Rails.root, "data", "ei", "peaks"))
    end
    unless File.directory?(File.join(Rails.root, "data", "ei", "annotated"))
      FileUtils.mkdir_p(File.join(Rails.root, "data", "ei", "annotated"))
    end

    # List of HMDB names to fill in missing HMDB_Deriv names
    hmdb_names = {}
    CSV.foreach("data/hmdb_id_names.csv") do |row|
      hmdb_names[row[0]] = row[1]
    end

    HmdbDerivative.all.each do |m|
      d_name = HmdbMolecule.find_by_hmdb_id(m.hmdb_id).try(:name)
      if d_name.blank?
        d_name = hmdb_names[m.hmdb_id]
      end
      # Add to table
      dm = DatabaseMolecule.find_by!( source: "HMDB_Deriv",
                                    source_id: m.hmdb_deriv_id,
                                    name: d_name,
                                    smiles: m.smiles,
                                    inchi_key: Jchem.structure_to_inchikey(m.inchi), 
                                    inchi: m.inchi,
                                    neutral_mass: m.neutral_mass,
                                    derivatization_type: m.deriv_type,
                                    derivatization_of_source_id: m.hmdb_id
                                  )
      create_existing_spectra("HMDB_Deriv", dm, m.hmdb_deriv_id, m.deriv_type)
    end
  end

  desc "Normalize and standardize all the adducts"
  task :normalize_adducts => :environment do
    err_filename = 'normalize_adducts_errors.txt'
    err_file = File.open(err_filename, "w+")
		Spectrum.where("adduct_type IS NOT NULL and method = 'Computed'").order(id: :desc).find_each(batch_size: 100) do |spectrum|
			puts spectrum.id
			#puts "original adduct_type: " + spectrum.adduct_type
			#Handle EI spectra and adduct types
			if spectrum.spectra_type.upcase == "EI"
			  if spectrum.adduct_type == 'Neutral'
          original_file_peaks = File.join(Spectrum::PRECOMPUTED_DATA_DIR,
                                            "ei",
                                            "peaks",
                                            spectrum.inchi_key + ".txt")
          original_file_annotated = File.join(Spectrum::PRECOMPUTED_DATA_DIR,
                                              "ei",
                                              "annotated",
                                              spectrum.inchi_key + ".txt")
          spectrum.adduct_type = "[M]+"
			  else
          original_file_peaks = File.join(Spectrum::PRECOMPUTED_DATA_DIR,
                                            "ei",
                                            "peaks",
                                            spectrum.inchi_key + "_" + spectrum.adduct_type + ".txt")
          original_file_annotated = File.join(Spectrum::PRECOMPUTED_DATA_DIR,
                                              "ei",
                                              "annotated",
                                              spectrum.inchi_key + "_" + spectrum.adduct_type + ".txt")
          spectrum.adduct_type = AdductCalculator.normalized(spectrum.adduct_type)
			  end

				normalized_file_peaks = File.join(Spectrum::PRECOMPUTED_DATA_DIR,
																						"ei",
																						"peaks",
																						spectrum.inchi_key + "_" + spectrum.adduct_type + ".txt")
				normalized_file_annotated = File.join(Spectrum::PRECOMPUTED_DATA_DIR,
																							"ei",
																							"annotated",
																							spectrum.inchi_key + "_" + spectrum.adduct_type + ".txt")
			#Handle ESI spectra and adduct types
			elsif spectrum.spectra_type.upcase == "ESI"
				# To-do remove the Neutral behavior
				if spectrum.adduct_type == "Neutral"
					original_file_peaks = File.join(Spectrum::PRECOMPUTED_DATA_DIR,
																						"esi",
																						spectrum.ion_mode.downcase,
																						"peaks",
																						spectrum.inchi_key + ".txt")
					original_file_annotated = File.join(Spectrum::PRECOMPUTED_DATA_DIR,
																							"esi",
																							spectrum.ion_mode.downcase,
																							"annotated",
																							spectrum.inchi_key + ".txt")
					if spectrum.ion_mode.downcase == "positive"
						spectrum.adduct_type = "[M+H]+"
					elsif spectrum.ion_mode.downcase == "negative"
						spectrum.adduct_type = "[M-H]-"
					end														 	
				else
					original_file_peaks = File.join(Spectrum::PRECOMPUTED_DATA_DIR,
																					"esi",
																					spectrum.ion_mode.downcase,
																					"peaks",
																					spectrum.inchi_key + "_" + spectrum.adduct_type + ".txt")
					original_file_annotated = File.join(Spectrum::PRECOMPUTED_DATA_DIR,
																							"esi",
																							spectrum.ion_mode.downcase,
																							"annotated",
																							spectrum.inchi_key + "_" + spectrum.adduct_type + ".txt")
					spectrum.adduct_type = AdductCalculator.normalized(spectrum.adduct_type)
				end
				normalized_file_peaks = File.join(Spectrum::PRECOMPUTED_DATA_DIR,
																						"esi",
																						spectrum.ion_mode.downcase,
																						"peaks",
																						spectrum.inchi_key + "_" + spectrum.adduct_type + ".txt")
				normalized_file_annotated = File.join(Spectrum::PRECOMPUTED_DATA_DIR,
																							"esi",
																							spectrum.ion_mode.downcase,
																							"annotated",
																							spectrum.inchi_key + "_" + spectrum.adduct_type + ".txt")
			end
			if normalized_file_peaks == original_file_peaks && normalized_file_annotated == original_file_annotated
				next
			else
				#puts "normalized adduct_type: " + spectrum.adduct_type
				#puts "original file name: " + original_file_peaks
				#puts "normalized file name: " + normalized_file_peaks

        if File.file?(normalized_file_peaks)
			    if File.file?(original_file_peaks)
			      FileUtils.rm(original_file_peaks)
			    end
			  else
          found = false
          if File.file?(original_file_peaks)
            FileUtils.move(original_file_peaks, normalized_file_peaks)
            found = true
          end
          if !found
            if !File.file?(original_file_peaks)
              puts 'Could not find ' + original_file_peaks
              err_file.puts 'Could not find ' + original_file_peaks
            end
          end
			  end

        if File.file?(normalized_file_annotated)
			    if File.file?(original_file_annotated)
			      FileUtils.rm(original_file_annotated)
			    end
			    if spectrum.spectra_type.upcase == "EI" and spectrum.adduct_type == 'Neutral'
            if File.file?(original_file_annotated2)
              FileUtils.rm(original_file_annotated2)
            end
			    end
			  else
          found = false
          if File.file?(original_file_annotated)
            FileUtils.move(original_file_annotated, normalized_file_annotated)
            found = true
          end
          if spectrum.spectra_type.upcase == "EI" and spectrum.adduct_type == 'Neutral'
            if File.file?(original_file_annotated2)
              FileUtils.move(original_file_annotated2, normalized_file_annotated)
              found = true
            end
          end
          if !found
            if !File.file?(original_file_annotated)
              puts 'Could not find ' + original_file_annotated
              err_file.puts 'Could not find ' + original_file_annotated
            elsif spectrum.spectra_type.upcase == "EI" and spectrum.adduct_type == 'Neutral'
              if !File.file?(original_file_annotated2)
                puts 'Could not find ' + original_file_annotated2
                err_file.puts 'Could not find ' + original_file_annotated2
              end
            end
          end
			  end
			end

			spectrum.save!
		end
		puts 'Start list with NULL adduct type'
		err_file.puts 'Start list with NULL adduct type'
		Spectrum.where("adduct_type IS NULL and spectra_type = 'EI' and method = 'Computed'").order(id: :desc).find_each(batch_size: 100) do |spectrum|
		  puts spectrum.id
			original_file_peaks = File.join(Spectrum::PRECOMPUTED_DATA_DIR,
																				"ei",
																				"peaks",
																				spectrum.inchi_key + ".txt")
			original_file_annotated = File.join(Spectrum::PRECOMPUTED_DATA_DIR,
																					"ei",
																					"annotated",
																					spectrum.inchi_key + ".txt")
			spectrum.adduct_type = "[M]+"
			normalized_file_peaks = File.join(Spectrum::PRECOMPUTED_DATA_DIR,
																				"ei",
																				"peaks",
																				spectrum.inchi_key + "_" + spectrum.adduct_type + ".txt")
			normalized_file_annotated = File.join(Spectrum::PRECOMPUTED_DATA_DIR,
																						"ei",
																						"annotated",
																						spectrum.inchi_key + "_" + spectrum.adduct_type + ".txt")


      if File.file?(normalized_file_peaks)
        if File.file?(original_file_peaks)
          FileUtils.rm(original_file_peaks)
        end
      else
        found = false
        if File.file?(original_file_peaks)
          FileUtils.move(original_file_peaks, normalized_file_peaks)
          found = true
        end
        if !found
          if !File.file?(original_file_peaks)
            puts 'Could not find ' + original_file_peaks
            err_file.puts 'Could not find ' + original_file_peaks
          end
        end
      end

      if File.file?(normalized_file_annotated)
        if File.file?(original_file_annotated)
          FileUtils.rm(original_file_annotated)
        end
      else
        found = false
        if File.file?(original_file_annotated)
          FileUtils.move(original_file_annotated, normalized_file_annotated)
          found = true
        end
        if !found
          if !File.file?(original_file_annotated)
            puts 'Could not find ' + original_file_annotated
            err_file.puts 'Could not find ' + original_file_annotated
          end
        end
      end

			#puts "normalized adduct_type: " + spectrum.adduct_type
			#puts "original file name: " + original_file_peaks
			#puts "normalized file name: " + normalized_file_peaks
			spectrum.save!
		end
		puts 'Errors written to ' + err_filename
  end
	
	desc "remove derivatives that have non standard inchikey"
	task :rm_derivatives => :environment do
		Spectrum.where('derivatization_type IS NOT NULL').order(id: :desc).find_each(batch_size: 100) do |spectrum|
			if !spectrum.inchi_key.match(/\AInChIKey=[A-Z]{14}-[A-Z]{8}SA-[A-Z]{1}/)
				puts "removing row with inchi_key = #{spectrum.inchi_key}"
				spectrum.destroy!
			end
		end
	end
  
	desc "Import 2016 training and testing CASMI compounds plus their experimental spectra"
  task :import_casmi_experimental => :environment do
    year = "2016"

    # Remove existing casmi spectra first
    Spectrum.where(spectra_source: "CASMI#{year}").destroy_all

    ["testing", "training"].each do |type|
      puts "#{type} #{year}"
      original_num_molecules = DatabaseMolecule.count
      original_num_spectra = Spectrum.count
      masses = {}
      masses_file = "data/casmi#{year}/casmi-#{year}-#{type}-masses.tsv"
      if File.exists?(masses_file)
        File.readlines(masses_file).each do |line|
          items = line.split("\t")
          masses[items[0].strip] = items[2].strip.to_f
        end
      else
        puts "Missing #{type} masses file"
      end

      identifiers = {}
      CSV.foreach("data/casmi#{year}/casmi-#{year}-#{type}.csv", headers: true) do |row|
        dm = DatabaseMolecule.find_by(source: "CASMI#{year}",
                                      source_id: row[0])

        # Store id and energy level for each spectra
        # Format: { "Challenge001": ["XX834151", "10"]}
        identifiers[row[1]] = [row[0], row[13]]
        if dm.blank?
          dm = DatabaseMolecule.create!(source: "CASMI#{year}",
                                        source_id: row[0],
                                        name: row[6],
                                        smiles: row[7],
                                        inchi_key: "InChIKey=" + row[9],
                                        inchi: row[8],
                                        neutral_mass: row[2]
                                        )
        end

        # Fix mass
        dm.update_attribute(:neutral_mass, masses[row[1]]) if masses[row[1]].present?


        classify(dm)
      end

      ["positive", "negative"].each do |ion_mode|
        files = Dir.glob("data/casmi#{year}/#{type == "testing" ? "challenge" : "training"}_#{ion_mode}/*")
        files.each do |input_file|
          identifer = input_file.split("/").last.gsub(".txt", "")
          puts identifer
          compound = DatabaseMolecule.find_by(source: "CASMI#{year}",
                                              source_id: identifiers[identifer].first)

          new_spectrum = Spectrum.find_or_create_by!(spectra_source: "CASMI#{year}",
                                      inchi_key: compound.inchi_key,
                                      method: "Experimental",
                                      spectra_type: "ESI",
                                      ion_mode: ion_mode,
                                      collision_energy: identifiers[identifer].second,
                                      adduct_type: AdductCalculator.normalized(ion_mode == "positive" ? "[M+H]+" : "[M-H]-"),
                                      instrument_type: "Q Exactive Plus Orbitrap",
                                      derivatization_type: nil,
                                      splash_key: nil
                                    )

          # Create the spectra text
          spectra_text = ["energy0"]
          spectra_text = spectra_text.concat(File.read(input_file).split(/\n/))

          write_spectra_file(spectra_text, new_spectrum)
        end
      end

      puts "New Molecules: #{DatabaseMolecule.count - original_num_molecules}"
      puts "New Spectra: #{Spectrum.count - original_num_spectra}"
    end
  end

  desc "Import GNPS spectra"
  task :import_gnps => :environment do
    original_num_molecules = DatabaseMolecule.count
    original_num_spectra = Spectrum.count

    files = Dir.glob("data/gnps/*")

    files.each do |input_file|
      data = File.read(input_file)
      identifer = input_file.split("/").last.gsub(".ms", "")
      compound_data = data.split(">collision 0\r\n").first.strip.split("\r\n").map { |r| r.split(/\s/, 2) }.to_h
      spectra = data.split(">collision 0\r\n").last.strip

      dm = DatabaseMolecule.find_by(source: "GNPS",
                                    inchi: compound_data["#inchi"])

      if dm.blank?
        count = DatabaseMolecule.where(source: "GNPS").count
        dm = DatabaseMolecule.create!(source: "GNPS",
                                      source_id: "GNPS#{count + 1}",
                                      name: compound_data[">compound"],
                                      smiles: compound_data["#Smiles"],
                                      inchi_key: Jchem.structure_to_inchikey(compound_data["#inchi"]),
                                      inchi: compound_data["#inchi"],
                                      neutral_mass: compound_data["#ExactMass"]
                                      )
      end

      classify(dm)

      new_spectrum = Spectrum.find_or_create_by!(spectra_source: "GNPS",
                                  inchi_key: dm.inchi_key,
                                  method: "Experimental",
                                  spectra_type: "ESI",
                                  ion_mode: nillify(compound_data["#IonMode"].downcase),
                                  collision_energy: nil,
                                  adduct_type: nillify(AdductCalculator.normalized(compound_data[">ionization"])),
                                  instrument_type: nillify(compound_data["#Instrument"]),
                                  derivatization_type: nil,
                                  splash_key: nil
                                )

      # Create the spectra text
      spectra_text = ["energy0"]
      spectra_text = spectra_text.concat(spectra.split(/\r\n/))

      write_spectra_file(spectra_text, new_spectrum)
    end

    puts "New Molecules: #{DatabaseMol
    fecule.count - original_num_molecules}"
    puts "New Spectra: #{Spectrum.count - original_num_spectra}"
  end

  # desc "Import MolDB spectra"
  # task :import_moldb => :environment do
  #   # require 'faraday_middleware'

  #   original_num_molecules = DatabaseMolecule.count
  #   original_num_spectra = Spectrum.count

  #   CSV.foreach("data/import/moldb_test.csv", headers: true) do |row|
  #     ["DrugBank", "HMDB", "ChemDB"].each do |source|
  #       source_name = source == "ChemDB" ? "ContaminantDB" : source
  #       if row["#{source.downcase}_id"].present?
  #         if row["adduct_type"].present? && source == "HMDB"
  #           dm = DatabaseMolecule.find_by(source: source_name,
  #                                         derivatization_of_source_id: row["#{source.downcase}_id"],
  #                                         derivatization_type: row["adduct_type"])

  #           if dm.blank?
  #             puts "Missing derivative for #{row["#{source.downcase}_id"]}"
  #             last_deriv = DatabaseMolecule.where(source: source_name).where('derivatization_of_source_id IS NOT NULL').last
  #             id = last_deriv.source_id.split("_").last.to_i + 1
  #             # dm = DatabaseMolecule.create!(source: "#{source_name}_Deriv",
  #             #                               source_id: "#{source_name}_DERIV_#{id}",
  #             #                               name: row["name"],
  #             #                               smiles: row["smiles"],
  #             #                               inchi_key: Jchem.structure_to_inchikey(row["smiles"]),
  #             #                               inchi: Jchem.structure_to_inchi(row["smiles"]),
  #             #                               neutral_mass: row["mass"],
  #             #                               derivatization_of_source_id: row["#{source.downcase}_id"],
  #             #                               derivatization_of_id: DatabaseMolecule.find_by(source_id: row["#{source.downcase}_id"]).id,
  #             #                               derivatization_type: row["adduct_type"]
  #             #                               )
  #           end
  #         else
  #           dm = DatabaseMolecule.find_by(source: source_name,
  #                                         source_id: row["#{source.downcase}_id"])

  #           if dm.blank?
  #             puts "Missing molecule for #{row["#{source.downcase}_id"]}"
  #             # dm = DatabaseMolecule.create!(source: source_name,
  #             #                               source_id: row["#{source.downcase}_id"],
  #             #                               name: row["name"],
  #             #                               smiles: row["smiles"],
  #             #                               inchi_key: "InChIKey=" + row["inchikey"],
  #             #                               inchi: row["inchi"],
  #             #                               neutral_mass: row["mass"]
  #             #                               )
  #           end
  #         end
  #         # classify(dm)
  #       end
  #     end

  #     # conn = Faraday.new(url: "http://moldb.wishartlab.com") do |c|
  #     #   c.response :json, :content_type => /\bjson$/
  #     #   c.adapter Faraday.default_adapter
  #     # end

  #     # # Get C-MS
  #     # api_response = conn.get "/c_ms/#{row["c_ms"]}.json"

  #     # if api_response.status == 200
  #     #   r = api_response.body

  #     #   new_spectrum = Spectrum.find_or_create_by!(spectra_source: "CFMID",
  #     #                               inchi_key: dm.inchi_key,
  #     #                               method: "Computed",
  #     #                               spectra_type: "EI",
  #     #                               ion_mode: "positive",
  #     #                               collision_energy: "all",
  #     #                               adduct_type: nil,
  #     #                               instrument_type: nil,
  #     #                               derivatization_type: nil,
  #     #                               splash_key: nil
  #     #                             )

  #     #   api_response = conn.get "/c_ms/#{row["c_ms"]}/peaks.json"

  #     #   if api_response.status == 200
  #     #     r = api_response.body
  #     #     # Create the spectra text
  #     #     spectra_text = ["energy0"]
  #     #     spectra_text = spectra_text.concat(r.map { |i| "#{i[:mass_charge]}\t#{i[:intensity]}"})

  #     #     write_spectra_file(spectra_text, new_spectrum)
  #     #   else
  #     #     puts "ERROR getting peaks for #{row["structure_id"]}"
  #     #   end
  #     # else
  #     #   puts "ERROR getting spectra for #{row["structure_id"]}"
  #     # end

  #     # ["positive", "negative"].each do |ionization_mode|
  #     #   spectra = {}
  #     #   ["10", "20", "40"].each_with_index do |energy_level, i|
  #     #     col = "ms_ms_#{ionization_mode}_#{energy_level}"
  #     #     next if row[col].blank?

  #     #     # Get C-MS
  #     #     api_response = conn.get "/ms_ms/#{row[col]}/peaks.json"

  #     #     if api_response.status == 200
  #     #       r = api_response.body

  #     #       spectra_text = ["energy#{i}"]
  #     #       spectra_text = spectra_text.concat(r.map { |i| "#{i[:mass_charge]}\t#{i[:intensity]}"})
  #     #       spectra[energy_level] = spectra_text
  #     #     else
  #     #       puts "ERROR getting peaks for #{row["structure_id"]}"
  #     #     end
  #     #   end

  #     #   spectra["10"] = spectra["10"] || spectra["20"] || spectra["40"]
  #     #   spectra["20"] = spectra["20"] || spectra["10"] || spectra["40"]
  #     #   spectra["40"] = spectra["40"] || spectra["10"] || spectra["20"]

  #     #   if spectra.values.length != 3
  #     #     puts "No spectra found for #{row["structure_id"]}"
  #     #     next
  #     #   end

  #     #   new_spectrum = Spectrum.find_or_create_by!(spectra_source: "CFMID",
  #     #                               inchi_key: dm.inchi_key,
  #     #                               method: "Computed",
  #     #                               spectra_type: "ESI",
  #     #                               ion_mode: ionization_mode,
  #     #                               collision_energy: "all",
  #     #                               adduct_type: nil,
  #     #                               instrument_type: nil,
  #     #                               derivatization_type: nil,
  #     #                               splash_key: nil
  #     #                             )

  #     #   write_spectra_file(spectra.values.flatten, new_spectrum)
  #     # end
  #   end

  #   puts "New Molecules: #{DatabaseMolecule.count - original_num_molecules}"
  #   puts "New Spectra: #{Spectrum.count - original_num_spectra}"
  # end

  desc "Delete HMDB derivatives whose SMILES strings are probably truncated."
  task :delete_bad_derivatives => :environment do
    DatabaseMolecule.where("length(smiles) = 255 and source = 'HMDB_Deriv'").destroy_all
  end

  desc "Create DatabaseMolecule entries for MolDB compounds and/or derivatives, and import their spectra."
  task :import_moldb_compounds_and_spectra, [:spectra_method, :input_file, :log_file, :destroy_moldb_spectra] => :environment  do |t, args|
    require 'faraday_middleware'

    usage = "Usage:\n    bundle exec rake spectra:import_moldb_compounds_and_spectra[spectra_method,input_file,log_file,destroy_moldb_spectra]"
    usage += "\n  where"
    usage += "\n    spectra_method = If 'predicted', import predicted spectral data. If 'experimental', import"
    usage += "\n      experimental spectral data."
    usage += "\n    input_file = file produced by MolDB rake task cfmid:get_cfmid_spectra (master branch)"
    usage += "\n    log_file = file to which to write import errors"
    usage += "\n    destroy_moldb_spectra = if value is 'DESTROY', delete MolDB spectra before performing import (optional)"

    # Check arguments
    if args.spectra_method.nil?
      STDERR.puts "ERROR: spectra_method not specified. Exiting."
      STDERR.puts usage
      exit(1)
    else
      if not ['experimental', 'predicted'].include?(args.spectra_method.downcase)
        STDERR.puts "ERROR: spectra_method must be 'experimental' or 'predicted'. Exiting."
        STDERR.puts usage
        exit(1)
      end
    end
    if args.input_file.nil?
      STDERR.puts "ERROR: input_file not specified. Exiting."
      STDERR.puts usage
      exit(1)
    end
    if args.log_file.nil?
      STDERR.puts "ERROR: log_file not specified. Exiting."
      STDERR.puts usage
      exit(1)
    end

    # Remove existing MolDB spectra first
    if !args.destroy_moldb_spectra.nil? and args.destroy_moldb_spectra == 'DESTROY'
      Spectrum.where(spectra_source: "MolDB").destroy_all
    end

    errors = []
    original_num_molecules = DatabaseMolecule.count
    original_num_spectra = Spectrum.count

    CSV.foreach(args.input_file, headers: true) do |row|
      puts row
      ["DrugBank", "HMDB", "ChemDB"].each do |source|
        source_name = (source == "ChemDB" ? "ContaminantDB" : source)
        if row["#{source.downcase}_id"].present?
          if row["adduct_type"].present?
            next if source != "HMDB" # Only import HMDB derivatives
            dm = DatabaseMolecule.find_by(source: source_name,
                                          derivatization_of_source_id: row["#{source.downcase}_id"],
                                          derivatization_type: row["adduct_type"])

            if dm.blank?
              # puts "New deriv for #{row["#{source.downcase}_id"]}"
              last_deriv = DatabaseMolecule.where(source: "#{source_name}_Deriv").where('derivatization_of_source_id IS NOT NULL').last
              id = last_deriv.source_id.split("_").last.to_i + 1
              dm = DatabaseMolecule.create!(source: "#{source_name}_Deriv",
                                            source_id: "#{source_name}_DERIV_#{id}",
                                            name: row["name"],
                                            smiles: row["smiles"],
                                            inchi_key: Jchem.structure_to_inchikey(row["smiles"]),
                                            inchi: Jchem.structure_to_inchi(row["smiles"]),
                                            neutral_mass: row["mass"],
                                            derivatization_of_source_id: row["#{source.downcase}_id"],
                                            derivatization_of_id: DatabaseMolecule.find_by(source_id: row["#{source.downcase}_id"]).id,
                                            derivatization_type: row["adduct_type"]
                                            )
            end
          else
            dm = DatabaseMolecule.find_by(source: source_name,
                                          source_id: row["#{source.downcase}_id"])

            if dm.blank?
              # puts "New mol for #{row["#{source.downcase}_id"]}"
              dm = DatabaseMolecule.create!(source: source_name,
                                            source_id: row["#{source.downcase}_id"],
                                            name: row["name"],
                                            smiles: row["smiles"],
                                            inchi_key: "InChIKey=" + row["inchikey"],
                                            inchi: row["inchi"],
                                            neutral_mass: row["mass"]
                                            )
            end
          end
          classify(dm)
        end
      end

      conn = Faraday.new(url: "http://moldb.wishartlab.com") do |c|
        c.response :json, :content_type => /\bjson$/
        c.adapter Faraday.default_adapter
      end

      ["c_ms", "ms_ms"].each do |type|
        row[type].gsub(/\[|\]/, "").split(", ").map(&:strip).each do |spectra_id|
          api_response = conn.get "/#{type}/#{spectra_id}.json"

          if api_response.status == 200
            r = api_response.body

            if row["inchikey"].nil?
              inchi_key = Jchem.structure_to_inchikey(row["smiles"])
            else
              inchi_key = row["inchikey"]
            end

            new_spectrum = Spectrum.find_or_create_by!(spectra_source: "MolDB",
                                        inchi_key: "InChIKey=" + inchi_key,
                                        method: args.spectra_method.downcase == 'predicted' ? 'Computed' : 'Experimental',
                                        spectra_type: type == "c_ms" ? "EI" : "ESI",
                                        ion_mode: r["ionization_mode"].downcase,
                                        collision_energy: r["collision_energy_voltage"],
                                        adduct_type: nil,
                                        instrument_type: r["instrument_type"],
                                        derivatization_type: row["adduct_type"],
                                        splash_key: r["splash_key"]
                                      )

            api_response = conn.get "/#{type}/#{spectra_id}/peaks.json"

            if api_response.status == 200
              r = api_response.body
              # Create the spectra text
              spectra_text = ["energy0"]
              spectra_text = spectra_text.concat(r.map { |i| "#{i["mass_charge"]}\t#{i["intensity"]}" })

              write_spectra_file(spectra_text, new_spectrum)
            else
              puts "ERROR getting peaks for #{row["structure_id"]}"
              errors << row["structure_id"]
            end
          else
            puts "ERROR getting spectra for #{row["structure_id"]}"
            errors << row["structure_id"]
          end
        end
      end
    end

    puts "New Molecules: #{DatabaseMolecule.count - original_num_molecules}"
    puts "New Spectra: #{Spectrum.count - original_num_spectra}"

    if errors.present?
      File.open(args.log_file, "w+") do |f|
        f.puts(errors.compact.uniq)
      end
    end
  end

  desc "There are duplicates due to incorrect IDing, fix by creating our own IDS"
  task :fix_gnps_ids => :environment do
    existing = []
    DatabaseMolecule.where(source: "GNPS").each_with_index do |compound, i|
      if !existing.include?(compound.inchi_key)
        compound.update_attribute(:source_id, "GNPS#{(i + 1).to_s.rjust(5, "0")}")
        existing << compound.inchi_key
      else
        # This one already exists so remove it
        compound.destroy
      end
    end
  end

  desc "Save all to nillify blanks"
  task :save_all => :environment do
    Spectrum.find_each(batch_size: 100) do |spectrum|
      spectrum.save!
    end
    DatabaseMolecule.find_each(batch_size: 100) do |dm|
      dm.save!
    end
  end

  task :set_neutral_adducts => :environment do
    Spectrum.where(method: "Computed").update_all(adduct_type: "Neutral")
  end

  desc "Delete predicted spectra of any type for compounds corresponding to given list of InChIKeys"
  task :delete_predicted_spectra => :environment do
    CSV.open("data/inchikeys_for_which_to_remove_spectra_12Jul2019.tsv", "r", :col_sep => "\t").each do |row|
      inchi_key = row[0]

      spectra = Spectrum.where(inchi_key: inchi_key, method: 'Computed')
      next if spectra.nil?
      spectra.each do |spectrum|

        ['peaks', 'annotated'].each do |type_dir|
          adduct_str = ''
          if !(spectrum.adduct_type.nil? || spectrum.adduct_type == 'Neutral')
            adduct_str = '_' + spectrum.adduct_type
          end

          if spectrum.spectra_type == 'ESI'
            spectrum_file = File.join(Spectrum::PRECOMPUTED_DATA_DIR, spectrum.spectra_type.downcase, spectrum.ion_mode, type_dir, inchi_key + adduct_str + ".txt")
          elsif spectrum.spectra_type == 'EI'
            spectrum_file = File.join(Spectrum::PRECOMPUTED_DATA_DIR, spectrum.spectra_type.downcase, type_dir, inchi_key + adduct_str + ".txt")
          end

          if File.exist?(spectrum_file)
            puts 'Deleting ' + spectrum_file
            File.delete(spectrum_file)
          end
        end

        puts 'Deleting spectra database entry for ' + inchi_key
        spectrum.destroy!
      end
    end
  end

  desc "Re-label spectra source and/or delete predicted spectra based on MSRB-Fragmenter status codes"
  # Process status codes produced by MSRB-Fragmenter, which tells us which spectra
  # prediction program is suitable for predicting particular molecules. This rake task
  # is intended to
  # (1) Re-label existing spectra in CFM-ID's database that have spectra_source 'CFMID',
  #     which is ambiguous, as 'CFM-ID 2.0' where appropriate, or delete the predicted
  #     spectra if they should not be in the database.
  # (2) Record some MSRB-Fragmenter status codes in the database for future reference.
  #
  # MSRB-Fragmenter 1.0.8 status report codes:
  #
  #  "STATUS REPORT = 1": Each specified adduct is covered for the chemical class of the
  #     compound, and corresponding spectra will be predicted.
  #  "STATUS REPORT = 2": Some adducts are not covered for the chemical class of the
  #     query compound, and spectra will be computed for the remaining adducts. That
  #     applies if you provide more than one adduct.
  #  "STATUS REPORT = 3": The only adduct specified by the user is not covered for the
  #     chemical class of the query compound, and spectra will be computed all adducts
  #     applicable for the query compound. That applies if you provide only one adduct.
  #  "STATUS REPORT = 4": "Invalid chemical class. The query compound belongs to a lipid
  #     class not covered in the current version of the fragmenter. Here, an error is
  #     returned and CFM-ID's combinatorial fragmentation is NOT used (because it does
  #     not perform well for those classes, and takes too long to the point of often
  #     timing out). No spectra are computed.
  #  "STATUS REPORT = 5": The query compound does not belong to any of the classes
  #     covered in the current version of the fragmenter; nor does it belong to the
  #     classes of glycerolipids, glycerophpspholipids, sphingolipids, ether lipids.
  #     Continue with CFM-ID's combinatorial fragmentation.
  #
  # The input files to this rake task is not expected to have status codes 1, 2, or 3, so
  # we do not handle them here. The input file may contain status codes -1 or 0, which are
  # not produced by MSRB-Fragmenter but instead by a Python script used to run it.
  #  0 = MSRB-Fragmenter predicted spectra without showing a status code.
  #  -1 = MSRB-Fragmenter exited with system error code 1
  #
  # The input file required is produced by David Arndt's Python script cfmidplus_batch.py,
  # run on Compute Canada.
  #
  # Input is in tab-separated format like this:
  #   inchi_key	smiles	status_code
  #   InChIKey=UXGXCGPWGSUMNI-BVHTXILBSA-N	n/a	-1
  #   InChIKey=WVBVUWWRPSWGDS-JWVBYZEHSA-N	[H][C@](CO)(COC(=O)CCCCCCCCCCCCC)OC(=O)CCCCCCCC=C/CC=C/CCCCC	0
  #   InChIKey=SSSVBZIEDMXADP-SZIQPNEASA-N	[H]C(CCCCCCCCCCCCCC)=C([H])O[C@]([H])(COC(=O)CCCCCCCCCCCCC)COP(O)(=O)OCCN	4
  #   InChIKey=DBPWSSGDRRHUNT-AYUYWISESA-N	CC(=O)[C@@]1(O)CC[C@H]2[C@@H]3CCC4=CC(=O)CC[C@]4(C)[C@H]3CC[C@@]21C	5
  #
  # Though input to MSRB-Fragmenter is by SMILES, it was manually verified that it gave
  # the same status code for each SMILES associated with the same InChIKey, except in the
  # case of 6 DatabaseMolecule entries with smiles = 'n/a' (program exited with system
  # error code 1 = -1 in input file).
  #
  # This rake task was run Jan 30, 2020.
  task :process_msrb_fragmenter_status_codes, [:infile, :msrb_frag_version] => :environment  do |t, args|
    usage = "Usage:\n    bundle exec rake spectra:process_msrb_fragmenter_status_codes[infile,msrb_frag_version]"
    usage += "\n  where"
    usage += "\n    infile = relative path to input file"
    usage += "\n    msrb_frag_version = version of MSRB-Fragmenter, e.g. \"MSRB-Fragmenter 1.0.8\""

    if args.infile.nil?
      STDERR.puts "ERROR: infile not specified. Exiting."
      STDERR.puts usage
      exit(1)
    end
    if args.msrb_frag_version.nil?
      STDERR.puts "ERROR: msrb_frag_version not specified. Exiting."
      STDERR.puts usage
      exit(1)
    end

    count = 0
    CSV.open(args.infile, "r", :col_sep => "\t", headers: true).each do |row|
      inchi_key = row[0]
      smiles = row[1]
      status_code = row[2].to_i

      if status_code == -1
        # MSRB-Fragmenter exited and OS reported error code. Skip this (do not handle this case in this rake task).
        next
      end

      if status_code == 0
        # Spectrum was predicted by MSRB-Fragmenter. Remove predictions from 'CFMID' source, assuming they are from v2.0.
        Spectrum.where(inchi_key: inchi_key, method: 'Computed', spectra_type: 'ESI', spectra_source: 'CFMID').each do |spectrum|
          puts "Deleting spectrum with spectra_source = 'CFMID' with id " + spectrum.id.to_s
          spectrum.destroy!
        end
      elsif status_code == 4
        # Mark the DatabaseMolecules column with status code. Delete predicted spectra.
        DatabaseMolecule.where(inchi_key: inchi_key, smiles: smiles).each do |dm|
          dm.msrb_frag_status_code = status_code
          dm.msrb_frag_ver = args.msrb_frag_version
          dm.save!
        end
        Spectrum.where(inchi_key: inchi_key, method: 'Computed', spectra_type: 'ESI').each do |spectrum|
          if !spectrum.spectra_source.start_with?('CFM-ID 4')
            puts "Deleting spectrum too challenging to predict, with id " + spectrum.id.to_s
            spectrum.destroy!
          end
        end
      elsif status_code == 5
        # Record status code for future reference.
        DatabaseMolecule.where(inchi_key: inchi_key, smiles: smiles).each do |dm|
          dm.msrb_frag_status_code = status_code
          dm.msrb_frag_ver = args.msrb_frag_version
          dm.save!
        end

        # Re-label predicted spectra from 'CFMID' to 'CFM-ID 2.0'.
        Spectrum.where(inchi_key: inchi_key, method: 'Computed', spectra_type: 'ESI', spectra_source: 'CFMID').each do |spectrum|
          spectrum.spectra_source = 'CFM-ID 2.0'
          spectrum.save!
        end
      end

      if count % 100 == 0
        puts 'Processed ' + count.to_s
      end
      count = count + 1
    end
    puts 'Processed total of ' + count.to_s
  end

  def create_existing_spectra(database, molecule, spectra_id, derivatization_type = nil)
    positive_file = File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "positive", database.downcase, spectra_id + ".txt")
    negative_file = File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "negative", database.downcase, spectra_id + ".txt")
    ei_file = File.join(Spectrum::PRECOMPUTED_DATA_DIR, "ei", database.downcase, spectra_id + ".txt")
    positive_annotated = File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "positive", database.downcase + "_annotated", spectra_id + ".txt")
    negative_annotated = File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "negative", database.downcase + "_annotated", spectra_id + ".txt")
    ei_annotated = File.join(Spectrum::PRECOMPUTED_DATA_DIR, "ei", database.downcase + "_annotated", spectra_id + ".txt")
    if File.exists?(positive_file)
      Spectrum.find_or_create_by!(spectra_source: "CFMID",
                                  inchi_key: molecule.inchi_key,
                                  method: "Computed",
                                  spectra_type: "ESI",
                                  ion_mode: "positive",
                                  derivatization_type: derivatization_type,
                                  collision_energy: "all"
                                )
      FileUtils.copy(positive_file, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "positive", "peaks", molecule.inchi_key + ".txt"))
      if File.exists?(positive_annotated)
        FileUtils.copy(positive_annotated, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "positive", "annotated", molecule.inchi_key + ".txt"))
      end
    end
    if File.exists?(negative_file)
      Spectrum.find_or_create_by!(spectra_source: "CFMID",
                                  inchi_key: molecule.inchi_key,
                                  method: "Computed",
                                  spectra_type: "ESI",
                                  ion_mode: "negative",
                                  derivatization_type: derivatization_type,
                                  collision_energy: "all"
                                )
      FileUtils.copy(negative_file, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "negative", "peaks", molecule.inchi_key + ".txt"))
      if File.exists?(negative_annotated)
        FileUtils.copy(negative_annotated, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "esi", "negative", "annotated", molecule.inchi_key + ".txt"))
      end
    end
    if File.exists?(ei_file)
      Spectrum.find_or_create_by!(spectra_source: "CFMID",
                                  inchi_key: molecule.inchi_key,
                                  method: "Computed",
                                  spectra_type: "EI",
                                  ion_mode: "positive",
                                  derivatization_type: derivatization_type,
                                  collision_energy: "all"
                                )
      FileUtils.copy(ei_file, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "ei", "peaks", molecule.inchi_key + ".txt"))
      if File.exists?(ei_annotated)
        FileUtils.copy(ei_annotated, File.join(Spectrum::PRECOMPUTED_DATA_DIR, "ei", "annotated", molecule.inchi_key + ".txt"))
      end
    end
  end

  def fill_energy_levels(spectra)
    spectra.each do |ionization, levels|
      levels.each do |level, spectrum|
        if spectrum.blank?
          # If energy level is missing, fill with first existing
          existing = levels.select { |e, s| !s.blank? }.values.first
          spectra[ionization][level] = existing ? existing : []
        else
          spectra[ionization][level] = spectrum
        end
      end
    end
    spectra
  end

  # Add spectra file using paperclip
  def write_spectra_file(spectra_text, spectra)
    output_file = "public/#{spectra.id}_peak-list.txt"
    File.open(output_file, "w+") do |f|
      f.puts(spectra_text)
    end
    spectra.peak_list = File.open(output_file)
    spectra.save!
    # Remove temp files
    if File.exists?(output_file)
      File.delete(output_file)
    end
  end

  def write_ei_spectra_file(dir_name, compound, spectra_text)
    FileUtils::mkdir_p(dir_name) unless File.exists?(dir_name)
    if spectra_text.length > 1
      # Write the spectra to file
      file_name = dir_name + "/" + compound["source_id"].gsub("-", "_") + ".txt"
      File.open(file_name, "w+") do |f|
        f.puts(spectra_text)
      end
    end
  end

  def write_esi_spectra_file(dir_name, compound, spectra_text)
    FileUtils::mkdir_p(dir_name) unless File.exists?(dir_name)
    if spectra_text.length > 3
      # Write the spectra to file
      file_name = dir_name + "/" + compound["source_id"].gsub("-", "_") + ".txt"
      File.open(file_name, "w+") do |f|
        f.puts(spectra_text)
      end
    end
  end

  # Get an attribute in the compound
  def compound_inchi(object)
    inchi_key = object["compound"][0]["inchiKey"]
    if inchi_key.blank?
      inchi_key = compound_metadata(object, ["inchiKey"])
    end
    return inchi_key
  end

  # Find an attribute in the compound metaData
  def compound_metadata(object, attributes)
    attributes.each do |attribute|
      found = object["compound"][0]["metaData"].detect {|i| i["name"].downcase == attribute.downcase}
      if found.present?
        return found["value"]
      end
    end

    return nil
  end

  # Find an attribute in the spectra metaData
  def metadata(object, attributes)
    attributes.each do |attribute|
      found = object["metaData"].detect {|i| i["name"].downcase == attribute.downcase}
      if found.present?
        return found["value"]
      end
    end

    return nil
  end

  def check_structures(molecule)
    if molecule.inchi.present?
      if molecule.smiles.blank?
        begin
          molecule.update_attribute(:smiles, Jchem.structure_to_smiles(molecule.inchi))
        rescue
          puts "Jchem error: " + molecule.id.to_s
        end
      end
      if molecule.inchi_key.blank?
        begin
          molecule.update_attribute(:inchi_key, Jchem.structure_to_inchikey(molecule.inchi))
        rescue
          puts "Jchem error: " + molecule.id.to_s
        end
      end
    elsif molecule.smiles.present?
      if molecule.inchi.blank?
        begin
          molecule.update_attribute(:inchi, Jchem.structure_to_inchi(molecule.smiles))
        rescue
          puts "Jchem error: " + molecule.id.to_s
        end
      end
      if molecule.inchi_key.blank?
        begin
          molecule.update_attribute(:inchi_key, Jchem.structure_to_inchikey(molecule.smiles))
        rescue
          puts "Jchem error: " + molecule.id.to_s
        end
      end
    end

    if molecule.inchi.present? && !molecule.inchi.starts_with?("InChI=")
      molecule.update_attribute(:inchi, molecule.inchi.gsub(/^([^=]+=)?/, "InChI="))
    end

    if molecule.inchi_key.present? && !molecule.inchi_key.starts_with?("InChIKey=")
      molecule.update_attribute(:inchi_key, molecule.inchi_key.gsub(/^([^=]+=)?/, "InChIKey="))
    end
  end

  def nillify(val)
    return val.blank? ? nil : val
  end

end
