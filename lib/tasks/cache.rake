namespace :cache do
  namespace :clear do

    desc "Clear all cache (use with extreme caution)"
    task all: [:environment] do
      Rails.cache.clear
      puts "Cache Cleared"
    end

    desc "Clear intro on HMDB homepage"
    task intro: [:environment] do
      Rails.cache.delete_matched(/intro/)
    end

    desc "Clear stats on stats page"
    task stats: [:environment] do
      Rails.cache.delete_matched(/statistics/)
    end

    desc "Clear metabolite index cache"
    task metabolite_index: [:environment] do
      Rails.cache.delete_matched(/biofluid-locations-list/)
    end

    desc "Clear metabolite show cache"
    task metabolite_show: [:environment] do
      Rails.cache.delete_matched(/metabolites/)
      Rails.cache.delete_matched(/with-includes/)
    end

    desc "Clear protein show cache (HTML and XML)"
    task protein_show: [:environment] do
      Rails.cache.delete_matched(/views\/proteins\/\d+/)
    end

    desc "Clear disease index cache"
    task disease_index: [:environment] do
      Rails.cache.delete_matched(/views\/diseases/)
    end

    namespace :moldbi do
      desc "Clear all MolDBi cache"
      task all: [:environment] do
        Rails.cache.delete_matched(/moldbi/)
      end

      desc "Clear structure resource cache (includes files)"
      task structure_resources: [:environment] do
        Rails.cache.delete_matched(/moldbi\/structure-resources\/NP.+/)
      end

      desc "Clear curation resource cache"
      task curation_resources: [:environment] do
        Rails.cache.delete_matched(/moldbi\/curation-resources/)
      end

      desc "Clear all files (mol, pdb, images, etc)"
      task files: [:environment] do
        Rails.cache.delete_matched(/moldbi\/structure-resources\/files/)
      end
    end

  end

  namespace :refresh do
    desc "RUN AFTER metabolite_show - ping pages to refresh the cache"
    task metabolite_shows: [:environment] do
      start = Time.now
      Rake::Task["cache:clear:metabolite_show"].invoke() #clear metabolite show cache
      Rake::Task["cache:clear:moldbi:all"].invoke()
      finish = Time.now
      puts "Time to delete cache's is: #{(finish-start).round(3)}"
      batch_n = 0
      Metabolite.find_in_batches(batch_size: 256) do |batch| #grab 256 metabolites
        batch_n += 1
        group_n = 0
        batch.each_slice(4) do |group| #split them into groups of 4
          group_n += 1      
          puts ("Processing batch #{batch_n}, group #{group_n}/64")
          start = Time.now
          child_pid_0 = fork {Rake::Task["cache:refresh:page_by_id"].execute({hmdb_id: group[0].hmdb_id})} #fork each rake task call with size 8 array of hmdb_ids
          child_pid_1 = fork {Rake::Task["cache:refresh:page_by_id"].execute({hmdb_id: group[1].hmdb_id})}
          child_pid_2 = fork {Rake::Task["cache:refresh:page_by_id"].execute({hmdb_id: group[2].hmdb_id})}
          child_pid_3 = fork {Rake::Task["cache:refresh:page_by_id"].execute({hmdb_id: group[3].hmdb_id})}
          begin
            Timeout.timeout(10) do
              Process.waitall
            end
          rescue Timeout::Error
            puts "Timed out killing process"
            begin
              Process.kill(0, child_pid_0)
            rescue Errno::ESRCH
            end
            begin
              Process.kill(1, child_pid_1)
            rescue Errno::ESRCH
            end
            begin
              Process.kill(2, child_pid_2)
            rescue Errno::ESRCH
            end
            begin
              Process.kill(3, child_pid_3)
            rescue Errno::ESRCH
            end
          end
          puts ""
          GC.start
          finish = Time.now
          puts "Time for 4 Cache Refreshes is: #{(finish-start).round(3)}"
          #rinse repear iterating through entire metabolite table
        end
        ActiveRecord::Base.connection.verify!
      end
    end

    desc "Refresg metabolite by hmdb_id"
    task :page_by_id, :hmdb_id do |t, args|
      hmdb_id = args[:hmdb_id]
      metab = Metabolite.find_by(hmdb_id: hmdb_id)
      print"#{hmdb_id}  "
      if !metab.export_to_hmdb
        metab.save!
        url ="http://www.hmdb.ca/metabolites/#{hmdb_id}"
        link = URI.parse(url)
        request = Net::HTTP::Get.new(link.path)
        begin
          response = Net::HTTP.start(link.host, link.port) {|http|
            http.read_timeout = 5 #Default is 60 seconds
            http.request(request)
          }
        rescue Net::ReadTimeout => e  
          puts e.message + " #{hmdb_id}"
        end
      end
    end
  end

end

