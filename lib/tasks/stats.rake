namespace :stats do
  task :v3 => :environment do

    # With derivatives
    unique_molecules = DatabaseMolecule.select(:inchi_key).distinct
    excluded_classes = File.read("data/training/excluded_classes.txt").split("\n")

    reference_molecules = DatabaseMolecule.
                        select('DISTINCT(database_molecules.inchi_key)').
                        where(derivatization_of_id: nil).
                        joins("INNER JOIN reference_counts ON reference_counts.inchi_key = database_molecules.inchi_key")

    derivative_reference_molecules = DatabaseMolecule.
                                    joins(:derivatization_of).
                                    select('DISTINCT(database_molecules.inchi_key)', 'derivatization_ofs_database_molecules.inchi_key').
                                    joins("INNER JOIN reference_counts ON reference_counts.inchi_key = derivatization_ofs_database_molecules.inchi_key")
    total_referenced_molecules = reference_molecules.count + derivative_reference_molecules.count
    total_reference_count = reference_molecules.sum('reference_counts.count') + derivative_reference_molecules.sum('reference_counts.count')

    with_classification = unique_molecules.joins(:classification)

    original_inchikeys = (HmdbMolecule.uniq.pluck(:inchi_key).concat(KeggMolecule.uniq.pluck(:inchi_key))).uniq
    all_inchikeys = DatabaseMolecule.uniq.pluck(:inchi_key)

    # Stats from V2
    hmdb_derivative_count = 75801
    original_inchikeys_count = 48865 # Number of unique InChIKeys within the set of HMDB and KEGG molecules

    # Stats to output
    puts "ALL"
    puts "Unique Compounds: " + unique_molecules.count.to_s
    puts "Exp EI: " + Spectrum.where(method: "Experimental", spectra_type: "EI").count.to_s
    puts "Exp ESI: " + Spectrum.where(method: "Experimental", spectra_type: "ESI").count.to_s
    puts "Compounds with Exp: " + unique_molecules.joins(:spectra).where(spectra: {method: "Experimental"}).count.to_s
    puts "Compounds with Exp EI: " + unique_molecules.joins(:spectra).where(spectra: {method: "Experimental", spectra_type: "EI"}).count.to_s
    puts "Compounds with Exp ESI: " + unique_molecules.joins(:spectra).where(spectra: {method: "Experimental", spectra_type: "ESI"}).count.to_s
    puts "Pred EI: " + Spectrum.where(method: "Computed", spectra_type: "EI").count.to_s
    puts "Pred ESI: " + Spectrum.where(method: "Computed", spectra_type: "ESI").count.to_s
    puts "Compounds with Pred: " + unique_molecules.joins(:spectra).where(spectra: {method: "Computed"}).count.to_s
    puts "Compounds with Pred EI: " + unique_molecules.joins(:spectra).where(spectra: {method: "Computed", spectra_type: "EI"}).count.to_s
    puts "Compounds with Pred ESI: " + unique_molecules.joins(:spectra).where(spectra: {method: "Computed", spectra_type: "ESI"}).count.to_s
    puts "Compounds with Citations: " + (total_referenced_molecules).to_s
    puts "Avg Citations per Compound: " + (total_reference_count / total_referenced_molecules.to_f).to_s
    puts "Compounds with Classification: " + with_classification.count.to_s
    puts "Avg Ancestors per Compound: " + (with_classification.map { |dm| dm.classification.ancestors.count }.sum / with_classification.count.to_f).to_s
    puts "Uniq Compounds in V2: " + (original_inchikeys_count + hmdb_derivative_count).to_s
  end

  task :v2 => :environment do

    # With derivatives
    unique_molecules = DatabaseMolecule.group('database_molecules.inchi_key')
    excluded_classes = File.read("data/training/excluded_classes.txt").split("\n")

    ref_counts = []
    ref_count_1 = 0
    unique_molecules.find_each(batch_size: 100) do |dm|
      if dm.reference_count.present?
        ref_counts << dm.reference_count.count
        if dm.reference_count.count > 0
          ref_count_1 += 1
        end
      end
    end

    with_classification = unique_molecules.joins(:classification)

    original_inchikeys = (HmdbMolecule.uniq.pluck(:inchi_key).concat(KeggMolecule.uniq.pluck(:inchi_key))).uniq
    all_inchikeys = DatabaseMolecule.uniq.pluck(:inchi_key)

    # Stats from V2
    hmdb_derivative_count = 75801
    original_inchikeys_count = 48865 # Number of unique InChIKeys within the set of HMDB and KEGG molecules

    # Stats to output
    puts "ALL"
    puts "Unique Compounds: " + unique_molecules.length.to_s
    puts "Exp EI: " + Spectrum.where(method: "Experimental", spectra_type: "EI").count.to_s
    puts "Exp ESI: " + Spectrum.where(method: "Experimental", spectra_type: "ESI").count.to_s
    puts "Compounds with Exp: " + unique_molecules.joins(:spectra).where(spectra: {method: "Experimental"}).length.to_s
    puts "Compounds with Exp EI: " + unique_molecules.joins(:spectra).where(spectra: {method: "Experimental", spectra_type: "EI"}).length.to_s
    puts "Compounds with Exp ESI: " + unique_molecules.joins(:spectra).where(spectra: {method: "Experimental", spectra_type: "ESI"}).length.to_s
    puts "Pred EI: " + Spectrum.where(method: "Computed", spectra_type: "EI").count.to_s
    puts "Pred ESI: " + Spectrum.where(method: "Computed", spectra_type: "ESI").count.to_s
    puts "Compounds with Pred: " + unique_molecules.joins(:spectra).where(spectra: {method: "Computed"}).length.to_s
    puts "Compounds with Pred EI: " + unique_molecules.joins(:spectra).where(spectra: {method: "Computed", spectra_type: "EI"}).length.to_s
    puts "Compounds with Pred ESI: " + unique_molecules.joins(:spectra).where(spectra: {method: "Computed", spectra_type: "ESI"}).length.to_s
    puts "Compounds with Citations: " + ref_count_1.to_s
    puts "Avg Citations per Compound: " + (ref_counts.sum / ref_counts.length.to_f).to_s
    puts "Compounds with Classification: " + with_classification.length.to_s
    puts "Avg Ancestors per Compound: " + (with_classification.map { |dm| dm.classification.ancestors.length }.sum / with_classification.length.to_f).to_s
    puts "Uniq Compounds in V2: " + (original_inchikeys_count + hmdb_derivative_count).to_s

    puts "\n"

    non_lipids = unique_molecules.select { |m| m.classification.blank? || (m.classification.ancestors & excluded_classes).blank? }

    ref_counts = []
    ref_count_1 = 0
    non_lipids.each do |dm|
      if dm.reference_count.present?
        ref_counts << dm.reference_count.count
        if dm.reference_count.count > 0
          ref_count_1 += 1
        end
      end
    end

    with_classification = non_lipids.select { |m| m.classification.present? }

    puts "NON-LIPIDS ONLY"
    puts "Unique Compounds: " + non_lipids.length.to_s
    puts "Exp EI: " + Spectrum.where(method: "Experimental", spectra_type: "EI").count.to_s
    puts "Exp ESI: " + Spectrum.where(method: "Experimental", spectra_type: "ESI").count.to_s
    puts "Compounds with Exp: " + non_lipids.select { |m| m.spectra.where(spectra: {method: "Experimental"}).present? }.length.to_s
    puts "Compounds with Exp EI: " + non_lipids.select { |m| m.spectra.where(spectra: {method: "Experimental", spectra_type: "EI"}).present? }.length.to_s
    puts "Compounds with Exp ESI: " + non_lipids.select { |m| m.spectra.where(spectra: {method: "Experimental", spectra_type: "ESI"}).present? }.length.to_s
    puts "Pred EI: " + Spectrum.where(method: "Computed", spectra_type: "EI").count.to_s
    puts "Pred ESI: " + Spectrum.where(method: "Computed", spectra_type: "ESI").count.to_s
    puts "Compounds with Pred: " + non_lipids.select { |m| m.spectra.where(spectra: {method: "Computed"}).present? }.length.to_s
    puts "Compounds with Pred EI: " + non_lipids.select { |m| m.spectra.where(spectra: {method: "Computed", spectra_type: "EI"}).present? }.length.to_s
    puts "Compounds with Pred ESI: " + non_lipids.select { |m| m.spectra.where(spectra: {method: "Computed", spectra_type: "ESI"}).present? }.length.to_s
    puts "Compounds with Citations: " + ref_count_1.to_s
    puts "Avg Citations per Compound: " + (ref_counts.sum / ref_counts.length.to_f).to_s
    puts "Compounds with Classification: " + with_classification.length.to_s
    puts "Avg Ancestors per Compound: " + (with_classification.map { |dm| dm.classification.ancestors.length }.sum / with_classification.length.to_f).to_s
    puts "Uniq Compounds in V2: " + (original_inchikeys.count + HmdbDerivative.count).to_s
    puts "Uniq Compounds added in V3: " + (all_inchikeys - original_inchikeys).count.to_s

    # puts "\n"

    # unique_no_derivs = DatabaseMolecule.group('database_molecules.inchi_key').where("source != ?", "HMDB_Deriv")

    # ref_counts = []
    # ref_count_1 = 0
    # unique_no_derivs.each do |dm|
    #   if dm.reference_count.present?
    #     ref_counts << dm.reference_count.count
    #     if dm.reference_count.count > 0
    #       ref_count_1 += 1
    #     end
    #   end
    # end

    # with_classification = unique_no_derivs.joins(:classification)

    # puts "WITHOUT DERIVATIVES"
    # puts "Unique Compounds: " + unique_no_derivs.length.to_s
    # puts "Exp EI: " + Spectrum.where(method: "Experimental", spectra_type: "EI").count.to_s
    # puts "Exp ESI: " + Spectrum.where(method: "Experimental", spectra_type: "ESI").count.to_s
    # puts "Compounds with Exp: " + unique_no_derivs.joins(:spectra).where(spectra: {method: "Experimental"}).length.to_s
    # puts "Compounds with Exp EI: " + unique_no_derivs.joins(:spectra).where(spectra: {method: "Experimental", spectra_type: "EI"}).length.to_s
    # puts "Compounds with Exp ESI: " + unique_no_derivs.joins(:spectra).where(spectra: {method: "Experimental", spectra_type: "ESI"}).length.to_s
    # puts "Pred EI: " + Spectrum.where(method: "Computed", spectra_type: "EI").count.to_s
    # puts "Pred ESI: " + Spectrum.where(method: "Computed", spectra_type: "ESI").count.to_s
    # puts "Compounds with Pred: " + unique_no_derivs.joins(:spectra).where(spectra: {method: "Computed"}).length.to_s
    # puts "Compounds with Pred EI: " + unique_no_derivs.joins(:spectra).where(spectra: {method: "Computed", spectra_type: "EI"}).length.to_s
    # puts "Compounds with Pred ESI: " + unique_no_derivs.joins(:spectra).where(spectra: {method: "Computed", spectra_type: "ESI"}).length.to_s
    # puts "Compounds with Citations: " + ref_count_1.to_s
    # puts "Avg Citations per Compound: " + (ref_counts.sum / ref_counts.length.to_f).to_s
    # puts "Compounds with Classification: " + with_classification.length.to_s
    # puts "Avg Ancestors per Compound: " + (with_classification.map { |dm| dm.classification.ancestors.length }.sum / with_classification.length.to_f).to_s
    # puts "Uniq Compounds in V2: " + original_inchikeys.count.to_s
    # puts "Uniq Compounds added in V3: " + (added_inchikeys - original_inchikeys).count.to_s
  end

  # Figure out what percentage of each database has ESI/EI spectra
  task :counts => :environment do
    puts "ESI"
    databases = DatabaseMolecule.joins(:spectra).
                  where(spectra: {spectra_type: "ESI" }).
                  where("lower(spectra.collision_energy) REGEXP ?", '^[1,2,4]0(\.0)?[[:blank:]]?(e?v|cid)?$|^all$').
                  map(&:source).uniq

    databases.each do |database|
      count = databases = DatabaseMolecule.joins(:spectra).
                  where(source: database).
                  where(spectra: {spectra_type: "ESI" }).
                  where("lower(spectra.collision_energy) REGEXP ?", '^[1,2,4]0(\.0)?[[:blank:]]?(e?v|cid)?$|^all$').
                  uniq.count
      total = DatabaseMolecule.where(source: database).count
      puts "#{database}: #{count}/#{total} (#{count / total.to_f * 100})"
    end

    puts "\n"
    puts "EI"
    databases = DatabaseMolecule.joins(:spectra).
                  where(spectra: {spectra_type: "EI" }).
                  where("lower(spectra.collision_energy) REGEXP ?", '^[1,2,4]0(\.0)?[[:blank:]]?(e?v|cid)?$|^all$').
                  map(&:source).uniq

    databases.each do |database|
      count = databases = DatabaseMolecule.joins(:spectra).
                  where(source: database).
                  where(spectra: {spectra_type: "EI" }).
                  where("lower(spectra.collision_energy) REGEXP ?", '^\-?70(\.0)?[[:blank:]]?(e?v)?$|^all$').
                  uniq.count
      total = DatabaseMolecule.where(source: database).count
      puts "#{database}: #{count}/#{total} (#{count / total.to_f * 100})"
    end
  end

  task :export_molecules => :environment do
    File.open("database_molecules_Aug7.tsv", "wb") do |file|
      DatabaseMolecule.all.each do |molecule|
        file.puts([molecule.name, molecule.source, molecule.inchi_key, molecule.inchi, molecule.smiles, molecule.reference_count.try(:count), molecule.classification.try(:direct_parent), molecule.classification.try(:ancestors).try(:join, "; ")].join("\t"))
      end
    end
  end

  task :need_classifications => :environment do
    File.open("need_classifications5-ids.tsv", "wb") do |file|
      DatabaseMolecule.all.each do |molecule|
        if molecule.classification.nil?
          file.puts([molecule.name, molecule.source_id, molecule.source, molecule.inchi_key, molecule.inchi, molecule.smiles].join("\t"))
        end
      end
    end
  end
end