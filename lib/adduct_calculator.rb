class AdductCalculator
  NEUTRAL = "Neutral"

  MASS_OF_ELECTRON = 0.00054858026

  # Given the positive adduct mass calculate the neutral mass of the
  # parent molecule
  POSITIVE_ION_MASS_TO_NEUTRAL_MASS = {
    "[M]+"               => lambda { |mass| mass - MASS_OF_ELECTRON},
    "[M+H]+"             => lambda { |mass| mass - 1.007276    },
    "[M+K]+"             => lambda { |mass| mass - 38.963158   },
    "[M+Na]+"            => lambda { |mass| mass - 22.989218   },
    "[M+NH4]+"           => lambda { |mass| mass - 18.033823   },
    "[M+3H]3+"           => lambda { |mass| 3*(mass - 1.007276)  },
    "[M+2H+Na]3+"        => lambda { |mass| 3*(mass - 8.334590)  },
    "[M+H+2Na]3+"        => lambda { |mass| 3*(mass - 15.7661904)},
    "[M+3Na]3+"          => lambda { |mass| 3*(mass - 22.989218) },
    "[M+2H]2+"           => lambda { |mass| 2*(mass - 1.007276)  },
    "[M+H+NH4]2+"        => lambda { |mass| 2*(mass - 9.520550)  },
    "[M+H+Na]2+"         => lambda { |mass| 2*(mass - 11.998247) },
    "[M+H+K]2+"          => lambda { |mass| 2*(mass - 19.985217) },
    "[M+CH3CN+2H]3+"       => lambda { |mass| 2*(mass - 21.520550) },
    "[M+2Na]2+"          => lambda { |mass| 2*(mass - 22.989218) },
    "[M+2CH3CN+2H]2+"      => lambda { |mass| 2*(mass - 42.033823) },
    "[M+3CH3CN+2H]2+"      => lambda { |mass| 2*(mass - 62.547097) },
    "[M+CH3OH+H]+"       => lambda { |mass| mass - 33.033489   },
    "[M+CH3CN+H]+"         => lambda { |mass| mass - 42.033823   },
    "[M+2Na+H]3+"        => lambda { |mass| mass + 44.971160   },
    "[M+IsoProp+H]+"     => lambda { |mass| mass - 61.06534    },
    "[M+CH3CN+Na]+"        => lambda { |mass| mass - 64.015765   },
    "[M+2K+H]3+"         => lambda { |mass| mass - 76.919040   },
    "[M+DMSO+H]+"        => lambda { |mass| mass - 79.02122    },
    "[M+2CH3CN+H]+"        => lambda { |mass| mass - 83.060370   },
    "[M+2Na-H]+"         => lambda { |mass| mass - 44.971160   },
    "[M+IsoProp+Na+H]2+" => lambda { |mass| mass - 84.05511    },
    "[2M+H]+"            => lambda { |mass| (mass - 1.007276)/2  },
    "[2M+NH4]+"          => lambda { |mass| (mass - 18.033823)/2 },
    "[2M+Na]+"           => lambda { |mass| (mass - 22.989218)/2 },
    "[2M+3H2O+2H]+"      => lambda { |mass| (mass - 28.02312)/2  },
    "[2M+K]+"            => lambda { |mass| (mass - 38.963158)/2 },
    "[2M+CH3CN+H]+"        => lambda { |mass| (mass - 42.033823)/2 },
    "[2M+CH3CN+Na]+"       => lambda { |mass| (mass - 64.015765)/2 },
    # added by Yannick using CDK
    "[M+Li]+"            => lambda { |mass| (mass - 6.9400376247955)}
  }

  # Given the negative adduct mass calculate the neutral mass of the
  # parent molecule
  NEGATIVE_ION_MASS_TO_NEUTRAL_MASS = {
    "[M]-"              => lambda { |mass| mass + MASS_OF_ELECTRON},
    "[M-3H]3-"          => lambda { |mass| 3*(mass + 1.007276)  },
    "[M-2H]2-"          => lambda { |mass| 2*(mass + 1.007276)  },
    "[M-H20-H]-"        => lambda { |mass| mass + 19.01839    },
    "[M-H]-"            => lambda { |mass| mass + 1.007276    },
    "[M+Na-2H]-"        => lambda { |mass| mass - 20.974666   },
    "[M+Cl]-"           => lambda { |mass| mass - 34.969402   },
    "[M+K-2H]-"         => lambda { |mass| mass - 36.948606   },
    "[M+HCOOH-H]-"      => lambda { |mass| mass - 44.998201   },
    "[M+CH3COOH-H]-"    => lambda { |mass| mass - 59.013851   },
    "[M+Br]-"           => lambda { |mass| mass - 78.918885   },
    "[M+TFA-H]-"        => lambda { |mass| mass - 112.985586  },
    "[2M-H]-"           => lambda { |mass| (mass + 1.007276)/2  },
    "[2M+HCOOH-H]-"     => lambda { |mass| (mass - 44.998201)/2 },
    "[2M+CH3COOH-H]-"   => lambda { |mass| (mass - 59.013851)/2 },
    "[3M-H]-"           => lambda { |mass| (mass + 1.007276)/3  },
  }

  # Define the methods to allow for calculating the ion mass
  # given the ion type and mass of the compound
  MASS_TO_POSITIVE_ION_MASS = {
    "[M]+"                => lambda { |mass| mass - MASS_OF_ELECTRON},
    "[M+3H]3+"            => lambda { |mass| mass/3 + 1.007276  },
    "[M+2H+Na]3+"         => lambda { |mass| mass/3 + 8.334590  },
    "[M+H+2Na]3+"         => lambda { |mass| mass/3 + 15.7661904},
    "[M+3Na]3+"           => lambda { |mass| mass/3 + 22.989218 },
    "[M+2H]2+"            => lambda { |mass| mass/2 + 1.007276  },
    "[M+H+NH4]2+"         => lambda { |mass| mass/2 + 9.520550  },
    "[M+H+Na]2+"          => lambda { |mass| mass/2 + 11.998247 },
    "[M+H+K]2+"           => lambda { |mass| mass/2 + 19.985217 },
    "[M+CH3CN+2H]2+"      => lambda { |mass| mass/2 + 21.520550 },
    "[M+2Na]2+"           => lambda { |mass| mass/2 + 22.989218 },
    "[M+2CH3CN+2H]2+"     => lambda { |mass| mass/2 + 42.033823 },
    "[M+3CH3CN+2H]2+"     => lambda { |mass| mass/2 + 62.547097 },
    "[M+H]+"              => lambda { |mass| mass + 1.007276    },
    "[M+NH4]+"            => lambda { |mass| mass + 18.033823   },
    "[M+Na]+"             => lambda { |mass| mass + 22.989218   },
    "[M+CH3OH+H]+"        => lambda { |mass| mass + 33.033489   },
    "[M+K]+"              => lambda { |mass| mass + 38.963158   },
    "[M+CH3CN+H]+"        => lambda { |mass| mass + 42.033823   },
    "[M+2Na+H]3+"         => lambda { |mass| mass - 44.971160   },
    "[M+IsoProp+H]+"      => lambda { |mass| mass + 61.06534    },
    "[M+CH3CN+Na]+"       => lambda { |mass| mass + 64.015765   },
    "[M+2K+H]3+"          => lambda { |mass| mass + 76.919040   },
    "[M+DMSO+H]+"         => lambda { |mass| mass + 79.02122    },
    "[M+2CH3CN+H]+"       => lambda { |mass| mass + 83.060370   },
    "[M+2Na-H]+"          => lambda { |mass| mass + 44.971160   },
    "[M+IsoProp+Na+H]2+"  => lambda { |mass| mass + 84.05511    },
    "[2M+H]+"             => lambda { |mass| 2*mass + 1.007276  },
    "[2M+NH4]+"           => lambda { |mass| 2*mass + 18.033823 },
    "[2M+Na]+"            => lambda { |mass| 2*mass + 22.989218 },
    "[2M+3H2O+2H]+"       => lambda { |mass| 2*mass + 28.02312  },
    "[2M+K]+"             => lambda { |mass| 2*mass + 38.963158 },
    "[2M+CH3CN+H]+"       => lambda { |mass| 2*mass + 42.033823 },
    "[2M+CH3CN+Na]+"      => lambda { |mass| 2*mass + 64.015765 },
    # added by Yannick using CDK
    "[M+Li]+"             => lambda { |mass| (mass + 6.9400376247955)}
  }

  MASS_TO_NEGATIVE_ION_MASS = {
    "[M]-"              => lambda { |mass| mass + MASS_OF_ELECTRON},
    "[M-3H]3-"          => lambda { |mass| mass/3 - 1.007276  },
    "[M-2H]2-"          => lambda { |mass| mass/2 - 1.007276  },
    "[M-H-H2O]-"        => lambda { |mass| mass - 19.01839    },
    "[M-H]-"            => lambda { |mass| mass - 1.007276    },
    "[M+Na-2H]-"        => lambda { |mass| mass + 20.974666   },
    "[M+Cl]-"           => lambda { |mass| mass + 34.969402   },
    "[M+K-2H]-"         => lambda { |mass| mass + 36.948606   },
    "[M+HCOOH-H]-"      => lambda { |mass| mass + 44.998201   },
    "[M+CH3COOH-H]-"    => lambda { |mass| mass + 59.013851   },
    "[M+Br]-"           => lambda { |mass| mass + 78.918885   },
    "[M+TFA-H]-"        => lambda { |mass| mass + 112.985586  },
    "[2M-H]-"           => lambda { |mass| 2*mass - 1.007276  },
    "[2M+HCOOH-H]-"     => lambda { |mass| 2*mass + 44.998201 },
    "[2M+CH3COOH-H]-"   => lambda { |mass| 2*mass + 59.013851 },
    "[3M-H]-"           => lambda { |mass| 3*mass - 1.007276  },
  }

  def self.positive_ion_types
    POSITIVE_ION_MASS_TO_NEUTRAL_MASS.keys
  end

  def self.negative_ion_types
    NEGATIVE_ION_MASS_TO_NEUTRAL_MASS.keys
  end

  def self.adduct_types
      POSITIVE_ION_MASS_TO_NEUTRAL_MASS.keys +
      NEGATIVE_ION_MASS_TO_NEUTRAL_MASS.keys
  end

  def self.positive_adduct_type?(adduct_type)
    POSITIVE_ION_MASS_TO_NEUTRAL_MASS.has_key? adduct_type
  end

  def self.negative_adduct_type?(adduct_type)
    NEGATIVE_ION_MASS_TO_NEUTRAL_MASS.has_key? adduct_type
  end

  def self.positively_charged_adduct?(adduct_type)
    adduct_type[-1] == "+"
  end

  def self.negatively_charged_adduct?(adduct_type)
    adduct_type[-1] == "-"
  end

  def self.normalized(adduct_type)
    valid_adduct_types = ['[M+H]+', '[M]+', '[M+Na]+', '[M+2H]2+', '[M+K]+', '[2M+K]+', '[M+NH4]+', '[2M+H]+', '[2M+Na]+', '[M+DMSO+H]+', '[2M+NH4]+', '[M+CH3CN+H]+', '[M+H+K]2+', '[M-H+2Na]+', '[M+2Na]2+', '[M+H+Na]2+', '[M+H-NH3]+', '[M+H-H2O]+', '[M+H+H2O]+', '[3M+H]+', '[M+H2O]+', '[M+Li]+', '[M+3H]3+', '[M+2H+Na]3+', '[M+H+2Na]3+', '[M+3Na]3+', '[2M+CH3CN+Na]+', '[2M+CH3CN+H]+','[M-H]-', '[M]-', '[M+Cl]-', '[2M-H]-',  '[M+HCOOH-H]-', '[M+Na-2H]-', '[M-2H]2-', '[M+K-2H]-', '[M+CH3COOH-H]-',  '[M+Br]-',  '[M-H-H2O]-',  '[3M-H]-',  '[M-H+H2O]-', '[M-H-NH3]-', '[2M+CH3COOH-H]-', '[2M+HCOOH-H]-', '[M-3H]3-', '[M+TFA-H]-']
		pos_singly_charged = ["M+H","2M+K","2M+H","3M+H","M+Na","M+Li","2M+Na","M+NH4","2M+NH4","M+H-NH3","M+H-H2O","M+H+H2O","M+DMSO+H"]
		neg_singly_charged = ["M-H","2M-H","M+Br","3M-H","M+Cl","M+K-2H","M+Na-2H","M-H-H2O","M-H+H2O","M-H-NH3"]
		pos_doubly_charged = ["M+2H","M+H+K","M+2Na","M+H+Na"]
		if adduct_type.in?valid_adduct_types
		  # Do nothing.
    elsif adduct_type.in?neg_singly_charged
			adduct_type = "[#{adduct_type}]-"
		elsif adduct_type.in?pos_singly_charged 
			adduct_type = "[#{adduct_type}]+"
		elsif adduct_type.in?pos_doubly_charged 
			adduct_type = "[#{adduct_type}]2+"
		elsif adduct_type == "M+" || adduct_type == "Cat+"
			adduct_type = "[M]+"
		elsif adduct_type == "M-"
			adduct_type = "[M]-"
		elsif adduct_type == "M+2Na-H" || adduct_type == "M-H+2Na"
			adduct_type = "[M-H+2Na]+"
		elsif adduct_type == "M+Hac-H" || adduct_type == "[M+CH3COO]-"
			adduct_type = "[M+CH3COOH-H]-"
		elsif adduct_type == "M+ACN+H"
			adduct_type = "[M+CH3CN+H]+"
		elsif adduct_type == "[M-2H](2H)-"
			adduct_type = "[M-2H]2-"
		elsif adduct_type == "M+FA-H"
			adduct_type = "[M+HCOOH-H]-"
		elsif adduct_type == "Cat+H2O"
			adduct_type = "[M+H2O]+"
		elsif adduct_type == "M-H+2Na" || adduct_type == "M+2Na-H"
			adduct_type = "[M-H+2Na]+"
		else
			raise ArgumentError.new("Normalization function does not handle such adduct type: #{adduct_type}")
		end
		return adduct_type
		#this code was here before
		# return nil if adduct_type.nil?
    # return "M-" if adduct_type == "[M]-"
    # return "M+" if adduct_type == "[M]+"
    # return "Cat+" if adduct_type == "[Cat]+"
    # adduct_type.gsub(/(\[|\]\(?[1-2H]*\)?(\+$|-$))/,"")
  end


  # Calculate the neutral mass given an ion mass
  # Example: AdductCalculator.adduct_mass_to_neutral_mass("3M-H",291.099391)
  def self.adduct_mass_to_neutral_mass(adduct_type,adduct_mass)
    normalized_notation = normalized(adduct_type)
    # Set the function to calculate the mass based on the adduct type
    mass_function =
      if positive_adduct_type?(normalized_notation)
        POSITIVE_ION_MASS_TO_NEUTRAL_MASS[normalized_notation]
      elsif negative_adduct_type?(normalized_notation)
        NEGATIVE_ION_MASS_TO_NEUTRAL_MASS[normalized_notation]
      end

    raise "Adduct type '#{adduct_type}' is not defined" unless mass_function
    mass_function.call(adduct_mass)
  end

  def self.neutral_mass_to_adduct_mass(adduct_type,neutral_mass)
    normalized_notation = normalized(adduct_type)
    mass_function =
      if positive_adduct_type?(normalized_notation)
        MASS_TO_POSITIVE_ION_MASS[normalized_notation]
      elsif negative_adduct_type?(normalized_notation)
        MASS_TO_NEGATIVE_ION_MASS[normalized_notation]
      end
    raise "Adduct type '#{adduct_type}' is not defined" unless mass_function
    mass_function.call(neutral_mass)
  end

end
