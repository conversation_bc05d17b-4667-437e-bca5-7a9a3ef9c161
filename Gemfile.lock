GIT
  remote: *****************:wishartlab/admin-mailer
  revision: 77556ba7fee983381a1d7a8548a544ae30f0c06f
  specs:
    admin_mailer (1.0.3)
      mail

GIT
  remote: *****************:wishartlab/cite_this
  revision: e6afcb06d3e77f63ccca32deb53caf977903913e
  specs:
    cite_this (3.5.4)
      faraday (>= 0.9.0)
      faraday_middleware
      httpclient
      nokogiri
      rails (>= 4.0.2)
      slim
      wishart (>= 1.0.0)

GIT
  remote: *****************:wishartlab/moldbi
  revision: 66acf710ec98b7752cf1674f096d81567ffb4d72
  specs:
    moldbi (7.6.9)
      activemodel (~> 4.2.11)
      bootstrap-sass
      her
      progress_bar
      slim
      wishart (>= 3.3.0)

GIT
  remote: *****************:wishartlab/seqsearch
  revision: d60ea95bac404d814491d3506255db3fb07df1e1
  specs:
    seq_search (2.2.3)
      bio
      bootstrap-kaminari-views
      bootstrap-sass
      kaminari
      rails (>= 3.1.0)
      slim-rails
      wishart

GIT
  remote: *****************:wishartlab/specdbi
  revision: 5c06cd7c4aed1a8348b9412a318bafb7e82e5d22
  branch: np-mrd
  specs:
    specdb (3.18.1)
      activeresource
      faraday
      spectrum_hash
      wishart (>= 3.5.0)

GIT
  remote: *****************:wishartlab/syncfast
  revision: 1e2d80fc9f0f447ca6a96b3f7238efc504a6d672
  specs:
    syncfast (1.0.6)
      highline
      net-ssh

GIT
  remote: *****************:wishartlab/tmic_banner
  revision: 1d1d3a1fd97853397a558cdb68d9c8028871629f
  specs:
    tmic_banner (1.1.2)
      rails (~> 4.2.1)

GIT
  remote: *****************:wishartlab/unearth
  revision: f84ac1ebbab1f64bcc3f5ee7b7daaea30c054060
  branch: v6-ES6
  specs:
    unearth (7.5.2)
      admin_mailer
      elasticsearch
      hashie
      kaminari
      parallel
      patron
      rails (~> 4.2.0)
      responders (~> 2.0)
      ruby-progressbar
      sidekiq
      wishart

GIT
  remote: *****************:wishartlab/wishart
  revision: 0e8faae1c06cc7668b5af03ab0939cc6c6173524
  specs:
    wishart (3.7.3)
      autoprefixer-rails
      bootstrap-kaminari-views
      bootstrap-sass (~> 3.3.0)
      browser (~> 0.8.0)
      favicon_maker
      jquery-turbolinks
      rails (~> 4.2.0)
      rubyzip (>= 1.0.0)
      slim-rails

GIT
  remote: https://github.com/mimemagicrb/mimemagic.git
  revision: 01f92d86d15d85cfd0f20dabd025dcbd36a8a60f
  ref: 01f92d86d15d85cfd0f20dabd025dcbd36a8a60f
  specs:
    mimemagic (0.3.5)

GEM
  remote: https://rubygems.org/
  specs:
    actionmailer (********)
      actionpack (= ********)
      actionview (= ********)
      activejob (= ********)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 1.0, >= 1.0.5)
    actionpack (********)
      actionview (= ********)
      activesupport (= ********)
      rack (~> 1.6)
      rack-test (~> 0.6.2)
      rails-dom-testing (~> 1.0, >= 1.0.5)
      rails-html-sanitizer (~> 1.0, >= 1.0.2)
    actionview (********)
      activesupport (= ********)
      builder (~> 3.1)
      erubis (~> 2.7.0)
      rails-dom-testing (~> 1.0, >= 1.0.5)
      rails-html-sanitizer (~> 1.0, >= 1.0.3)
    activejob (********)
      activesupport (= ********)
      globalid (>= 0.3.0)
    activemodel (********)
      activesupport (= ********)
      builder (~> 3.1)
    activerecord (********)
      activemodel (= ********)
      activesupport (= ********)
      arel (~> 6.0)
    activeresource (4.1.0)
      activemodel (~> 4.0)
      activesupport (~> 4.0)
      rails-observers (~> 0.1.2)
    activesupport (********)
      i18n (~> 0.7)
      minitest (~> 5.1)
      thread_safe (~> 0.3, >= 0.3.4)
      tzinfo (~> 1.1)
    addressable (2.7.0)
      public_suffix (>= 2.0.2, < 5.0)
    airbrussh (1.4.0)
      sshkit (>= 1.6.1, != 1.7.0)
    appsignal (2.11.0)
      rack
    archive-zip (0.12.0)
      io-like (~> 0.3.0)
    arel (6.0.4)
    authlogic (4.5.0)
      activerecord (>= 4.2, < 5.3)
      activesupport (>= 4.2, < 5.3)
      request_store (~> 1.0)
      scrypt (>= 1.2, < 4.0)
    auto_strip_attributes (2.6.0)
      activerecord (>= 4.0)
    autoprefixer-rails (8.6.5)
      execjs
    better_errors (2.8.1)
      coderay (>= 1.0.0)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
    binding_of_caller (0.8.0)
      debug_inspector (>= 0.0.1)
    bio (2.0.1)
    bootsnap (1.4.8)
      msgpack (~> 1.0)
    bootstrap-kaminari-views (0.0.5)
      kaminari (>= 0.13)
      rails (>= 3.1)
    bootstrap-sass (3.3.7)
      autoprefixer-rails (>= 5.2.1)
      sass (>= 3.3.4)
    browser (0.8.0)
    builder (3.2.4)
    byebug (11.1.3)
    capistrano (3.14.1)
      airbrussh (>= 1.0.0)
      i18n
      rake (>= 10.0.0)
      sshkit (>= 1.9.0)
    capistrano-bundler (2.0.1)
      capistrano (~> 3.1)
    capistrano-rails (1.6.1)
      capistrano (~> 3.1)
      capistrano-bundler (>= 1.1, < 3)
    capistrano-rbenv (2.2.0)
      capistrano (~> 3.1)
      sshkit (~> 1.3)
    capistrano3-puma (4.0.0)
      capistrano (~> 3.7)
      capistrano-bundler
      puma (~> 4.0)
    capybara (3.33.0)
      addressable
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.8)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (~> 1.5)
      xpath (~> 3.2)
    childprocess (3.0.0)
    chromedriver-helper (2.1.1)
      archive-zip (~> 0.10)
      nokogiri (~> 1.8)
    chronic (0.10.2)
    chronic_duration (0.10.6)
      numerizer (~> 0.1.1)
    climate_control (0.2.0)
    cocoon (1.2.15)
    coderay (1.1.3)
    coffee-rails (4.2.2)
      coffee-script (>= 2.2.0)
      railties (>= 4.0.0)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    concurrent-ruby (1.1.10)
    connection_pool (2.2.3)
    crass (1.0.6)
    daemons (1.3.1)
    debug_inspector (0.0.3)
    digest (3.1.1)
    docile (1.4.0)
    elasticsearch (6.8.1)
      elasticsearch-api (= 6.8.1)
      elasticsearch-transport (= 6.8.1)
    elasticsearch-api (6.8.1)
      multi_json
    elasticsearch-transport (6.8.1)
      faraday (>= 0.14, < 1)
      multi_json
    erubi (1.9.0)
    erubis (2.7.0)
    eventmachine (1.2.7)
    execjs (2.8.1)
    faraday (0.17.3)
      multipart-post (>= 1.2, < 3)
    faraday_middleware (0.14.0)
      faraday (>= 0.7.4, < 1.0)
    favicon_maker (1.3.1)
      docile (~> 1.1)
    ffi (1.15.5)
    ffi-compiler (1.0.1)
      ffi (>= 1.0.0)
      rake
    flamegraph (0.9.5)
    get_process_mem (0.2.7)
      ffi (~> 1.0)
    globalid (0.4.2)
      activesupport (>= 4.2.0)
    has_scope (0.7.2)
      actionpack (>= 4.1)
      activesupport (>= 4.1)
    hashie (4.1.0)
    her (1.1.0)
      activemodel (>= 4.2.1)
      faraday (>= 0.8, < 1.0)
      multi_json (~> 1.7)
    highline (2.0.3)
    hodel_3000_compliant_logger (0.1.1)
    httparty (0.18.1)
      mime-types (~> 3.0)
      multi_xml (>= 0.5.2)
    httpclient (2.8.3)
    i18n (0.9.5)
      concurrent-ruby (~> 1.0)
    inherited_resources (1.9.0)
      actionpack (>= 4.2, < 5.3)
      has_scope (~> 0.6)
      railties (>= 4.2, < 5.3)
      responders
    io-like (0.3.1)
    io-wait (0.3.0)
    jbuilder (2.9.1)
      activesupport (>= 4.2.0)
    jquery-rails (4.4.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    jquery-turbolinks (2.1.0)
      railties (>= 3.1.0)
      turbolinks
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    libv8-node (15.14.0.1)
    libv8-node (15.14.0.1-x86_64-darwin-17)
    listen (3.1.5)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
      ruby_dep (~> 1.2)
    loofah (2.19.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    mail (*******)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    memory_profiler (0.9.14)
    mime-types (3.3.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2020.0512)
    mini_mime (1.1.2)
    mini_portile2 (2.6.1)
    mini_racer (0.4.0)
      libv8-node (~> *********)
    minitest (5.15.0)
    msgpack (1.3.3)
    multi_json (1.15.0)
    multi_xml (0.6.0)
    multipart-post (2.1.1)
    mysql2 (0.4.10)
    net-imap (0.2.2)
      digest
      net-protocol
      strscan
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.1.2)
      io-wait
      timeout
    net-scp (3.0.0)
      net-ssh (>= 2.6.5, < 7.0.0)
    net-smtp (0.3.0)
      digest
      net-protocol
      timeout
    net-ssh (6.1.0)
    newrelic_rpm (6.13.0)
    nio4r (2.5.4)
    nokogiri (1.12.5)
      mini_portile2 (~> 2.6.1)
      racc (~> 1.4)
    nokogiri (1.12.5-x86_64-darwin)
      racc (~> 1.4)
    numerizer (0.1.1)
    oink (0.10.1)
      activerecord
      hodel_3000_compliant_logger
    options (2.3.2)
    paperclip (6.1.0)
      activemodel (>= 4.2.0)
      activesupport (>= 4.2.0)
      mime-types
      mimemagic (~> 0.3.0)
      terrapin (~> 0.6.0)
    parallel (1.20.1)
    patron (0.13.3)
    progress_bar (1.3.3)
      highline (>= 1.6, < 3)
      options (~> 2.3.0)
    public_suffix (4.0.6)
    puma (4.3.6)
      nio4r (~> 2.0)
    puma_worker_killer (0.3.0)
      get_process_mem (~> 0.2)
      puma (>= 2.7, < 5)
    quiet_assets (1.1.0)
      railties (>= 3.1, < 5.0)
    racc (1.6.2)
    rack (1.6.13)
    rack-mini-profiler (2.1.0)
      rack (>= 1.2.0)
    rack-protection (2.1.0)
      rack
    rack-test (0.6.3)
      rack (>= 1.0)
    rails (********)
      actionmailer (= ********)
      actionpack (= ********)
      actionview (= ********)
      activejob (= ********)
      activemodel (= ********)
      activerecord (= ********)
      activesupport (= ********)
      bundler (>= 1.3.0, < 2.0)
      railties (= ********)
      sprockets-rails
    rails-deprecated_sanitizer (1.0.4)
      activesupport (>= 4.2.0.alpha)
    rails-dom-testing (1.0.9)
      activesupport (>= 4.2.0, < 5.0)
      nokogiri (~> 1.6)
      rails-deprecated_sanitizer (>= 1.0.1)
    rails-html-sanitizer (1.5.0)
      loofah (~> 2.19, >= 2.19.1)
    rails-observers (0.1.5)
      activemodel (>= 4.0)
    railties (********)
      actionpack (= ********)
      activesupport (= ********)
      rake (>= 0.8.7)
      thor (>= 0.18.1, < 2.0)
    rake (13.0.6)
    rb-fsevent (0.11.2)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    readmorejs-rails (0.0.12)
      railties (>= 3.2, < 5.0)
    redis (4.2.2)
    redis-actionpack (5.1.0)
      actionpack (>= 4.0, < 7)
      redis-rack (>= 1, < 3)
      redis-store (>= 1.1.0, < 2)
    redis-activesupport (5.2.0)
      activesupport (>= 3, < 7)
      redis-store (>= 1.3, < 2)
    redis-rack (2.0.6)
      rack (>= 1.5, < 3)
      redis-store (>= 1.2, < 2)
    redis-rails (5.0.2)
      redis-actionpack (>= 5.0, < 6)
      redis-activesupport (>= 5.0, < 6)
      redis-store (>= 1.2, < 2)
    redis-session-store (0.11.3)
      actionpack (>= 3, < 7)
      redis (>= 3, < 5)
    redis-store (1.9.0)
      redis (>= 4, < 5)
    regexp_parser (1.7.1)
    remotipart (1.4.4)
    request_store (1.5.0)
      rack (>= 1.4)
    responders (2.4.1)
      actionpack (>= 4.2.0, < 6.0)
      railties (>= 4.2.0, < 6.0)
    ruby-progressbar (1.11.0)
    ruby_dep (1.5.0)
    rubyzip (2.3.2)
    safe_attributes (1.0.10)
      activerecord (>= 3.0.0)
    sass (3.7.4)
      sass-listen (~> 4.0.0)
    sass-listen (4.0.0)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
    sass-rails (5.0.7)
      railties (>= 4.0.0, < 6)
      sass (~> 3.1)
      sprockets (>= 2.8, < 4.0)
      sprockets-rails (>= 2.0, < 4.0)
      tilt (>= 1.1, < 3)
    scrypt (3.0.7)
      ffi-compiler (>= 1.0, < 2.0)
    selenium-webdriver (3.142.7)
      childprocess (>= 0.5, < 4.0)
      rubyzip (>= 1.2.2)
    sidekiq (5.2.8)
      connection_pool (~> 2.2, >= 2.2.2)
      rack (< 2.1.0)
      rack-protection (>= 1.5.0)
      redis (>= 3.3.5, < 5)
    sidekiq-status (1.1.4)
      chronic_duration
      sidekiq (>= 3.0)
    slim (4.1.0)
      temple (>= 0.7.6, < 0.9)
      tilt (>= 2.0.6, < 2.1)
    slim-rails (3.5.1)
      actionpack (>= 3.1)
      railties (>= 3.1)
      slim (>= 3.0, < 5.0)
    spectrum_hash (0.2.1)
      httparty
    spring (2.1.1)
    spring-watcher-listen (2.0.1)
      listen (>= 2.7, < 4.0)
      spring (>= 1.2, < 3.0)
    sprockets (3.7.2)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.2.2)
      actionpack (>= 4.0)
      activesupport (>= 4.0)
      sprockets (>= 3.0.0)
    sshkit (1.21.0)
      net-scp (>= 1.1.2)
      net-ssh (>= 2.8.0)
    stackprof (0.2.15)
    strscan (3.0.5)
    temple (0.8.2)
    terrapin (0.6.0)
      climate_control (>= 0.0.3, < 1.0)
    thin (1.7.2)
      daemons (~> 1.0, >= 1.0.9)
      eventmachine (~> 1.0, >= 1.0.4)
      rack (>= 1, < 3)
    thor (1.2.1)
    thread_safe (0.3.6)
    tilt (2.0.11)
    timeout (0.3.1)
    turbolinks (5.2.1)
      turbolinks-source (~> 5.2)
    turbolinks-source (5.2.0)
    tzinfo (1.2.10)
      thread_safe (~> 0.1)
    uglifier (4.2.0)
      execjs (>= 0.3.0, < 3)
    web-console (3.3.0)
      activemodel (>= 4.2)
      debug_inspector
      railties (>= 4.2)
    whenever (1.0.0)
      chronic (>= 0.6.3)
    xpath (3.2.0)
      nokogiri (~> 1.8)

PLATFORMS
  ruby
  x86_64-darwin-17

DEPENDENCIES
  admin_mailer!
  appsignal
  authlogic
  auto_strip_attributes (~> 2.6)
  autoprefixer-rails (= 8.6.5)
  better_errors
  binding_of_caller
  bio
  bootsnap (>= 1.1.0)
  bootstrap-kaminari-views
  bootstrap-sass
  byebug
  capistrano
  capistrano-bundler
  capistrano-rails
  capistrano-rbenv
  capistrano3-puma
  capybara (>= 2.15)
  chromedriver-helper
  cite_this!
  cocoon
  coffee-rails (~> 4.2)
  elasticsearch (< 6.9)
  execjs
  flamegraph
  her (= 1.1.0)
  inherited_resources
  jbuilder (~> 2.5)
  jquery-rails
  jquery-turbolinks
  listen (>= 3.0.5, < 3.2)
  memory_profiler
  mimemagic!
  mini_racer
  moldbi!
  mysql2 (~> 0.4.9)
  newrelic_rpm
  oink
  paperclip
  puma (~> 4.3.1)
  puma_worker_killer
  quiet_assets
  rack-mini-profiler
  rails (~> 4.2.0)
  readmorejs-rails
  redis-rails
  redis-session-store
  remotipart
  responders (~> 2.0)
  rubyzip
  safe_attributes
  sass-rails (~> 5.0)
  selenium-webdriver
  seq_search!
  sidekiq
  sidekiq-status
  specdb!
  spring
  spring-watcher-listen (~> 2.0.0)
  sprockets-rails
  stackprof
  syncfast!
  thin
  tmic_banner!
  turbolinks (~> 5)
  tzinfo-data
  uglifier (>= 1.3.0)
  unearth!
  web-console (>= 3.3.0)
  whenever
  wishart!

RUBY VERSION
   ruby 2.5.1p57

BUNDLED WITH
   1.17.3
