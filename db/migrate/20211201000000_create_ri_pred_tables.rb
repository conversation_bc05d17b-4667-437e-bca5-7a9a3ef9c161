class CreateRiPredTables < ActiveRecord::Migration
  def change
    #users_table
    create_table :users do |t|
      t.string "email", limit: 255
      t.string "crypted_password", limit: 255
      t.string "password_salt", limit: 255
      t.string "persistence_token", limit: 255
      t.string "username", limit: 255
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.string "perishable_token", limit: 255
      t.boolean "verified", default: false
    end
	
	#user_sessions
    create_table :user_sessions do |t|
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
    end
	
	#for RI predictions
    create_table :retention_index_predictions do |t|
      t.integer  "user_id",            limit: 4
      t.integer  "user_session_id",    limit: 4
      t.integer  "compound_id", limit: 4
      t.string   "compound_name",            limit: 255	  
      t.text   "compound_smiles",            limit: 65535	  
      t.string   "compound_stationary_phase",            limit: 255
      t.string   "compound_derivative_type",            limit: 255
      t.datetime "created_at",                   null: false
      t.datetime "updated_at",                   null: false
      t.boolean  "valid"

      t.timestamps null: false
    end
	
	#for RI_compounds
    create_table :retention_index_compounds do |t|
      t.integer  "retention_index_compound_id",       limit: 4
	  t.datetime "created_at",                   null: false
      t.datetime "updated_at",                   null: false
      t.string   "RI_compound_name",       limit: 255
	  t.text     "RI_compound_smiles",       limit: 65535
	  t.string   "RI_compound_formula",       limit: 255
      t.string   "RI_base_compound_inchikey",       limit: 255
      t.decimal  "RI_compound_average_mass",       precision: 9,  scale: 4
	  t.string   "hmdb_id",       limit: 255
	  t.string   "compound_stationary_phase",       limit: 255
	  t.string   "compound_derivative_type",       limit: 255
	  t.integer   "derivative_number",       limit: 4
	  t.integer   "isomer_number",       limit: 4
      t.decimal  "retention_index_value",                          precision: 9,  scale: 4
      t.boolean "compound_derivatized", default: false	  
    end	
	
    create_table "wishart_notices", force: :cascade do |t|
      t.text     "content",    limit: 65535,                 null: false
      t.boolean  "display",                  default: false
      t.datetime "created_at"
      t.datetime "updated_at"
    end
  end
end