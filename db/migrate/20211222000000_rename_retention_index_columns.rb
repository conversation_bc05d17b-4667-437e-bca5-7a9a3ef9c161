class RenameRetentionIndexColumns < ActiveRecord::Migration
  def change
    change_table :retention_index_compounds do |t|
		t.attachment :thumb
    end
    rename_column :retention_index_compounds, :RI_compound_name, :ri_compound_name
	rename_column :retention_index_compounds, :RI_compound_smiles, :ri_compound_smiles
	rename_column :retention_index_compounds, :RI_compound_formula, :ri_compound_formula
	rename_column :retention_index_compounds, :RI_base_compound_inchikey, :ri_base_compound_inchikey
	rename_column :retention_index_compounds, :RI_compound_average_mass, :ri_compound_average_mass
  end

  
end