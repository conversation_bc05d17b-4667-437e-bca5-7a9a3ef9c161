class AddCascadeDeleteToForeignKeys < ActiveRecord::Migration
  def change
    remove_foreign_key :chemical_shift_submission_meta_data, :chemical_shift_submissions
    remove_foreign_key :chemical_shifts, :chemical_shift_submissions

    add_foreign_key :chemical_shift_submission_meta_data, :chemical_shift_submissions, on_delete: :cascade
    add_foreign_key :chemical_shifts, :chemical_shift_submissions, on_delete: :cascade
  end
end
