# encoding: UTF-8
# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# Note that this schema.rb definition is the authoritative source for your
# database schema. If you need to create the application database on another
# system, you should be using db:schema:load, not running all the migrations
# from scratch. The latter is a flawed and unsustainable approach (the more migrations
# you'll amass, the slower it'll run and the greater likelihood for issues).
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 20210408052412) do

  create_table "accession_numbers", force: :cascade do |t|
    t.string   "number",       limit: 255, null: false
    t.integer  "element_id",   limit: 4,   null: false
    t.string   "element_type", limit: 255, null: false
    t.datetime "created_at",               null: false
    t.datetime "updated_at",               null: false
  end

  add_index "accession_numbers", ["element_id"], name: "index_accession_numbers_on_element_id", using: :btree
  add_index "accession_numbers", ["number", "element_type"], name: "index_accession_numbers_on_number_and_element_type", using: :btree

  create_table "articles", force: :cascade do |t|
    t.string   "title",      limit: 255
    t.string   "body",       limit: 255
    t.datetime "created_at",             null: false
    t.datetime "updated_at",             null: false
  end

  create_table "batch_submissions", force: :cascade do |t|
    t.datetime "created_at",                     null: false
    t.datetime "updated_at",                     null: false
    t.integer  "natural_product_id", limit: 4
    t.integer  "user_id",            limit: 4
    t.string   "user_session_id",    limit: 255
    t.boolean  "valid"
  end

  add_index "batch_submissions", ["natural_product_id"], name: "fk_rails_238bad7b34", using: :btree

  create_table "batch_uploads", force: :cascade do |t|
    t.string   "batch_file_file_name",     limit: 255
    t.string   "batch_file_content_type",  limit: 255
    t.integer  "batch_file_file_size",     limit: 8
    t.datetime "batch_file_updated_at"
    t.datetime "created_at",                           null: false
    t.datetime "updated_at",                           null: false
    t.string   "user_session_id",          limit: 255
    t.integer  "user_id",                  limit: 4
    t.integer  "batch_submission_id",      limit: 4
    t.integer  "excel_submission_id",      limit: 4
    t.string   "excel_file_file_name",     limit: 255
    t.string   "excel_file_content_type",  limit: 255
    t.integer  "excel_file_file_size",     limit: 8
    t.datetime "excel_file_updated_at"
    t.string   "generated_excel_location", limit: 255
  end

  add_index "batch_uploads", ["batch_submission_id"], name: "fk_rails_217a662482", using: :btree
  add_index "batch_uploads", ["excel_submission_id"], name: "fk_rails_97f45f6efe", using: :btree
  add_index "batch_uploads", ["user_id"], name: "fk_rails_8ca6f773b6", using: :btree

  create_table "chemical_shift_submission_meta_data", force: :cascade do |t|
    t.integer  "chemical_shift_submission_id", limit: 4
    t.string   "literature_reference",         limit: 255
    t.string   "solvent",                      limit: 255
    t.string   "spectrometer_frequency",       limit: 255
    t.string   "temperature",                  limit: 255
    t.string   "genus",                        limit: 255
    t.datetime "created_at",                               null: false
    t.datetime "updated_at",                               null: false
    t.string   "chemical_shift_standard",      limit: 255
    t.string   "spectrum_type",                limit: 255
    t.string   "species",                      limit: 255
    t.string   "literature_reference_type",    limit: 255
    t.string   "physical_state_of_compound",   limit: 255
    t.string   "melting_point",                limit: 255
    t.string   "boiling_point",                limit: 255
    t.string   "method_of_isolation",          limit: 255
    t.string   "fid_spectrum_type",            limit: 255
    t.string   "provenance",                   limit: 255
  end

  add_index "chemical_shift_submission_meta_data", ["chemical_shift_submission_id"], name: "fk_rails_2eb445e3d1", using: :btree

  create_table "chemical_shift_submissions", force: :cascade do |t|
    t.integer  "user_id",             limit: 4
    t.string   "user_session_id",     limit: 255
    t.integer  "natural_product_id",  limit: 4
    t.datetime "created_at",                        null: false
    t.datetime "updated_at",                        null: false
    t.boolean  "valid"
    t.integer  "batch_submission_id", limit: 4
    t.text     "renumberedMol",       limit: 65535
  end

  add_index "chemical_shift_submissions", ["batch_submission_id"], name: "fk_rails_1c46806a56", using: :btree
  add_index "chemical_shift_submissions", ["natural_product_id"], name: "fk_rails_1c0e42ee40", using: :btree
  add_index "chemical_shift_submissions", ["user_id"], name: "fk_rails_b65629a168", using: :btree

  create_table "chemical_shifts", force: :cascade do |t|
    t.integer  "chemical_shift_submission_id", limit: 4
    t.string   "atom_id",                      limit: 255
    t.string   "atom_symbol",                  limit: 255
    t.string   "chemical_shift_true",          limit: 255
    t.string   "chemical_shift_pred",          limit: 255
    t.string   "multiplet_true",               limit: 255
    t.string   "multiplet_pred",               limit: 255
    t.string   "jcoupling_true",               limit: 255
    t.string   "jcoupling_pred",               limit: 255
    t.datetime "created_at",                               null: false
    t.datetime "updated_at",                               null: false
    t.integer  "submission_id",                limit: 4
    t.string   "custom_atom_id",               limit: 255
    t.string   "assigned_peaks",               limit: 255
    t.string   "assignment_level",             limit: 255
    t.string   "assignment_score",             limit: 255
  end

  add_index "chemical_shifts", ["chemical_shift_submission_id"], name: "fk_rails_51ba984b78", using: :btree
  add_index "chemical_shifts", ["submission_id"], name: "fk_rails_c085c4cd54", using: :btree

  create_table "cite_this_article_referencings", force: :cascade do |t|
    t.integer  "article_id",      limit: 4,   null: false
    t.integer  "referencer_id",   limit: 4,   null: false
    t.string   "referencer_type", limit: 255, null: false
    t.datetime "created_at",                  null: false
    t.datetime "updated_at",                  null: false
  end

  add_index "cite_this_article_referencings", ["article_id", "referencer_id", "referencer_type"], name: "unique_article_referencer", unique: true, using: :btree
  add_index "cite_this_article_referencings", ["article_id"], name: "index_cite_this_article_referencings_on_article_id", using: :btree
  add_index "cite_this_article_referencings", ["referencer_id", "article_id"], name: "new_index", using: :btree
  add_index "cite_this_article_referencings", ["referencer_id", "referencer_type"], name: "cite_this_article_referencings_all_ids", using: :btree
  add_index "cite_this_article_referencings", ["referencer_id"], name: "index_cite_this_article_referencings_on_referencer_id", using: :btree

  create_table "cite_this_articles", force: :cascade do |t|
    t.integer  "pubmed_id",  limit: 4
    t.text     "citation",   limit: 65535
    t.datetime "created_at",               null: false
    t.datetime "updated_at",               null: false
    t.string   "ref_id",     limit: 255
    t.string   "doi",        limit: 255
    t.string   "title",      limit: 1000,  null: false
    t.text     "authors",    limit: 65535, null: false
    t.string   "source",     limit: 255
    t.string   "journal",    limit: 255
    t.string   "volume",     limit: 255
    t.integer  "year",       limit: 4,     null: false
    t.string   "date",       limit: 255,   null: false
    t.string   "pages",      limit: 255
    t.string   "issue",      limit: 255
    t.text     "abstract",   limit: 65535
  end

  add_index "cite_this_articles", ["pubmed_id"], name: "index_cite_this_articles_on_pubmed_id", unique: true, using: :btree

  create_table "cite_this_external_link_referencings", force: :cascade do |t|
    t.integer  "external_link_id", limit: 4,   null: false
    t.integer  "referencer_id",    limit: 4,   null: false
    t.string   "referencer_type",  limit: 255, null: false
    t.datetime "created_at",                   null: false
    t.datetime "updated_at",                   null: false
  end

  add_index "cite_this_external_link_referencings", ["external_link_id"], name: "index_cite_this_external_link_referencings_on_external_link_id", using: :btree
  add_index "cite_this_external_link_referencings", ["referencer_id", "referencer_type"], name: "external_link_referencings_all_ids", using: :btree
  add_index "cite_this_external_link_referencings", ["referencer_id"], name: "index_cite_this_external_link_referencings_on_referencer_id", using: :btree

  create_table "cite_this_external_links", force: :cascade do |t|
    t.string   "url",        limit: 1000, null: false
    t.string   "name",       limit: 255,  null: false
    t.datetime "created_at",              null: false
    t.datetime "updated_at",              null: false
    t.string   "ref_id",     limit: 255
  end

  create_table "cite_this_textbook_referencings", force: :cascade do |t|
    t.integer  "textbook_id",     limit: 4,   null: false
    t.integer  "referencer_id",   limit: 4,   null: false
    t.string   "referencer_type", limit: 255, null: false
    t.datetime "created_at",                  null: false
    t.datetime "updated_at",                  null: false
    t.string   "pages",           limit: 255
    t.string   "chapter",         limit: 255
    t.string   "chapter_author",  limit: 255
  end

  add_index "cite_this_textbook_referencings", ["referencer_id", "referencer_type"], name: "cite_this_article_referencings_all_ids", using: :btree
  add_index "cite_this_textbook_referencings", ["referencer_id"], name: "index_cite_this_textbook_referencings_on_referencer_id", using: :btree
  add_index "cite_this_textbook_referencings", ["textbook_id"], name: "index_cite_this_textbook_referencings_on_textbook_id", using: :btree

  create_table "cite_this_textbooks", force: :cascade do |t|
    t.string   "isbn",        limit: 255
    t.text     "title",       limit: 65535, null: false
    t.datetime "created_at",                null: false
    t.datetime "updated_at",                null: false
    t.string   "ref_id",      limit: 255
    t.string   "authors",     limit: 255,   null: false
    t.string   "edition",     limit: 255
    t.string   "publisher",   limit: 255,   null: false
    t.string   "year",        limit: 255,   null: false
    t.string   "book_format", limit: 255
    t.string   "ean",         limit: 255
  end

  add_index "cite_this_textbooks", ["isbn"], name: "index_cite_this_textbooks_on_isbn", using: :btree

  create_table "excel_submissions", force: :cascade do |t|
    t.integer  "batch_submission_id", limit: 4
    t.datetime "created_at",                    null: false
    t.datetime "updated_at",                    null: false
  end

  add_index "excel_submissions", ["batch_submission_id"], name: "fk_rails_44764d1f04", using: :btree

  create_table "experimental_property_sets", force: :cascade do |t|
    t.integer  "natural_product_id",         limit: 4,    null: false
    t.string   "logp",                       limit: 255
    t.string   "logp_reference",             limit: 1000
    t.string   "water_solubility",           limit: 255
    t.string   "water_solubility_reference", limit: 1000
    t.string   "melting_point",              limit: 255
    t.string   "melting_point_reference",    limit: 1000
    t.string   "boiling_point",              limit: 255
    t.string   "boiling_point_reference",    limit: 1000
    t.datetime "created_at",                              null: false
    t.datetime "updated_at",                              null: false
  end

  add_index "experimental_property_sets", ["natural_product_id"], name: "index_experimental_property_sets_on_natural_product_id", using: :btree

  create_table "extractions", force: :cascade do |t|
    t.integer "submission_natural_product_id", limit: 4
    t.string  "organism",                      limit: 255
    t.string  "method_of_isolation",           limit: 255
    t.string  "reference",                     limit: 255
  end

  add_index "extractions", ["submission_natural_product_id"], name: "index_extractions_on_submission_natural_product_id", using: :btree

  create_table "natural_products", force: :cascade do |t|
    t.string   "np_mrd_id",                  limit: 25,                                default: "",    null: false
    t.datetime "created_at",                                                                           null: false
    t.datetime "updated_at",                                                                           null: false
    t.boolean  "export",                                                               default: false, null: false
    t.string   "name",                       limit: 255,                                               null: false
    t.text     "description",                limit: 16777215
    t.string   "cas",                        limit: 25
    t.text     "synthesis_reference",        limit: 16777215
    t.string   "state",                      limit: 255
    t.text     "comment",                    limit: 16777215
    t.string   "msds_file_name",             limit: 255
    t.string   "msds_content_type",          limit: 255
    t.integer  "msds_file_size",             limit: 4
    t.datetime "msds_updated_at"
    t.integer  "moldb_id",                   limit: 4
    t.text     "moldb_smiles",               limit: 65535
    t.string   "moldb_formula",              limit: 255
    t.text     "moldb_inchi",                limit: 65535
    t.string   "moldb_inchikey",             limit: 255
    t.text     "moldb_iupac",                limit: 65535
    t.string   "moldb_logp",                 limit: 255
    t.string   "moldb_pka",                  limit: 255
    t.decimal  "moldb_average_mass",                          precision: 9,  scale: 4
    t.decimal  "moldb_mono_mass",                             precision: 14, scale: 9
    t.string   "moldb_alogps_solubility",    limit: 255
    t.string   "moldb_alogps_logp",          limit: 255
    t.string   "moldb_alogps_logs",          limit: 255
    t.string   "moldb_acceptor_count",       limit: 255
    t.string   "moldb_donor_count",          limit: 255
    t.string   "moldb_rotatable_bond_count", limit: 255
    t.string   "moldb_polar_surface_area",   limit: 255
    t.string   "moldb_refractivity",         limit: 255
    t.string   "moldb_polarizability",       limit: 255
    t.string   "moldb_traditional_iupac",    limit: 255
    t.integer  "moldb_formal_charge",        limit: 4
    t.string   "moldb_physiological_charge", limit: 255
    t.string   "moldb_pka_strongest_acidic", limit: 255
    t.string   "moldb_pka_strongest_basic",  limit: 255
    t.string   "thumb_file_name",            limit: 255
    t.string   "thumb_content_type",         limit: 255
    t.integer  "thumb_file_size",            limit: 8
    t.datetime "thumb_updated_at"
    t.string   "origin",                     limit: 255
    t.string   "hmdb_id",                    limit: 255
    t.text     "threeDmol",                  limit: 65535
    t.text     "renumberedMol",              limit: 65535
  end

  add_index "natural_products", ["export", "np_mrd_id"], name: "index_natural_products_on_export_and_np_mrd_id", unique: true, using: :btree
  add_index "natural_products", ["export"], name: "index_natural_products_on_export", using: :btree
  add_index "natural_products", ["moldb_average_mass"], name: "index_natural_products_on_moldb_average_mass", using: :btree
  add_index "natural_products", ["moldb_inchikey"], name: "index_natural_products_on_moldb_inchikey", using: :btree
  add_index "natural_products", ["moldb_mono_mass"], name: "index_natural_products_on_moldb_mono_mass", using: :btree
  add_index "natural_products", ["name"], name: "index_natural_products_on_name", unique: true, using: :btree
  add_index "natural_products", ["np_mrd_id"], name: "index_natural_products_on_np_mrd_id", unique: true, using: :btree
  add_index "natural_products", ["updated_at"], name: "index_natural_products_on_updated_at", using: :btree

  create_table "nmr_preds", force: :cascade do |t|
    t.datetime "created_at",                        null: false
    t.datetime "updated_at",                        null: false
    t.string   "sdf_file_file_name",    limit: 255
    t.string   "sdf_file_content_type", limit: 255
    t.integer  "sdf_file_file_size",    limit: 8
    t.datetime "sdf_file_updated_at"
    t.string   "atom_id",               limit: 255
    t.string   "atom_symbol",           limit: 255
    t.string   "shift_pred",            limit: 255
    t.string   "nucleus",               limit: 255
    t.string   "solvent",               limit: 255
    t.integer  "natural_product_id",    limit: 4
    t.string   "user_session_id",       limit: 255
    t.integer  "user_id",               limit: 4
    t.string   "smiles",                limit: 255
  end

  add_index "nmr_preds", ["natural_product_id"], name: "fk_rails_a22ba2a801", using: :btree

  create_table "nmr_submissions", force: :cascade do |t|
    t.integer  "submission_id",           limit: 4
    t.string   "nmr_spectrum_type",       limit: 255
    t.string   "chemical_shift_standard", limit: 255
    t.string   "spectrometer_frequency",  limit: 255
    t.string   "spectra_file",            limit: 255
    t.string   "nmr_file_file_name",      limit: 255
    t.string   "nmr_file_content_type",   limit: 255
    t.integer  "nmr_file_file_size",      limit: 8
    t.datetime "nmr_file_updated_at"
    t.string   "solvent",                 limit: 255
    t.string   "nmr_file1_file_name",     limit: 255
    t.string   "nmr_file1_content_type",  limit: 255
    t.integer  "nmr_file1_file_size",     limit: 8
    t.datetime "nmr_file1_updated_at"
    t.string   "nmr_file2_file_name",     limit: 255
    t.string   "nmr_file2_content_type",  limit: 255
    t.integer  "nmr_file2_file_size",     limit: 8
    t.datetime "nmr_file2_updated_at"
    t.string   "nmr_file3_file_name",     limit: 255
    t.string   "nmr_file3_content_type",  limit: 255
    t.integer  "nmr_file3_file_size",     limit: 8
    t.datetime "nmr_file3_updated_at"
    t.string   "nmr_file4_file_name",     limit: 255
    t.string   "nmr_file4_content_type",  limit: 255
    t.integer  "nmr_file4_file_size",     limit: 8
    t.datetime "nmr_file4_updated_at"
    t.string   "nmr_file5_file_name",     limit: 255
    t.string   "nmr_file5_content_type",  limit: 255
    t.integer  "nmr_file5_file_size",     limit: 8
    t.datetime "nmr_file5_updated_at"
    t.string   "nmr_file6_file_name",     limit: 255
    t.string   "nmr_file6_content_type",  limit: 255
    t.integer  "nmr_file6_file_size",     limit: 8
    t.datetime "nmr_file6_updated_at"
  end

  add_index "nmr_submissions", ["submission_id"], name: "index_nmr_submissions_on_submission_id", using: :btree

  create_table "sdf_locations", force: :cascade do |t|
    t.integer  "natural_product_id", limit: 4
    t.string   "2d_path",            limit: 255
    t.string   "3d_path",            limit: 255
    t.datetime "created_at",                     null: false
    t.datetime "updated_at",                     null: false
  end

  add_index "sdf_locations", ["natural_product_id"], name: "fk_rails_cc4584e9ca", using: :btree

  create_table "species", force: :cascade do |t|
    t.string  "scientific_name",       limit: 255, null: false
    t.string  "kingdom",               limit: 255
    t.string  "phylum",                limit: 255
    t.string  "class",                 limit: 255
    t.string  "order",                 limit: 255
    t.string  "family",                limit: 255
    t.integer "scientific_name_taxid", limit: 4
  end

  add_index "species", ["scientific_name"], name: "index_species_on_scientific_name", unique: true, using: :btree

  create_table "species_mappings", force: :cascade do |t|
    t.integer  "species_id",            limit: 4
    t.integer  "species_mappable_id",   limit: 4,   null: false
    t.string   "species_mappable_type", limit: 255, null: false
    t.datetime "created_at"
    t.datetime "updated_at"
    t.string   "source",                limit: 255
  end

  add_index "species_mappings", ["source"], name: "index_species_mappings_on_source", using: :btree
  add_index "species_mappings", ["species_id"], name: "index_species_mappings_on_species_id", using: :btree
  add_index "species_mappings", ["species_mappable_id", "species_mappable_type"], name: "index_species_mappings_on_species_mappable_id_and_type", using: :btree

  create_table "submission_meta_data", force: :cascade do |t|
    t.integer  "submission_id",              limit: 4
    t.string   "genus",                      limit: 255
    t.string   "literature_reference",       limit: 255
    t.datetime "created_at",                             null: false
    t.datetime "updated_at",                             null: false
    t.string   "literature_reference_type",  limit: 255
    t.string   "chemical_shift_standard",    limit: 255
    t.string   "physical_state_of_compound", limit: 255
    t.string   "melting_point",              limit: 255
    t.string   "boiling_point",              limit: 255
    t.string   "spectrum_type",              limit: 255
    t.string   "species",                    limit: 255
    t.string   "solvent",                    limit: 255
    t.string   "spectrometer_frequency",     limit: 255
    t.string   "temperature",                limit: 255
    t.string   "provenance",                 limit: 255
  end

  add_index "submission_meta_data", ["submission_id"], name: "index_submission_meta_data_on_submission_id", using: :btree

  create_table "submissions", force: :cascade do |t|
    t.integer  "user_id",            limit: 4
    t.string   "user_session_id",    limit: 255
    t.integer  "natural_product_id", limit: 4
    t.datetime "created_at",                       null: false
    t.datetime "updated_at",                       null: false
    t.boolean  "valid"
    t.text     "renumberedMol",      limit: 65535
  end

  add_index "submissions", ["natural_product_id"], name: "fk_rails_eb5275f670", using: :btree
  add_index "submissions", ["user_id"], name: "fk_rails_8d85741475", using: :btree
  add_index "submissions", ["user_session_id"], name: "fk_rails_f36a223cde", using: :btree

  create_table "user_sessions", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "users", force: :cascade do |t|
    t.string   "email",             limit: 255
    t.string   "crypted_password",  limit: 255
    t.string   "password_salt",     limit: 255
    t.string   "persistence_token", limit: 255
    t.string   "username",          limit: 255
    t.datetime "created_at",                                    null: false
    t.datetime "updated_at",                                    null: false
    t.string   "perishable_token",  limit: 255
    t.boolean  "verified",                      default: false
  end

  create_table "wishart_notices", force: :cascade do |t|
    t.text     "content",    limit: 65535,                 null: false
    t.boolean  "display",                  default: false
    t.datetime "created_at"
    t.datetime "updated_at"
  end

  add_foreign_key "batch_submissions", "natural_products"
  add_foreign_key "batch_uploads", "batch_submissions"
  add_foreign_key "batch_uploads", "excel_submissions"
  add_foreign_key "batch_uploads", "users"
  add_foreign_key "chemical_shift_submission_meta_data", "chemical_shift_submissions", on_delete: :cascade
  add_foreign_key "chemical_shift_submissions", "batch_submissions"
  add_foreign_key "chemical_shift_submissions", "natural_products"
  add_foreign_key "chemical_shift_submissions", "users"
  add_foreign_key "chemical_shifts", "chemical_shift_submissions", on_delete: :cascade
  add_foreign_key "chemical_shifts", "submissions", on_delete: :cascade
  add_foreign_key "cite_this_article_referencings", "cite_this_articles", column: "article_id", on_delete: :cascade
  add_foreign_key "cite_this_external_link_referencings", "cite_this_external_links", column: "external_link_id", on_delete: :cascade
  add_foreign_key "cite_this_textbook_referencings", "cite_this_textbooks", column: "textbook_id", on_delete: :cascade
  add_foreign_key "excel_submissions", "batch_submissions"
  add_foreign_key "nmr_preds", "natural_products"
  add_foreign_key "nmr_submissions", "submissions"
  add_foreign_key "sdf_locations", "natural_products"
  add_foreign_key "species_mappings", "species"
  add_foreign_key "submission_meta_data", "submissions"
  add_foreign_key "submissions", "natural_products"
  add_foreign_key "submissions", "users"
end
