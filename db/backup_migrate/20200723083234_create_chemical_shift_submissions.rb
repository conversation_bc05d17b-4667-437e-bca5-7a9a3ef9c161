class CreateChemicalShiftSubmissions < ActiveRecord::Migration
  def change
    create_table :chemical_shift_submissions do |t|
      t.integer  "user_id",            limit: 4
      t.integer  "user_session_id",    limit: 4
      t.integer  "natural_product_id", limit: 4
      t.integer  "chemical_shift_id",  limit: 4
      t.datetime "created_at",                   null: false
      t.datetime "updated_at",                   null: false
      t.boolean  "valid"

      t.timestamps null: false
    end
  end
end
