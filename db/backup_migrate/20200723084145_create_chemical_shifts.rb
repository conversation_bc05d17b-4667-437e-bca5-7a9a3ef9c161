class CreateChemicalShifts < ActiveRecord::Migration
  def change
    create_table :chemical_shifts do |t|
      t.integer "chemical_shift_submission_id",           limit: 4
      t.string  "atom_id",  limit: 255
      t.string  "atom_symbol",  limit: 255
      t.string  "chemical_shift_true",  limit: 255
      t.string  "chemical_shift_pred",  limit: 255
      t.string  "multiplet_true",  limit: 255
      t.string  "multiplet_pred",  limit: 255
      t.string  "jcoupling_true",  limit: 255
      t.string  "jcoupling_pred",  limit: 255


      t.timestamps null: false
    end
  end
end
