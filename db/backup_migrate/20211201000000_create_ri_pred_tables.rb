class CreateRiPredTables < ActiveRecord::Migration
  def change
    create_table "users", force: :cascade do |t|
      t.string "email", limit: 255
      t.string "crypted_password", limit: 255
      t.string "password_salt", limit: 255
      t.string "persistence_token", limit: 255
      t.string "username", limit: 255
      t.datetime "created_at", null: false
      t.datetime "updated_at", null: false
      t.string "perishable_token", limit: 255
      t.boolean "verified", default: false
    end
    #create_table :user_submissions do |t|
    create_table :user_submissions do |t|
    end
    create_table :retention_index_compounds do |t|
    #create_table :chemical_shift_submission_meta_data do |t|
      t.integer  "chemical_shift_submission_id",       limit: 4
      t.string   "literature_reference",            limit: 255
      t.string   "solvent",            limit: 255
      t.string   "spectrometer_frequency",            limit: 255
      t.string   "temperature",            limit: 255
      t.string   "genome_species",            limit: 255
    end
    create_table :retention_index_predictions do |t|
      t.integer  "user_id",            limit: 4
      t.integer  "user_session_id",    limit: 4
      t.integer  "natural_product_id", limit: 4
      t.integer  "chemical_shift_id",  limit: 4
      t.datetime "created_at",                   null: false
      t.datetime "updated_at",                   null: false
      t.boolean  "valid"

      t.timestamps null: false
    end
  end
end
