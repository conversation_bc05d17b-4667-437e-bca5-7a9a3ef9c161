class CreateSpeciesMappings < ActiveRecord::Migration
  def change
    create_table :species_mappings do |t|
      t.references :species, index: true, foreign_key: true
      t.integer :species_mappable_id, null: false
      t.string :species_mappable_type, null: false
      t.timestamps
    end

    add_index :species_mappings, [:species_mappable_id, :species_mappable_type], name: "index_species_mappings_on_species_mappable_id_and_type"
  end
end
