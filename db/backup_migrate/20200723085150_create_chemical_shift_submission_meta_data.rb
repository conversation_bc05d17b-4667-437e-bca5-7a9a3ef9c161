class CreateChemicalShiftSubmissionMetaData < ActiveRecord::Migration
  def change
    create_table :chemical_shift_submission_meta_data do |t|
      t.integer  "chemical_shift_submission_id",       limit: 4
      t.string   "literature_reference",            limit: 255
      t.string   "solvent",            limit: 255
      t.string   "spectrometer_frequency",            limit: 255
      t.string   "temperature",            limit: 255
      t.string   "genome_species",            limit: 255

      t.timestamps null: false
    end
  end
end
