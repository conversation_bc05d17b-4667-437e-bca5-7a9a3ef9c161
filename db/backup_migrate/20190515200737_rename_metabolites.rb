class RenameMetabolites < ActiveRecord::Migration
  def change
    rename_table :metabolites, :natural_products
    rename_column :natural_products, :hmdb_id, :np_mrd_id
    rename_column :natural_products, :export_to_hmdb, :export
    remove_column :natural_products, :status
    remove_column :natural_products, :quantified
    remove_column :natural_products, :detected
    remove_column :natural_products, :hml_compound
    remove_column :natural_products, :predicted_in_hmdb
    remove_index :natural_products, name: 'index_tbl_chemical_on_hmdb_id'
    add_index :natural_products, :np_mrd_id, unique: true

    rename_index :accession_numbers, 'index_accession_numbers_on_metabolite_id', 'index_accession_numbers_on_element_id'
    rename_column :experimental_property_sets, :metabolite_id, :natural_product_id
  end
end
