# encoding: UTF-8
# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# Note that this schema.rb definition is the authoritative source for your
# database schema. If you need to create the application database on another
# system, you should be using db:schema:load, not running all the migrations
# from scratch. The latter is a flawed and unsustainable approach (the more migrations
# you'll amass, the slower it'll run and the greater likelihood for issues).
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema.define(version: 20230104183418) do

  create_table "retention_index_compounds", force: :cascade do |t|
    t.datetime "created_at",                                                                        null: false
    t.datetime "updated_at",                                                                        null: false
    t.string   "ri_compound_name",            limit: 255
    t.text     "ri_compound_smiles",          limit: 65535
    t.string   "ri_compound_formula",         limit: 255
    t.string   "ri_base_compound_inchikey",   limit: 255
    t.decimal  "ri_compound_average_mass",                  precision: 9, scale: 4
    t.string   "hmdb_id",                     limit: 255
    t.string   "compound_stationary_phase",   limit: 255
    t.string   "compound_derivative_type",    limit: 255
    t.integer  "derivative_number",           limit: 4
    t.integer  "isomer_number",               limit: 4
    t.decimal  "retention_index_value",                     precision: 9, scale: 4
    t.boolean  "compound_derivatized",                                              default: false
    t.string   "retention_index_compound_id", limit: 255
    t.string   "thumb_file_name",             limit: 255
    t.string   "thumb_content_type",          limit: 255
    t.integer  "thumb_file_size",             limit: 8
    t.datetime "thumb_updated_at"
    t.integer  "ri_id_pred_table",            limit: 4
  end

  create_table "retention_index_predictions", force: :cascade do |t|
    t.integer  "compound_id",               limit: 4
    t.string   "compound_name",             limit: 255
    t.text     "compound_smiles",           limit: 65535
    t.string   "compound_stationary_phase", limit: 255
    t.string   "compound_derivative_type",  limit: 255
    t.datetime "created_at",                              null: false
    t.datetime "updated_at",                              null: false
    t.boolean  "valid"
    t.string   "user_session_id",           limit: 255
    t.string   "predicted_RI",              limit: 255
    t.string   "label",                     limit: 255
    t.string   "original_smile",            limit: 255
  end

  add_index "retention_index_predictions", ["compound_derivative_type"], name: "index_retention_index_predictions_on_compound_derivative_type", using: :btree

  create_table "user_sessions", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "users", force: :cascade do |t|
    t.string   "email",             limit: 255
    t.string   "crypted_password",  limit: 255
    t.string   "password_salt",     limit: 255
    t.string   "persistence_token", limit: 255
    t.string   "username",          limit: 255
    t.datetime "created_at",                                    null: false
    t.datetime "updated_at",                                    null: false
    t.string   "perishable_token",  limit: 255
    t.boolean  "verified",                      default: false
  end

  create_table "wishart_notices", force: :cascade do |t|
    t.text     "content",    limit: 65535,                 null: false
    t.boolean  "display",                  default: false
    t.datetime "created_at"
    t.datetime "updated_at"
  end

end
