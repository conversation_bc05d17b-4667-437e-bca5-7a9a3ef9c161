set :stage, :production
# set :chruby_ruby, 'ruby-2.5.1'
set :rbenv_ruby, '2.5.1'
# set :rbenv_path, '/apps/np-mrd/.rbenv'

# role :app, %w{np-mrd@107.170.114.201}
# role :web, %w{np-mrd@107.170.114.201}
# role :db,  %w{np-mrd@107.170.114.201}

# # # Production server
role :app, %w{ri-pred@161.35.239.152}
role :web, %w{ri-pred@161.35.239.152}
role :db,  %w{ri-pred@161.35.239.152}

# # # Staging server
# role :app, %w{np-mrd@34.68.7.98}
# role :web, %w{np-mrd@34.68.7.98}
# role :db,  %w{np-mrd@34.68.7.98}