require 'sidekiq/web'

Rails.application.routes.draw do

  # user registration path
  resources :users, only: [:new, :create]
  resources :user_sessions, only: [:create, :destroy]
  resources :password_resets, :except => [:destroy, :index, :show ]
  resources :user_verifications, :only => [:show]
  delete 'sign_out', to: 'user_sessions#destroy', as: :sign_out
  get 'sign_in', to: 'user_sessions#new', as: :sign_in
  get 'register', to: 'users#new', as: :register

  # data deposition path: spectrum
  resources :submissions do
    member do
      patch action: :update
      patch action: :create
      get :download_submitted_chemical_shift_data
      post :download_submitted_chemical_shift_data

      get :download_renumbered_mol
      post :download_renumbered_mol

      get :show_assignment_report
      post :show_assignment_report

      get :download_assignment_report
      post :download_assignment_report
    end
    # resources :chemical_shift_submissions
    member do
      patch action: :create
      get :download_nmrml
      post :download_nmrml
      get :download_nmrml_from_outside
      post  :download_nmrml_from_outside
      get :download_spectra_image_from_outside
      post  :download_spectra_image_from_outside


    end
  end
  
  #define ri pred path
  resources :retention_index_predictions do
    member do
      patch action: :update
      patch action: :create
      # patch action: :edit

    end
  end
  
  #define ri compounds path
  resources :retention_index_compounds do
    member do
      patch action: :update
      patch action: :create
      # patch action: :edit

    end
  end  

  #batch upload


  resources :chemical_shift_submissions do
    member do
      patch action: :update
      patch action: :create
      # patch action: :edit
      get :download_submitted_data
      post :download_submitted_data

      get :download_renumbered_mol
      post :download_renumbered_mol


      get :show_assignment_report
      post :show_assignment_report

      get :download_assignment_report
      post :download_assignment_report

      get :score_rules
      post :score_rules

    end

    member do
      patch action: :create
      get :download_nmrml
      post :download_nmrml
      get :download_nmrml_from_outside
      post  :download_nmrml_from_outside
      get :download_spectra_image_from_outside
      post  :download_spectra_image_from_outside
    end

  end


  # WishartLab gems routes
  mount Wishart::Engine => "/w" , as: 'wishart'
  mount Moldbi::Engine => "/structures"
  mount Specdb::Engine => "/spectra"
  mount SeqSearch::Engine => "/seq_search"
  mount Unearth::Engine => "/unearth", as: "unearth"
  mount CiteThis::Engine => "/cite_this", as: "cite_this"

  # NMR Prediction Route
  resources :nmr_preds
  #batch upload
  resources :batch_uploads do
    member do
      get :downloadexcel
      post  :downloadexcel
    end
  end

  resources :batch_submissions do
    member do
      get :downloadexcel
      post  :downloadexcel
      # get :example
      # post :example
    end
  end

  resources :excel_submissions
  # Root route
  root to: 'simple#home'

  # Named routes
  get 'about' => 'simple#about', as: :about
  get 'downloads' => 'simple#downloads', as: :downloads
  get "statistics" => "simple#statistics", as: :statistics
  #get "contact" => "simple#contact", as: :contact
  get "contact" => "retention_index_predictions#contact", as: :'retention_index_contact'
  get 'textquery' => 'simple#textquery', as: :textquery
  get 'utilities' => 'simple#utilities', as: :utilities
  get 'construction' => 'simple#construction', as: :construction
  get 'download_chemical_shifts' => "chemical_shift_submissions#download_submitted_data"
  get 'example_batch_upload_csv', to: "batch_submissions#example"
  get 'tutorial_batch_upload', to: "batch_submissions#tutorial"
  get 'smiles', to: "simple#download_smiles"
  get 'jsmol/:id/new_modal', to: 'jsmol#new_modal', as: 'jsmol_modal'
  get 'jsmol/:id/new_nmr_modal', to: 'jsmol#new_nmr_modal', as: 'jsmol_nmr_modal'
  get 'jsmol/:id/stereochemistry_modal', to: 'jsmol#stereochemistry_modal', as: 'jsmol_stereochemistry_modal'
  get 'jsmol/:id/ri_modal', to: 'jsmol#ri_modal', as: 'jsmol_ri_modal'
  get 'display_all', to: 'retention_index_predictions#display_all', as: 'retention_index_predictions_display_all'
  get 'download_csv', to: 'retention_index_predictions#download_csv', as: 'retention_index_predictions_download'
  get 'download_mol', to: 'retention_index_predictions#download_mol', as: 'retention_index_predictions_mol_download'
  get 'mw_of_smiles', to: 'retention_index_compounds#mw_of_smiles', as: 'retention_index_compounds_smiles_to_mw_convert'
  get 'search', to: 'retention_index_compounds#search', as: 'retention_index_search'
  get 'results', to: 'retention_index_compounds#results', as: 'retention_index_results'
  #get 'jsmol/:smiles/structure_modal',to: 'jsmol#new_structure_modal', as: 'jsmol_structure_modal'
  # patch 'chemical_shift_submissions/:id', to: 'chemical_shift_submissions#update'
  # Resources
  resources :natural_products do
    member do
      get 'structure'
    end


    member do
      # patch action: :update
      patch action: :create
      get :spectrum_view
      post :spectrum_view
      get :submission_spectrum_view
      post :submission_spectrum_view
    end
  end

  # resources :chemical_shift_submissions do
  #   member do
  #     # patch action: :create
  #     post  action: :create
  #     # patch action: :show
  #     get 'structure'
  #   end
  # end

  # resources :chemical_shift_submissions
  # resources :nmr_submissions do
    # member do
      # get  :newsavenmrfile
      # patch action: :createsavenmrfile
      #
    # end
  # end
  # resources :simple do
  #   member do
  #     get :download_smiles
  #     post  :download_smiles
  #   end
  # end
  mount Sidekiq::Web, at: "/sidekiq"

end
