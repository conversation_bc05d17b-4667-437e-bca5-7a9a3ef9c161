name: ri-pred
channels:
  - rdkit
  - pytorch
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=4.5=1_gnu
  - blas=1.0=mkl
  - bottleneck=1.3.2=py37heb32a55_1
  - brotli=1.0.9=he6710b0_2
  - bzip2=1.0.8=h7b6447c_0
  - ca-certificates=2021.10.8=ha878542_0
  - cairo=1.16.0=hf32fb01_1
  - certifi=2021.10.8=py37h89c1867_1
  - chembl_structure_pipeline=1.0.0=pyh9f0ad1d_0
  - cpuonly=2.0=0
  - cudatoolkit=10.0.130=0
  - cycler=0.11.0=pyhd3eb1b0_0
  - dbus=1.13.18=hb2f20db_0
  - expat=2.4.1=h2531618_2
  - ffmpeg=4.3=hf484d3e_0
  - fontconfig=2.13.1=h6c09931_0
  - fonttools=4.25.0=pyhd3eb1b0_0
  - freetype=2.11.0=h70c0345_0
  - giflib=5.2.1=h7b6447c_0
  - glib=2.69.1=h5202010_0
  - gmp=6.2.1=h2531618_2
  - gnutls=3.6.15=he1e5248_0
  - gst-plugins-base=1.14.0=h8213a91_2
  - gstreamer=1.14.0=h28cd5cc_2
  - icu=58.2=he6710b0_3
  - intel-openmp=2021.4.0=h06a4308_3561
  - joblib=1.1.0=pyhd3eb1b0_0
  - jpeg=9d=h7f8727e_0
  - kiwisolver=1.3.1=py37h2531618_0
  - lame=3.100=h7b6447c_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.35.1=h7274673_9
  - libboost=1.73.0=h3ff78a5_11
  - libffi=3.3=he6710b0_2
  - libgcc-ng=9.3.0=h5101ec6_17
  - libgfortran-ng=7.5.0=ha8ba4b0_17
  - libgfortran4=7.5.0=ha8ba4b0_17
  - libgomp=9.3.0=h5101ec6_17
  - libiconv=1.15=h63c8f33_5
  - libidn2=2.3.2=h7f8727e_0
  - libllvm11=11.1.0=h3826bc1_0
  - libpng=1.6.37=hbc83047_0
  - libstdcxx-ng=9.3.0=hd4cf53a_17
  - libtasn1=4.16.0=h27cfd23_0
  - libtiff=4.2.0=h85742a9_0
  - libunistring=0.9.10=h27cfd23_0
  - libuuid=1.0.3=h7f8727e_2
  - libuv=1.40.0=h7b6447c_0
  - libwebp=1.2.0=h89dd481_0
  - libwebp-base=1.2.0=h27cfd23_0
  - libxcb=1.14=h7b6447c_0
  - libxml2=2.9.12=h03d6c58_0
  - llvmlite=0.37.0=py37h295c915_1
  - lz4-c=1.9.3=h295c915_1
  - matplotlib=3.5.0=py37h06a4308_0
  - matplotlib-base=3.5.0=py37h3ed280b_0
  - mkl=2021.4.0=h06a4308_640
  - mkl-service=2.4.0=py37h7f8727e_0
  - mkl_fft=1.3.1=py37hd3c417c_0
  - mkl_random=1.2.2=py37h51133e4_0
  - munkres=1.1.4=py_0
  - ncurses=6.3=h7f8727e_2
  - nettle=3.7.3=hbbd107a_1
  - numba=0.54.1=py37h51133e4_0
  - numexpr=2.8.1=py37h6abb31d_0
  - numpy=1.20.3=py37hf144106_0
  - numpy-base=1.20.3=py37h74d4b33_0
  - olefile=0.46=py37_0
  - openh264=2.1.1=h4ff587b_0
  - openssl=1.1.1m=h7f8727e_0
  - packaging=21.3=pyhd3eb1b0_0
  - pandas=1.3.5=py37h8c16a72_0
  - pcre=8.45=h295c915_0
  - pillow=8.4.0=py37h5aabda8_0
  - pip=21.2.2=py37h06a4308_0
  - pixman=0.40.0=h7f8727e_1
  - py-boost=1.73.0=py37ha9443f7_11
  - pyparsing=3.0.4=pyhd3eb1b0_0
  - pyqt=5.9.2=py37h05f1152_2
  - python=3.7.7=hcff3b4d_5
  - python-dateutil=2.8.2=pyhd3eb1b0_0
  - python_abi=3.7=2_cp37m
  - pytorch=1.10.1=py3.7_cpu_0
  - pytorch-mutex=1.0=cpu
  - pytz=2021.3=pyhd3eb1b0_0
  - qt=5.9.7=h5867ecd_1
  - rdkit=2020.09.1.0=py37hd50e099_1
  - readline=8.1.2=h7f8727e_1
  - scikit-learn=1.0.2=py37h51133e4_1
  - scipy=1.7.3=py37hc147768_0
  - setuptools=58.0.4=py37h06a4308_0
  - sip=4.19.8=py37hf484d3e_0
  - six=1.16.0=pyhd3eb1b0_0
  - sqlite=3.37.0=hc218d9a_0
  - tbb=2021.5.0=hd09550d_0
  - threadpoolctl=2.2.0=pyh0d69192_0
  - tk=8.6.11=h1ccaba5_0
  - torchvision=0.11.2=py37_cpu
  - tornado=6.1=py37h27cfd23_0
  - tqdm=4.62.3=pyhd3eb1b0_1
  - typing_extensions=********=pyh06a4308_0
  - wheel=0.37.1=pyhd3eb1b0_0
  - xz=5.2.5=h7b6447c_0
  - zlib=1.2.11=h7f8727e_4
  - zstd=1.4.9=haebb681_0
