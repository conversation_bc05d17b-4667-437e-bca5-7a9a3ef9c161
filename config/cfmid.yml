# Configure the trained models for cfmid
---
trained_models : "/apps/cfmid/project/shared/data/metab_se_cfm"
extra_params: "DYLD_LIBRARY_PATH=/Users/<USER>/Development/RDKit_2013_09_1/lib:/Users/<USER>/Development/boost_1_55_0/lib:/Users/<USER>/Development/lp_solve_5.5/lpsolve55/bin/osx64"

# true = The CFM-ID Rails app should try to run Dockerized CFM-ID and MSRB-Fragmenter
#        command-line executables using 'docker run ...' commands.
use_cfmid_docker: false

# CFM-ID CLI Docker image name and version
docker_img: <%= ENV['CFMID_IMG'] %>

# Path to queries directory on Docker host
host_queries_path: <%= ENV['HOST_QUERIES_PATH'] %>

# Path to Rails app's system directory on Docker host
host_system_path: <%= ENV['HOST_SYSTEM_PATH'] %>

# Path to directory Docker host that appears to the Rails app at public/spectra, which is
# where spectra files are written by the Compound Identification module when processing a
# user query.
host_cmpd_id_path: <%= ENV['HOST_CMPD_ID_PATH'] %>
