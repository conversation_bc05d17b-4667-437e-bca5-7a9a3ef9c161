require_relative 'boot'

# require 'active_storage/engine'
require 'rails/all'

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module NpMrd
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    # config.load_defaults 5.2

    # Settings in config/environments/* take precedence over those specified here.
    # Application configuration can go into files in config/initializers
    # -- all .rb files in that directory are automatically loaded after loading
    # the framework and any gems in your application.
    
    # Custom directories with classes and modules you want to be autoloadable.
    config.assets.compile = true
    config.autoload_paths += %W(#{config.root}/lib)
    config.autoload_paths += %W(#{config.root}/app/models/proteins)
    config.autoload_paths += %W(#{config.root}/app/models/taxonomies)
    config.autoload_paths += %W(#{config.root}/app/models/pathways)
    config.autoload_paths += %W(#{config.root}/app/models/transports)
    config.autoload_paths += %W(#{config.root}/app/models/reactions)
    config.autoload_paths += %W(#{config.root}/app/models/ontology)
    config.assets.precompile += ["#{config.root}/app/assets/3dimage/jsmol/*"]
    config.active_record.raise_in_transactional_callbacks = true
    config.active_job.queue_adapter = :sidekiq
    # Oink (for memory profiling)
    config.middleware.use Oink::Middleware
  end
end
