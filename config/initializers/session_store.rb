# Be sure to restart your server when you modify this file.

# Specify a serializer for the signed and encrypted cookie jars.
# Valid options are :json, :marshal, and :hybrid.
#Rails.application.config.action_dispatch.cookies_serializer = :json
Rails.application.config.session_store :redis_session_store, {
  key: 'your_session_key',
  serializer: :json,
  redis: {
    expire_after: 120.minutes,  # cookie expiration
    ttl: 120.minutes,           # Redis expiration, defaults to 'expire_after'
    key_prefix: 'npmrd:session:',
    url: 'redis://localhost:6379/0',
  }
}