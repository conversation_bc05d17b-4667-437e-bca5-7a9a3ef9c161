# config valid for current version and patch releases of Capistrano
lock "~> 3.14.1"

set :application, "ri-pred"
set :repo_url, "*****************:wishartlab/ri-pred.git"
set :branch, ENV['REVISION'] || ENV['BRANCH'] || 'master'
set :deploy_to, '/apps/ri-pred/project'
set :use_sudo, false
set :linked_files, ['config/database.yml', 'config/moldb.yml', 'config/specdb.yml',
									  'public/robots.txt', 'config/unearth.yml', 'config/secrets.yml',
                                                                          'config/python_paths.yml']
set :linked_dirs, %w{public/downloads index log public/system public/extractions tmp/pids tmp/sockets tmp/unearth cache}
set :keep_releases, 3
set :sidekiq_config, "#{current_path}/config/sidekiq.yml"
# set :sidekiq_pid,  File.join('/', 'tmp', 'gc-autofit.sidekiq.pid')
namespace :deploy do
  desc 'Start application'
  task :start do
    on roles(:web) do
      invoke('puma:start')
    end
  end

  desc 'Stop application'
  task :stop do
    on roles(:web) do
      invoke('puma:stop')
    end
  end

  desc 'Restart application'
  task :restart do
    on roles(:web) do
      invoke('puma:phased-restart')
    end
  end

  desc 'Hard-restart application'
  task :hard_restart do
    on roles(:web) do
      invoke('puma:restart')
    end
  end
end
# Flush all redis caches. Not necessary unless large update to database
# namespace :redis do
#   desc "Flushes all Redis data"
#   task :flushall do
#     on roles(:web) do
#       execute "redis-cli", "flushall"
#     end
#   end
# end
# namespace :redis_2 do
#   namespace :flush do
#     desc "Flushes all redis cached moldbi views"
#     task :moldbi do
#       on roles(:db) do
#         execute "redis-cli KEYS \"moldbi/*\" | xargs redis-cli DEL"
#       end
#     end

#     desc "Flushes all redis cached moldbi resources"
#     task :moldbi_resources do
#       on roles(:db) do
#         execute "redis-cli KEYS \"moldbi/*/resource\" | xargs redis-cli DEL"
#       end
#     end
#   end
# end
