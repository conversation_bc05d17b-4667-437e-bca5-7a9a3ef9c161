#https://stackoverflow.com/questions/18124673/downloading-rails-assets-file-paths-in-development-vs-production

#INSTALLING PYTHON USING ANACONDA
#
#1) first install anaconda or miniconda using the installer script from the offical site
#   installation sucessful if following command works:
#
#   conda activate
#
#2) on command line enter:
#
#   conda create -n ri-pred python=3.7.7
#   conda install -n ri-pred numpy scipy matplotlib
#   conda install -n ri-pred -c rdkit rdkit
#
#3) to use on the command line:
#
#   conda activate ri-pred
#   python myscript.py
#
#4) to use in scripts, find the path of the conda-np-mrd-python:
#
#   conda activate ri-pred
#   which python
#
#   this path can be used directly without 'conda activate', e.g.:
#
#   /opt/miniconda3/envs/np-mrd/bin/python myscript.py
#
#5) Fill in development -> python_path with path obtained in part 4
#
# ALSO install pytorch related things

# conda create -c rdkit -n prop_predictor rdkit
# source activate prop_predictor
# conda install pytorch torchvision cudatoolkit=10.0 -c pytorch
# conda install scikit-learn tqdm
    

#INSTALLING JYTHON
#
#1) download jython installer from website
#
#2) on command line enter:
#
#   java -jar jython-installer-2.7.2.jar --console
#
#   Please select your language [E/g] >>> E
#   Do you want to read the license agreement now ? [y/N] >>> N
#   Do you accept the license agreement ? [Y/n] >>> Y
#   Please select the installation type [ 1 /2/3/9] >>> 2
#   Do you want to install additional parts ? [y/N] >>> N
#   Do you want to exclude parts from the installation ? [y/N] >>> N
#   Please enter the target directory >>> /opt/jython
#   Unable to find directory /opt/jython, create it ? [Y/n] >>> Y
#
#3) to run from command line, add .../jython/bin/ to your PATH
#
#4) can also run directly by calling the executable file


development:
    python_path: /home/<USER>/miniconda3/envs/ri-pred/bin/python
    #python_path: /usr/bin/python
    jython_path: jython

production:
    python_path: /opt/miniconda3/envs/ri-pred/bin/python
    jython_path: /opt/jython/bin/jython
test:
    python_path: python
    jython_path: jython







