# The optional settings below can be uncommented and
# configured. The value displayed is the default value.
#
# cache_folder must be set
#
# If the user and password are not set then the
# you will only be able to access readonly.
development:
  cache_folder: tmp/cache/specdb
  #accessor_database_name: test
  #password: ~
  #user: ~
  site: 'http://moldb.np-mrd.org'
  compound_class: RetentionIndexCompound
  accessor_database_name: RI-Pred
  compound_id_column: id
  inchi_key_column: ri_base_compound_inchikey
  mono_mass_column: ri_compound_average_mass
  compound_name_column: ri_compound_name



test:
  cache_folder: tmp/cache
  #accessor_database_name: test
  compound_class: RetentionIndexCompound
  accessor_database_name: RI-Pred
  compound_id_column: id
  inchi_key_column: ri_base_compound_inchikey
  mono_mass_column: ri_compound_average_mass
  compound_name_column: ri_compound_name



production:
  cache_folder: /apps/np-mrd/project/shared/cache/specdb
  user: spectra
  password: ~
  #site: 'http://specdb.wishartlab.com/'z
  compound_class: NaturalProduct
  accessor_database_name: NP-MRD
  compound_id_column: np_mrd_id
  inchi_key_column: InChIKey
  mono_mass_column: molecular_weight
  compound_name_column: name

