puts "Removing bad HMDB Compounds"
File.open(File.join(Rails.root, "data", "bad_hmdb.txt"), "r").each_line do |line|
  hmdb_id = line.strip()
  if HmdbMolecule.find_by_hmdb_id(hmdb_id).present?
    puts "Removing " + hmdb_id
    HmdbMolecule.find_by_hmdb_id(hmdb_id).destroy
  end
end

puts "Removing bad KEGG Compounds"
File.open(File.join(Rails.root, "data", "bad_kegg.txt"), "r").each_line do |line|
  kegg_id = line.strip()
  if KeggMolecule.find_by_kegg_id(kegg_id).present?
    puts "Removing " + kegg_id
    KeggMolecule.find_by_kegg_id(kegg_id).destroy
  end
end
