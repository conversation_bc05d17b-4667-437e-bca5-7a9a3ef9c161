# Check if we are production:
def on_production?
  @on_production ||= 
    begin
      production_locations = %w[releases shared current]
      (Dir.glob("../../*").map{|s| File.basename(s) } & production_locations) == production_locations
    end
end

RAILS_ENV = on_production? ? "production" : "development"
datafile = File.absolute_path("data/kegg-table.txt")
mysql_cmd_file = "/tmp/load_kegg_data.sql"
puts "Load data to mysql"
File.open(mysql_cmd_file,"w"){ |mysql_cmd| mysql_cmd.puts(<<-MYSQL) }
truncate kegg_molecules;
load data #{'local' if on_production? } infile '#{datafile}' into table kegg_molecules (kegg_id, name, neutral_mass, inchi_key, inchi);
MYSQL
`bundle exec rails db #{RAILS_ENV} < #{mysql_cmd_file}`
