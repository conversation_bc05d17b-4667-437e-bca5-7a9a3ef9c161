require 'fileutils'

puts "Generating structure images"
FileUtils::mkdir_p(Rails.root + 'public/structures')
Dir.foreach(File.join(Rails.root, "data", "hmdb_annotated")) do |compound_file|
  next if compound_file == '.' or compound_file == '..'
  if compound_file =~ /([^\.]+)\.txt/
    compound = $1
    puts "Generating structures for " + compound
    FileUtils::mkdir_p(Rails.root + 'public/structures/hmdb/' + compound)
    read_data = open(File.join(Rails.root, "data", "hmdb_annotated", compound_file)).read
    fragment_string = read_data.split(/\n\n/)[1]
    fragment_string.split(/\n/).each do |fragment|
      if fragment =~ /^(\d+)\s+([-+]?[0-9]*\.?[0-9]+)\s+(\S+)$/
        id = $1
        structure = $3
        begin
          Jchem.structure_to_png_file(structure, File.join(Rails.root, "public", "structures", "hmdb", compound, id + ".png"))
        rescue
          puts "Skipping " + structure + ", error generating structure"
        end
      end
    end
  else
    puts "Skipping " + compound_file + ", invalid file"
  end
end

Dir.foreach(File.join(Rails.root, "data", "kegg_annotated")) do |compound_file|
  next if compound_file == '.' or compound_file == '..'
  if compound_file =~ /([^\.]+)\.txt/
    compound = $1
    puts "Generating structures for " + compound
    FileUtils::mkdir_p(Rails.root + 'public/structures/kegg/' + compound)
    read_data = open(File.join(Rails.root, "data", "kegg_annotated", compound_file))
    fragment_string = read_data.split(/\n\n/)[1]
    fragment_string.split(/\n/).each do |fragment|
      if fragment =~ /^(\d+)\s+([-+]?[0-9]*\.?[0-9]+)\s+(\S+)$/
        id = $1
        structure = $3
        begin
          Jchem.structure_to_png_file(structure, File.join(Rails.root, "public", "structures", "kegg", compound, id + ".png"))
        rescue
          puts "Skipping " + structure + ", error generating structure"
        end
      end
    end
  else
    puts "Skipping " + compound_file + ", invalid file"
  end
end
