DATA_KEYS = %w(HMDB_ID INCHI_IDENTIFIER INCHI_KEY EXACT_MASS SMILES GENERIC_NAME)

puts "Downloading data"
structures_file = '/tmp/structures.sdf'
`wget http://www.hmdb.ca/downloads/structures.zip && unzip structures.zip && mv structures.sdf #{structures_file}`

outfile = File.absolute_path("/tmp/hmdb_data_table.txt")
out = File.open(outfile,'w')

# Check if we are production:
def on_production?
  @on_production ||= 
    begin
      production_locations = %w[releases shared current]
      (Dir.glob("../../*").map{|s| File.basename(s) } & production_locations) == production_locations
    end
end

RAILS_ENV = on_production? ? "production" : "development"

puts "Preparing data for mysql"
File.open(structures_file).read.split(/\${4}/).each do |data_string|
  # Remove the structure
  data_string = data_string.strip.sub(/^.+^M  END/m,"").strip
  # Turn the data into a hash
  split_data = data_string.
    # split each line
    split(/^> </).select{|s|!s.empty?}.compact.
    # split the data within the line
    map{|s|s.split(/>$/).map(&:strip) }
  begin
    mol_data = Hash[ split_data ]
  rescue => e
    puts split_data.map(&:count).join(",")
    puts split_data.join("****")
    raise e
  end
  next unless mol_data["HMDB_ID"]
  out.puts mol_data.values_at(*DATA_KEYS).join("\t")
end

puts "Load data to mysql"
mysql_cmd_file = "/tmp/mysql_hmdb_data_cmd.sql"
File.open(mysql_cmd_file,"w"){ |mysql_cmd| mysql_cmd.puts(<<-MYSQL) }
truncate hmdb_molecules;
load data #{'local' if on_production? } infile '#{outfile}' into table hmdb_molecules (hmdb_id, inchi, inchi_key, neutral_mass, smiles, name);
MYSQL
`bundle exec rails db #{RAILS_ENV} < #{mysql_cmd_file}`

puts "Cleaning up"
File.delete(structures_file)
File.delete(outfile)
File.delete(mysql_cmd_file)
File.delete("structures.zip")
