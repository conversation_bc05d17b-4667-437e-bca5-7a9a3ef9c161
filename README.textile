h1. Cfm

This application was generated with the "rails_apps_composer":https://github.com/RailsApps/rails_apps_composer gem provided by the "RailsApps Project":http://railsapps.github.io/.

h2. Diagnostics

This application was built with recipes that are known to work together.

This application was built with preferences that are NOT known to work together.

If the application doesn't work as expected, please "report an issue":https://github.com/RailsApps/rails_apps_composer/issues and include these diagnostics:

We'd also like to know if you've found combinations of recipes or preferences that do work together.

Recipes:
["apps4", "controllers", "core", "email", "extras", "frontend", "gems", "git", "init", "models", "prelaunch", "railsapps", "readme", "routes", "saas", "setup", "testing", "views"]

Preferences:
{:git=>true, :railsapps=>"none", :dev_webserver=>"thin", :prod_webserver=>"puma", :database=>"mysql", :templates=>"erb", :unit_test=>"rspec", :integration=>"rspec-capybara", :continuous_testing=>"guard", :fixtures=>"factory_girl", :frontend=>"bootstrap3", :email=>"gmail", :authentication=>"devise", :devise_modules=>"confirmable", :authorization=>"cancan", :form_builder=>"simple_form", :starter_app=>"admin_app", :rvmrc=>false, :quiet_assets=>true, :better_errors=>true}

h2. Ruby on Rails

This application requires:

* Ruby version 2.0.0
* Rails version 3.2.15

Learn more about "Installing Rails":http://railsapps.github.io/installing-rails.html.

h2. Database

This application uses MySQL with ActiveRecord.

h2. Development

* Template Engine: ERB
* Testing Framework: RSpec and Factory Girl
* Front-end Framework: Twitter Bootstrap 3.0 (Sass)
* Form Builder: SimpleForm
* Authentication: Devise
* Authorization: CanCan

h2. Email

The application is configured to send email using a Gmail account.

h2. Getting Started

Lorem ipsum dolor sit amet, consectetur adipiscing elit.

h2. Documentation and Support

This is the only documentation.

h4. Issues

Lorem ipsum dolor sit amet, consectetur adipiscing elit.

h2. Similar Projects

Lorem ipsum dolor sit amet, consectetur adipiscing elit.

h2. Contributing

If you make improvements to this application, please share with others.

* Fork the project on GitHub.
* Make your feature addition or bug fix.
* Commit with Git.
* Send the author a pull request.

If you add functionality to this application, create an alternative implementation, or build an application that is similar, please contact me and I'll add a note to the README so that others can find your work.

h2. Credits

Lorem ipsum dolor sit amet, consectetur adipiscing elit.

h2. License

Lorem ipsum dolor sit amet, consectetur adipiscing elit.
