require 'spec_helper'
include ActionDispatch::TestProcess

describe PredictQuery do
  before {
    @predict_query = create(:predict_query)
  }

  subject { @predict_query }

  it { should respond_to(:type) }
  it { should respond_to(:user) }
  it { should respond_to(:runtime) }
  it { should respond_to(:error) }
  it { should respond_to(:compound) }
  it { should respond_to(:threshold) }
  it { should respond_to(:param_file) }
  it { should respond_to(:config_file) }
  it { should respond_to(:parsed_output) }
  it { should respond_to(:job_id) }
  it { should respond_to(:output_file) }
  it { should respond_to(:ion_mode) }
  it { should respond_to(:spectra_type) }
  it { should respond_to(:adduct_type) }

  describe "when type is not present" do
    before { @predict_query.type = " " }
    it { should_not be_valid }
  end

  describe "when param_file is not present" do
    before { @predict_query.param_file = " " }
    it { should_not be_valid }
  end

  describe "when config_file is not present" do
    before { @predict_query.config_file = " " }
    it { should_not be_valid }
  end

  describe "when secret_id is not unique" do
    before { create(:predict_positive_esi, secret_id: "1234567890") }
    before { @predict_query.secret_id = "1234567890" }
    it { should_not be_valid }
  end

  describe "if spectra type is incorrent" do
    before { @predict_query.spectra_type = "ABC" }
    it { should_not be_valid }
  end

  # Predict query specific tests

  describe "when compound is not present" do
    before { @predict_query.compound = " " }
    it { should_not be_valid }
  end

  describe "when threshold is not present" do
    before { @predict_query.threshold = " " }
    it { should_not be_valid }
  end

  describe "if ion mode is incorrent" do
    before { @predict_query.spectra_type = "neutral" }
    it { should_not be_valid }
  end

  describe "when adduct_type is invalid" do
    before { @predict_query.adduct_type = "what is an adduct" }
    it { should_not be_valid }
  end

  describe "if compound name is invalid" do
    before { @predict_query.compound = "Not a good Inchi" }
    it { should_not be_valid }
  end

  # Must rebuild this one because molecule size is set on create
  describe "if compound is too big" do
    before { @predict_query = build(:predict_query, compound: "InChI=1S/C76H104N18O19S2/c1-41(79)64(100)82-37-61(99)83-58-39-114-115-40-59(76(112)113)92-72(108)57(38-95)91-75(111)63(43(3)97)94-71(107)54(33-46-23-11-6-12-24-46)90-74(110)62(42(2)96)93-66(102)51(28-16-18-30-78)84-69(105)55(34-47-36-81-49-26-14-13-25-48(47)49)88-68(104)53(32-45-21-9-5-10-22-45)86-67(103)52(31-44-19-7-4-8-20-44)87-70(106)56(35-60(80)98)89-65(101)50(85-73(58)109)27-15-17-29-77/h4-14,19-26,36,41-43,50-59,62-63,81,95-97H,15-18,27-35,37-40,77-79H2,1-3H3,(H2,80,98)(H,82,100)(H,83,99)(H,84,105)(H,85,109)(H,86,103)(H,87,106)(H,88,104)(H,89,101)(H,90,110)(H,91,111)(H,92,108)(H,93,102)(H,94,107)(H,112,113)") }
    it { should_not be_valid }
  end
end

describe AssignQuery do
  before {
    @assign_query = create(:assign_query)
  }

  subject { @assign_query }

  it { should respond_to(:input_file) }
  it { should respond_to(:spectra) }
  it { should respond_to(:spectra_id) }

  describe "when compound is not present" do
    before { @assign_query.compound = " " }
    it { should_not be_valid }
  end

  describe "when input_file is not present" do
    before { @assign_query.input_file = nil }
    it { should_not be_valid }
  end

  describe "when ppm_mass_tol is not present" do
    before { @assign_query.ppm_mass_tol = " " }
    it { should_not be_valid }
  end

  describe "when abs_mass_tol is not present" do
    before { @assign_query.abs_mass_tol = " " }
    it { should_not be_valid }
  end

  describe "if ion mode is incorrent" do
    before { @assign_query.spectra_type = "neutral" }
    it { should_not be_valid }
  end

  describe "if compound name is invalid" do
    before { @assign_query.compound = "Not a good Inchi" }
    it { should_not be_valid }
  end

  # Check spectra formatting

  describe "if spectra has wrong energy level headers" do
    before { @assign_query.input_file = fixture_file_upload('inputs/example_spec_bad_energy_level.txt', 'text/plain') }
    it { should_not be_valid }
  end

  describe "if spectra is missing energy levels" do
    before { @assign_query.input_file = fixture_file_upload('inputs/example_spec_missing_energy_level.txt', 'text/plain') }
    # this should still be valid because convert_spectra should add the missing levels
    it { should be_valid }
  end

  describe "if spectra has bad formatting" do
    before { @assign_query.input_file = fixture_file_upload('inputs/example_spec_bad_formatting.txt', 'text/plain') }
    it { should_not be_valid }
  end

  describe "if spectra is in .msp format" do
    before { 
      @assign_query.input_file = fixture_file_upload('inputs/example_esi_spec.msp', 'text/plain')
      @assign_query.spectra_id = "ID_3"
    }
    it { should be_valid }
  end

end

describe IdentifyQuery do
  before {
    @identify_query = create(:identify_query)
  }

  subject { @identify_query }

  it { should respond_to(:database) }
  it { should respond_to(:num_results) }
  it { should respond_to(:ppm_mass_tol) }
  it { should respond_to(:abs_mass_tol) }
  it { should respond_to(:scoring_function) }
  it { should respond_to(:candidates) }
  it { should respond_to(:candidates_file) }
  it { should respond_to(:candidate_limit) }
  it { should respond_to(:candidate_ppm_mass_tol) }
  it { should respond_to(:candidate_abs_mass_tol) }
  it { should respond_to(:process_time) }
  it { should respond_to(:neutral_mass) }
  it { should respond_to(:parent_ion_mass) }
  it { should respond_to(:candidate_spectra_file) }

  describe "when input_file is not present" do
    before { @identify_query.input_file = nil }
    it { should_not be_valid }
  end

  describe "when num_results is not present" do
    before { @identify_query.num_results = " " }
    it { should_not be_valid }
  end

  describe "when threshold is not present" do
    before { @identify_query.threshold = " " }
    it { should_not be_valid }
  end

  describe "when ppm_mass_tol is not present" do
    before { @identify_query.ppm_mass_tol = " " }
    it { should_not be_valid }
  end

  describe "when abs_mass_tol is not present" do
    before { @identify_query.abs_mass_tol = " " }
    it { should_not be_valid }
  end

  describe "when scoring_function is not present" do
    before { @identify_query.scoring_function = " " }
    it { should_not be_valid }
  end

  describe "when candidate_limit is invalid" do
    before { @identify_query.candidate_limit = "lots" }
    it { should_not be_valid }
  end

  describe "when neutral_mass is invalid" do
    before { @identify_query.neutral_mass = "heavy" }
    it { should_not be_valid }
  end

  describe "when candidate_ppm_mass_tol is invalid" do
    before { @identify_query.candidate_ppm_mass_tol = "number" }
    it { should_not be_valid }
  end

  describe "when candidate_ppm_mass_tol is invalid" do
    before { @identify_query.candidate_ppm_mass_tol = "number" }
    it { should_not be_valid }
  end

  describe "when database is invalid" do
    before { @identify_query.database = "somewhere" }
    it { should_not be_valid }
  end

  describe "when parent_ion_mass is invalid" do
    before { @identify_query.parent_ion_mass = "something" }
    it { should_not be_valid }
  end

  describe "when adduct_type is invalid" do
    before { @identify_query.adduct_type = "what is an adduct" }
    it { should_not be_valid }
  end

  describe "when ion_mode is invalid" do
    before { @identify_query.ion_mode = "neutral" }
    it { should_not be_valid }
  end

  # Check spectra formatting

  describe "if spectra is missing spectra ids" do
    before { @identify_query.input_file = fixture_file_upload('inputs/example_spec_missing_id.msp', 'text/plain') }
    it { should_not be_valid }
  end

  describe "if spectra is missing num peaks" do
    before { @identify_query.input_file = fixture_file_upload('inputs/example_spec_missing_num_peaks.msp', 'text/plain') }
    it { should_not be_valid }
  end

  describe "if spectra has bad formatting" do
    before { @identify_query.input_file = fixture_file_upload('inputs/example_spec_bad_formatting.msp', 'text/plain') }
    it { should_not be_valid }
  end

  # Check candidate params

  describe "if is querying the database" do
    before {
      @identify_database_query = create(:identify_database_positive_esi_peak_list_input)
    }
    it "has the correct params" do
      expect(@identify_database_query.find_candidates_params_ok?).to be true
    end

    it "indicates if missing params" do
      @identify_database_query.database = ""
      expect(@identify_database_query.find_candidates_params_ok?).to be false
    end
  end

  describe "if is using a submitted list" do
    before {
      @identify_list_query = create(:identify_list_positive_esi_peak_list_input)
    }
    it "has the correct params" do
      expect(@identify_list_query.submit_candidate_params_ok?).to be true
    end

    it "indicates if missing params" do
      @identify_list_query.candidates_file = nil
      @identify_list_query.candidates = nil
      expect(@identify_list_query.submit_candidate_params_ok?).to be false
    end
  end

  # Check candidate files

  describe "if candidates file is too long" do
    before { @identify_query.candidates_file = fixture_file_upload('inputs/example_candidates_too_long.txt', 'text/plain') }
    it "indicates the file is invalid" do
      expect(@identify_query.candidates_valid?).to be false
    end
  end

  describe "if candidates file has bad compounds" do
    before { @identify_query.candidates_file = fixture_file_upload('inputs/example_candidates_bad_compound.txt', 'text/plain') }
    it "indicates the file is invalid" do
      expect(@identify_query.candidates_valid?).to be false
    end
  end

  describe "if no candidates are found" do
    before {
      @identify_query.candidates_file = nil
      @identify_query.candidates = nil
      @identify_query.parent_ion_mass = 10.0
      @identify_query.candidate_ppm_mass_tol = 0
    }
    it "indicates the file is invalid" do
      expect(@identify_query.candidates_valid?).to be false
    end
  end

end
