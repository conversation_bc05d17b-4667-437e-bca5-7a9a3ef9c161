require 'spec_helper'

describe "queries/show.html.slim", type: :view, js: true do
  it "displays the predict results" do
    query = create(:predict_query)
    query.parsed_output = create_predict_output(query.output_file.path, query)
    query.save

    assign(:query, query)
    assign(:spectra_data, query.parsed_data)

    render

    expect(rendered).to have_content(/Results:/)
    expect(rendered).to have_selector("#energy0")
    expect(rendered).to have_selector(".query-compound img.structure")
    expect(rendered).to have_selector(".assign-fragments-table")
    expect(rendered).to have_selector(".assign-fragments-table tr", count: 101)

  end

  it "displays the assign results" do
    query = create(:assign_query)
    query.parsed_output = create_assign_output(query.output_file.path, query)
    query.save

    assign(:query, query)
    assign(:spectra_data, query.parsed_data)

    render

    expect(rendered).to have_content(/Results:/)
    expect(rendered).to have_selector("#energy0")
    expect(rendered).to have_selector(".assign-fragments-table")
    expect(rendered).to have_selector(".assign-fragments-table tr", count: 76)
    expect(rendered).to have_selector(".transition-table")
    expect(rendered).to have_selector(".transition-table tr", count: 100)
  end

  it "displays the identify results" do

    query = create(:identify_query)
    query.parsed_output = create_identify_output(query.output_file.path, query)
    query.save

    assign(:query, query)
    assign(:spectra_data, query.parsed_data)

    render

    expect(rendered).to have_content(/Results:/)
    expect(rendered).to have_selector("#energy0")
    expect(rendered).to have_selector(".identify-display-table")
    expect(rendered).to have_selector(".identify-display-table tr", count: 6)

  end
end