# Read about factories at https://github.com/thoughtbot/factory_girl
include ActionDispatch::TestProcess

FactoryGirl.define do

  trait :predict do
    type "PredictQuery"
    compound "InChI=1S/C16H21N3O8/c17-10(7-20)14(24)18-11(5-8-1-3-9(21)4-2-8)15(25)19-12(16(26)27)6-13(22)23/h1-4,10-12,20-21H,5-7,17H2,(H,18,24)(H,19,25)(H,22,23)(H,26,27)"
    threshold 0.001

    output_file { fixture_file_upload(Rails.root.join('spec', 'fixtures', 'outputs', 'predict_output.txt'), 'text/plain') }
  end

  trait :assign do
    type "AssignQuery"
    compound "InChI=1S/C16H21N3O8/c17-10(7-20)14(24)18-11(5-8-1-3-9(21)4-2-8)15(25)19-12(16(26)27)6-13(22)23/h1-4,10-12,20-21H,5-7,17H2,(H,18,24)(H,19,25)(H,22,23)(H,26,27)"
    ppm_mass_tol -1
    abs_mass_tol 0.5

    output_file { fixture_file_upload(Rails.root.join('spec', 'fixtures', 'outputs', 'assign_output.txt'), 'text/plain') }
  end

  trait :identify do
    type "IdentifyQuery"
    num_results 10
    ppm_mass_tol 10
    abs_mass_tol -1
    scoring_function "Jaccard"
    threshold 0.001
  end

  trait :identify_database do
    database "HMDB"
    candidate_limit 100
    neutral_mass 296.100800000
    candidate_ppm_mass_tol 100
    candidate_abs_mass_tol nil
    parent_ion_mass 296.100800000
    parent_ion_mass_type "Original"
    adduct_type "Neutral"
    candidates_file { fixture_file_upload(Rails.root.join('spec', 'fixtures', 'inputs', 'example_precomputed_candidates.txt'), 'text/plain') }

    output_file { fixture_file_upload(Rails.root.join('spec', 'fixtures', 'outputs', 'identify_database_output.txt'), 'text/plain') }
  end

  trait :identify_list do
    candidates_file { fixture_file_upload(Rails.root.join('spec', 'fixtures', 'inputs', 'example_candidates.txt'), 'text/plain') }

    candidate_spectra_file { fixture_file_upload(Rails.root.join('spec', 'fixtures', 'outputs', 'identity_predicted_candidates_spectra.msp'), 'text/plain') }
    output_file { fixture_file_upload(Rails.root.join('spec', 'fixtures', 'outputs', 'identify_list_output.txt'), 'text/plain') }
  end

  trait :ei_msp_input do
    spectra_id "ID_3"
    input_file { fixture_file_upload(Rails.root.join('spec', 'fixtures', 'inputs', 'example_spec.msp'), 'text/plain') }
  end

  trait :esi_msp_input do
    spectra_id "ID_3"
    input_file { fixture_file_upload(Rails.root.join('spec', 'fixtures', 'inputs', 'example_esi_spec.msp'), 'text/plain') }
  end

  trait :peak_list_input do
    input_file { fixture_file_upload(Rails.root.join('spec', 'fixtures', 'inputs', 'example_spec.txt'), 'text/plain') }
  end

  trait :positive do
    ion_mode "positive"
    adduct_type "[M+H]+"
  end

  trait :negative do
    ion_mode "negative"
    adduct_type "[M-H]-"
  end

  trait :ei do
    spectra_type "EI"
    param_file { File.join CFMID.config[:trained_models], "ei_ms_models", "ei_nn_iso_new", "param_output.log" }
    config_file { File.join CFMID.config[:trained_models], "ei_ms_models", "ei_nn_iso_new", "param_config.txt" }
  end

  trait :esi do
    spectra_type "ESI"
  end

  trait :positive_esi do
    esi
    positive
    param_file { File.join CFMID.config[:trained_models], "esi_msms_models", "metab_se_cfm", "params_metab_se_cfm", "param_output0.log" }
    config_file { File.join CFMID.config[:trained_models], "esi_msms_models", "metab_se_cfm", "param_config.txt" }
  end

  trait :negative_esi do
    esi
    negative
    param_file { File.join CFMID.config[:trained_models], "esi_msms_models", "negative_metab_se_cfm", "negative_se_params", "param_output0.log" }
    config_file { File.join CFMID.config[:trained_models], "esi_msms_models", "negative_metab_se_cfm", "param_config.txt" }
  end

  trait :query do
    user
    secret_id { SecureRandom.hex(Query::SECRET_ID_LENGTH)}
    job_id { SecureRandom.hex(Query::SECRET_ID_LENGTH)}
    parsed_output "Successful"
  end

  factory :predict_query do
    query
    predict
    positive_esi

    factory :predict_positive_esi do
      positive_esi
    end

    factory :predict_negative_esi do
      negative_esi
    end

    factory :predict_ei do
      ei
    end
  end

  factory :assign_query do
    query
    assign
    positive_esi
    peak_list_input

    factory :assign_positive_esi_peak_list_input do
      positive_esi
      peak_list_input
    end

    factory :assign_negative_esi_msp_input do
      negative_esi
      esi_msp_input
    end

    factory :assign_ei_peak_list_input do
      ei
      peak_list_input
    end

    factory :assign_ei_msp_input do
      ei
      ei_msp_input
    end
  end

  factory :identify_query do
    query
    identify
    identify_database
    positive_esi
    peak_list_input

    factory :identify_database_positive_esi_peak_list_input do
      identify_database
      positive_esi
      peak_list_input
    end

    factory :identify_database_negative_esi_msp_input do
      identify_database
      negative_esi
      esi_msp_input
    end

    factory :identify_list_positive_esi_peak_list_input do
      identify_list
      positive_esi
      peak_list_input
    end

    factory :identify_list_negative_esi_msp_input do
      identify_list
      negative_esi
      esi_msp_input
    end

    factory :identify_database_ei_peak_list_input do
      identify_database
      ei
      peak_list_input
    end

    factory :identify_list_ei_msp_input do
      identify_list
      ei
      ei_msp_input
    end

  end

end
