require 'spec_helper'

describe QueriesController, type: :controller do
  context "when user is an admin" do
    login_admin

    describe "GET status" do
      it "redirects to query page if completed" do
        query = create(:predict_query)
        get :status, query_id: query.secret_id
        expect(response).to redirect_to query_path(query.secret_id)
        expect(assigns(:query)).to eq(query)
      end

      it "redirects to query page with error if failed" do
        # Not sure how to simulate error
      end

      it "redirects to error page if missing" do
        query = create(:predict_query)
        get :status, query_id: "2k3u5hw967jdng14kd5"
        expect(response).to redirect_to error_path
      end
    end

    describe "GET show" do
      it "redirects to status page if still processing" do
        # Not sure how to simulate "working" status in test mode...
      end

      it "loads spectra data if complete" do
        query = create(:predict_query)
        get :show, query_id: query.secret_id
        expect(response).to render_template(:show)
        expect(assigns(:query)).to eq(query)
      end

      it "adds errors if something went wrong" do
        query = create(:predict_query, parsed_output: nil)
        get :show, query_id: query.secret_id
        expect(response).to render_template(:show)
        expect(assigns(:query)).to eq(query)
        expect(assigns(:query).error).not_to be_empty
      end

      it "redirects to error page if missing" do
        query = create(:predict_query)
        get :show, query_id: "2k3u5hw967jdng14kd5"
        expect(response).to redirect_to error_path
      end

    end

    # This is for identify only
    describe "GET load_compound" do
      it "responds with correct candidate as JSON" do
      end
    end

    describe "GET index" do
      it "shows all queries" do
        query = create(:predict_query)
        get :index
        expect(response).to render_template(:index)
        expect(assigns(:queries)).to eq([query])
      end
    end

    describe "DELETE destroy" do
      it "destroys the given query" do
        query = create(:predict_query)
        expect {
          delete :destroy, id: query.id
        }.to change(Query, :count).by(-1)
      end
    end

    describe "GET rerun" do
      it "redirects to status page" do
        query = create(:predict_query)
        get :rerun, query_id: query.secret_id
        expect(response).to redirect_to query_status_path(query.secret_id)
      end

      it "starts a sidekiq job" do
        query = create(:predict_query)
        expect {
          get :rerun, query_id: query.secret_id
        }.to change(PredictWorker.jobs, :size).by(1)
      end
    end

  end

   context "when user is not an admin" do
    login_user

    describe "GET index" do
      it "redirects to home page" do
        query = create(:predict_query)
        get :index
        expect(response).to redirect_to root_path
      end
    end

    describe "DELETE destroy" do
      it "redirects to home page" do
        query = create(:predict_query)
        delete :destroy, id: query.id
        expect(response).to redirect_to root_path
      end
    end

    describe "GET rerun" do
      it "redirects to home page" do
        query = create(:predict_query)
        get :rerun, query_id: query.secret_id
        expect(response).to redirect_to root_path
      end
    end

  end

  context "when user is not logged in" do

    describe "GET index" do
      it "redirects to home page" do
        query = create(:predict_query)
        get :index
        expect(response).to redirect_to root_path
      end
    end

    describe "DELETE destroy" do
      it "redirects to home page" do
        query = create(:predict_query)
        delete :destroy, id: query.id
        expect(response).to redirect_to root_path
      end
    end

    describe "GET rerun" do
      it "redirects to home page" do
        query = create(:predict_query)
        get :rerun, query_id: query.secret_id
        expect(response).to redirect_to root_path
      end
    end

  end
end
