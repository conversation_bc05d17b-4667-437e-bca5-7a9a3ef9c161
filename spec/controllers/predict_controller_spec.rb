require 'spec_helper'

describe <PERSON><PERSON><PERSON>ontroller, type: :controller do

  describe "GET index" do
    it "shows the index page" do
      get :index
      expect(response).to render_template(:index)
      expect(assigns(:predict_query)).to be_a_new(PredictQuery)
    end
  end

  describe "GET new" do
    it "Creates a new query" do
      expect {
        xhr :post, :new, predict_query: FactoryGirl.attributes_for(:predict_query)
      }.to change(Query, :count).by(1)
    end

    it "starts a sidekiq job" do
      expect {
        xhr :post, :new, predict_query: FactoryGirl.attributes_for(:predict_query)
      }.to change(PredictWorker.jobs, :size).by(1)
    end

    it "redirects to query status page" do
      xhr :post, :new, predict_query: FactoryGirl.attributes_for(:predict_query)
      expect(response).to redirect_to query_status_path(assigns(:predict_query).secret_id)
    end

    it "re-renders if there are form errors" do
      predict_params = FactoryGirl.attributes_for(:predict_query)
      predict_params[:compound] = ""
      xhr :post, :new, predict_query: predict_params
      expect(response).to render_template(:index)
    end
  end

end
