require 'spec_helper'

describe IdentifyController, type: :controller do

  describe "GET index" do
    it "shows the index page" do
      get :index
      expect(response).to render_template(:index)
      expect(assigns(:identify_query)).to be_a_new(IdentifyQuery)
    end
  end

  describe "GET new" do
    it "Creates a new query" do
      expect {
        xhr :post, :new, 
          identify_query: FactoryGirl.attributes_for(:identify_query),
          text_or_file: "file",
          submit_or_find: "find",
          mass_tol: 10,
          mass_tol_units: "ppm",
          candidate_mass_tol_units: "ppm",
          candidate_mass_tol: 100,
          adduct_search_ion_mode: "positive"
      }.to change(Query, :count).by(1)
    end

    it "starts a sidekiq job" do
      expect {
        xhr :post, :new, 
          identify_query: FactoryGirl.attributes_for(:identify_query),
          text_or_file: "file",
          submit_or_find: "find",
          mass_tol: 10,
          mass_tol_units: "ppm",
          candidate_mass_tol_units: "ppm",
          candidate_mass_tol: 100,
          adduct_search_ion_mode: "positive"
      }.to change(IdentifyWorker.jobs, :size).by(1)
    end

    it "redirects to query status page" do
      xhr :post, :new, 
        identify_query: FactoryGirl.attributes_for(:identify_query),
        text_or_file: "file",
        submit_or_find: "find",
        mass_tol: 10,
        mass_tol_units: "ppm",
        candidate_mass_tol_units: "ppm",
        candidate_mass_tol: 100,
        adduct_search_ion_mode: "positive"
      expect(response).to redirect_to query_status_path(assigns(:identify_query).secret_id)
    end

    it "re-renders if there are form errors" do
      identify_params = FactoryGirl.attributes_for(:identify_query)
      identify_params[:parent_ion_mass] = ""
      xhr :post, :new, 
        identify_query: identify_params,
        text_or_file: "file",
        submit_or_find: "find",
        mass_tol: 10,
        mass_tol_units: "ppm",
        candidate_mass_tol_units: "ppm",
        candidate_mass_tol: 100,
        adduct_search_ion_mode: "positive"
      expect(response).to render_template(:index)
    end
  end

end
