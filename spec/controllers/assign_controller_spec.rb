require 'spec_helper'

describe <PERSON><PERSON><PERSON><PERSON>roller, type: :controller do

  describe "GET index" do
    it "shows the index page" do
      get :index
      expect(response).to render_template(:index)
      expect(assigns(:assign_query)).to be_a_new(AssignQuery)
    end
  end

  describe "GET new" do
    it "Creates a new query" do
      expect {
        xhr :post, :new, 
          assign_query: FactoryGirl.attributes_for(:assign_query),
          text_or_file: "file",
          mass_tol: 10,
          mass_tol_units: "ppm"
      }.to change(Query, :count).by(1)
    end

    it "starts a sidekiq job" do
      expect {
        xhr :post, :new, 
          assign_query: FactoryGirl.attributes_for(:assign_query),
          text_or_file: "file",
          mass_tol: 10,
          mass_tol_units: "ppm"
      }.to change(AssignWorker.jobs, :size).by(1)
    end

    it "redirects to query status page" do
      xhr :post, :new, 
        assign_query: FactoryGirl.attributes_for(:assign_query),
        text_or_file: "file",
        mass_tol: 10,
        mass_tol_units: "ppm"
      expect(response).to redirect_to query_status_path(assigns(:assign_query).secret_id)
    end

    it "re-renders if there are form errors" do
      assign_params = FactoryGirl.attributes_for(:assign_query)
      assign_params[:compound] = ""
      xhr :post, :new, 
        assign_query: assign_params,
        text_or_file: "file",
        mass_tol: 10,
        mass_tol_units: "ppm"
      expect(response).to render_template(:index)
    end
  end

end
