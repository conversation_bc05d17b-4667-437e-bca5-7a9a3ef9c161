#! /usr/bin/env ruby
#
# In this fake bin, all params expect the output name are ignored and the same 
# data is returned every time ( data at the end of the file )
#
#  So this would give the same result:
#   cfm-id 1 1 1 1 1 1 1 output_filename.txt
#

require "yaml"

outfile = ARGV.last
data = YAML.load(DATA.read)
puts data["print"]
open(outfile,"w") do |out|
  out.puts data["file"]
end

__END__
print: |+
  THIS RESULT IS NOT REAL
  public/queries/26ef8abf842debf67813f863e3bd58762faa64d8/output/output.txt 
  Warning: No features found in feature list 
  Using Jaccard score function
file: |+
  1 0.13742072 HMDB37138 InChI=1S/C12H22O11/c13-1-3-5(15)6(16)9(19)12(22-3)23-10-4(2-14)21-11(20)8(18)7(10)17/h3-20H,1-2H2/t3-,4-,5-,6+,7-,8-,9-,10-,11+,12-/m1/s1
  2 0.046511628 HMDB04162 InChI=1S/C14H26O11/c1-21-11-5(3-15)24-14(10(20)7(11)17)25-12-6(4-16)23-13(22-2)9(19)8(12)18/h5-20H,3-4H2,1-2H3/t5-,6-,7-,8-,9-,10-,11+,12+,13-,14+/m1/s1
  3 0.046511628 HMDB03403 InChI=1S/C14H26O11/c1-21-11-5(3-15)24-14(10(20)7(11)17)25-12-6(4-16)23-13(22-2)9(19)8(12)18/h5-20H,3-4H2,1-2H3/t5-,6-,7-,8-,9-,10-,11-,12-,13+,14-/m1/s1
  4 0 HMDB13711 InChI=1S/3CH2O3.2La.H2O/c3*2-1(3)4;;;/h3*(H2,2,3,4);;;1H2/q;;;2*+3;/p-6
  5 0 HMDB06353 InChI=1S/C15H29O4P/c1-13(2)7-5-8-14(3)9-6-10-15(4)11-12-19-20(16,17)18/h7,9,15H,5-6,8,10-12H2,1-4H3,(H2,16,17,18)/b14-9+
  6 0 HMDB52185 InChI=1S/C65H108O6/c1-4-7-10-13-16-19-22-25-28-30-31-32-33-35-38-40-43-46-49-52-55-58-64(67)70-61-62(71-65(68)59-56-53-50-47-44-41-36-27-24-21-18-15-12-9-6-3)60-69-63(66)57-54-51-48-45-42-39-37-34-29-26-23-20-17-14-11-8-5-2/h8,11,17-18,20-21,25-29,36-37,3
  7 0 HMDB00632 InChI=1S/C18H31NO14S/c1-4-8-12(22)13(23)10(31-14(8)17(24)25)6-30-16-11(19-7(2)21)18(29-3)32-9(5-20)15(16)33-34(26,27)28/h8-16,18,20,22-23H,4-6H2,1-3H3,(H,19,21)(H,24,25)(H,26,27,28)/p-1/t8-,9+,10+,11+,12-,13-,14+,15-,16+,18+/m0/s1
  8 0 HMDB01304 InChI=1S/C19H28O4/c1-12(2)8-7-9-13(3)10-11-15-14(4)16(20)18(22-5)19(23-6)17(15)21/h8,10,20-21H,7,9,11H2,1-6H3/b13-10+
