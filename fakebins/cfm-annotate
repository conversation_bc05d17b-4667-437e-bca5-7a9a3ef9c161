#! /usr/bin/env ruby
#
# This is a bin for faking the cfm-predict code when you don't wnat to spend the
# time to install it
#
# To run like so:
#  cfm-annotate "Oc1ccc(CC(NC(=O)C(N)CO)C(=O)NC(CC(O)=O)C(O)=O)cc1" specfile.txt \
#  project/shared/data/metab_se_cfm/param_output0.log project/shared/data/metab_se_cfm/param_config.txt \
#  annotateout.txt
#
# In this fake bin, all params expect the output name are ignored and the same 
# data is returned every time ( data at the end of the file )
#
# can run with :
#
# cfm-annotate 0 0 0 0 annotateout.txt
#
#

require "yaml"

outfile = ARGV.last
data = YAML.load(DATA.read)
puts data["print"]
open(outfile,"w") do |out|
  out.puts data["file"]
end

__END__
---
print: |+
  THIS IS A FAKE OUTPUT
  Using Single Energy CFM
  Positive Ionization Mode
  Using Lambda 1
  Using Lambda Hold 0
  Using Converge Count Threshold 1
  Using EM Convergence Threshold 10
  Using GA Convergence Threshold 1
  Using Fragmentation Graph Depth 2
  Using Model Depth 2
  Using Spectrum Depths and Weights: (2,1) (2,1) (2,1)
  Not interpolated spectra
  Using Absolute mass tolerance 0.01
  Using PPM mass tolerance10
  Using Line Search Alpha Beta 0.1 0.5
  Using Starting Step Size 0.01
  Using Max Line Search Count 20
  Using IPFP with Oscillatory Adjustment
  Using IPFP Converge Thresh 0.005
  Using IPFP Oscillatory Converge Thresh 0.999
file: |+
  energy0
  87.054687 4.071272337
  105.069174 0.9636028163
  136.07616 7.037977857 17 (7.038)
  160.076289 1.197298221
  178.084616 2.861739768
  223.106608 53.80100032 5 19 20 (53.752 0.04868 0.00076345)
  251.10173 21.90932756 11 18 15 16 (13.153 5.9659 1.7634 1.0273)
  297.107567 2.122976713 10 (2.123)
  384.140384 6.034804405 0 (6.0348)
  energy1
  energy2

  21
  0 384.1406897 NC(CO)C(O)=NC(CC1=CC=C(O)C=C1)C(O)=NC(CC(=O)O)C(=O)O
  1 324.1195603 [NH3+]C(=C=O)C(O)NC(=C=C1C=CC(=O)C=C1)C(O)NCC(O)O
  2 338.1352104 C=C(NC(O)C(=C=C1C=CC(O)C=C1)NC(O)C([NH3+])=C=O)C(O)O
  3 340.1508604 C=C(NC(O)C(=C=C1C=CC(O)C=C1)NC(O)C([NH3+])=CO)C(O)O
  4 367.1141406 OC#CC(O)[NH2+]C(=C=C1C=CC(O)C=C1)C(O)NC(=C=C(O)O)C(O)O
  5 223.1082673 [NH3+]C(=CO)C(O)NC=C=C1C=CC(O)C=C1
  6 338.1352104 [NH3+]C(=CO)C(O)NC(=C=C1C=CC(O)C=C1)C(O)NC#CC(O)O
  7 340.1508604 [NH3+]C(CO)C(O)NC(=C=C1C=CC(O)C=C1)C(O)NC#CC(O)O
  8 268.1297311 NC(O)C(=C=C1C=CC(O)C=C1)NC(O)C([NH3+])=CO
  9 366.130125 [NH3+]C(=C=O)C(O)NC(=C=C1C=CC(O)C=C1)C(O)NC(=C=CO)C(O)O
  10 297.1086613 [NH3+]C(=C=C1C=CC(O)C=C1)C(O)NC(=C=C(O)O)C(O)O
  11 251.103182 [NH3+]C(=C=O)C(O)NC(=C=C1C=CC(O)C=C1)CO
  12 253.118832 [NH3+]C(=CO)C(O)NC(=C=C1C=CC(O)C=C1)CO
  13 366.130125 [NH3+]C(=C=O)C(O)NC(=C=C1C=CC(O)C=C1)C(O)NC(=C=C(O)O)CO
  14 354.130125 C#CC(C#C)=C=C(NC(O)C([NH3+])CO)C(O)NC(=C=C(O)O)C(O)O
  15 251.103182 C=C(NC(O)C([NH3+])=C=C1C=CC(=O)C=C1)C(O)O
  16 251.103182 NC(O)C(=C=C1C=CC(O)C=C1)[NH2+]C(O)C#CO
  17 136.0762389 [NH3+]C=C=C1C=CC(O)C=C1
  18 251.103182 [NH3+]C(=C=C1C=CC(O)C=C1)C(O)NC#CC(O)O
  19 223.1082673 C#CC(C#C)=C=C(CO)NC(O)C([NH3+])CO
  20 223.1082673 [NH3+]CC(O)NC(=C=C1C=CC(=O)C=C1)CO

  0 1 C=C(O)O
  0 2 O=CO
  0 3 O=C=O
  0 4 N
  0 5 OC=NC(=C=C(O)O)C(O)O
  0 6 O=CO
  0 7 O=C=O
  0 8 O=C(O)C#CC(O)O
  0 9 O
  0 10 NC(=C=O)CO
  0 11 NC(=C=C(O)O)C(O)O
  0 12 NC(=C=C(O)O)C(=O)O
  0 13 O
  0 14 C=O
  1 5 O=CN=C=C(O)O
  1 11 N#CC(O)O
  2 5 C=C(N=C=O)C(O)O
  2 15 NC(=C=O)CO
  2 11 C=C(N)C(=O)O
  3 5 C=C(N=CO)C(O)O
  3 15 NC(=CO)CO
  3 11 C=C(N)C(O)O
  4 16 O=C(O)C#CC(O)O
  4 10 O=C=C=CO
  5 17 NC(=C=O)CO
  6 5 O=C(O)C#CNCO
  6 18 NC(=C=O)CO
  6 11 NC#CC(O)O
  7 5 OCNC#CC(O)O
  7 18 NC(=CO)CO
  7 11 NC=CC(O)O
  8 16 N
  8 5 N=CO
  8 11 N
  9 5 O=C=C=C(N=CO)C(O)O
  9 11 NC(=C=C=O)C(O)O
  10 15 O=CO
  10 17 OC=NC(=C=C(O)O)C(O)O
  10 18 O=CO
  12 5 C=O
  12 19 C=O
  13 5 O=C=NC(=C=C(O)O)CO
  13 11 NC(=C=C(O)O)C=O
  14 19 NC(=C=C(O)O)C(=O)O
  1 12 N#CC(=O)O
  2 8 C#CC(=O)O
  3 8 C#CC(O)O
  6 8 C#CC(=O)O
  6 12 NC#CC(=O)O
  7 8 C#CC(O)O
  7 12 NC#CC(O)O
  9 1 C#CO
  9 8 O=C=C=C=C(O)O
  9 12 NC(=C=C=O)C(=O)O
  12 20 C=O
  13 8 O=CC#CC(=O)O
  3 12 C=C(N)C(=O)O
