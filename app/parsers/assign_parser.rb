# Parses data from the assignment output text and turns it into a 
# hash so we can use it in the javascript and view
#
# The hash looks like:
#
# { "energy1" => [
#        {x: 1.0, y: 3.0, structures: [4,5,16], scores: [0.5 0.7 0.2]  },
#        {x: 2.0, y: 5.0, structures: [4,12,10], scores: [0.1 0.5 0.9] }
#     ]
#  }
#
class AssignParser < Parser
  def parse!
    # Split the data on two new lines to separate the spectra and the structures
    results = read_data.split(/\n\n/)
    data_string = results[0]
    structures = results[1]
    structure_list = {}
    fragments = results[2]
    fragments_list = []

    if structures.present? # We only have these if the spectra is annotated
      structures.split(/\n/).each do |line|
        # Make a hash of structure_id => inchi
        if line =~ /^(\d+)\s+([-+]?[0-9]*\.?[0-9]+)\s+(\S+)$/
          structure_list[$1] = [$2, $3]
        end
      end
    end

    if fragments.present?
      fragments.split(/\n/).each do |line|
        # Make a hash of structure_id => inchi
        if line =~ /^(\d+)\s+(\d+)\s+(\S+)$/
          fragments_list << [structure_list[$1][1], structure_list[$2][1], $3]
        end
      end
    end

    # Split the data on the title energy1, energy2, ... etc.
    parsed_data = data_string.split(/(\n*[A-Za-z]+[\d]*\s*\n)/i).
      # Strip the empty space
      map(&:strip).
      # Filter out empty chunks
      select{|l|!l.empty?}

    # Assign consistent energy level labels
    parsed_data[0] = "energy0"
    # Insert a blank item if there is no spectra data for an energy level
    if parsed_data[1] !~ /[-+]?[0-9]*\.?[0-9]+\s+[-+]?[0-9]*\.?[0-9]+/
      parsed_data.insert(1, "")
    end
    parsed_data[2] = "energy1"
    if parsed_data[3] !~ /[-+]?[0-9]*\.?[0-9]+\s+[-+]?[0-9]*\.?[0-9]+/
      parsed_data.insert(3, "")
    end
    parsed_data[4] = "energy2"
    if parsed_data[5] !~ /[-+]?[0-9]*\.?[0-9]+\s+[-+]?[0-9]*\.?[0-9]+/
      parsed_data.insert(5, "")
    end

    # Turn the split data into a hash like: {"energy1" => data, "energy2" => data }
    hashed_data = Hash[ *parsed_data ].delete_if { |k, v| v.empty? }

    # Parse the data for each energy level
    hashed_data.keys.each do |key|
      spectrum_data = hashed_data[key]

      # Split the data on newlines
      hashed_data[key] = spectrum_data.split("\n").map do |peak|
        # Split scores from spectra and fragments
        divided = peak.strip.split("(")
        # Split spectra and fragments
        peak_data = divided[0].strip.split("\s")
        # If there are scores, put them into and array
        if divided[1].present?
          scores = divided[1].chomp(")").strip.split("\s")
        else
          scores = []
        end

        x = peak_data.shift.to_f
        y = peak_data.shift.to_f

        structure_urls = peak_data.map { |id|
          if (s = Structure.find_by_smiles(structure_list[id][1])).present?
            s.image.url
          else
            ""
          end
        }

        # Turn the data into a hash
        {x: x, y: y, structure_ids: peak_data, structure_urls: structure_urls, scores: scores }
      end
    end

    # set the data for the parser object
    @data = {spectra: hashed_data, structures: structure_list, fragments: fragments_list}
  end
end
