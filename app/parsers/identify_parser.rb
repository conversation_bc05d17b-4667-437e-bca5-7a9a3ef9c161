# Parses data from the identify input text and turns it into a 
# hash so we can use it in the javascript and view
#
# The hash looks like:
#
# { "energy1" => [
#        {x: 1.0, y: 3.0, structures: [4,5,16], scores: [0.5 0.7 0.2]  },
#        {x: 2.0, y: 5.0, structures: [4,12,10], scores: [0.1 0.5 0.9] }
#     ]
#  }
#

class IdentifyParser < Parser
  def read_data
    open(@query.input_file.path).read
  end

  # Reads the spectra from the input file
  # Need to allow both peak list and .msp format
  def parse!
    
    if File.extname(@query.input_file_file_name) == ".msp"
      parsed_data = []
      # Find the spectra
      spectra_blocks = read_data.split(/\n\n/)
      spectra_blocks.each do |block|
        if block =~ /($|\n)ID: #{@query.print_spectra_id}\n/
          # Remove all lines except peak list
          peak_list = block.gsub(/[\S\s]+:\s*.+\n/, "")
          parsed_data[0] = "energy0"
          parsed_data[1] = peak_list
          break
        end
      end

      if !parsed_data[0].present?
        parsed_data[0] = "energy0"
        parsed_data[1] = ""
      end

      # These two are empty
      parsed_data[2] = "energy1"
      parsed_data.insert(3, "")
      parsed_data[4] = "energy2"
      parsed_data.insert(5, "")
    else
      # Split on energy levels
      puts read_data
      parsed_data = read_data.gsub(/\r/,"").split(/(\n*[A-Za-z]+[\d]*\s*\n*)/i).map(&:strip).select{|l|!l.empty?}
      # Assign consistent energy level labels
      parsed_data[0] = "energy0"
      # Insert a blank item if there is no spectra data for an energy level
      if parsed_data[1] !~ /[-+]?[0-9]*\.?[0-9]+\s+[-+]?[0-9]*\.?[0-9]+/
        parsed_data.insert(1, "")
      end
      parsed_data[2] = "energy1"
      if parsed_data[3] !~ /[-+]?[0-9]*\.?[0-9]+\s+[-+]?[0-9]*\.?[0-9]+/
        parsed_data.insert(3, "")
      end
      parsed_data[4] = "energy2"
      if parsed_data[5] !~ /[-+]?[0-9]*\.?[0-9]+\s+[-+]?[0-9]*\.?[0-9]+/
        parsed_data.insert(5, "")
      end
    end

    hashed_data = Hash[*parsed_data].delete_if { |k, v| v.empty? }
    hashed_data.keys.each do |key|
      spectrum_data = hashed_data[key]
      hashed_data[key] = spectrum_data.split("\n").map do |peak|
        peak_data = peak.strip.split("\s").map(&:to_f)
        if @query.nl_spectrum
          x_peak = @query.parent_ion_mass.to_f - peak_data[0].to_f
          if x_peak < 0.0
            x_peak = 0.0
          end
          {x: x_peak, y: peak_data[1].to_f}
        else 
          {x: peak_data[0].to_f, y: peak_data[1].to_f}
        end
      end
    end
    @data = hashed_data
  end
end
