class GenerateMolExcelJob < ActiveJob::Base
  queue_as :default
  # sidekiq_options retry: false
  # after_perform :notify_manager
  include Shared::NaturalProductLoader
 
  require 'fileutils'
  require 'open-uri'
  require 'zip'
  require 'csv'


  def perform(backend_dir,nmr_pred_dir,draw_mol_script_basename,backend_batch_dir,excel_generator_script_with_path,fpath,project_name,list_of_input_files_for_excel_generator,batch_values,batch_upload_user_id,batch_upload_id,batch_directory,batch_submission_id)
    # Do something
    @backend_dir = backend_dir
    @nmr_pred_dir = nmr_pred_dir
    @draw_mol_script_basename = draw_mol_script_basename
    @backend_batch_dir = backend_batch_dir
    @excel_generator_script_with_path = excel_generator_script_with_path
    @fpath = fpath
    puts("@fpath in job = #{@fpath}")
    @project_name = project_name
    @list_of_input_files_for_excel_generator = list_of_input_files_for_excel_generator
    puts("@list_of_input_files_for_excel_generator in job = #{@list_of_input_files_for_excel_generator}")
    @batch_values = batch_values
    @batch_upload_user_id = batch_upload_user_id
    @batch_upload_id = batch_upload_id
    @batch_directory = batch_directory
    @batch_submission_id = batch_submission_id
    @var = 1
    puts("@list_of_input_files_for_excel_generator  in job = #{@list_of_input_files_for_excel_generator }")
    CSV.open("#{@list_of_input_files_for_excel_generator}", "wb") do |csv|  
      @batch_values.each do |b_v|
        @smiles = b_v[1]
        # save natural product here
        c_name = b_v[0]
        if c_name.match(/_/)
            c_name = c_name.gsub!(/_/, ' ')
        end

        @natural_product, newly_created = load_create_natural_product(nil, c_name, b_v[1])
        
        batch_submission = BatchSubmission.find(@batch_submission_id)
        
        chemical_shift_submission = ChemicalShiftSubmission.create(:user_id => @batch_upload_user_id, :user_session_id => batch_submission.user_session_id, :natural_product_id => @natural_product.id, :batch_submission_id => @batch_submission_id.to_i, :valid => 0)
        chemical_shift_submission.save
        
        chemical_shift_submission.create_chemical_shift_submission_meta_data(:chemical_shift_submission_id => chemical_shift_submission.id, :solvent => b_v[2], :spectrum_type => b_v[3])
        #creating mol, imgae and prediction file
        @path_to_temp_model, @path_to_temp_image, @path_to_csv, @compound_name = BatchUpload.DrawMol(@smiles,@batch_directory,@backend_dir,@nmr_pred_dir,@draw_mol_script_basename,@batch_upload_id,b_v[0],@batch_upload_user_id,@var)
        #creating mol, imgae and prediction file
        
        csv<<["#{@compound_name}","#{@path_to_csv}","#{@path_to_temp_image}"]
        @var = @var + 1
      end
    end
    puts("@var = #{@var}")
    
    begin
      BatchUpload.GenerateExcel(@project_name,@excel_generator_script_with_path, @list_of_input_files_for_excel_generator, @batch_directory,@batch_upload_id)
    rescue
      puts("rescue reached in GenerateExcel")
    end
    @email = User.find(@batch_upload_user_id).email
    puts("email = #{@email}")
    begin
      Notifier.finished_job(@email,@project_name.split("_")).deliver
    rescue
      puts("rescued reached")
    end
    # email = User.find(@batch_upload_user_id).email
    # puts("@batch_upload_user_id).email = #{email}")
    # Notifier.job_done(User.find(@batch_upload_user_id).email,@project_name.split("_")[1]).deliver
  end

  # private
  # after_perform do |j|
  #   notify_manager
  # end

  # def notify_manager
  #   puts("inside notify manager")
  #   Notifier.job_done(User.find(@batch_upload_user_id.email),@project_name.split("-")[1]).deliver
  # end
end
