class Notifier < ActionMailer::Base
  default :from => "<EMAIL>"

  
  # Subject can be set in your I18n file at config/locales/en.yml
  # with the following lookup:
  #
  #   en.notifier.password_reset_instructions.subject
  #
  def deliver_password_reset_instructions(user)
    @edit_password_reset_url = edit_password_reset_url(user.perishable_token)

    mail :to => user.email, :subject => "Password Reset Instructions"
  end
  
  def account_verification_instructions(user)
    @confirm_account_url = user_verification_url(user.perishable_token)
    
    mail :to => user.email, :subject => "Validate your details"
  end
  
  def finished_job(email,file_name)
    @file_name = file_name
    mail :to => email, :subject => "Bulk Offline Upload Status"
  end

end