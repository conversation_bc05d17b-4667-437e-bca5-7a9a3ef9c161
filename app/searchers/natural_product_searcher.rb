class NaturalProductSearcher
  include Unearth::Searcher::Searcher
  
  search_index "natural_products"
  search_with :name, :basic, :fuzzy_like_this, 
    name_fields: [:name, :synonyms],
    fuzzy_fields: [:name, :synonyms]
  
  def self.exact_match_redirect_path(query)
    if /\ANP\d+\z/.match(query.upcase)
      Rails.application.routes.url_helpers.natural_product_path(query.upcase)
    elsif CasValidator.cas_valid?(query)
      natural_product = NaturalProduct.exported.where(cas: query)
      # CAS is not necessarily unique, only jump to entry if only 1 hit
      if natural_product.count == 1 
        Rails.application.routes.url_helpers.natural_product_path(natural_product.take.to_param)
      else
        nil
      end
    else
      nil
    end
  end 

  def self.highlights
    { np_mrd_id: { },
      name: {},
      synonyms: { number_of_fragments: 3 },
      cas: { },
      iupac: { },
      description: { number_of_fragments: 3 } }
  end

  def self.suggestions(query)
    candidates = [
        {
          field: "synonyms",
          suggest_mode: "popular",
          min_word_len: 1
        },
        {
          field: "description",
          suggest_mode: "popular",
          min_word_len: 2
        }
    ]

    self.index_class.suggestions(query, 'name', candidates)
  end
end
