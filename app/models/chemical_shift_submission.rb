class ChemicalShiftSubmission < ActiveRecord::Base
  require 'csv'
  require 'fileutils'
  belongs_to :natural_product
  has_one :chemical_shift_submission_meta_data, dependent: :destroy
  accepts_nested_attributes_for :chemical_shift_submission_meta_data
  has_many :chemical_shifts, dependent: :destroy
  accepts_nested_attributes_for :chemical_shifts
  belongs_to :batch_submission

  def set_user(current_user,current_user_session)
      self.user_id = current_user.id
      self.user_session_id = current_user_session.id
  end

  def finished?
    if self.valids
      return true
    else
      self.valid = false
      return false
    end
  end

  def self.ReadAtoms(mol_csv)
    atom = []
    symbol = []
    lines = []
    c_p = [[]]
    CSV.open(mol_csv, 'r', :col_sep => ",", :quote_char => "\"").each do |row|
      lines << row
    end
    lines.delete_at(0)
    print("lines = #{lines}")


    return lines
  end

  def self.new_atom_order(c_shift_submission_id)
    cs = ChemicalShiftSubmission.find(c_shift_submission_id.to_i)
    np = NaturalProduct.find((cs.natural_product_id).to_i)
    original_atom_ids = []
    custom_atom_ids = []
    temp_mol_basename_out="#{cs.id}" + "_#{np.np_mrd_id}_" + "#{cs.user_id}" + "_temp_3D.mol"
    threeD_mol_url = Rails.root.join("public","downloads","#{cs.user_session_id}","#{temp_mol_basename_out}")
    puts("threeD_mol_url = #{threeD_mol_url}")

    stdin,stdout,stderr = Open3.popen3("#{PYTHON_ENV["#{Rails.env}"]['python_path']} #{Rails.root}/public/python/number_of_atoms.py '#{threeD_mol_url}'")
    atom_length = stdout.gets(nil).to_s
    puts("atom_length = #{atom_length}")
    # atom_length = 4 ####### python program to fetch original atom length
    original_order = (0..atom_length.to_i-1).to_a
    puts("original_order = #{original_order}")
    cs.chemical_shifts.each do |s|
      original_atom_ids.push(s.atom_id)
      puts("original_atom_ids = #{original_atom_ids}")
      custom_atom_ids.push(s.custom_atom_id)
      puts("custom_atom_ids = #{custom_atom_ids}")
    end
    if original_atom_ids == custom_atom_ids
      puts("original ids and custom ids are equal")
      return original_order
    else
       puts("original_order = #{original_order}")
      for i in (0..(original_atom_ids.length-1)) do
        puts("i = #{i}")
        original_atom_ids[i] = original_atom_ids[i].to_i-1
        custom_atom_ids[i] = custom_atom_ids[i].to_i-1
      end
      puts("original_atom_ids = #{original_atom_ids}")
      puts("custom_atom_ids = #{custom_atom_ids}")
      for y in custom_atom_ids do
        index_of_y = custom_atom_ids.index(y)
        x_of_index_of_y = original_atom_ids[index_of_y]
        # index_of_o = original_order.index(x_of_index_of_y)
        # original_order[index_of_o] = y
        original_order[x_of_index_of_y] = y
        #original_order[y] = x_of_index_of_y
      end
      puts("after reordering original_order = #{original_order}")
      return original_order
    end
  end





  def self.Generate_Reference_Pubmed(css_id)
    css = ChemicalShiftSubmission.find(css_id)
    np = NaturalProduct.find(css.natural_product_id)
    if css.chemical_shift_submission_meta_data.literature_reference_type == "PMID"
      if np.articles.where(pubmed_id: css.chemical_shift_submission_meta_data.literature_reference).blank?
        begin
          article = CiteThis::Article.where(pubmed_id: css.chemical_shift_submission_meta_data.literature_reference.to_i).first_or_create
          if article.persisted?
            np.articles << article
          end
        rescue
          np.errors[:description] << "contains invalid PubMed reference (#{css.chemical_shift_submission_meta_data.literature_reference.to_i})"
          raise ActiveRecord::Rollback
        end
      end
      np.save!
    else
      puts"reference was not PubMed/PMID is already saved in articles"
    end
  end

  def self.Download_file(chemical_shift_submission)
    chemical_shifts = chemical_shift_submission.chemical_shifts
    np_product = NaturalProduct.find(chemical_shift_submission.natural_product_id)
    file_name = "#{np_product.np_mrd_id}"+"_#{chemical_shift_submission.user_id}_#{chemical_shift_submission.id}.csv"
    CSV.open(Rails.root.join('public','downloads',file_name).to_s, "wb") do |csv|
      csv << ["Compound Name: #{np_product.name}"]
      csv << ["NP-MRD ID: #{np_product.np_mrd_id}"]
      csv << ["Literature Reference: #{chemical_shift_submission.chemical_shift_submission_meta_data.literature_reference_type}: #{chemical_shift_submission.chemical_shift_submission_meta_data.literature_reference}"]
      csv << ["Provenance: #{chemical_shift_submission.chemical_shift_submission_meta_data.provenance}"]
      csv << ["Genus: #{chemical_shift_submission.chemical_shift_submission_meta_data.genus}"]
      csv << ["Species: #{chemical_shift_submission.chemical_shift_submission_meta_data.species}"]
      csv << ["Physical State of Compound: #{chemical_shift_submission.chemical_shift_submission_meta_data.physical_state_of_compound}"]
      csv << ["Melting Point: #{chemical_shift_submission.chemical_shift_submission_meta_data.melting_point}"]
      csv << ["Boiling Point: #{chemical_shift_submission.chemical_shift_submission_meta_data.boiling_point}"]
      csv << ["Solvent: #{chemical_shift_submission.chemical_shift_submission_meta_data.solvent}"]
      csv << ["Spectrum Type: #{chemical_shift_submission.chemical_shift_submission_meta_data.spectrum_type}"]
      csv << ["Spectrometer Frequency: #{chemical_shift_submission.chemical_shift_submission_meta_data.spectrometer_frequency}"]
      csv << ["Temperature: #{chemical_shift_submission.chemical_shift_submission_meta_data.temperature}"]
      csv << ["Chemical Shift Reference: #{chemical_shift_submission.chemical_shift_submission_meta_data.chemical_shift_standard}"]
      csv << ["Atom Type","Atom No"," Custom Atom No", "Predicted Shift(ppm)","Actual Shift(ppm)","Predicted Multiplet Type","Actual Multiplet Type","Predicted J coupling(Hz)","Actual J coupling(Hz)"]
      for i in chemical_shifts do
        data_row = ["#{i.atom_symbol}","#{i.atom_id}","#{i.custom_atom_id}","#{i.chemical_shift_pred}","#{i.chemical_shift_true}","#{i.multiplet_pred}","#{i.multiplet_true}","#{i.jcoupling_pred}","#{i.jcoupling_true}"]
        csv << data_row
      end
    end
    return file_name
  end



def self.save_user_table(chemical_shift_submission,file_path="NONE")
  chemical_shifts = chemical_shift_submission.chemical_shifts
  puts("Running function save_user_table()")

  CSV.open(Rails.root.join(file_path).to_s, "wb") do |csv|
    for i in chemical_shifts do
      data_row = ["#{i.atom_symbol}","#{i.atom_id}","#{i.chemical_shift_true}","#{i.multiplet_true}","#{i.jcoupling_true}"]
      csv << data_row
    end
  end
  return
end

def self.save_assignment_report(chemical_shift_submission,file_path="NONE")
  chemical_shifts = chemical_shift_submission.chemical_shifts
  information_file = Rails.root.join("backend","magmet/assignment_score_note.txt")
  File.open(information_file) do |text|
    File.open(Rails.root.join(file_path).to_s, "wb", col_sep: "\t") do |csv|
      IO.copy_stream(text,csv)
      csv << "\r"
      header = ["Atom Type","Atom No.", "Shift (ppm) ","Multiplet Type","J-Coupling ( Hz )","Assignment Level","Assignment Quality Score"].map {|fn| fn.center(20)}.join("\t")
      header_formatted = []
      header_length = []
      header = ["Atom Type","Atom No.", "Shift (ppm) ","Multiplet Type","J-Coupling ( Hz )","Assignment Level","Assignment Quality Score"]

      header.each do |i|
        h_length = i.length+4
        header_f = i.center(h_length)
        header_length.push(h_length)
        header_formatted.push(header_f)
      end
      csv << header_formatted.join(" ")
      csv << "\r"

      for i in chemical_shifts do
        data_row = ["#{i.atom_symbol}".center(header_length[0]),
                    "#{i.atom_id}".center(header_length[1]),
                    "#{i.chemical_shift_true}".center(header_length[2]),
                    "#{i.multiplet_true}".center(header_length[3]),
                    "#{i.jcoupling_true}".center(header_length[4]),
                    "#{i.assignment_level}".center(header_length[5]),
                    "#{i.assignment_score}".center(header_length[6])].join(" ")

        csv << data_row
        csv << "\r"
      end
    end
  end
  return
end



#def self.Metrices(chemical_shift_submission_id)
#
#    total_number_of_atoms = ChemicalShiftSubmission.find(chemical_shift_submission_id.to_i).chemical_shifts.count
#    all_atoms = ChemicalShiftSubmission.find(chemical_shift_submission_id.to_i).chemical_shifts
#    no_of_atoms_to_be_submitted = 0
#    for i in all_atoms do
#      if i.atom_symbol == "H" or i.atom_symbol == "C" or i.atom_symbol == "N"
#        no_of_atoms_to_be_submitted = no_of_atoms_to_be_submitted+1
#      else
#       next
#      end
#    end
#    n_of_assigned_true_atoms = ChemicalShift.where("chemical_shift_submission_id" => chemical_shift_submission_id.to_i).where.not( "chemical_shift_true" => "NA").where("atom_symbol" => "H" || "C" || "N").count
#    percentage_of_assigned_atoms = (Float(n_of_assigned_true_atoms)/Float(no_of_atoms_to_be_submitted))*100
#    puts("percentage_of_assigned_atoms = #{percentage_of_assigned_atoms}")



    # n_of_assigned_true_atoms = ChemicalShift.where("chemical_shift_submission_id" => 454).where.not( "chemical_shift_true" => "NA").where("atom_symbol" => "H" || "C" || "N").count

    # n_of_assigned_predicted_atoms = ChemicalShift.where("chemical_shift_submission_id" => chemical_shift_submission_id).where.not( "chemical_shift_pred" => "NA").count
    # n_of_assigned_true_atoms = ChemicalShift.where("chemical_shift_submission_id" => chemical_shift_submission_id).where.not( "chemical_shift_true" => "NA").count
    # percentage_of_assigned_atoms = ((n_of_assigned_true_atoms.to_f/n_of_assigned_predicted_atoms.to_f)*100).to_f
    # puts("percentage_of_assigned_atoms = #{percentage_of_assigned_atoms}")

    # n_of_assigned_predicted_jcoupling = ChemicalShift.where("chemical_shift_submission_id" => chemical_shift_submission_id).where.not( "jcoupling_pred" => "NA").count
    # n_of_assigned_true_jcoupling = ChemicalShift.where("chemical_shift_submission_id" => chemical_shift_submission_id).where.not( "jcoupling_true" => "NA").count
    # percentage_of_assigned_jcoupling = ((n_of_assigned_true_jcoupling.to_f/n_of_assigned_predicted_jcoupling.to_f)*100).to_f
    # puts("percentage_of_assigned_jcoupling = #{percentage_of_assigned_jcoupling}")

    # n_of_assigned_predicted_multiplet = ChemicalShift.where("chemical_shift_submission_id" => chemical_shift_submission_id).where.not( "multiplet_pred" => "NA").count
    # puts("n_of_assigned_predicted_multiplet = #{n_of_assigned_predicted_multiplet}")
    # n_of_assigned_true_multiplet = ChemicalShift.where("chemical_shift_submission_id" => chemical_shift_submission_id).where.not( "multiplet_true" => "NA").count
    # puts("n_of_assigned_true_multiplet = #{n_of_assigned_true_multiplet}")
    # percentage_of_assigned_multiplet = ((n_of_assigned_true_multiplet.to_f/n_of_assigned_predicted_multiplet.to_f)*100).to_f
    # puts("percentage_of_assigned_multiplet = #{percentage_of_assigned_multiplet}")

    # atoms = ChemicalShift.where("chemical_shift_submission_id" => chemical_shift_submission_id)


    # good_true_values = 0
    # atoms.each do |atom|
    #   if atom.chemical_shift_true != "NA" and (atom.chemical_shift_pred.to_f - atom.chemical_shift_true.to_f).abs < 0.20
    #     good_true_values = good_true_values+1
    #   end
    # end
    # percentage_of_good_true_values = (good_true_values/n_of_assigned_true_atoms.to_f)*100

#    return percentage_of_assigned_atoms.round(2), percentage_of_assigned_jcoupling.round(2), percentage_of_assigned_multiplet.round(2), percentage_of_good_true_values.round(2)
#end

# def self.name_species_litererature_ref?(c_sub,np_name,species,literature_ref)
#   if !np_name.present? && !species.present? && !literature_ref.present?
#     c_sub.errors.add(:natural_product_id, "None of the field can be empty")
#     puts"#{c_sub.errors.first}"
#   end
# end

end
