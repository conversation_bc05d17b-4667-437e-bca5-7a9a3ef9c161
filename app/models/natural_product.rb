class NaturalProduct < ActiveRecord::Base
  include XmlConversion
  include SdfConversion
  include CiteThis::Referencer


  has_structure resource: 'NP-MRD',
                finder: :find_by_moldb_ids,
                id_field: :np_mrd_id,
                mass_scope: :moldb_filtered_scope,
                prefilter: :moldb_searchable_ids

  has_spectra # Associate the natural product with spectra through SpecDBi

  # Aliases for moldb fields (perhaps add this to moldbi)
  alias_attribute :chemical_formula, :moldb_formula
  alias_attribute :smiles, :moldb_smiles
  alias_attribute :average_mass, :moldb_average_mass
  alias_attribute :mono_mass, :moldb_mono_mass
  alias_attribute :traditional_iupac, :moldb_traditional_iupac
  alias_attribute :iupac, :moldb_iupac
  alias_attribute :inchi, :moldb_inchi
  alias_attribute :inchikey, :moldb_inchikey

  delegate :water_solubility, :water_solubility_reference, :logp,
    :logp_reference,
    :melting_point, :melting_point_reference, :boiling_point,
    :boiling_point_reference,
    to: :experimental_property_set, prefix: :experimental

  has_one :experimental_property_set, dependent: :destroy
  accepts_nested_attributes_for :experimental_property_set

  has_many :accession_numbers, dependent: :destroy, as: :element
  accepts_nested_attributes_for :accession_numbers, allow_destroy: true

  has_many :submissions
  has_many :chemical_shift_submissions
  has_many :species_mappings, as: :species_mappable, dependent: :destroy
  has_many :species, -> { order "species.scientific_name" }, through: :species_mappings
  has_one :sdf_location
  has_many :nmr_preds

  scope :exported, -> { where(export: 1) }
  scope :not_exported, -> { where(export: 0) }
  
  validates :name, presence: true, length: {minimum: 4, maximum: 255}, uniqueness: true
  validates :np_mrd_id, presence: true, format: { with: /\ANP\d{7}\z/, message: "Must be in the format NP0000001" }
  validates :cas, cas: true
  has_attached_file :thumb
  validates_attachment_content_type :thumb, content_type: "image/png"

  before_validation :increment_np_mrd_id, on: :create
  after_initialize :init_has_ones
  after_save :extract_description_references

  def label
    "#{self.name} (#{self.np_mrd_id})"
  end

  def to_param
    self.np_mrd_id
  end

  def load_thumb_from_url(url)
    self.thumb = URI.parse(url).open
  end

  def valid_thumb?
    return self.thumb.exists?
  end

  def get_description
    description = self.cs_description
    ###### uncommit later ##########
    # description = nil
    # if description && (self.description.length < 400 || (description.length/self.description.length) > 1.95)
    #   return description
    # end
    # return self.description
    #######################
    #### for now we are not checking description length ####
    if description
      return description.gsub(/([a-z])([^.?!:]*)/i) { $1.upcase + $2.rstrip }
    elsif self.description
      return self.description.gsub(/([a-z])([^.?!:]*)/i) { $1.upcase + $2.rstrip }
    else
      return ""
    end
  end

  def moldb_alogps_solubility
    value = self.read_attribute(:moldb_alogps_solubility)
    return if value.blank?
    value = value.to_f
    if value < 0.001
      "%.3E" % value
    else
      value.smart_round_to_s
    end.to_s + " g/L"
  end

  def title
    "#{self.np_mrd_id} (#{self.name})"
  end

  def to_s
    self.title
  end

  # Set the NP-MRD ID to the next available ID based on the natural product entries that already exist. This
  # runs pre-validation, and only sets the NP-MRD ID if it hasn't been set explicitely.
  def increment_np_mrd_id
    if self.np_mrd_id.blank?
      self.np_mrd_id = NaturalProduct.exists? ? "NP" + NaturalProduct.last.np_mrd_id[2..-1].next : 'NP0000001'
    end
  end

  def mol_file
    self.has_structure? ? self.structure_resource.mol : nil
  end

  def load_threeDmol
    return self.threeDmol if self.threeDmol
    generate_threeDmol
  end

  def generate_threeDmol
    error = false
    mol = nil
    message = nil
    begin
      stdin,stdout,stderr = Open3.popen3("#{PYTHON_ENV["#{Rails.env}"]['python_path']} #{Rails.root}/public/python/rdkit_smiles_to_mol.py '#{self.smiles}'")
      mol = stdout.gets(nil).to_s
      puts mol
    rescue Exception => e
      message = e.message
      error = true
    end
    self.threeDmol = mol
    self.save!
    self.threeDmol
  end

  def tmp_mol_file
    #return @tmp_mol_file if @tmp_mol_file
    if mol_file
      file = Tempfile.new('mol_file',"#{Rails.root}/app/assets/")
      file.path      # => A unique filename in the OS's temp directory,
                     #    e.g.: "/tmp/foo.24722.0"
                     #    This filename contains 'foo' in its basename.
      file.write(mol_file)
      @tmp_mol_file = file.path
    end
    puts @tmp_mol_file
    return @tmp_mol_file
  end

  def exported?
    export?
  end

  # Get species mappings ordered by species' scientific name and source
  def ordered_species_mappings
    species_mappings.joins(:species).order("species.scientific_name, species_mappings.source")
  end

  # Get an array of MolDB spectrum summaries (for chemical shift submission alert)
  def moldb_nmr_spectra_summaries
    spectra_summaries = []

    if spectra.any?
      nmr_spectra = spectra.select { |s| s.is_a?(Specdb::NmrOneD) || s.is_a?(Specdb::NmrTwoD) }
      earliest_creation_date = nmr_spectra.map{ |s| s.created_at.to_date }.sort.first.strftime("%Y-%m-%d")
      last_update_date = nmr_spectra.map{ |s| s.updated_at.to_date }.sort.last.strftime("%Y-%m-%d")

      nmr_spectra.each do |spectrum|
        spectrum_summary = "Expt. 1H assignment"

        # Frequency
        if spectrum.frequency
          spectrum_summary += " @#{spectrum.frequency}"
        end

        # Solvent
        if spectrum.solvent
          spectrum_summary += ", #{spectrum.solvent} solvent"
        end

        # Spectrum Type
        if spectrum.is_a?(Specdb::NmrOneD)
          nucleus = spectrum.nucleus
        else spectrum.is_a?(Specdb::NmrTwoD)
          nucleus = spectrum.nucleus_y
        end
        spectrum_summary += " (1D-#{nucleus})"

        # Store in array
        spectra_summaries.push(spectrum_summary)
      end
      return spectra_summaries, earliest_creation_date, last_update_date
    end
    return spectra_summaries, nil, nil
  end

  def self.from_param(id)
    self.where(np_mrd_id: id).take!
  end

  # Find records based on the matching IDs returned from a
  # MolDB structure search
  def self.find_by_moldb_ids(ids)
    self.exported.where(np_mrd_id: ids).uniq
  end

  # The list of IDs that are passed to the MolDB server when a search is
  # sent
  def self.moldb_searchable_ids(filters={})
    self.exported.pluck(:np_mrd_id)
  end

  def self.moldb_filtered_scope(filters={})
    self.exported
  end

  # For specdbi as it expects that the inchi_key does not have InChIKey at the start
  def self.find_by_inchi_key(inchi_key)
    self.where(moldb_inchikey: "InChIKey=#{inchi_key.strip}").take
  end

  # Finder to find by NP-MRD ID (avoids method missing - we use this in many places)
  def self.find_by_np_mrd_id(np_mrd_id)
    self.where(np_mrd_id: np_mrd_id).take
  end

  protected

  # Initialize in the model since the these models don't make sense outside
  # of a relationship with Natural Product
  def init_has_ones
    if self.new_record?
      self.experimental_property_set ||= self.build_experimental_property_set
    end
  end

  def self.to_csv
    attributes = %w{NP-MRD_ID NAME SMILES}
    CSV.generate(headers: true) do |csv|
      csv << attributes
      all.each do |natural_product|
        csv << attributes.map{ |attr| natural_product.send(attr.downcase)}
      end
    end
  end

  # Look for newly added PubMed references in the description text. If there
  # are new ones, add them to the general references.
  def extract_description_references
    if self.description_changed?
      matches = self.description.scan(/PMID:?\s*([\d\,\s]+)/i)
      if matches.present?
        matches.each do |match|
          match.first.split(/,\s*/).each do |id|
            next if id.blank?

            if self.articles.where(pubmed_id: id).blank?
              article = CiteThis::Article.where(pubmed_id: id).first_or_create

              if article.persisted?
                self.articles << article
              else
                self.errors[:description] << "contains invalid PubMed reference (#{id})"
                raise ActiveRecord::Rollback
              end
            end
          end
        end
      end
    end
  end
end
