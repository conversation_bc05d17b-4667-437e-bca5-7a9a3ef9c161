class SubmissionMetaData < ActiveRecord::Base
	LIT_REF_TYPES = ['PMID', 'DOI', 'Book'].freeze
  auto_strip_attributes :genus, :species, :literature_reference, :temperature, squish: true

  belongs_to :submission
  # belongs_to :submission

  before_save :evaluate_literature_reference_type
  validates :literature_reference_type, inclusion: { in: LIT_REF_TYPES }

  def to_s
    "Expt. 1H assignment @#{spectrometer_frequency}, #{solvent} solvent (#{spectrum_type})"
  end

  def scientific_name
    "#{genus} #{species}"
  end

  private

  # Reassign mis-attributed literature reference types (PMID and DOI only)
  def evaluate_literature_reference_type
    # PMID
    is_number = true if Float(literature_reference) rescue false
    if is_number
      self.literature_reference_type = "PMID"
    end

    # DOI
    if literature_reference.match(/10.\d{4}.+/)
      self.literature_reference_type = "DOI"
    end
  end
end
