class RetentionIndexPrediction < ActiveRecord::Base
  require 'csv'
  require 'fileutils'
  validates :compound_smiles, presence: true


  def check_retention_index

    if self.compound_smiles.present?
      puts RetentionIndexCompound.select(Arel.star).where(RetentionIndexCompound.arel_table[:ri_compound_smiles].eq(self.compound_smiles)).present?
    end

    if !RetentionIndexCompound.where(ri_compound_smiles: self.compound_smiles).present?
      self.start_processing
    else
      puts "Inside else"
    end
  end

	def increment_retention_index_prediction_id
		if self.retention_index_prediction_id.blank?
			self.retention_index_prediction_id = RetentionIndexPrediction.exists? ? "RIP" + RetentionIndexPrediction.last.retention_index_prediction_id[3..-1].next : 'RIP0000001'
		end
	end


  def start_processing
    puts self.compound_name
    #cp=self.compound_smiles
	  nm = Regexp.escape(self.compound_name)
    nm = nm.gsub("'"){"\\'"}

    cp = self.compound_smiles
    st=self.compound_stationary_phase   #change the structure of the form
    #st="SemiStdNP"
    dt=self.compound_derivative_type
    st=st.parameterize("_")
    puts "dt = #{dt}"

    ###multiple argument script starts

    python_path=PYTHON_ENV["#{Rails.env}"]['python_path']

    script_path=Rails.root.join("public", "python", "non_der_ri_pred.py")

    puts "script = #{script_path}"

    script_command = ""
    script_command += "#{python_path} "
    script_command += "#{script_path} "

    script_command += "-input_smiles '#{cp}' "
    script_command += "-input_stat #{st} "
    script_command += "-input_dtype #{dt} "

    script_command += "-input_name #{nm} "

    puts "script_command = #{script_command}"
    stdin,stdout,stderr = Open3.popen3(script_command)



    if (dt=="TMS") or (dt=="TBDMS") or (dt=="TMS_AND_TBDMS")
      script_path=Rails.root.join("public", "python", "der_ri_pred.py")
    end
    puts "script = #{script_path}"

    script_command = ""
    script_command += "#{python_path} "
    script_command += "#{script_path} "

    script_command += "-input_smiles '#{cp}' "
    script_command += "-input_stat #{st} "
    script_command += "-input_dtype #{dt} "

    script_command += "-input_name #{nm} "

    puts "script_command = #{script_command}"

    ###multiple argument script ends

    stdin,stdout,stderr = Open3.popen3(script_command)
    # sleep 5

    # #sending the derivatized input to get their non-derivatized counterpart:
    # if (dt=="TMS") or (dt=="TBDMS") or (dt=="TMS_AND_TBDMS")
    #   script_path=Rails.root.join("public", "python", "non_der_ri_pred.py")
    #   script_command = ""
    #   script_command += "#{python_path} "
    #   script_command += "#{script_path} "
  
    #   #script_command += "-sid #{session_name} "
    #   #script_command += "-odir_path #{session_directory} "
  
    #   script_command += "-input_smiles '#{cp}' "
    #   script_command += "-input_stat #{st} "
    #   script_command += "-input_dtype #{dt} "

    #   script_command += "-input_name #{nm} "
  
    #   #script_command += "-cs_path #{output_csv_path} "
  
    #   #script_command += ">> #{python_log_abs_path} "
  
    #   puts "script_command = #{script_command}"
    #   stdin,stdout,stderr = Open3.popen3(script_command)
    # end
    
    puts stdout.gets(nil).to_s
    puts stderr.gets(nil).to_s

    #puts stdout.gets(nil).to_s
    #puts stderr.gets(nil).to_s
    #atom_length = stdout.gets(nil).to_s
    #atom_length2 = stderr.gets(nil).to_s
    #puts("atom_length = #{atom_length}")
    #puts("atom_length2 = #{atom_length2}")
    #organize the inputs that is needed to be passed as parameter
    #call the open.open3.popen3 and pass the above parameters as input to the designated python script
    #retrieve the values.
  end

  def set_user
      self.user_id = current_user.id
      self.user_session_id = current_user_session.id
  end

  def self.retrieve_smiles(input_smiles)
      puts("Input smiles inside model ruby file = #{input_smiles}")
  end
  
end
