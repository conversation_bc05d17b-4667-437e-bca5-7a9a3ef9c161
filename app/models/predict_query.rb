class PredictQuery < Query
  ION_MODES = %w[positive negative]

  validates :compound, presence: true
  validates :threshold, presence: true, numericality: { greater_than: 0, less_than: 1 }
  validate :compound_valid
  validates :ion_mode, presence: true, inclusion: { in: ION_MODES }
  validates :adduct_type, presence: true, inclusion: { in: POSITIVE_ADDUCTS + NEGATIVE_ADDUCTS }, :allow_blank => true

  @parser = PredictParser

  def matching_in_database
    if compound_is_inchi?
      DatabaseMolecule.where(inchi: compound).map(&:inchi_key).compact
    else
      DatabaseMolecule.where(smiles: compound).map(&:inchi_key).compact
    end
  end

  def has_computed
    filter_spectra("Computed")
  end

  def has_experimental
    filter_spectra("Experimental")
  end

  private

  def filter_spectra(method)
    filtered = Spectrum.where(method: method).where(inchi_key: matching_in_database).
      where(spectra_type: spectra_type).
      where(ion_mode: ion_mode)

    # For ESI spectra, also filter by adduct type if there is one
    if adduct_type != "Unknown" && spectra_type == "ESI"
      filtered = filtered.where(adduct_type: AdductCalculator.normalized(adduct_type))
    end

    # Omit spectra if they are missing the peak list for some reason
    filtered.select {| s|
      peak_list_file = s.get_peak_list_file
      peak_list_file.nil? ? false : File.exists?(peak_list_file)
    }
  end

end
