require 'fileutils'
require 'open3'
require 'mspire/molecular_formula'

class Query < ActiveRecord::Base
  SECRET_ID_LENGTH = 20
  MAX_COMPOUND_SIZE = 250
  MAX_COMPOUND_NUMBER = 100
  # Max times calculated using MAX_COMPOUND_SIZE and MAX_COMPOUND_NUMBER, with no precomputing
  MAX_PREDICT_TIME = 720
  MAX_ASSIGN_TIME = 900
  MAX_IDENTIFY_TIME = 1500
  MAX_IDENTIFY_DATABASE_TIME = 16
  STATUSES = %w[Queued Working Completed Failed]

  TYPES = %w[PredictQuery AssignQuery IdentifyQuery NeutralLossQuery]
#   SPECTRA_TYPES = %w[ESI EI]
  SPECTRA_TYPES = %w[EI] # Disable EI
  ION_MODES = %w[positive negative]
  POSITIVE_ADDUCTS = %w{[M+H]+ [M]+ [M+NH4]+ [M+Na]+ [M+K]+ [M+Li]+}
  NEGATIVE_ADDUCTS = %w{[M-H]- [M]- [M+Cl]- [M+HCOOH-H]- [M+CH3COOH-H]- [M-2H]2-}

  has_attached_file :output_file
  validates_attachment :output_file, content_type: { content_type: "text/plain" }
  validates :type, presence: true, inclusion: { in: TYPES }
  validates :param_file, presence: true
  validates :config_file, presence: true
  validates :secret_id, presence: true, uniqueness: true
  validates :spectra_type, presence: true, inclusion: { in: SPECTRA_TYPES }
  validate :check_molecule_size, :if => proc { |query| query.type == "PredictQuery" || query.type == "AssignQuery" }
  validate :matching_spectra_id, :if => proc { |query| query.type == "AssignQuery" || query.type == "IdentifyQuery" }

  before_validation :generate_secret

  before_create :set_process_time

  class << self; attr_accessor :parser end
  @parser = Parser

  def parser
    @parser ||= self.class.parser.new(self)
  end

  def parsed_data
    @parsed_data ||= parser.data
  end

  # Generate private URL
  def generate_secret
    self.secret_id ||= SecureRandom.hex(SECRET_ID_LENGTH)
  end

  def compound_is_inchi?
    @compound_is_inchi ||= self.compound =~ /^InChI/
  end

  def compound_is_smiles?
    !compound_is_inchi?
  end

  def compound_format
    @compound_format ||= self.compound_is_inchi? ? "InChi" : "SMILES"
  end

  def compound_formula
    @compound_formula ||= inchi_to_formula(structure_as_inchi)
  end

  def structure_as_inchi
    @structure_as_inchi ||= structure_to_inchi(self.compound)
  end

  def structure_as_inchikey
    @structure_as_inchikey ||= structure_to_inchikey(self.compound)
  end

  def structure_to_inchi(structure)
    if structure =~ /^inchi/i
      structure
    else
      begin
        Jchem.structure_to_inchi(structure)
      rescue
        nil
      end
    end
  end

  def structure_to_inchikey(structure)
    begin
      Jchem.structure_to_inchikey(structure)
    rescue
      nil
    end
  end

  def structure_as_smiles
    @structure_as_smiles ||=
      if self.compound_is_smiles?
        self.compound
      else
        begin
          Jchem.structure_to_smiles(self.compound)
        rescue
          nil
        end
      end
  end

  def status
    sidekiq_status = Sidekiq::Status::status(self.job_id)
    if sidekiq_status == :working
      "Working"
    elsif sidekiq_status == :queued
      "Queued"
    elsif self.error.blank? && self.output_file.present?
      "Completed"
    elsif self.output_file.present?
      "Completed with Errors"
    else
      "Failed"
    end
  end

  def molecule_size
    @molecule_size ||= formula_to_size(self.compound_formula)
  end

  def compound_mass
    @compound_mass ||= Mspire::MolecularFormula[self.compound_formula].mass
  end

  def parent_ion_mass_string
    if parent_ion_mass_type.present?
      parent_ion_mass.to_s + " (" + parent_ion_mass_type + ")"
    else
      parent_ion_mass
    end
  end

  # Count of the number of atoms in an InChi string
  def inchi_to_size(inchi)
    formula_to_size(inchi_to_formula(inchi))
  end

  def inchi_to_formula(inchi)
    inchi.split(/\//)[1]
  end

  def formula_to_size(compound_formula)
    compound_formula.scan(/\d+/).map(&:to_i).reduce(0,&:+).to_f
  end

  def print_spectra_id
    if spectra_id.blank?
      return "Input_Spectra"
    else
      return spectra_id
    end
  end

  def post_process?
    if self.spectra_type == "ESI"
      return 1
    else
      return 0
    end
  end

  # Set the approximate processing time, for progress bar display
  def set_process_time
    # if self.type == "PredictQuery"
    #   self.process_time = 16.56 * Math::E ** (0.02027 * self.molecule_size)
    # elsif self.type == "AssignQuery"
    #   self.process_time = self.molecule_size * MAX_ASSIGN_TIME / MAX_COMPOUND_SIZE
    # elsif self.type == "IdentifyQuery"
    #   file =
    #     if !self.candidates_file.queued_for_write[:original].nil?
    #       self.candidates_file.queued_for_write[:original]
    #     else
    #       self.candidates_file
    #     end
    #   count = File.readlines(file.path).count
    #   if self.database.blank?
    #     self.process_time = [count * MAX_IDENTIFY_TIME / MAX_COMPOUND_NUMBER, 5].max
    #   else
    #     self.process_time = [count * MAX_IDENTIFY_DATABASE_TIME / MAX_COMPOUND_NUMBER, 5].max
    #   end
    # end
    self.process_time = 100
  end

  # Convert spectra input to valid format and save as file
  def convert_spectra
    # If there are spectra text input use that,
    # otherwise use the uploaded file
    if !self.spectra.blank? && self.input_file.blank?
      spectra_lines = self.spectra.gsub(/\r\n/m, "\n").split("\n")
      return convert_peak_list(spectra_lines)
    elsif !self.input_file.blank?
      file = !self.input_file.queued_for_write[:original].nil? ? self.input_file.queued_for_write[:original] : self.input_file
      if File.extname(file.path) == ".msp"
        if self.spectra_type == "EI"
          # If this is an .msp file for EI, we don't need to convert it
          return true
        else
          spectra_text = File.read(file.path)
          return convert_msp(spectra_text)
        end
      end
      spectra_lines = File.readlines(file.path)
      return convert_peak_list(spectra_lines)
    end
  end

  def convert_peak_list(spectra_lines)
    begin
      # This iterates through the input to ensure that either all three
      # energy levels are present (for ESI) or only one energy level is
      # present (for EI) and that the headings are correct
      string = ""
      file = Tempfile.new('spectra')
      level = "low"
      low_header = false
      medium_header = false
      high_header = false
      spectra_lines.each do |line|
        if level == "low"
          if !low_header
            if line.strip !~ /^low|energy0$/i
              string.concat("energy0\n")
              low_header = true
              level = "medium"
              redo
            else
              string.concat(line.strip + "\n")
              low_header = true
            end
          elsif line.strip !~ /^low|med(ium)?|high|energy[0-2]$/i
            string.concat(line.strip + "\n")
          else
            level = "medium"
            redo
          end
        elsif level == "medium" && self.spectra_type == "ESI"
          if !medium_header
            if line.strip !~ /^med(ium)?|energy1$/i
              string.concat("energy1\n")
              medium_header = true
              level = "high"
              redo
            else
              string.concat(line.strip + "\n")
              medium_header = true
            end
          elsif line.strip !~ /^low|med(ium)?|high|energy[0-2]$/i
            string.concat(line.strip + "\n")
          else
            level = "high"
            redo
          end
        elsif level == "high" && self.spectra_type == "ESI"
          if !high_header
            if line.strip !~ /^high|energy2$/i
              string.concat("energy2\n")
              high_header = true
              break
            else
              string.concat(line.strip + "\n")
              high_header = true
            end
          elsif line.strip !~ /^low|med(ium)?|high|energy[0-2]$/i
            string.concat(line.strip + "\n")
          end
        end
      end
      if !medium_header && self.spectra_type == "ESI"
        string.concat("energy1\n")
      end
      if !high_header && self.spectra_type == "ESI"
        string.concat("energy2\n")
      end
      string = fill_empty_spectra(string)
    rescue
      self.errors.add(:base, "Error converting spectra.")
      return false
    else
      file.puts(string)
      file.rewind
      self.input_file = file
      return true
    ensure
      file.close
      file.unlink
    end
  end

  def convert_msp(spectra_text)
    # This converts spectra text from an .msp file to peak list format

    # Get only the spectra for this ID
    spectra = spectra_text.gsub(/\r\n/m, "\n").split("\n\n").
      reject { |x| x !~ /(\n|^)ID:\s*#{self.spectra_id}\s*\n/i }

    # Get each energy level
    low_spectra = spectra.grep(/(\n|^)Comment:\s*(low|energy0)\s*\n/i).try(:first)
    medium_spectra = spectra.grep(/(\n|^)Comment:\s*(med(ium)?|energy1)\s*\n/i).try(:first)
    high_spectra = spectra.grep(/(\n|^)Comment:\s*(high|energy2)\s*\n/i).try(:first)

    begin
      string = ""
      file = Tempfile.new('spectra')
      string.concat("energy0\n")
      if low_spectra.present?
        low_spectra.split(/\n/).each do |line|
          if line.strip =~ /^[-+]?[0-9]*\.?[0-9]+\s+[-+]?[0-9]*\.?[0-9]+$/
            string.concat(line.strip + "\n")
          end
        end
      end
      if self.spectra_type == "ESI"
        string.concat("energy1\n")
        if medium_spectra.present?
          medium_spectra.split(/\n/).each do |line|
            if line.strip =~ /^[-+]?[0-9]*\.?[0-9]+\s+[-+]?[0-9]*\.?[0-9]+$/
              string.concat(line.strip + "\n")
            end
          end
        end
        string.concat("energy2\n")
        if high_spectra.present?
          high_spectra.split(/\n/).each do |line|
            if line.strip =~ /^[-+]?[0-9]*\.?[0-9]+\s+[-+]?[0-9]*\.?[0-9]+$/
              string.concat(line.strip + "\n")
            end
          end
        end
      end
      string = fill_empty_spectra(string)
    rescue
      self.errors.add(:base, "Error converting spectra.")
      return false
    else
      file.puts(string)
      file.rewind
      self.input_file = file
      return true
    ensure
      file.close
      file.unlink
    end
  end

  # For ESI, makes sure there are 3 energy levels
  # If there are not, replicate the existing to make 3
  def fill_empty_spectra(spectra_text)
    first_spectra = spectra_text.split(/energy\d\n/).reject(&:blank?).first
    last_spectra = spectra_text.split(/energy\d\n/).reject(&:blank?).last

    if spectra_text.scan(/energy0\nenergy1/).length > 0
      spectra_text.insert(Regexp.last_match.begin(0) + 8, first_spectra)
    end

    if spectra_text.scan(/energy1\nenergy2/).length > 0
      spectra_text.insert(Regexp.last_match.begin(0) + 8, first_spectra)
    end

    if spectra_text.scan(/energy2\n$/).length > 0
      spectra_text.concat(last_spectra)
    end

    return spectra_text
  end

  # VALIDATIONS

  # Check that compound size is valid
  def matching_spectra_id
    # If we are using a file
    if self.spectra.blank? && !self.input_file.blank?
      file = !self.input_file.queued_for_write[:original].nil? ? self.input_file.queued_for_write[:original] : self.input_file
      if File.extname(file.path) == ".msp"
        found = false
        spectra_lines = File.readlines(file.path)
        spectra_lines.each do |line|
          if line =~ /^ID: #{self.print_spectra_id}$/i
            found = true
            break
          end
        end
        if !found
          self.errors.add(:base, "Spectra ID not found in file")
        end
      end
    end
  end

  # Check that compound size is valid
  def check_molecule_size
    if self.compound.blank?
      self.errors.add(:compound, "can't be blank")
    elsif  self.structure_as_inchi.blank? || self.compound_formula.blank?
      self.errors.add(:compound, "is invalid")
    elsif self.molecule_size > MAX_COMPOUND_SIZE
      self.errors.add(:compound, "is invalid or compound is too large")
    end
  end

  # Validate compound string
  def compound_valid
    if self.compound !~ /\AInChI=\dS?\/[^\/"]+(?:\/[^\/"]+)*\Z/ && self.compound !~ /\A([0-9a-zA-Z@\+\-\[\]\(\)\\\/%=#\$\.]+)\Z/
      if !self.errors[:compound].any?
        self.errors.add(:compound, "is invalid")
      end
    end
  end

  # Check is spectra is valid
  def spectra_valid
    begin
      if !self.spectra.blank? && self.input_file.blank?
        spectra_lines = self.spectra.gsub(/\r\n/m, "\n").split("\n")
        validate_peak_list(spectra_lines)
      elsif !self.input_file.blank?
        file = !self.input_file.queued_for_write[:original].nil? ? self.input_file.queued_for_write[:original] : self.input_file
        if File.extname(file.path) == ".msp"
          file_text = File.read(file.path)
          validate_msp(file_text)
        else
          spectra_lines = File.readlines(file.path)
          validate_peak_list(spectra_lines)
        end
      else
        if !self.errors[:spectra].any? && !self.errors[:input_file].any?
          self.errors.add(:base, "Missing spectra text and file")
        end
        return
      end
    rescue
      self.errors.add(:base, "Spectra file is invalid")
    end
  end

  private

  # validates spectra in peak list format
  # spectra_lines should be peak list as an array
  def validate_peak_list(spectra_lines)
    first_line = true
    num_headers = 0
    count = 0
    spectra_lines.each do |line|
      count += 1
      if first_line
        first_line = false
        if line.strip !~ /^low|med(ium)?|high|energy[0-2]$/i
          self.errors.add(:base, "Invalid energy level at line #{count}")
        else
          num_headers += 1
        end
      elsif line.strip =~ /^[-+]?[0-9]*\.?[0-9]+\s+[-+]?[0-9]*\.?[0-9]+$/
        next
      elsif line.strip =~ /^low|med(ium)?|high|energy[0-2]$/i
        num_headers += 1
      else
        self.errors.add(:base, "Invalid or missing spectra at line #{count}")
      end
    end
    if num_headers < 1
      self.errors.add(:base, "Missing spectra energy level headers")
    end
  end

  # Validates data in msp file format
  # file_text should be entire file content as string
  def validate_msp(file_text)
    count = 0
    spectra_blocks = file_text.gsub(/\r\n/m, "\n").split("\n\n")
    spectra_blocks.each do |block|
      found_id = false
      found_num_peaks = false
      found_energy_comment = false
      block.split("\n").each do |line|
        count += 1
        if line.strip =~ /^ID:\s*\S+$/i
          found_id = true
        elsif line.strip =~ /^Num peaks:\s*[0-9]+$/i
          found_num_peaks = true
        elsif line.strip =~ /^Comment:\s*(low|med(ium)?|high|energy[0-2])$/i
          found_energy_comment = true
        elsif line.strip =~ /^[-+]?[0-9]*\.?[0-9]+\s+[-+]?[0-9]*\.?[0-9]+$/
          next
        elsif line.strip =~ /^[\S\s]+:\s*.+$/
          next
        else
          self.errors.add(:base, "Invalid line at #{count}")
        end
      end
      if !found_id
        self.errors.add(:base, "Spectra ID missing")
      end
      if !found_num_peaks
        self.errors.add(:base, "Num peaks missing")
      end
      if self.spectra_type == "ESI" && !found_energy_comment
        # Energies required in Comment of ESI spectra only
        self.errors.add(:base, "Energy level missing")
      end
      # Extra count to account for dividing newline
      count += 1
    end
  end
end
