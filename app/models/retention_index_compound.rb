class RetentionIndexCompound < ActiveRecord::Base
	#validates :retention_index_compound_id, presence: true, format: { with: /\ANP\d{7}\z/, message: "Must be in the format RI0000001" }
	#before_validation :increment_retention_index_compound_id, :increment_retention_index_prediction_id, on: :create
	
	before_validation :increment_retention_index_compound_id, on: :create
	# Set the NP-MRD ID to the next available ID based on the natural product entries that already exist. This

	# runs pre-validation, and only sets the NP-MRD ID if it hasn't been set explicitely.

  has_structure resource: 'NP-MRD',
                finder: :find_by_moldb_ids,
                id_field: :retention_index_compound_id,
                prefilter: :moldb_searchable_ids
  has_attached_file :thumb
  validates_attachment_content_type :thumb, content_type: "image/png"
  
    def self.moldb_searchable_ids(filters={})
        self.pluck(:retention_index_compound_id)
    end
	def self.find_by_moldb_ids(ids)
        self.where(retention_index_compound_id: ids).uniq
	end

	def valid_thumb?
        return self.thumb.exists?
    end
	
	def increment_retention_index_compound_id
		if self.retention_index_compound_id.blank?
			self.retention_index_compound_id = RetentionIndexCompound.exists? ? "RI" + RetentionIndexCompound.last.retention_index_compound_id[2..-1].next : 'RI0000001'
		end
	end

	#def increment_retention_index_prediction_id
	#	if self.retention_index_prediction_id.blank?
	#		self.retention_index_prediction_id = RetentionIndexCompound.exists? ? "RIP" + RetentionIndexCompound.last.retention_index_prediction_id[2..-1].next : 'RIP0000001'
	#	end
	#end	

	def to_param
		self.retention_index_compound_id
    end

	def smiles_to_mw_convert(mol_smile)
		python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
		script_path = Rails.root.join("public", "python", "smiles_to_mol_wt.py")
		script_smile = "#{Regexp.escape(mol_smile)}"
		#script_smile = mol_smile
		#script_smile = "C"
		script_command = ""
		script_command += "#{python_path} "
		script_command += "#{script_path} "
		script_command += "#{script_smile}"
		#debugger
		puts script_command
		#mw = `#{script_command}`
		stdin,stdout,stderr = Open3.popen3(script_command)
		puts stdout.gets(nil).to_s
		#self.ri_compound_average_mass=stdout.gets(nil).to_s
		#self.save!
		#send_data mw
	  end
end
