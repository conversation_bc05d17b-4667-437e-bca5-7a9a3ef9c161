class BatchSubmission < ActiveRecord::Base
  has_one :batch_upload
  accepts_nested_attributes_for :batch_upload
  has_one :excel_submission
  accepts_nested_attributes_for :excel_submission

  has_many :chemical_shift_submissions
  accepts_nested_attributes_for :chemical_shift_submissions

  @PY_PATH = PYTHON_ENV["#{Rails.env}"]['python_path']
  def self.ParseCSV(received_csv,batch_directory,user_session_id)
      puts("received_csv = #{received_csv}")
      compound_name = []
      smiles = []
      solvent = []
      spectrum_type = []
      lines = []
      @batch_values = [[]]
      CSV.open(received_csv, 'r', :col_sep => ",", :quote_char => "|").each do |row|
        lines << row
      end
      print("lines = #{lines}")
     
      lines.each do |line|
        temp_line = line
        compound_name << temp_line[0]
        smiles << temp_line[1]
        solvent << temp_line[2]
        spectrum_type << temp_line[3]
      end
  
      
      for j in (0..(compound_name.length)-1) do
        print("compound_name[j]=#{compound_name[j]}")
        print("smiles[j]=#{smiles[j]}")
        print("solvent[j]=#{solvent[j]}")
        print("spectrum_type[j]=#{spectrum_type[j]}")
        @batch_values << [compound_name[j],smiles[j],solvent[j],spectrum_type[j]]
        print("#{@batch_values}")
  
      end
  
      @batch_values.delete_at(0)
      print("final @batch_values = #{@batch_values}")
  
      #  print("after deletion final c_p = #{c_p}")
      
      return @batch_values
  end

  def self.DrawMol(smiles,session_directory,backend_dir,nmr_pred_dir,draw_mol_script_basename,b_u_id,compound_name,user_id,var)

    if compound_name.match(/\s/)
      compound_name = compound_name.gsub! /\s+/, '_'
    end


    draw_mol_script=Rails.root.join("#{backend_dir}", "#{nmr_pred_dir}","#{draw_mol_script_basename}")
    puts "draw_mol_script = #{draw_mol_script}"
    session_name=b_u_id.to_s + "_#{compound_name}_" + "#{user_id}_" + "#{var}"
    

    draw_mol_log_basename="#{session_name}_draw_mol.log"
    outputprefix="#{session_name}_draw_mol"
    outputprefix_canonical="#{session_name}_draw_mol_canonical"
    draw_mol_log_abs_path=Rails.root.join("#{session_directory}",draw_mol_log_basename)
    draw_mol_log_abs_path_canonical=Rails.root.join("#{session_directory}","canonical_#{draw_mol_log_basename}")
    puts "draw_mol_log_abs_path = #{draw_mol_log_abs_path}"

    draw_mol_command=""
    draw_mol_command+="#{@PY_PATH} "
    draw_mol_command+="#{draw_mol_script} "

    draw_mol_arguments=""
    draw_mol_arguments+="--smiles '#{smiles}' "
    draw_mol_arguments+="--outputpath '#{session_directory}' "
    draw_mol_arguments+="--writemol "
    draw_mol_arguments+="--optmol "
    draw_mol_arguments+="--showstereo "
    draw_mol_arguments+="--showequiv "
    draw_mol_arguments+="--smilesorder "

    draw_mol_arguments_non_canonical=draw_mol_arguments
    draw_mol_arguments_non_canonical+="--outputprefix '#{outputprefix}' "
    draw_mol_arguments_non_canonical+=" > '#{draw_mol_log_abs_path}' "
    draw_mol_command_non_canonical=draw_mol_command
    draw_mol_command_non_canonical+="#{draw_mol_arguments_non_canonical} "
    puts "draw_mol_command_non_canonical: #{draw_mol_command_non_canonical}"
    `#{draw_mol_command_non_canonical}`

    draw_mol_arguments_canonical=draw_mol_arguments
    draw_mol_arguments_canonical+="--canonicalorder "
    draw_mol_arguments_canonical+="--outputprefix '#{outputprefix_canonical}' "
    draw_mol_arguments_canonical+=" > '#{draw_mol_log_abs_path_canonical}' "
    draw_mol_command_canonical=draw_mol_command
    draw_mol_command_canonical+="#{draw_mol_arguments_canonical} "
    puts "draw_mol_command_canonical: #{draw_mol_command_canonical}"
    # bll: these output files appear to not be currently used,
    #      and we want to avoid running the command twice.
    #      there is still ongoing discussion on the best/easiest way to get predictable numbering,
    #      so the output could also change
    # `#{draw_mol_command_canonical}`

    non_canonical_mol_basename="#{outputprefix}_output.mol"
    non_canonical_base_image_basename="#{outputprefix}_2d.png"
    non_canonical_equiv_image_basename="#{outputprefix}_equiv.png"

    canonical_mol_basename="#{outputprefix_canonical}_output.mol"
    canonical_base_image_basename="#{outputprefix_canonical}_2d.png"
    canonical_equiv_image_basename="#{outputprefix_canonical}_equiv.png"


    non_canonical_mol_abs_path=Rails.root.join("#{session_directory}",non_canonical_mol_basename)
    non_canonical_base_image_abs_path=Rails.root.join("#{session_directory}",non_canonical_base_image_basename)
    non_canonical_equiv_image_abs_path=Rails.root.join("#{session_directory}",non_canonical_equiv_image_basename)

    canonical_mol_abs_path=Rails.root.join("#{session_directory}",canonical_mol_basename)
    canonical_base_image_abs_path=Rails.root.join("#{session_directory}",canonical_base_image_basename)
    canonical_equiv_image_abs_path=Rails.root.join("#{session_directory}",canonical_equiv_image_basename)

    puts "non_canonical_mol_abs_path: #{non_canonical_mol_abs_path}"
    puts "non_canonical_base_image_abs_path: #{non_canonical_base_image_abs_path}"
    puts "non_canonical_equiv_image_abs_path: #{non_canonical_equiv_image_abs_path}"
    puts "canonical_mol_abs_path: #{canonical_mol_abs_path}"
    puts "canonical_base_image_abs_path: #{canonical_base_image_abs_path}"
    puts "canonical_equiv_image_abs_path: #{canonical_equiv_image_abs_path}"



    

    drawmolrdkit_script=Rails.root.join('public', 'python','drawmolrdkit.py')
    output_dir_for_mol=session_directory
    python_log_basename="#{session_name}_nmrpred_initial_predictions.log"
    python_log_abs_path=Rails.root.join("#{session_directory}",python_log_basename)

    input_mol_file = "#{session_name}_draw_mol_output.mol"
    path_to_input_mol_file = Rails.root.join("#{session_directory}","#{input_mol_file}")

    temp_model_basename="#{session_name}_temp_3D.mol"
    temp_image_basename="#{session_name}_temp_3D.png"
    csv_basename="#{session_name}_mol.csv"

    path_to_temp_model=Rails.root.join("#{session_directory}","#{temp_model_basename}")
    path_to_temp_image=Rails.root.join("#{session_directory}","#{temp_image_basename}")
    path_to_csv=Rails.root.join("#{session_directory}","#{csv_basename}")
    nmr_pred_location=Rails.root.join("#{backend_dir}", "#{nmr_pred_dir}")

    puts "path_to_temp_model = #{path_to_temp_model}"
    puts "path_to_temp_image = #{path_to_temp_image}"
    puts "path_to_csv = #{path_to_csv}"
    puts "output_dir_for_mol = #{output_dir_for_mol}"
    puts "nmr_pred_location = #{nmr_pred_location}"
    puts "python_drawmolrdkit_path = #{@PY_PATH}"
    puts "drawmolrdkit_script = #{drawmolrdkit_script}"

    python_drawmolrdkit_command=""
    python_drawmolrdkit_command+="#{@PY_PATH} "
    python_drawmolrdkit_command+="#{drawmolrdkit_script} "
    python_drawmolrdkit_arguments=""
    python_drawmolrdkit_arguments+="-s '#{smiles}' "
    python_drawmolrdkit_arguments+="-sid '#{session_name}' "
    python_drawmolrdkit_arguments+="-odir_path '#{session_directory}' "
    python_drawmolrdkit_arguments+="-mol_path '#{path_to_temp_model}' "
    python_drawmolrdkit_arguments+="-cs_path '#{path_to_csv}' "
    python_drawmolrdkit_arguments+="-mol_img_path '#{path_to_temp_image}' "
    python_drawmolrdkit_arguments+="-nmrpred_loc '#{nmr_pred_location}' "
    python_drawmolrdkit_arguments+="-input_mol '#{path_to_input_mol_file}' " #USE THIS AS INPUT TO NMRPRED
    python_drawmolrdkit_arguments+="-input_img '#{non_canonical_base_image_abs_path}' " #COPY THIS AND USE AS DISPLAY
    python_drawmolrdkit_arguments+=" > '#{python_log_abs_path}' "
    python_drawmolrdkit_command+="#{python_drawmolrdkit_arguments} "

    puts "Python log is:"
    puts "#{python_log_abs_path}"
    puts "Running drawmolrdkit Python script:"
    puts "#{python_drawmolrdkit_command}"

    `#{python_drawmolrdkit_command}`

    return path_to_temp_model, path_to_temp_image, path_to_csv, compound_name
  end

  
  



  def self.GenerateExcel(project_name,excel_generator_script_with_path, list_of_input_files_for_excel_generator, batch_directory)
    python_excel_generator_command=""
    python_excel_generator_command+="#{@PY_PATH} "
    python_excel_generator_command+="#{excel_generator_script_with_path} "
    python_excel_generator_command+=""
    python_excel_generator_command+="-i '#{list_of_input_files_for_excel_generator}' "
    python_excel_generator_command+="-p '#{project_name}' "
    python_excel_generator_command+="-l '#{batch_directory}' "

    puts "python_excel_generator_command = #{python_excel_generator_command}"

    `#{python_excel_generator_command}`
    
    execl_file_with_path = "#{batch_directory}/project_name/project_name.xlsx"
    return execl_file_with_path

  end






  
end
