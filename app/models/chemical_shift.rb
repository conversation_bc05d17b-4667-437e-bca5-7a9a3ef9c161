class ChemicalShift < ActiveRecord::Base
  belongs_to :chemical_shift_submission
  belongs_to :submission

  def self.conditional_save(s)
    s.chemical_shifts.each do |a|
      if a.chemical_shift_true.empty?
        a.update(:chemical_shift_true => "NA")
      end
      if a.multiplet_true.empty?
        a.update(:multiplet_true => "#{a.multiplet_pred}")
      end
      if a.jcoupling_true.empty?
        a.update(:jcoupling_true => "NA")
      end
      if a.custom_atom_id.empty?
        a.update(:custom_atom_id => "#{a.atom_id}")
      end
    end
  end
end
