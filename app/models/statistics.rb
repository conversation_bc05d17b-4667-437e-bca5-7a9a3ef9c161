module Statistics

### Overall Statistics ###

  def self.number_of_spectra
    # Assume all predicted ESI spectra with 1 row in the spectra table have 3 energy levels in the file.
    Spectrum.where(method: "Experimental").count + Spectrum.where(method: "Computed", spectra_type: 'EI').count + (Spectrum.where(method: "Computed", spectra_type: 'ESI').count * 3)
  end

  def self.predicted_spectra
    # Assume all predicted ESI spectra with 1 row in the spectra table have 3 energy levels in the file.
    Spectrum.where(method: "Computed", spectra_type: 'EI').count + (Spectrum.where(method: "Computed", spectra_type: 'ESI').count * 3)
  end
  
  def self.experimental_spectra
    Spectrum.where(method: "Experimental").count
  end

  def self.distinct_compounds_with_spectra
    Spectrum.select(:inchi_key).distinct.count
  end

  def self.distinct_compounds_with_predicted_spectra
    Spectrum.where(method: "Computed").select(:inchi_key).distinct.count
  end
  
  def self.distinct_compounds_with_experimental_spectra
    Spectrum.where(method: "Experimental").select(:inchi_key).distinct.count
  end
  
  def self.compounds_with_spectra_with_citations
    ReferenceCount.where('count>0').joins(:database_molecules => :spectra).distinct.count
  end


### ESI-MS/MS Statistics ###

  def self.number_of_esi_spectra
    # Assume all predicted ESI spectra with 1 row in the spectra table have 3 energy levels in the file.
    Spectrum.where(method: "Experimental", spectra_type: 'ESI').count + (Spectrum.where(method: "Computed", spectra_type: 'ESI').count * 3)
  end

  def self.predicted_esi_spectra
    # Assume all predicted ESI spectra with 1 row in the spectra table have 3 energy levels in the file.
    Spectrum.where(spectra_type: "ESI", method: "Computed").count* 3
  end
  
  def self.experimental_esi_spectra
    Spectrum.where(spectra_type: "ESI", method: "Experimental").count
  end

  def self.distinct_compounds_with_esi_spectra
    Spectrum.where(spectra_type: "ESI").select(:inchi_key).distinct.count
  end

  def self.distinct_compounds_with_predicted_esi_spectra
    Spectrum.where(spectra_type: "ESI", method: "Computed").select(:inchi_key).distinct.count
  end
  
  def self.distinct_compounds_with_experimental_esi_spectra
    Spectrum.where(spectra_type: "ESI", method: "Experimental").select(:inchi_key).distinct.count
  end
  
  def self.compounds_with_spectra_with_esi_citations
    ReferenceCount.where('count>0').joins(:database_molecules => :spectra).where('spectra.spectra_type = "ESI"').distinct.count
  end


### EI-MS Statistics ###

  def self.number_of_ei_spectra
    Spectrum.where(spectra_type: "EI").count
  end

  def self.predicted_ei_spectra
    Spectrum.where(spectra_type: "EI", method: "Computed").count
  end
  
  def self.experimental_ei_spectra
    Spectrum.where(spectra_type: "EI", method: "Experimental").count
  end

  def self.distinct_compounds_with_ei_spectra
    Spectrum.where(spectra_type: "EI").select(:inchi_key).distinct.count
  end

  def self.distinct_compounds_with_predicted_ei_spectra
    Spectrum.where(spectra_type: "EI", method: "Computed").select(:inchi_key).distinct.count
  end
  
  def self.distinct_compounds_with_experimental_ei_spectra
    Spectrum.where(spectra_type: "EI", method: "Experimental").select(:inchi_key).distinct.count
  end
  
  def self.compounds_with_spectra_with_ei_citations
    ReferenceCount.where('count>0').joins(:database_molecules => :spectra).where('spectra.spectra_type = "EI"').distinct.count
  end

end
