class AssignQuery < Query
  ION_MODES = %w[positive negative]

  has_attached_file :input_file, :default_url => ""
  # This doesn't work for .msp files, so omit for now
  # validates_attachment :input_file, content_type: { content_type: "text/plain" }
  validates :compound, presence: true
  validates :input_file, presence: true, :unless => :spectra?
  validates :spectra, presence: true, :unless => :input_file?
  validates :ppm_mass_tol, presence: true, numericality: true
  validates :abs_mass_tol, presence: true, numericality: true
  validate :compound_valid
  validate :spectra_valid
  validates :ion_mode, presence: true, inclusion: { in: ION_MODES }

  @parser = AssignParser

end
