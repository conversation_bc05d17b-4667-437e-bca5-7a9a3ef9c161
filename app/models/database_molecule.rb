class DatabaseMolecule < ActiveRecord::Base
  include MassSearch
  include SpectraSearch

  nilify_blanks

  has_many :spectra, class_name: "<PERSON>", primary_key: 'inchi_key', foreign_key: 'inchi_key', dependent: :destroy
  belongs_to :derivatization_of, class_name: "DatabaseMolecule"

  has_one :classification, primary_key: 'inchi_key', foreign_key: 'inchi_key'
  has_one :reference_count, -> (dm) { unscope(:where).where(reference_counts: {inchi_key: (dm.derivatization_of.present? ? dm.derivatization_of.inchi_key : dm.inchi_key)}) }

  validates :source, presence: true
  validates :source_id, presence: true, uniqueness: { scope: :source }
  validates :neutral_mass, presence: true, numericality: true
  validates :inchi_key, presence: true, format: { with: /\AInChIKey=[A-Z]{14}-[A-Z]{8}SA-[A-Z]{1}/ }
  validates :inchi, presence: true, format: { with: /\AInChI=1S/ }, if: "smiles.blank?"
  validates :smiles, presence: true, if: "inchi.blank?"
  validates :name, presence: true

  def full_name
    if derivatization_type.present?
      name + " " + derivatization_type
    else
      name
    end
  end

  def database_id
    source_id
  end

  def cfmid_id
    source + "-" + source_id
  end

  def get_spectra(spectra_type, ion_mode, adduct_type = nil, omit_sources = nil)
    self.spectra.find_spectra(spectra_type, ion_mode, adduct_type, omit_sources)
  end

  def get_all_spectra(spectra_type, ion_mode, adduct_type = nil)
    self.spectra.find_all_spectra(spectra_type, ion_mode, adduct_type)
  end

  # Merge any acceptable (ESI 10, 20, 40 or EI 70) experimental spectra
  # together and/or into the computed spectra
  def make_spectra(spectra_type, ion_mode, adduct_type = nil, annotated = false, omit_sources = nil)

    matching = get_spectra(spectra_type, ion_mode, adduct_type, omit_sources)
    # For the computed we don't filter by adduct type, because we want to use
    # the [M+H]+ or [M-H]- ones if we have to
    all_computed = matching.where(method: "Computed")

    if spectra_type == "ESI"

      # Get the computed spectra and any spectra for each energy level
      # TO DO: If there are multiple we just use the first one? This means
      # that if the specified adduct type is "Unknown" we may get many
      # possible spectra but will just use the first one...
      # If we don't have the adduct type, preferentially use the [M+H]+ or [M-H]- computed ones
      computed = all_computed.where(adduct_type: adduct_type).first || all_computed.where("adduct_type in (?)", ['[M+H]+', '[M-H]-']).first
      experimental_low = matching.
        find_experimental_low.
        first.
        try(:get_spectra_as_hash, annotated) || {}
      experimental_med = matching.
        find_experimental_medium.
        first.
        try(:get_spectra_as_hash, annotated) || {}
      experimental_high = matching.
        find_experimental_high.
        first.
        try(:get_spectra_as_hash, annotated) || {}

      experimental_spectra_hash = HashWithIndifferentAccess.new(
                                    energy0: experimental_low["energy0"],
                                    energy1: experimental_med["energy0"],
                                    energy2: experimental_high["energy0"])
      experimental_annotations_hash = HashWithIndifferentAccess.new(
                                        energy0_annotations: experimental_low["annotations"],
                                        energy1_annotations: experimental_med["annotations"],
                                        energy2_annotations: experimental_high["annotations"])

      if computed.present?
        spectra_hash = computed.get_spectra_as_hash(annotated, adduct_type) # Hold the merged spectra
        experimental_spectra_hash.each do |level, spectrum|
          if spectrum.present?
            spectra_hash[level] = spectrum
            spectra_hash["annotations"].concat(experimental_annotations_hash[level.to_s + "_annotations"])
          end
        end
      else
        spectra_hash = {"annotations" => []} # Hold the merged spectra
        experimental_spectra_hash.each do |level, spectrum|
          if spectrum.blank?
            # If energy level is missing, fill with first existing
            existing = experimental_spectra_hash.select { |e, s| !s.blank? }.values.first
            spectra_hash[level] = existing ? existing : []
          else
            spectra_hash[level] = spectrum
            spectra_hash["annotations"].concat(experimental_annotations_hash[level.to_s + "_annotations"])
          end
        end
      end
    else # For EI
      # Get the computed spectra and any spectra for each energy level
      computed = all_computed.where(adduct_type: adduct_type).first || all_computed.where(adduct_type: '[M]+').first
      experimental_low = matching.
        find_experimental_ei.
        first.
        try(:get_spectra_as_hash, annotated) || {}

      experimental_spectra_hash = HashWithIndifferentAccess.new(energy0: experimental_low["energy0"])
      experimental_annotations_hash = HashWithIndifferentAccess.new(energy0_annotations: experimental_low["annotations"])
      if computed.present?
        spectra_hash = computed.get_spectra_as_hash(annotated, adduct_type) #Hold the merged spectra
        experimental_spectra_hash.each do |level, spectrum|
          if spectrum.present?
            spectra_hash[level] = spectrum
            spectra_hash["annotations"].concat(experimental_annotations_hash[level.to_s + "_annotations"])
          end
        end
      else
        spectra_hash = {"annotations" => []}
        experimental_spectra_hash.each do |level, spectrum|
          if spectrum.blank?
            # If energy level is missing, fill with first existing
            existing = experimental_spectra_hash.select { |e, s| !s.blank? }.values.first
            spectra_hash[level] = existing ? existing : []
          else
            spectra_hash[level] = spectrum
            spectra_hash["annotations"].concat(experimental_annotations_hash[level.to_s + "_annotations"])
          end
        end
      end
    end

    # Remove unecessary annotations
    if annotated
      # Make a list of all the frag ids present
      present_frags = []
      spectra_hash.each do |key, value|
        if key =~ /^energy/
          value.each do |line|
            if line =~ /^\s*([-+]?[0-9]*\.?[0-9]+)\s+([-+]?[0-9]*\.?[0-9]+)\s+((?:[\d-]+\s*)*)(?:\((.*)\))*$/
              present_frags.concat($3.split("\s").map(&:strip))
            end
          end
        end
      end
      present_frags.uniq!

      # The remove annotations for frags that aren't present anymore
      spectra_hash["annotations"].reject! { |a|
        a !~ /^(#{present_frags.join("|").gsub("-", "\\-")})\s/
      }
    else
      spectra_hash.delete("annotations")
    end

    spectra_hash
  end

  # This makes the spectra in a special way for training
  # 1 exp + computed = Use computed only, leave exp for query
  # > 1 exp = Leave non-10, 20, 40 or highest exp for query
  # > 1 exp + computed = Leave non-10, 20, 40 or highest exp for query and merge remaining
  # with computed
  def make_training_spectra(spectra_type, ion_mode, adduct_type = nil, annotated = false)

    matching = get_spectra(spectra_type, ion_mode, adduct_type)

    test, computed_hash, experimental_spectra_hash, experimental_annotations_hash = remove_experimental_test_spectra(spectra_type, ion_mode, adduct_type, annotated)

    if computed_hash.present?
      spectra_hash = computed_hash # Hold the merged spectra
      experimental_spectra_hash.each do |level, spectrum|
        if spectrum.present?
          spectra_hash[level] = spectrum
          spectra_hash["annotations"].concat(experimental_annotations_hash[level.to_s + "_annotations"])
        end
      end
    else
      spectra_hash = {"annotations" => []} # Hold the merged spectra
      experimental_spectra_hash.each do |level, spectrum|
        if spectrum.blank?
          # If energy level is missing, fill with first existing
          existing = experimental_spectra_hash.select { |e, s| !s.blank? }.values.first
          spectra_hash[level] = existing ? existing : []
        else
          spectra_hash[level] = spectrum
          spectra_hash["annotations"].concat(experimental_annotations_hash[level.to_s + "_annotations"])
        end
      end
    end

    # Remove unecessary annotations
    if annotated
      # Make a list of all the frag ids present
      present_frags = []
      spectra_hash.each do |key, value|
        if key =~ /^energy/
          value.each do |line|
            if line =~ /^\s*([-+]?[0-9]*\.?[0-9]+)\s+([-+]?[0-9]*\.?[0-9]+)\s+((?:[\d-]+\s*)*)(?:\((.*)\))*$/
              present_frags.concat($3.split("\s").map(&:strip))
            end
          end
        end
      end
      present_frags.uniq!

      # The remove annotations for frags that aren't present anymore
      spectra_hash["annotations"].reject! { |a|
        a !~ /^(#{present_frags.join("|").gsub("-", "\\-")})\s/
      }
    else
      spectra_hash.delete("annotations")
    end

    spectra_hash
  end

  # NOTE: For training only
  def make_computed_spectra(spectra_type, ion_mode, adduct_type = nil, annotated = false)

    matching = get_spectra(spectra_type, ion_mode, adduct_type)

    computed = matching.where(method: "Computed").first

    if computed.present?
      spectra_hash = computed.get_spectra_as_hash(annotated, adduct_type) # Hold the merged spectra
    else
      if spectra_type == "ESI"
        spectra_hash = {"energy0" => [], "energy1" => [], "energy2" => [], "annotations" => []} # Hold the merged spectra
      else
        spectra_hash = {"energy0" => [], "annotations" => []}
      end
    end

    # Remove unecessary annotations
    if annotated
      # Make a list of all the frag ids present
      present_frags = []
      spectra_hash.each do |key, value|
        if key =~ /^energy/
          value.each do |line|
            if line =~ /^\s*([-+]?[0-9]*\.?[0-9]+)\s+([-+]?[0-9]*\.?[0-9]+)\s+((?:[\d-]+\s*)*)(?:\((.*)\))*$/
              present_frags.concat($3.split("\s").map(&:strip))
            end
          end
        end
      end
      present_frags.uniq!

      # The remove annotations for frags that aren't present anymore
      spectra_hash["annotations"].reject! { |a|
        a !~ /^(#{present_frags.join("|").gsub("-", "\\-")})\s/
      }
    else
      spectra_hash.delete("annotations")
    end

    spectra_hash
  end

  # NOTE: For training only
  # Merge any acceptable (ESI 10, 20, 40 or EI 70) experimental spectra
  def make_experimental_spectra(spectra_type, ion_mode, adduct_type = nil, annotated = false)

    matching = get_spectra(spectra_type, ion_mode, adduct_type)

    if spectra_type == "ESI"

      experimental_low = matching.
        find_experimental_low.
        first.
        try(:get_spectra_as_hash, annotated) || {}
      experimental_med = matching.
        find_experimental_medium.
        first.
        try(:get_spectra_as_hash, annotated) || {}
      experimental_high = matching.
        find_experimental_high.
        first.
        try(:get_spectra_as_hash, annotated) || {}

      experimental_spectra_hash = HashWithIndifferentAccess.new(
                                    energy0: experimental_low["energy0"],
                                    energy1: experimental_med["energy0"],
                                    energy2: experimental_high["energy0"])
      experimental_annotations_hash = HashWithIndifferentAccess.new(
                                        energy0_annotations: experimental_low["annotations"],
                                        energy1_annotations: experimental_med["annotations"],
                                        energy2_annotations: experimental_high["annotations"])

      spectra_hash = {"annotations" => []} # Hold the merged spectra
      experimental_spectra_hash.each do |level, spectrum|
        if spectrum.blank?
          # If energy level is missing, fill with first existing
          existing = experimental_spectra_hash.select { |e, s| !s.blank? }.values.first
          spectra_hash[level] = existing ? existing : []
        else
          spectra_hash[level] = spectrum
          spectra_hash["annotations"].concat(experimental_annotations_hash[level.to_s + "_annotations"])
        end
      end
    else # For EI
      experimental_low = matching.
        find_experimental_ei.
        first.
        try(:get_spectra_as_hash, annotated) || {}

      experimental_spectra_hash = HashWithIndifferentAccess.new(energy0: experimental_low["energy0"])
      experimental_annotations_hash = HashWithIndifferentAccess.new(energy0_annotations: experimental_low["annotations"])

      spectra_hash = {"annotations" => []}
      experimental_spectra_hash.each do |level, spectrum|
        if spectrum.blank?
          # If energy level is missing, fill with first existing
          existing = experimental_spectra_hash.select { |e, s| !s.blank? }.values.first
          spectra_hash[level] = existing ? existing : []
        else
          spectra_hash[level] = spectrum
          spectra_hash["annotations"].concat(experimental_annotations_hash[level.to_s + "_annotations"])
        end
      end
    end

    # Remove unecessary annotations
    if annotated
      # Make a list of all the frag ids present
      present_frags = []
      spectra_hash.each do |key, value|
        if key =~ /^energy/
          value.each do |line|
            if line =~ /^\s*([-+]?[0-9]*\.?[0-9]+)\s+([-+]?[0-9]*\.?[0-9]+)\s+((?:[\d-]+\s*)*)(?:\((.*)\))*$/
              present_frags.concat($3.split("\s").map(&:strip))
            end
          end
        end
      end
      present_frags.uniq!

      # The remove annotations for frags that aren't present anymore
      spectra_hash["annotations"].reject! { |a|
        a !~ /^(#{present_frags.join("|").gsub("-", "\\-")})\s/
      }
    else
      spectra_hash.delete("annotations")
    end

    spectra_hash
  end

  def remove_experimental_test_spectra(spectra_type, ion_mode, adduct_type = nil, annotated = false)

    matching = get_all_spectra(spectra_type, ion_mode, adduct_type)

    computed_hash = matching.where(method: "Computed").first.try(:get_spectra_as_hash, annotated, adduct_type) || {}

    if spectra_type == "ESI"

      all_experimental_low = matching.find_experimental_low.to_a
      all_experimental_med = matching.find_experimental_medium.to_a
      all_experimental_high = matching.find_experimental_high.to_a
      all_non_qtof = matching.find_experimental.to_a - all_experimental_low - all_experimental_med - all_experimental_high

      if all_non_qtof.present?
        test = all_non_qtof.shift.try(:get_spectra_as_hash, annotated) || {}
        experimental_low = all_experimental_low.first.try(:get_spectra_as_hash, annotated) || {}
        experimental_med = all_experimental_med.first.try(:get_spectra_as_hash, annotated) || {}
        experimental_high = all_experimental_high.first.try(:get_spectra_as_hash, annotated) || {}
      elsif all_experimental_high.present?
        test = all_experimental_high.shift.try(:get_spectra_as_hash, annotated) || {}
        experimental_low = all_experimental_low.first.try(:get_spectra_as_hash, annotated) || {}
        experimental_med = all_experimental_med.first.try(:get_spectra_as_hash, annotated) || {}
        experimental_high = all_experimental_high.first.try(:get_spectra_as_hash, annotated) || {}
      elsif all_experimental_med.present?
        test = all_experimental_med.shift.try(:get_spectra_as_hash, annotated) || {}
        experimental_low = all_experimental_low.first.try(:get_spectra_as_hash, annotated) || {}
        experimental_med = all_experimental_med.first.try(:get_spectra_as_hash, annotated) || {}
        experimental_high = all_experimental_high.first.try(:get_spectra_as_hash, annotated) || {}
      else
        test = all_experimental_low.shift.try(:get_spectra_as_hash, annotated) || {}
        experimental_low = all_experimental_low.first.try(:get_spectra_as_hash, annotated) || {}
        experimental_med = all_experimental_med.first.try(:get_spectra_as_hash, annotated) || {}
        experimental_high = all_experimental_high.first.try(:get_spectra_as_hash, annotated) || {}
      end

      experimental_spectra_hash = HashWithIndifferentAccess.new(
                                    energy0: experimental_low["energy0"],
                                    energy1: experimental_med["energy0"],
                                    energy2: experimental_high["energy0"])
      experimental_annotations_hash = HashWithIndifferentAccess.new(
                                        energy0_annotations: experimental_low["annotations"],
                                        energy1_annotations: experimental_med["annotations"],
                                        energy2_annotations: experimental_high["annotations"])
    else
      all_experimental_ei = matching.find_experimental_ei.to_a
      all_non_qtof = matching.find_experimental.to_a - all_experimental_ei

      if all_non_qtof.present?
        test = all_non_qtof.shift.try(:get_spectra_as_hash, annotated) || {}
        experimental_low = all_experimental_ei.first.try(:get_spectra_as_hash, annotated) || {}
      else
        test = all_experimental_ei.shift.try(:get_spectra_as_hash, annotated) || {}
        experimental_low = all_experimental_ei.first.try(:get_spectra_as_hash, annotated) || {}
      end

      experimental_spectra_hash = HashWithIndifferentAccess.new(energy0: experimental_low["energy0"])
      experimental_annotations_hash = HashWithIndifferentAccess.new(energy0_annotations: experimental_low["annotations"])
    end

    return [test, computed_hash, experimental_spectra_hash, experimental_annotations_hash]
  end

  def self.spectra_location(spectra_type, ion_mode)
    if spectra_type == "ESI"
      "public/spectra/" + spectra_type.downcase + "/" + ion_mode
    else
      "public/spectra/" + spectra_type.downcase
    end
  end

  def self.get_molecules_for_neutral_mass(spectra_type, ion_mode, mass, database, adduct_type = nil, tolerance_options = {}, candidate_limit)
    puts "Goes to get_molecules_for_neutral_mass"
    begin 
      if mass.is_a? Array
      # Complicated bit here to build a raw SQL query so we can get the
      # union of the queries for all the different masses in order to sort by delta propery
        query = "SELECT * FROM ("
        query_items = mass.map do |m|
          DatabaseMolecule.joins(:spectra).
            where('database_molecules.source IN (?)', database).
            find_by_mass(m, tolerance_options, false).
            find_spectra(spectra_type, ion_mode, adduct_type).to_sql
        end
        query << query_items.join(" UNION ")
        query << ") As U GROUP BY id ORDER BY delta asc LIMIT #{candidate_limit}"
        find_by_sql(query).reject{|dm| dm.classification.nil?}
      else
        DatabaseMolecule.joins(:spectra).
          where('database_molecules.source IN (?)', database).
          find_by_mass(mass, tolerance_options).
          find_spectra(spectra_type, ion_mode, adduct_type).
          group('database_molecules.id'). # Get only distinct molecules
          limit(candidate_limit).reject{|dm| dm.classification.nil?}
      end
    rescue Exception => e
      return "Error Getting Database Molecules #{e}"
    end
  end

end
