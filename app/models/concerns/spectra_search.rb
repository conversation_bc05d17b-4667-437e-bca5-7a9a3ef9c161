module SpectraSearch
  extend ActiveSupport::Concern

  module ClassMethods

    def find_spectra(spectra_type, ion_mode, adduct_type = nil, omit_sources = nil)
      if spectra_type == "ESI" && adduct_type.present? && adduct_type != "Unknown"
        # For ESI spectra, filter by adduct type if this was specified by the user.
        # (Filtering by adduct type is not necessary for EI spectra, since they should all
        # have adduct type M+).
        matching = where('spectra.adduct_type = ?', AdductCalculator.normalized(adduct_type))
      else
        matching = where(nil) # nil means do not add any restrictions with where clauses
      end

      # Omit spectra from these sources, if specified
      if omit_sources.present?
        matching = matching.where.not(spectra: { spectra_source: [omit_sources].flatten })
      end

      # Match the energy level
      if spectra_type == "ESI"
        matching.where(spectra: {spectra_type: "ESI", ion_mode: ion_mode}).
          where("lower(spectra.collision_energy) REGEXP ?", '^[1,2,4]0(\.0)?[[:blank:]]?(e?v|cid)?$|^all$')
      else
        matching.where(spectra: {spectra_type: "EI", ion_mode: ion_mode}).
          where("lower(spectra.collision_energy) REGEXP ?", '^\-?70(\.0)?[[:blank:]]?(e?v)?$|^all$')
      end
    end

    def find_all_spectra(spectra_type, ion_mode, adduct_type = nil)
      if spectra_type == "ESI" && adduct_type.present? && adduct_type != "Unknown"
        # For ESI spectra, filter by adduct type if this was specified by the user.
        # (Filtering by adduct type is not necessary for EI spectra, since they should all
        # have adduct type M+).
        matching = where('spectra.adduct_type = ?', AdductCalculator.normalized(adduct_type))
      else
        matching = where(nil) # nil means do not add any restrictions with where clauses
      end

      if spectra_type == "ESI"
        matching.where(spectra: {spectra_type: "ESI", ion_mode: ion_mode})
      else
        matching.where(spectra: {spectra_type: "EI", ion_mode: ion_mode})
      end
    end

    def find_experimental
      where(method: "Experimental")
    end

    def find_experimental_low
      where(method: "Experimental").
      where("collision_energy REGEXP ?", '^10(\.0)?[[:blank:]]?(e?v|CID)?$')
    end

    def find_experimental_medium
      where(method: "Experimental").
      where("collision_energy REGEXP ?", '^20(\.0)?[[:blank:]]?(e?v|CID)?$')
    end

    def find_experimental_high
      where(method: "Experimental").
      where("collision_energy REGEXP ?", '^40(\.0)?[[:blank:]]?(e?v|CID)?$')
    end

    def find_experimental_ei
      where(method: "Experimental").
      where("collision_energy REGEXP ?", '^\-?70(\.0)?[[:blank:]]?(e?v)?$')
    end

  end
end