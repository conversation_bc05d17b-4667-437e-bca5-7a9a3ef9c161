module MassSearch
  extend ActiveSupport::Concern

  module <PERSON>Methods

    def find_by_mass(mass, tolerance_options={}, order=true)
      tolerance =
        if tolerance_options[:ppm]
          self.ppm_to_dalton(tolerance_options[:ppm],mass)
        else
          tolerance_options[:dalton]
        end

      if order
        where(nil).select( "database_molecules.*, abs(neutral_mass - #{mass}) as delta").
          where("neutral_mass >= #{mass-tolerance} AND neutral_mass <= #{mass+tolerance}").
          order('delta asc')
      else # This is for combined queries, in which case we will order the results elsewhere
        where(nil).select( "database_molecules.*, abs(neutral_mass - #{mass}) as delta").
          where("neutral_mass >= #{mass-tolerance} AND neutral_mass <= #{mass+tolerance}")
      end
    end

    def ppm_to_dalton(ppm,mass)
      mass * ppm / 1_000_000
    end

  end
end
