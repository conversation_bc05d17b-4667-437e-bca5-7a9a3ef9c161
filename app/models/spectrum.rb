class Spectrum < ActiveRecord::Base
  include SpectraSearch

  nilify_blanks

  self.table_name = "spectra"

  METHODS = %w[Experimental Computed]
#   SPECTRA_TYPES = %w[ESI EI]
  SPECTRA_TYPES = %w[ESI] # Disable EI
  ION_MODES = %w[positive negative]
 
  PRECOMPUTED_DATA_DIR = File.join(Rails.root,"/data/")

  has_many :database_molecules, primary_key: 'inchi_key', foreign_key: 'inchi_key'

  validates :inchi_key, presence: true#, format: { with: /\AInChIKey=[A-Z]{14}-[A-Z]{8}SA-[A-Z]{1}/ }
  validates :spectra_source, presence: true
  validates :method, presence: true, inclusion: { in: METHODS }
  validates :spectra_type, presence: true, inclusion: { in: SPECTRA_TYPES }
  validates :ion_mode, presence: true, inclusion: { in: ION_MODES }
  validates :adduct_type, presence: true, format: { with: /\A\[.+\]\d?[+,-]{1}\Z/} #assumes upto 9 charges

  has_attached_file :peak_list
  has_attached_file :annotated_peak_list
  validates_attachment_content_type :peak_list, content_type: ["text/plain"]
  validates_attachment_content_type :annotated_peak_list, content_type: ["text/plain"]

  scope :computed, -> { where(method: "Computed") }
  scope :experimental, -> { where(method: "Experimental") }
  scope :esi, -> { where(spectra_type: "ESI") }
  scope :ei, -> { where(spectra_type: "EI") }
  scope :positive, -> { where(ion_mode: "positive") }
  scope :negative, -> { where(ion_mode: "negative") }

  before_destroy :delete_spectra_files, prepend: true

  def name
    n = "Experimental MsMs Spectrum"
    if collision_energy.present?
      n += " (" + collision_energy + ")"
    end
    if adduct_type.present?
      n += ", " + adduct_type
    end

    n
  end

  # Get the database molecule matching the source, for experimental spectra
  # Or just the first matching molecule if the sources are different
  def database_molecule
    self.database_molecules.where(source: self.spectra_source).first || self.database_molecules.first
  end

  # Get the annotated peak list if exists, else get unannotated
  def get_peak_list_file
    if File.exists?(peak_list_file_annotated)
      peak_list_file_annotated
    else
      peak_list_file
    end
  end

  def get_peak_list_url
    if annotated_peak_list.present?
      annotated_peak_list.url
    else
      peak_list.url
    end
  end

  # Get the path to the annotated peak list file
  def peak_list_file_annotated
    if method == "Experimental"
      # TO DO return annotated file

      # Try to find cached paperclip object
      return annotated_peak_list.try(:path) || ""
    else
      if spectra_type == "ESI"
        filepath = File.join(PRECOMPUTED_DATA_DIR, "esi", ion_mode, "annotated", computed_file_name)
      else
        filepath = File.join(PRECOMPUTED_DATA_DIR, "ei", "annotated", computed_file_name)
      end
      if File.exists?(filepath)
        return filepath
      else
        return ""
      end
    end
  end

  # Get the path to the peak list file
  def peak_list_file
    if method == "Experimental"
      peak_list.try(:path)
    else
      if spectra_type == "ESI"
        filepath = File.join(PRECOMPUTED_DATA_DIR, "esi", ion_mode, "peaks", computed_file_name)
      else
        filepath = File.join(PRECOMPUTED_DATA_DIR, "ei", "peaks", computed_file_name)
      end
      if File.exists?(filepath)
        return filepath
      else
        return nil
      end
    end
  end

  def delete_spectra_files
    annotated_file = peak_list_file_annotated
    if !(annotated_file.nil? or annotated_file == "")
      puts "Deleting #{annotated_file}"
      FileUtils.rm_f(annotated_file)
    end

    peaks_file = peak_list_file
    if !(peaks_file.nil? or peaks_file == "")
      puts "Deleting #{peaks_file}"
      FileUtils.rm_f(peaks_file)
    end
  end

  def get_spectra_as_hash(annotated = false, add_adduct = nil)
    spectra_hash = {"annotations" => []}
    peak_list_array = []
    current_energy_level = nil
    if annotated && peak_list_file_annotated.present?
      file = peak_list_file_annotated
    else
      file = peak_list_file
    end
    content = File.read(file).split(/\n\n/)
    content[0].split(/\n/).each do |line|
      if line =~ /^#/ # skip lines that are comments
        next
      elsif line =~ /^\S+$/
        if current_energy_level.present?
          # Add the adduct peak on, if specified
          if add_adduct?(add_adduct)
            parent_ion_mass = AdductCalculator.neutral_mass_to_adduct_mass(adduct_type, database_molecules.first.neutral_mass)
            peak_list_array << peak_list_array.last.gsub(/^[\d\.]+/, parent_ion_mass.to_s)
          end
          spectra_hash[current_energy_level] = peak_list_array
          peak_list_array = []
        end
        current_energy_level = line.strip
      else
        # If annotated, add spectra id to fragment id
        # This is to separate annotations when we merge spectra
        if annotated && peak_list_file_annotated.present?
          parsed_items = []
          if line =~ /^\s*([-+]?[0-9]*\.?[0-9]+)\s+([-+]?[0-9]*\.?[0-9]+)\s*((?:[\d-]+\s+)+)(?:\((.*)\))+$/
            items = $3.strip.split("\s").map {|i|
              id.to_s + "-" + i
            }
            line = [$1, $2, items, "(" + $4 + ")"].flatten.join(" ")
          end
        else
          # Make sure we only get the peaks, in case there is other weird stuff (i.e. in some NIST spectra)
          if line =~ /^\s*([-+]?[0-9]*\.?[0-9]+)\s+([-+]?[0-9]*\.?[0-9]+)\s*/
            line = [$1, $2].flatten.join(" ")
          end
        end
        peak_list_array << line.strip
      end
    end

    if current_energy_level.present?
      # Add the adduct peak on, if specified
      if add_adduct?(add_adduct)
        parent_ion_mass = AdductCalculator.neutral_mass_to_adduct_mass(adduct_type, database_molecules.first.neutral_mass)
        peak_list_array << peak_list_array.last.gsub(/^[\d\.]+/, parent_ion_mass.to_s)
      end
      spectra_hash[current_energy_level] = peak_list_array
    end

    # These will be the annotations, if we got any
    if content[1].present?
      content[1].split(/\n/).each do |line|
        # Add spectra id to fragment id
        items = line.strip.split("\s")
        items[0] = id.to_s + "-" + items[0]
        line = items.join(" ")
        spectra_hash["annotations"] << line
      end
    end
    spectra_hash
  end

  private

  # Standard file name for computed spectra
  def computed_file_name
    "#{inchi_key}_#{adduct_type}.txt"
  end

  def add_adduct?(add_adduct)
    add_adduct.present? && add_adduct != 'Unknown' &&
      ['[M+H]+', '[M-H]-', '[M]+'].include?(adduct_type) &&
      database_molecules.first.neutral_mass.present?
  end

end
