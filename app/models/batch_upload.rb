class BatchUpload < ActiveRecord::Base
  has_attached_file :batch_file 
  has_attached_file :excel_file   
  do_not_validate_attachment_file_type :batch_file
  do_not_validate_attachment_file_type :excel_file
  belongs_to :batch_submission
  belongs_to :excel_submission

  @PY_PATH = PYTHON_ENV["#{Rails.env}"]['python_path']
  def self.ParseCSV(received_csv,batch_directory,user_session_id)
      compound_name = []
      smiles = []
      solvent = []
      spectrum_type = []
      lines = []
      @batch_values = [[]]
      CSV.open(received_csv, 'r', :col_sep => ",", :quote_char => "|").each do |row|
        lines << row
      end
      
     
      lines.each do |line|
        temp_line = line
        compound_name << temp_line[0]
        smiles << temp_line[1]
        solvent << temp_line[2]
        spectrum_type << temp_line[3]
      end
  
      
      for j in (0..(compound_name.length)-1) do
        @batch_values << [compound_name[j],smiles[j],solvent[j],spectrum_type[j]]
  
      end
  
      @batch_values.delete_at(0)
    
      return @batch_values
  end

  def self.DrawMol(smiles,session_directory,backend_dir,nmr_pred_dir,draw_mol_script_basename,b_u_id,compound_name,user_id,var)

    if compound_name.match(/\s/)
      compound_name = compound_name.gsub! /\s+/, '_'
    end


    draw_mol_script=Rails.root.join("#{backend_dir}", "#{nmr_pred_dir}","#{draw_mol_script_basename}")
    
    session_name=b_u_id.to_s + "_#{compound_name}_" + "#{user_id}_" + "#{var}"
    

    draw_mol_log_basename="#{session_name}_draw_mol.log"
    outputprefix="#{session_name}_draw_mol"
    outputprefix_canonical="#{session_name}_draw_mol_canonical"
    draw_mol_log_abs_path=Rails.root.join("#{session_directory}",draw_mol_log_basename)
    draw_mol_log_abs_path_canonical=Rails.root.join("#{session_directory}","canonical_#{draw_mol_log_basename}")
    

    draw_mol_command=""
    draw_mol_command+="#{@PY_PATH} "
    draw_mol_command+="#{draw_mol_script} "

    draw_mol_arguments=""
    draw_mol_arguments+="--smiles '#{smiles}' "
    draw_mol_arguments+="--outputpath '#{session_directory}' "
    draw_mol_arguments+="--writemol "
    draw_mol_arguments+="--optmol "
    draw_mol_arguments+="--showstereo "
    draw_mol_arguments+="--showequiv "
    draw_mol_arguments+="--smilesorder "

    draw_mol_arguments_non_canonical=draw_mol_arguments
    draw_mol_arguments_non_canonical+="--outputprefix '#{outputprefix}' "
    draw_mol_arguments_non_canonical+=" > '#{draw_mol_log_abs_path}' "
    draw_mol_command_non_canonical=draw_mol_command
    draw_mol_command_non_canonical+="#{draw_mol_arguments_non_canonical} "
    puts "draw_mol_command_non_canonical: #{draw_mol_command_non_canonical}"
    `#{draw_mol_command_non_canonical}`

    draw_mol_arguments_canonical=draw_mol_arguments
    draw_mol_arguments_canonical+="--canonicalorder "
    draw_mol_arguments_canonical+="--outputprefix '#{outputprefix_canonical}' "
    draw_mol_arguments_canonical+=" > '#{draw_mol_log_abs_path_canonical}' "
    draw_mol_command_canonical=draw_mol_command
    draw_mol_command_canonical+="#{draw_mol_arguments_canonical} "
    puts "draw_mol_command_canonical: #{draw_mol_command_canonical}"
    # bll: these output files appear to not be currently used,
    #      and we want to avoid running the command twice.
    #      there is still ongoing discussion on the best/easiest way to get predictable numbering,
    #      so the output could also change
    # `#{draw_mol_command_canonical}`

    non_canonical_mol_basename="#{outputprefix}_output.mol"
    non_canonical_base_image_basename="#{outputprefix}_2d.png"
    non_canonical_equiv_image_basename="#{outputprefix}_equiv.png"

    canonical_mol_basename="#{outputprefix_canonical}_output.mol"
    canonical_base_image_basename="#{outputprefix_canonical}_2d.png"
    canonical_equiv_image_basename="#{outputprefix_canonical}_equiv.png"


    non_canonical_mol_abs_path=Rails.root.join("#{session_directory}",non_canonical_mol_basename)
    non_canonical_base_image_abs_path=Rails.root.join("#{session_directory}",non_canonical_base_image_basename)
    non_canonical_equiv_image_abs_path=Rails.root.join("#{session_directory}",non_canonical_equiv_image_basename)

    canonical_mol_abs_path=Rails.root.join("#{session_directory}",canonical_mol_basename)
    canonical_base_image_abs_path=Rails.root.join("#{session_directory}",canonical_base_image_basename)
    canonical_equiv_image_abs_path=Rails.root.join("#{session_directory}",canonical_equiv_image_basename)

    

    drawmolrdkit_script=Rails.root.join('public', 'python','drawmolrdkit.py')
    output_dir_for_mol=session_directory
    python_log_basename="#{session_name}_nmrpred_initial_predictions.log"
    python_log_abs_path=Rails.root.join("#{session_directory}",python_log_basename)

    input_mol_file = "#{session_name}_draw_mol_output.mol"
    path_to_input_mol_file = Rails.root.join("#{session_directory}","#{input_mol_file}")

    temp_model_basename="#{session_name}_temp_3D.mol"
    temp_image_basename="#{session_name}_temp_3D.png"
    csv_basename="#{session_name}_mol.csv"

    path_to_temp_model=Rails.root.join("#{session_directory}","#{temp_model_basename}")
    path_to_temp_image=Rails.root.join("#{session_directory}","#{temp_image_basename}")
    path_to_csv=Rails.root.join("#{session_directory}","#{csv_basename}")
    nmr_pred_location=Rails.root.join("#{backend_dir}", "#{nmr_pred_dir}")

    
    python_drawmolrdkit_command=""
    python_drawmolrdkit_command+="#{@PY_PATH} "
    python_drawmolrdkit_command+="#{drawmolrdkit_script} "
    python_drawmolrdkit_arguments=""
    python_drawmolrdkit_arguments+="-s '#{smiles}' "
    python_drawmolrdkit_arguments+="-sid '#{session_name}' "
    python_drawmolrdkit_arguments+="-odir_path '#{session_directory}' "
    python_drawmolrdkit_arguments+="-mol_path '#{path_to_temp_model}' "
    python_drawmolrdkit_arguments+="-cs_path '#{path_to_csv}' "
    python_drawmolrdkit_arguments+="-mol_img_path '#{path_to_temp_image}' "
    python_drawmolrdkit_arguments+="-nmrpred_loc '#{nmr_pred_location}' "
    python_drawmolrdkit_arguments+="-input_mol '#{path_to_input_mol_file}' " #USE THIS AS INPUT TO NMRPRED
    python_drawmolrdkit_arguments+="-input_img '#{non_canonical_base_image_abs_path}' " #COPY THIS AND USE AS DISPLAY
    python_drawmolrdkit_arguments+=" > '#{python_log_abs_path}' "
    python_drawmolrdkit_command+="#{python_drawmolrdkit_arguments} "

    puts "Python log is:"
    puts "#{python_log_abs_path}"
    puts "Running drawmolrdkit Python script:"
    puts "#{python_drawmolrdkit_command}"

    `#{python_drawmolrdkit_command}`

    return path_to_temp_model, path_to_temp_image, path_to_csv, compound_name
  end

  def self.GenerateExcel(project_name,excel_generator_script_with_path, list_of_input_files_for_excel_generator, batch_directory, batch_upload_id)
    python_excel_generator_command=""
    python_excel_generator_command+="#{@PY_PATH} "
    python_excel_generator_command+="#{excel_generator_script_with_path} "
    python_excel_generator_command+=""
    python_excel_generator_command+="-i '#{list_of_input_files_for_excel_generator}' "
    python_excel_generator_command+="-p '#{project_name}' "
    python_excel_generator_command+="-l '#{batch_directory}' "

    puts "python_excel_generator_command = #{python_excel_generator_command}"

    `#{python_excel_generator_command}`
    
    execl_file_with_path = "#{batch_directory}/#{project_name}/#{project_name}.xlsx"
    b_u = BatchUpload.find(batch_upload_id.to_i)
    b_u.generated_excel_location = execl_file_with_path
    b_u.save
    # return execl_file_with_path

  end

  def self.ParseExcel(excel_parser_script_with_path,excel_file_with_user_chemical_shifts_and_meta_data, project_name_parser, existing_location_where_project_directory_will_be_created)
    python_excel_parser_command=""
    python_excel_parser_command+="#{@PY_PATH} "
    python_excel_parser_command+="#{excel_parser_script_with_path} "
    python_excel_parser_command+=""
    python_excel_parser_command+="-i '#{excel_file_with_user_chemical_shifts_and_meta_data}' "
    python_excel_parser_command+="-p '#{project_name_parser}' "
    python_excel_parser_command+="-l '#{existing_location_where_project_directory_will_be_created}' "

    puts "python_excel_parser_command = #{python_excel_parser_command}"

    `#{python_excel_parser_command}`
    
    # execl_file_with_path = "#{batch_directory}/#{project_name}/#{project_name}.xlsx"
    # return execl_file_with_path

  end

  def self.SaveNP(parsed_excel_file_path,batch_upload_id)
    files_to_import_csv = parsed_excel_file_path+"/files_to_import.csv"
    
    @batch_upload = BatchUpload.find(batch_upload_id.to_i)
    @batch_submission_id = @batch_upload.batch_submission_id
    @chemical_shift_submissions = ChemicalShiftSubmission.where(:batch_submission_id => @batch_submission_id.to_i)
    
    @lines_in_file = [[]]
    CSV.open(files_to_import_csv, 'r', :col_sep => ",", :quote_char => "|").each do |row|
      @lines_in_file << row
    end
    @lines_in_file.delete_at(0)

    for i in (0..@lines_in_file.length-1) do
      @chemical_shift_submission = @chemical_shift_submissions[i]
      
      line = @lines_in_file[i]
      
      @meta_data_file = line[3]
      
      @shift_assignment_file = line[2]
      
      save_meta_data(@chemical_shift_submission.id,@meta_data_file)
      save_chemical_shift(@chemical_shift_submission.id,@shift_assignment_file)
      @np = NaturalProduct.find(@chemical_shift_submission.natural_product_id)
      generate_nmrml(@chemical_shift_submission.id,@chemical_shift_submission.user_session_id,@np,@chemical_shift_submission.user_id)
    end
  end

  def self.save_meta_data (chemical_shift_submission_id,meta_data_file)
    @chemical_shift_meta_data = ChemicalShiftSubmissionMetaData.find_by(:chemical_shift_submission_id => chemical_shift_submission_id.to_i)
    
    control_var = 0
    CSV.open(meta_data_file, 'r', :col_sep => ",", :quote_char => "|").each do |row|
      control_var = control_var +1
      if control_var != 1 and control_var != 2 and control_var == 3
        if row[1] == "Enter_value_here"
          
          @chemical_shift_meta_data.literature_reference = "NA"
        else
          @chemical_shift_meta_data.literature_reference = row[1]
          
        end

      elsif control_var != 1 and control_var != 2 and control_var == 4
        if row[1] == "Enter_value_here"
          
          @chemical_shift_meta_data.genus = "NA"
        else
          @chemical_shift_meta_data.genus = row[1]
        end
      elsif control_var != 1 and control_var != 2 and control_var == 5
        if row[1] == "Enter_value_here"
          
          @chemical_shift_meta_data.species = "NA"
        else
          @chemical_shift_meta_data.species = row[1]
        end
      
      elsif control_var != 1 and control_var != 2 and control_var == 8
        if row[1] == "Enter_value_here"
          
          @chemical_shift_meta_data.spectrometer_frequency = "NA"
        else
          @chemical_shift_meta_data.spectrometer_frequency = row[1]
        end
      elsif control_var != 1 and control_var != 2 and control_var == 9
        if row[1] == "Enter_value_here"
         
          @chemical_shift_meta_data.temperature = "20"
        else
          @chemical_shift_meta_data.temperature = row[1]
        end
      elsif control_var != 1 and control_var != 2 and control_var == 10
        if row[1] == "Enter_value_here"
          puts("row[1] = #{row[1]} and control_var = #{control_var}")
          @chemical_shift_meta_data.chemical_shift_standard = "NA"
        else
          @chemical_shift_meta_data.chemical_shift_standard = row[1]
        end
      end
    @chemical_shift_meta_data.save
    end
  end

  def self.save_chemical_shift(chemical_shift_submission_id,shift_assignment_file)
    meta_data = ChemicalShiftSubmissionMetaData.find_by(:chemical_shift_submission_id => chemical_shift_submission_id.to_i)
    
    control_var = 0
    CSV.open(shift_assignment_file, 'r', :col_sep => ",", :quote_char => "|").each do |row|
      control_var = control_var +1
      if control_var != 1 
        
        if meta_data.spectrum_type == "1D-1H"
          if row[1] != "C"
            chemical_shift = ChemicalShift.create(:chemical_shift_submission_id => chemical_shift_submission_id.to_i, :atom_id => row[0], :atom_symbol => row[1], :chemical_shift_pred=> row[2], :multiplet_pred => row[4], :jcoupling_pred => "NA")
            chemical_shift.save
            if row[3] = "Enter_value_here" or row[3] == ""
              chemical_shift.chemical_shift_true = "NA"
            else
              chemical_shift.chemical_shift_true = row[3]
            end
            if row[5] = "Enter_value_here" or row[5] == ""
              chemical_shift.multiplet_pred = "NA"
            else
              chemical_shift.multiplet_pred = row[5]
            end
            if row[6] = "Enter_value_here" or row[6] == ""
              chemical_shift.jcoupling_pred = "NA"
            else
              chemical_shift.jcoupling_pred = row[6]
            end
            chemical_shift.save
          end
        elsif meta_data.spectrum_type == "1D-13C"
          if row[1] != "H"
            chemical_shift = ChemicalShift.create(:chemical_shift_submission_id => chemical_shift_submission_id.to_i,:atom_id => row[0], :atom_symbol => row[1], :chemical_shift_pred => row[2],:multiplet_pred => row[4], :jcoupling_pred => "NA")
            chemical_shift.save
            if row[3] = "Enter_value_here" or row[3] == ""
              chemical_shift.chemical_shift_true = "NA"
            else
              chemical_shift.chemical_shift_true = row[3]
            end
            if row[5] = "Enter_value_here" or row[5] == ""
              chemical_shift.multiplet_pred = "NA"
            else
              chemical_shift.multiplet_pred = row[5]
            end
            if row[6] = "Enter_value_here" or row[6] == ""
              chemical_shift.jcoupling_pred = "NA"
            else
              chemical_shift.jcoupling_pred = row[6]
            end
            chemical_shift.save
          end
        elsif meta_data.spectrum_type == "2D-1H-13C" or meta_data.spectrum_type == "2D-1H/13C"
          chemical_shift = ChemicalShit.create(:chemical_shift_submission_id => chemical_shift_submission_id.to_i,atom_id: row[0], atom_symbol: atom[1], chemical_shift_pred: atom[2],multiplet_pred: atom[4],jcoupling_pred: "NA")
          chemical_shift.save
          if row[3] = "Enter_value_here" or row[3] == ""
            chemical_shift.chemical_shift_true = "NA"
          else
            chemical_shift.chemical_shift_true = row[3]
          end
          if row[5] = "Enter_value_here" or row[5] == ""
            chemical_shift.multiplet_pred = "NA"
          else
            chemical_shift.multiplet_pred = row[5]
          end
          if row[6] = "Enter_value_here" or row[6] == ""
            chemical_shift.jcoupling_pred = "NA"
          else
            chemical_shift.jcoupling_pred = row[6]
          end
          chemical_shift.save
        end
    chemical_shft_submission = ChemicalShiftSubmission.find(chemical_shift_submission_id.to_i)
    chemical_shft_submission.valid = 1
    chemical_shft_submission.save
      end
    end
    batch_submission = BatchSubmission.find(meta_data.chemical_shift_submission.batch_submission_id)
    batch_submission.valid = 1
    batch_submission.save
  end

  def self.generate_nmrml(chemical_shift_submission_id,chemical_shift_submission_user_session_id,np,chemical_shift_submission_user_id)
    puts "Running function generate_nmrml()"
    python_path=@PY_PATH
    backend_dir="backend"
    nmr_pred_dir="nmr_ml"
    nmrml_creator_script_basename="nmrml_creator.py"
    session_name=chemical_shift_submission_id.to_s + "_#{np.np_mrd_id}_" + "#{chemical_shift_submission_user_id}"
    session_id=chemical_shift_submission_user_session_id
    nmrml_creator_script=Rails.root.join("#{backend_dir}", "#{nmr_pred_dir}","#{nmrml_creator_script_basename}")
    session_directory=Rails.root.join('public','downloads',"#{session_id}")
    natural_product_name=np.np_mrd_id
    #New arguments, August 12th 2020:
    chemical_shift_submission = ChemicalShiftSubmission.find(chemical_shift_submission_id.to_i)
    genus=chemical_shift_submission.chemical_shift_submission_meta_data.genus
    species=chemical_shift_submission.chemical_shift_submission_meta_data.species
    literature_reference=chemical_shift_submission.chemical_shift_submission_meta_data.literature_reference
    solvent=chemical_shift_submission.chemical_shift_submission_meta_data.solvent
    spectrum_type=chemical_shift_submission.chemical_shift_submission_meta_data.spectrum_type
    spectrometer_frequency=chemical_shift_submission.chemical_shift_submission_meta_data.spectrometer_frequency
    temperature=chemical_shift_submission.chemical_shift_submission_meta_data.temperature
    chemical_shift_standard=chemical_shift_submission.chemical_shift_submission_meta_data.chemical_shift_standard
    # literature_reference_type=chemical_shift_submission.chemical_shift_submission_meta_data.literature_reference_type
    # physical_state_of_compound=chemical_shift_submission.chemical_shift_submission_meta_data.physical_state_of_compound
    # melting_point=chemical_shift_submission.chemical_shift_submission_meta_data.melting_point
    # boiling_point=chemical_shift_submission.chemical_shift_submission_meta_data.boiling_point
    literature_reference_type="NA"
    physical_state_of_compound="NA"
    melting_point="NA"
    boiling_point="NA"

    


    nmrpred_1h_peaklist_abs_path=Rails.root.join("#{session_directory}","#{session_name}_1h_peaklist.txt")
    nmrpred_output_mol_abs_path=Rails.root.join("#{session_directory}","#{session_name}_output.mol")
    nmrml_creator_log_abs_path=Rails.root.join("#{session_directory}","#{session_name}_creator.log")
    # New NMRpred inputs August 12th 2020
    nmrpred_param_abs_path=Rails.root.join("#{session_directory}","#{session_name}_1h_params.txt")
    nmrpred_fid_abs_path=Rails.root.join("#{session_directory}","#{session_name}_1h_fid.txt")
    nmrpred_spectrum_abs_path=Rails.root.join("#{session_directory}","#{session_name}_1h_spectrum.txt")
    nmrpred_13c_peaklist_abs_path=Rails.root.join("#{session_directory}","#{session_name}_13c_peaklist.txt")
    nmrpred_13c_param_abs_path=Rails.root.join("#{session_directory}","#{session_name}_13c_params.txt")
    nmrpred_13c_fid_abs_path=Rails.root.join("#{session_directory}","#{session_name}_13c_fid.txt")
    nmrpred_13c_spectrum_abs_path=Rails.root.join("#{session_directory}","#{session_name}_c13_spectrum.txt")

    nmrml_basename="#{session_name}.nmrML"
    nmrml_url="public/downloads/#{session_name}.nmrML"
    nmrml_abs_path=Rails.root.join("#{session_directory}","#{nmrml_basename}")

    # session[:nmrml_basename] = nmrml_basename
    # session[:nmrml_url] = nmrml_url
    # session[:nmrml_abs_path] = nmrml_abs_path

    
    nmrml_command=""
    nmrml_command+="#{python_path} "
    nmrml_command+="#{nmrml_creator_script} "
    nmrml_arguments=""
    nmrml_arguments+=" -mol #{nmrpred_output_mol_abs_path} "
    nmrml_arguments+=" -pl #{nmrpred_1h_peaklist_abs_path} "
    nmrml_arguments+=" -output_path #{nmrml_abs_path} "
    nmrml_arguments+=" -name '#{natural_product_name}' "
	nmrml_arguments+=' -name "%s" ' % natural_product_name	
    #New arguments, August 12th 2020
    nmrml_arguments+=" -genus '#{genus}' "
    nmrml_arguments+=" -solvent '#{solvent}' "
    nmrml_arguments+=" -species '#{species}' "
    nmrml_arguments+=" -freq #{spectrometer_frequency} "
    nmrml_arguments+=" -ref '#{literature_reference}' "
    nmrml_arguments+=" -standard #{chemical_shift_standard} "
    nmrml_arguments+=" -temp #{temperature} "
    nmrml_arguments+=" -spec_type '#{spectrum_type}' "
    nmrml_arguments+=" -param_path #{nmrpred_param_abs_path} "
    nmrml_arguments+=" -fid_path #{nmrpred_fid_abs_path} "
    nmrml_arguments+=" -spec_path #{nmrpred_spectrum_abs_path} "

    nmrml_arguments+=" -13C_pl #{nmrpred_13c_peaklist_abs_path} "
    nmrml_arguments+=" -13C_param_path #{nmrpred_13c_param_abs_path} "
    nmrml_arguments+=" -13C_fid_path #{nmrpred_13c_fid_abs_path} "
    nmrml_arguments+=" -13C_spec_path #{nmrpred_13c_spectrum_abs_path} "

    nmrml_arguments+=" -ref_type '#{literature_reference_type}' "
    nmrml_arguments+=" -phys_state '#{physical_state_of_compound}' "
    nmrml_arguments+=" -melt_point '#{melting_point}' "
    nmrml_arguments+=" -boil_point '#{boiling_point}' "

    nmrml_arguments+=" > #{nmrml_creator_log_abs_path} "
    nmrml_command+=nmrml_arguments

    puts "nmrml_command = #{nmrml_command}"
    `#{nmrml_command}`

  end

end