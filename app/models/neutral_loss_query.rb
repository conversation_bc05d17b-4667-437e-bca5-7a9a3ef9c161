class NeutralLossQuery < Query
  # Only these databases have spectra at the right energy levels (10, 20, or 40)
  ESI_DATABASES = Hash[["HMDB", "KEGG", "MassBank", "NIST",
                        "DrugBank", "PhytoHub", "FiehnLib", "ContaminantDB",
                        "iTree", "CASMI2016", "MetaboBASE"].collect { |v| [v, v] }]
  EI_DATABASES = {"HMDB" => "HMDB",
                  "NIST" => "NIST"
                  }

  ESI_PREDICTED_DATABASES = Hash[["HMDB", "MassBank", "FiehnLib", "CASMI2016",
                    "CASMI2012", "ReSpect", "GNPS", "KEGG", "DrugBank", 
                    "FooDB", "ECMDB", "YMDB", "NP-MRD",
                   "LIPID MAPS", "ChEBI Complete",
                   "STOFF-IDENT", "DSSTox"].collect { |v| [v, v] }]

  ESI_EXPERIMENTAL_DATABASES = Hash[["MassBank", "CASMI 2016","CASMI 2012", "ReSpect", "HMDB","GNPS",
                    "FAHFA","RTX5 Fiehnlib","MetaboBASE","RIKEN IMS",
                    "Fiehn HILIC","Pathogen Box","VF-NPL QTOF", 
                    "VF-NPL QExactive","VF-NPL LTQ","RIKEN PlaSMA","Volatile FiehnLib",
                    "EMBL-MCF","POS_Metabolite_IDX"].collect { |v| [v, v] }]
  
                    MASS_TYPES = %w[Original Derivative]

  ESI_SCORING_FUNCTIONS = ['DotProduct + Metadata', 'Dice', 'DotProduct']
  EI_SCORING_FUNCTIONS = ['Jaccard', 'DotProduct']

  # Weights for scoring
  ESI_SIMILARITY_WEIGHT = 0.60
  ESI_REFERENCE_WEIGHT = 0.30
  ESI_ANCESTOR_WEIGHT = 0.10
  EI_SIMILARITY_WEIGHT = 0.80
  EI_REFERENCE_WEIGHT = 0.10
  EI_ANCESTOR_WEIGHT = 0.10
  REFERENCE_CAP = 156
  SIMILARITY_THRESHOLD = 3 * 0.95

  attr_accessor :train # training mode, so search computed only
  attr_accessor :omit_sources # sources to omit, for testing

  serialize :database, Array

  has_attached_file :input_file, :default_url => ""
  has_attached_file :candidates_file, :default_url => ""
  has_attached_file :candidate_spectra_file, :default_url => ""
  # This doesn't work for .msp files, so omit for now
  # validates_attachment :input_file, content_type: { content_type: "text/plain" }
  validates_attachment :candidates_file, content_type: { content_type: "text/plain" }
  validates_attachment :candidate_spectra_file, content_type: { content_type: "text/plain" }
  validates :input_file, presence: true, :unless => :spectra?
  validates :spectra, presence: true, :unless => :input_file?
  validates :num_results, presence: true,
    numericality: { only_integer: true }
  validates :threshold, presence: true,
    numericality: { greater_than: 0, less_than: 1 }
  validates :ppm_mass_tol, presence: true, numericality: true
  validates :abs_mass_tol, presence: true, numericality: true
  validates :scoring_function, presence: true
  validates :candidate_limit,
    numericality: { only_integer: true, less_than: MAX_COMPOUND_NUMBER + 1 },
    allow_blank: true
  validates :neutral_mass, numericality: { greater_than: 0 }, :allow_blank => true
  validates :candidate_ppm_mass_tol, numericality: true, :allow_blank => true
  validates :candidate_abs_mass_tol, numericality: true, :allow_blank => true  
  validates :parent_ion_mass, numericality: { greater_than: 0 }, :allow_blank => true
  validates :parent_ion_mass_type, inclusion: { in: MASS_TYPES }, :allow_blank => true
  validates :adduct_type, inclusion: { in: POSITIVE_ADDUCTS + NEGATIVE_ADDUCTS }, :allow_blank => true
  validates :ion_mode, presence: true, inclusion: { in: ION_MODES }
  validate :spectra_valid
  validate :databases_valid
  # validate :candidates_valid

  after_validation :remove_spectra, :unless => Proc.new{ |q| q.errors.empty? }

  MAX_CANDIDATE_TOLERANCE_DALTON = 10
  MAX_CANDIDATE_TOLERANCE_PPM = 1000
  MAX_CANDIDATES = 100

  @parser = IdentifyParser


  def score_with_metadata?
    scoring_function == 'DotProduct + Metadata'
  end

  def status_completed?
    status == 'Completed'
  end

  # The scoring function to input to cfm-id
  # (as opposed to the scoring function used by this app)
  def cfm_id_scoring_function
    if scoring_function == 'DotProduct + Metadata'
      'DotProduct'
    else
      scoring_function
    end
  end

  # We can use our own parameters if desired
  def get_results

    results = []
    rank = 0
    similarity_weight ||= self.spectra_type == "ESI" ? ESI_SIMILARITY_WEIGHT : EI_SIMILARITY_WEIGHT
    reference_weight ||= self.spectra_type == "ESI" ? ESI_REFERENCE_WEIGHT : EI_REFERENCE_WEIGHT
    ancestor_weight ||= self.spectra_type == "ESI" ? ESI_ANCESTOR_WEIGHT : EI_ANCESTOR_WEIGHT
    reference_cap ||= REFERENCE_CAP
    similarity_threshold ||= SIMILARITY_THRESHOLD
    puts "STATUS #{status}"
    if status_completed?
      
      if (output_file.present?)

        cfm_id_scores = []
        reference_counts = []
        ancestors = []
        predicted_class = nil
        nl_spectrum = self.nl_spectrum

        File.readlines(output_file.path).each do |line|

          rank += 1
  
          array = line.tr('[]', '').tr('"', '').split(' ')
          # puts array
          id_low = array[0]
          id_med = array[1]
          id_high = array[2]
          database_id, database = array[5].split("-")
          if database == 'NP'
            database = 'np-mrd'
          elsif database == 'ChEBI'
            database = 'ChEBI Complete'
          end
          if database.present?
            molecule = DatabaseMolecule.find_by(source: database, source_id: database_id)
            # puts "molecule: #{database_id} - #{molecule.inchi_key}"
            inchi_key = array[6]
            cfm_id_score = array[3].to_f
            structure = array[4]
            predicted = array[7]
            jchem_id = array[8]
            # puts predicted
            if predicted == "true"
              predicted = "Predicted"
            else 
              predicted = "Experimental"
            end

            if molecule.present?
              existing = results.find { |r|
                r[:inchi] == molecule.inchi && r[:database_id] != molecule.source_id
              }
              if existing.present?
                existing[:related] << {database: database, database_id: database_id}
              else 
                cfm_id_scores << cfm_id_score
                reference_count = [[molecule.reference_count.try(:count) || 2, 2].max, reference_cap].min
                ancestor_list = molecule.classification.try(&:ancestors) || []

                reference_counts << reference_count
                ancestors = ancestors.concat(ancestor_list)

                results << {
                  original_rank: rank,
                  cfm_id_score: cfm_id_score.round(4),
                  reference_count: reference_count,
                  direct_parent: molecule.classification.try(&:direct_parent),
                  alternative_parents: molecule.classification.try(&:alternative_parents) || [],
                  ancestors: ancestor_list,
                  id_low: id_low,
                  id_med: id_med,
                  id_high: id_high,
                  database: database,
                  database_id: database_id,
                  structure: structure,
                  smiles: molecule.smiles,
                  inchi: molecule.inchi,
                  inchi_key: molecule.inchi_key,
                  predicted: predicted,
                  jchem_id: jchem_id,
                  related: [],
                  neutral_loss: nl_spectrum
                }
              end
            end
          else 
            results << {
              original_rank: rank,
              cfm_id_score: cfm_id_score.round(4),
              id_low: id_low,
              id_med: id_med,
              id_high: id_high,
              predicted: predicted,
              smiles: structure,
              inchi_key: structure,
              structure: structure,
              jchem_id: jchem_id,
              neutral_loss: nl_spectrum
            }
          end

        end
      end

      if score_with_metadata?
        max_cfm_id_score = cfm_id_scores.max.to_f
        max_reference_count = reference_counts.max.to_f
        total_ancestors = ancestors.compact.uniq

        results.each do |candidate|
          normalized_cfmid_score = (max_cfm_id_score == 0) ? 0 : (candidate[:cfm_id_score] / max_cfm_id_score)
          normalized_reference_count = (max_reference_count == 0) ? 0 : (candidate[:reference_count] / max_reference_count)
          normalized_ancestor_score = (total_ancestors.empty?) ? 0 : (candidate[:ancestors] & total_ancestors).length / total_ancestors.length.to_f
          # If the CFMID score is above a certain threshold, disregard the reference and ancestory scores
          if candidate[:cfm_id_score] >= similarity_threshold
            combined_score = normalized_cfmid_score
          else
            combined_score  = (normalized_cfmid_score * similarity_weight) + (normalized_reference_count * reference_weight) + (normalized_ancestor_score * ancestor_weight)
          end
          candidate[:combined_score] = combined_score.round(4)
          candidate[:reference_score] = normalized_reference_count.round(4)
          candidate[:ancestor_score] = normalized_ancestor_score.round(4)
        end

        results.sort_by!{ |r| [r[:combined_score], r[:cfm_id_score], r[:reference_score], r[:ancestor_score]] }.reverse!
      end

      # Add ranks
      results = results.each_with_index.map do |r, i|
        r[:rank] = i + 1
        r
      end

      if score_with_metadata?
        # Add adjusted rankings to account for ties
        grouped_results = results.group_by { |r| r[:combined_score] }
        grouped_results.each do |score, group|
          group.each do |r|
            group_rank = group.map { |g| g[:rank] }.sum
            r[:adjusted_rank] = (group_rank / group.length).round(1)
          end
        end
        results = grouped_results.values.flatten

        # Get the predicted class, i.e. the direct parents of the top ranked candidate
        # If there is a tie, get the direct parent (if the same) or the most common
        # alternative parent
        first = results.first
        if first[:adjusted_rank] == 1
          predicted_class = [first[:direct_parent]]
        else
          first_group = results.select { |r| r[:adjusted_rank] == first[:adjusted_rank] }
          direct_parents = first_group.map { |r| r[:direct_parent] }
          if direct_parents.uniq.length == 1
            predicted_class = direct_parents.uniq
          else
            alternative_parents = first_group.map { |r| r[:alternative_parents] }.flatten
            counts = alternative_parents.uniq.map { |a| [a, alternative_parents.count(a)] }
            max = counts.map { |c| c[1] }.max
            mode = counts.select { |c| c[1] == max }.map { |c| c[0] }
            predicted_class = mode
          end
        end
      end


      results.sort_by!{ |r| [r[:cfm_id_score]] }.reverse!
    end
    # puts results
    number_of_results = self.num_results - 1
    { results: results[0..number_of_results], predicted_class: predicted_class }
    
  end


  private

  

  def calculate_neutral_mass_from_parent_ion_mass
    self.neutral_mass ||=
      AdductCalculator.adduct_mass_to_neutral_mass(
        self.adduct_type, self.parent_ion_mass)
  end

  # Check if candidate molecule size is valid
  def check_molecule_size(compound, line)
    inchi = structure_to_inchi(compound)
    if inchi.blank?
      self.errors.add(:base, "Invalid SMILES/InChI string at line #{line}")
    else
      compound_size = inchi_to_size(inchi)
      if compound_size.blank? || (compound_size > MAX_COMPOUND_SIZE)
        self.errors.add(:base, "Compound string is invalid or compound is too large at line #{line}.")
      end
    end
  end


  def databases_valid
    if database.is_a?(Array) && database.detect{ |d| !ESI_PREDICTED_DATABASES.values.concat(ESI_EXPERIMENTAL_DATABASES.values).concat(EI_DATABASES.values).include?(d) }
      errors.add(:database, :invalid)
    end
  end

  # Finds the DatabaseMolecules for the given query parameters
  def find_database_molecules(mass, tolerance_options = {}, candidate_limit)
   DatabaseMolecule.get_molecules_for_neutral_mass(self.spectra_type, self.ion_mode, mass, self.database, self.adduct_type, tolerance_options, candidate_limit)
  end

  
  # If validation fails, remove the directory
  def remove_spectra
    FileUtils.rm_rf("public/queries/" + self.secret_id)
  end

end
