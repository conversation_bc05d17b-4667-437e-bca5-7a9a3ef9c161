class IdentifyQuery < Query
  # Only these databases have spectra at the right energy levels (10, 20, or 40)
  ESI_DATABASES = Hash[["HMDB", "KEGG", "MassBank", "NIST",
                        "DrugBank", "PhytoHub", "FiehnLib", "ContaminantDB",
                        "iTree", "CASMI2016", "MetaboBASE"].collect { |v| [v, v] }]
  EI_DATABASES = {"HMDB" => "HMDB",
                  "NIST" => "NIST",
                  }

  ESI_PREDICTED_DATABASES = Hash[["ChEBI", "DrugBank", "DSSTox", "ECMDB", 
                                "FooDB", "HMDB", "KEGG", "LIPID MAPS",
                                "MassBankJP/MassBankEU", "MoNA","NP-MRD",
                                "STOFF-IDENT", "YMDB"].collect { |v| [v, v] }]

  ESI_EXPERIMENTAL_DATABASES = Hash[["HMDB", "MassBankJP/MassBankEU", "MoNA", "TMIC"].collect { |v| [v, v] }]

  MASS_TYPES = %w[Original Derivative]

  ESI_SCORING_FUNCTIONS = ['Dice', 'DotProduct + Metadata', 'DotProduct']
  EI_SCORING_FUNCTIONS = ['Jaccard', 'DotProduct']

  # Weights for scoring
  ESI_SIMILARITY_WEIGHT = 0.60
  ESI_REFERENCE_WEIGHT = 0.30
  ESI_ANCESTOR_WEIGHT = 0.10
  EI_SIMILARITY_WEIGHT = 0.80
  EI_REFERENCE_WEIGHT = 0.10
  EI_ANCESTOR_WEIGHT = 0.10
  REFERENCE_CAP = 156
  SIMILARITY_THRESHOLD = 3 * 0.95

  attr_accessor :train # training mode, so search computed only
  attr_accessor :omit_sources # sources to omit, for testing

  serialize :database, Array

  has_attached_file :input_file, :default_url => ""
  has_attached_file :candidates_file, :default_url => ""
  has_attached_file :candidate_spectra_file, :default_url => ""
  # This doesn't work for .msp files, so omit for now
  # validates_attachment :input_file, content_type: { content_type: "text/plain" }
  validates_attachment :candidates_file, content_type: { content_type: "text/plain" }
  validates_attachment :candidate_spectra_file, content_type: { content_type: "text/plain" }
  # validates :input_file, presence: true, :unless => :spectra?
  validate :spectra#, presence: true, :unless => :input_file?
  validates :num_results, presence: true,
    numericality: { only_integer: true }
  validates :threshold, presence: true,
    numericality: { greater_than: 0, less_than: 1 }
  validates :ppm_mass_tol, presence: true, numericality: true
  # validates :abs_mass_tol, presence: true, numericality: true
  # validates :scoring_function, presence: true
  validates :candidate_limit,
    numericality: { only_integer: true, less_than: MAX_COMPOUND_NUMBER + 1 },
    allow_blank: true
  validates :neutral_mass, numericality: { greater_than: 0 }, :allow_blank => true
  validates :candidate_ppm_mass_tol, numericality: true, :allow_blank => true
  validates :candidate_abs_mass_tol, numericality: true, :allow_blank => true  
  validates :parent_ion_mass, numericality: { greater_than: 0 }, :allow_blank => true
  validates :parent_ion_mass_type, inclusion: { in: MASS_TYPES }, :allow_blank => true
  validates :ion_mode, presence: true, inclusion: { in: ION_MODES }
  validate :spectra_valid
  validate :databases_valid
  validate :candidates_valid

  after_validation :remove_spectra, :unless => Proc.new{ |q| q.errors.empty? }

  MAX_CANDIDATE_TOLERANCE_DALTON = 10
  MAX_CANDIDATE_TOLERANCE_PPM = 1000
  MAX_CANDIDATES = 100

  @parser = IdentifyParser

  def score_with_metadata?
    scoring_function == 'DotProduct + Metadata'
  end

  # The scoring function to input to cfm-id
  # (as opposed to the scoring function used by this app)
  def cfm_id_scoring_function
    if scoring_function == 'DotProduct + Metadata'
      'DotProduct'
    else
      scoring_function
    end
  end

  # We can use our own parameters if desired
  def get_results(similarity_weight: nil, reference_weight: nil, ancestor_weight: nil,
                  reference_cap: nil, similarity_threshold: nil)
    results = []
    predicted_class = nil

    similarity_weight ||= self.spectra_type == "EI" ? ESI_SIMILARITY_WEIGHT : EI_SIMILARITY_WEIGHT
    reference_weight ||= self.spectra_type == "EI" ? ESI_REFERENCE_WEIGHT : EI_REFERENCE_WEIGHT
    ancestor_weight ||= self.spectra_type == "EI" ? ESI_ANCESTOR_WEIGHT : EI_ANCESTOR_WEIGHT
    reference_cap ||= REFERENCE_CAP
    similarity_threshold ||= SIMILARITY_THRESHOLD

    if (output_file.present?)

      cfm_id_scores = []
      reference_counts = []
      ancestors = []

      File.readlines(output_file.path).each do |line|
        if line =~ /^(\d+)\s+([-+]?[0-9]*\.?[0-9e\-]+)\s+(\S+)\s+(\S+)\s*$/
          rank = $1
          cfm_id_score = $2.to_f
          id = $3
          structure = $4

          if database.present?
            database, database_id = id.split("-", 2)
            database_id = database_id.gsub(/_[^_]+$/, "") # Remove the adduct
            
            molecule = DatabaseMolecule.find_by(source: database, source_id: database_id)

            if molecule.present?
              # Check if we have a higher ranked result
              existing = results.find { |r|
                r[:inchi_key] == molecule.inchi_key
              }

              # If a compound with this inchi key is already in the results, add a reference there
              # and don't add this one as a new result
              if existing.present?
                existing[:related] << {database: database, database_id: database_id}
              else
                cfm_id_scores << cfm_id_score
                reference_count = [[molecule.reference_count.try(:count) || 2, 2].max, reference_cap].min
                ancestor_list = molecule.classification.try(&:ancestors) || []

                reference_counts << reference_count
                ancestors = ancestors.concat(ancestor_list)

                results << {
                  original_rank: rank,
                  cfm_id_score: cfm_id_score.round(4),
                  reference_count: reference_count,
                  direct_parent: molecule.classification.try(&:direct_parent),
                  alternative_parents: molecule.classification.try(&:alternative_parents) || [],
                  ancestors: ancestor_list,
                  id: id,
                  database: database,
                  database_id: database_id,
                  structure: structure,
                  inchi_key: molecule.inchi_key,
                  related: []
                }
              end
            end
          else
            results << {
              original_rank: rank,
              cfm_id_score: cfm_id_score,
              id: id,
              structure: structure
            }
          end
        end
      end

      if score_with_metadata?
        max_cfm_id_score = cfm_id_scores.max.to_f
        max_reference_count = reference_counts.max.to_f
        total_ancestors = ancestors.compact.uniq

        results.each do |candidate|
          normalized_cfmid_score = (max_cfm_id_score == 0) ? 0 : (candidate[:cfm_id_score] / max_cfm_id_score)
          normalized_reference_count = (max_reference_count == 0) ? 0 : (candidate[:reference_count] / max_reference_count)
          normalized_ancestor_score = (total_ancestors.empty?) ? 0 : (candidate[:ancestors] & total_ancestors).length / total_ancestors.length.to_f
          # If the CFMID score is above a certain threshold, disregard the reference and ancestory scores
          if candidate[:cfm_id_score] >= similarity_threshold
            combined_score = normalized_cfmid_score
          else
            combined_score  = (normalized_cfmid_score * similarity_weight) + (normalized_reference_count * reference_weight) + (normalized_ancestor_score * ancestor_weight)
          end
          candidate[:combined_score] = combined_score.round(4)
          candidate[:reference_score] = normalized_reference_count.round(4)
          candidate[:ancestor_score] = normalized_ancestor_score.round(4)
        end

        results.sort_by!{ |r| [r[:combined_score], r[:cfm_id_score], r[:reference_score], r[:ancestor_score]] }.reverse!
      end

      # Add ranks
      results = results.each_with_index.map do |r, i|
        r[:rank] = i + 1
        r
      end

      if score_with_metadata?
        # Add adjusted rankings to account for ties
        grouped_results = results.group_by { |r| r[:combined_score] }
        grouped_results.each do |score, group|
          group.each do |r|
            group_rank = group.map { |g| g[:rank] }.sum
            r[:adjusted_rank] = (group_rank / group.length).round(1)
          end
        end
        results = grouped_results.values.flatten

        # Get the predicted class, i.e. the direct parents of the top ranked candidate
        # If there is a tie, get the direct parent (if the same) or the most common
        # alternative parent
        first = results.first
        if first[:adjusted_rank] == 1
          predicted_class = [first[:direct_parent]]
        else
          first_group = results.select { |r| r[:adjusted_rank] == first[:adjusted_rank] }
          direct_parents = first_group.map { |r| r[:direct_parent] }
          if direct_parents.uniq.length == 1
            predicted_class = direct_parents.uniq
          else
            alternative_parents = first_group.map { |r| r[:alternative_parents] }.flatten
            counts = alternative_parents.uniq.map { |a| [a, alternative_parents.count(a)] }
            max = counts.map { |c| c[1] }.max
            mode = counts.select { |c| c[1] == max }.map { |c| c[0] }
            predicted_class = mode
          end
        end
      end

    end

    { results: results, predicted_class: predicted_class }
  end

  private

  # Make candidates file from list input, and vice versa
  def make_candidates
    if !self.candidates.blank? && self.candidates_file.blank?
      file = Tempfile.new('candidates')
      begin
        file.write(self.candidates.strip)
        file.rewind
      rescue
        self.errors.add(:base, "Error processing candidates text.")
        return false
      else
        self.candidates_file = file
      ensure
        file.close
        file.unlink
      end
    elsif !self.candidates_file.blank?
      begin
        file = !self.candidates_file.queued_for_write[:original].nil? ? self.candidates_file.queued_for_write[:original] : self.candidates_file
        self.candidates = File.read(file.path)
      rescue
        self.errors.add(:base, "Error processing candidates file.")
        return false
      end
    end
    return true
  end

  # Find candidates from database
  # tolerance options hash can be :ppm or :daltons
  def find_candidates(tolerance={})
    begin
      if tolerance[:ppm] && tolerance[:daltons]
        raise "Should not set both ppm and daltons tolerance"
      end
      tolerance[:ppm] = MAX_CANDIDATE_TOLERANCE_PPM if tolerance[:ppm] > MAX_CANDIDATE_TOLERANCE_PPM
      tolerance[:daltons] = MAX_CANDIDATE_TOLERANCE_DALTON if tolerance[:ppm] > MAX_CANDIDATE_TOLERANCE_DALTON
      self.candidate_limit = MAX_CANDIDATES if self.candidate_limit.to_i > MAX_CANDIDATES

      # If adduct type is unknown, calculate and retrieve candidates for all possible adduct types
      # (If Unknown we know this is also an ESI spectra)
      if self.adduct_type == "Unknown"
        masses = []
        if self.ion_mode == "positive"
          (POSITIVE_ADDUCTS - ["Unknown"]).each do |adduct_type|
            masses << AdductCalculator.adduct_mass_to_neutral_mass(adduct_type, self.parent_ion_mass)
          end
        elsif self.ion_mode == "negative"
          (NEGATIVE_ADDUCTS - ["Unknown"]).each do |adduct_type|
            masses << AdductCalculator.adduct_mass_to_neutral_mass(adduct_type, self.parent_ion_mass)
          end
        end
        # Get only the max amount specified
        database_molecules = find_database_molecules(masses, tolerance, self.candidate_limit)
      else
        calculate_neutral_mass_from_parent_ion_mass

        database_molecules = find_database_molecules(self.neutral_mass, tolerance, self.candidate_limit)
      end

      # Generate candidate files and input list
      candidates_list = make_candidate_list(database_molecules)

      candidates_list = remove_bad_candidates(candidates_list)

      if candidates_list.size < 1
        return false
      else
        file = Tempfile.new('candidates')
        begin
          file.write(candidates_list.join("\n"))
          file.rewind
        rescue
          return false
        else
          self.candidates_file = file
          return true
        ensure
          file.close
          file.unlink
        end
      end
    rescue Exception => e
      self.errors.add(:base, "Error Finding Candidates #{e}")
      return false
    end
    return true
  end

  # Check is candidate list is valid
  def check_candidates
    count = 0
    file = !self.candidates_file.queued_for_write[:original].nil? ? self.candidates_file.queued_for_write[:original] : self.candidates_file
    begin
      File.readlines(file.path).each do |line|
        count += 1
        if count > 100
          self.errors.add(:base, "Candidates list too long - max is 100 compounds") and return
        end
        if line =~ /^\S+\s*(InChI=\dS?\/[^\/"]+(?:\/[^\/"]+)*)\s*$/ || line =~ /^\S+\s*([0-9a-zA-Z@\+\-\[\]\(\)\\\/%=#\$\.]+)\s*$/
          check_molecule_size($1, count)
        else
          self.errors.add(:base, "Invalid or missing candidate at line #{count}")
        end
      end
    rescue Exception => e
      self.errors.add(:base, "Invalid candidates file #{e}")
    end
  end

  # Check if input contains all necessary parameters to generate candidates from a database.
  def find_candidates_params_ok?
    self.database.present?  &&
      self.candidate_limit.present? &&
      self.parent_ion_mass.present? &&
      self.adduct_type.present? &&
      ( self.candidate_ppm_mass_tol.present? || self.candidate_abs_mass_tol.present? )
  end

  # Check if input contains either a candidates file or candidates list
  def submit_candidate_params_ok?
    self.candidates_file.present? || self.candidates.present?
  end

  def calculate_neutral_mass_from_parent_ion_mass
    self.neutral_mass ||=
      AdductCalculator.adduct_mass_to_neutral_mass(
        self.adduct_type, self.parent_ion_mass)
  end

  # Check if candidate molecule size is valid
  def check_molecule_size(compound, line)
    inchi = structure_to_inchi(compound)
    if inchi.blank?
      self.errors.add(:base, "Invalid SMILES/InChI string at line #{line}")
    else
      compound_size = inchi_to_size(inchi)
      if compound_size.blank? || (compound_size > MAX_COMPOUND_SIZE)
        self.errors.add(:base, "Compound string is invalid or compound is too large at line #{line}.")
      end
    end
  end

  # When retrieving candidates from the database, remove those that are invalid/too large
  def remove_bad_candidates(candidates_list)
    candidates_list.delete_if do |candidate_line|
      if candidate_line =~ /^\S+\s*(InChI=\dS?\/[^\/"]+(?:\/[^\/"]+)*)\s*$/
        compound = $1
        size = inchi_to_size(compound)
        ( size.blank? || (size > MAX_COMPOUND_SIZE) )
      else
        true
      end
    end
    return candidates_list
  end

  def candidates_valid
    if submit_candidate_params_ok?
      if make_candidates
        check_candidates
      end
    elsif find_candidates_params_ok?
      candidates_tolerance = 
        {ppm:     self.candidate_ppm_mass_tol.to_f} ||
        {daltons: self.candidate_abs_mass_tol.to_f}

      if !find_candidates(candidates_tolerance)
        self.errors.add(:base, "No candidates found in database. Check that your neutral mass and mass tolerance are correct.")
      else
        check_candidates
      end
    else
      self.errors.add(:base, "You must provide a candidate list or give complete information to generate a candidate list from a database.")
    end
    if self.errors.any?
      return false
    else
      return true
    end
    true
  end

  def databases_valid
    if database.is_a?(Array) && database.detect{ |d| !ESI_DATABASES.values.concat(EI_DATABASES.values).include?(d) }
      errors.add(:database, :invalid)
    end
  end

  # Finds the DatabaseMolecules for the given query parameters
  def find_database_molecules(mass, tolerance_options = {}, candidate_limit)
   DatabaseMolecule.get_molecules_for_neutral_mass(self.spectra_type, self.ion_mode, mass, self.database, self.adduct_type, tolerance_options, candidate_limit)
  end

  # Create spectra files and return candidate list
  def make_candidate_list(database_molecules)
    candidates_list = []
    begin
      candidate_root = DatabaseMolecule.spectra_location(self.spectra_type, self.ion_mode)
      database_molecules.each do |candidate|
        candidatedir = File.join(candidate_root, candidate.source.downcase)
        annotated_candidatedir = File.join(candidate_root, candidate.source.downcase + "_annotated")
        unless File.directory?(candidatedir)
          FileUtils.mkdir_p(Rails.root + candidatedir)
        end
        unless File.directory?(annotated_candidatedir)
          FileUtils.mkdir_p(Rails.root + annotated_candidatedir)
        end
        # Get merged spectra
        id = candidate.cfmid_id + "_" + self.adduct_type
        if train
          spectra_hash = candidate.make_training_spectra(self.spectra_type, self.ion_mode, self.adduct_type)
          annotated_spectra_hash = candidate.make_training_spectra(self.spectra_type, self.ion_mode, self.adduct_type, true)
        else
          spectra_hash = candidate.make_spectra(self.spectra_type, self.ion_mode, self.adduct_type, false, omit_sources)
          annotated_spectra_hash = candidate.make_spectra(self.spectra_type, self.ion_mode, self.adduct_type, true, omit_sources)
        end
        file_path = File.join(candidatedir, id + ".txt")
        annotated_file_path = File.join(annotated_candidatedir, id + ".txt")
        # Write spectra to file
        # Format: cfmid_id + " " + inchi + " " + file_path
        open(file_path, 'w') { |f|
          spectra_hash.each do |key, data|
            if key != "annotations"
              f.puts(key)
              f.puts(data)
            end
          end
        }
        open(annotated_file_path, 'w') { |f|
          annotations = []
          annotated_spectra_hash.each do |key, data|
            if key == "annotations"
              annotations.concat(data)
            else
              f.puts(key)
              f.puts(data)
            end
          end
          # All the annotations go at the end
          f.puts("\n")
          f.puts(annotations)
        }
        candidates_list << id + " " + candidate.inchi + " " + file_path
      end

    rescue => e
      puts e.message
      puts e.backtrace
      self.errors.add(:base, "Error creating candidate spectra files, please try again or contact an administrator." )
      return []
    end

    candidates_list
  end

  # If validation fails, remove the directory
  def remove_spectra
    FileUtils.rm_rf("public/queries/" + self.secret_id)
  end

end
