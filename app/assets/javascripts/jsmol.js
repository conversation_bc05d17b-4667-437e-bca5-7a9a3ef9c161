function toggleLabels() {
    // Toggle method to show and hide labels in JSmol
    let btn = $('#toggle-labels');

    if (btn.hasClass("btn-danger")) {
        let btnWidth = btn.width()
        Jmol.script(jmolApplet0, "select all; labels off");
        btn.removeClass("btn-danger")
        btn.addClass("btn-primary")
        btn.text("Show labels")
        // Keep size between label changes
        btn.width(btnWidth+'px')
    } else {
        Jmol.script(jmolApplet0, "select all; labels %e");
        btn.removeClass("btn-primary")
        btn.addClass("btn-danger")
        btn.text("Hide labels")
    }
}

function toggleLabelsStereochemistry() {
    // Toggle method to show and hide labels in JSmol that preserves stereochemistry labels
    let btn = $('#toggle-labels');

    if (btn.hasClass("btn-danger")) {
        let btnWidth = btn.width()
        Jmol.script(jmolApplet3, "select all; labels off;" + stereochemistryLabels());
        btn.removeClass("btn-danger")
        btn.addClass("btn-primary")
        btn.text("Show labels")
        // Keep size between label changes
        btn.width(btnWidth+'px')
    } else {
        Jmol.script(jmolApplet3, "select all; labels %a;" + stereochemistryLabels());
        btn.removeClass("btn-primary")
        btn.addClass("btn-danger")
        btn.text("Hide labels")
    }
}

function stereochemistryLabels(){
    // returns command to add stereochemistry labels in Stereo Labels View
    const rs_labels = $('#stereochemistry-view > #view-stereochemistry-3d').data('rs_configs');
    let stereolabels = "";
    if (rs_labels){
        rs_labels.forEach(assignment => {
            stereolabels += `select atomno=${assignment[0]}; set label "%a (${assignment[1]})";`
        })
    }
    return stereolabels
}

function toggleHydrogens() {
    // Toggle method to show and hide Hydrogens in JSmol
    let btn = $('#toggle-H')

    if(btn.hasClass('btn-danger')) {
        let btnWidth = btn.width()
        Jmol.script(jmolApplet0, "select all; set showHydrogens FALSE");
        btn.removeClass("btn-danger")
        btn.addClass("btn-primary")
        btn.text("Show Hydrogens")
        // Keep size between labels changes
        btn.width(btnWidth+'px')
    } else {
        Jmol.script(jmolApplet0, "select all; set showHydrogens TRUE");
        btn.removeClass("btn-primary")
        btn.addClass("btn-danger")
        btn.text("Hide Hydrogens")
    }
}


function recenter() {
    Jmol.script(jmolApplet0, 'rotate best');
}

function zoomFit() {
    Jmol.script(jmolApplet0, 'zoom 0');
}

function downloadJsmol(name) {
    Jmol.script(jmolApplet0, `write image png "${name}.png"`)
}

function downloadJsmolStereochemistry(name) {
    Jmol.script(jmolApplet3, `write image png "${name}_rs_configuration.png"`)
}