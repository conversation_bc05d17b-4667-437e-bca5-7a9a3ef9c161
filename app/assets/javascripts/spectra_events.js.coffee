window.data = {}
window.retrieving = false

$(document).ready ->
  $('.graphline').click showFragments
  $('td.compare a').click handleCandidateClickMoldb
  if window.compareCandidate?
    $("#comparison-compound").html(parseCompound(window.compareCandidate))

  # Make the identify results table sortable by score
  $(".identify-display-table thead th.no-sort").data("sorter", false);
  $(".identify-display-table").tablesorter({ sortList: [[1,1]] });

# When you click on a graph line, show a modal pop up of the possible fragments
# TO DO: Make this better so the clicking doesn't have to be so precise
showFragments = (evt) ->
  energy = $(this).closest(".chart_container").attr("id")
  query_id = $(this).closest(".chart_container").attr("data-query-id")
  line = $(this).attr("id")
  if window.data[energy][line].structure_ids? && $(this).attr("data-structures") == "true"
    structure_ids = window.data[energy][line].structure_ids
    scores = window.data[energy][line].scores
    html = "<div class=\"modal-dialog modal-sm\">"
    html = html + "<div class=\"modal-content\">"
    html = html + "<div class=\"modal-header\"><button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-hidden=\"true\">×</button>"
    html = html + "<h3>" + structure_ids.length + " Possible Predicted Fragment(s)</h3></div>"
    html = html + "<div class=\"modal-body\">"
    html = html + "<table><tr><th>Rank</th><th>Structure</th><th>Score</th></tr>"

    for structure, i in structure_ids
      html = html + "<tr><td>" + (i + 1) + "</td><td>" + printStructure(i, window.data[energy][line].structure_urls) + "</td><td>" + scores[i] + "</td></td>"

    html = html + "</table></div></div></div>"

    $("#fragment-modal").html(html)
    $("#fragment-modal").modal()

printStructure = (i, structure_urls) ->
  if (structure_urls && structure_urls[i])
    '<img class="small-structure" src="' + structure_urls[i] + '" alt="Structure">'
  else
    'Fragment unknown'

handleCandidateClick = (evt) ->
  if !window.retrieving
    $("#loading-overlay").show()
    window.retrieving = true
    loadCandidate(this)

handleCandidateClickMoldb = (evt) ->
  if !window.retrieving
    $("#loading-overlay").show()
    window.retrieving = true
    loadCandidateMoldb(this)

parseCompound = (candidate) ->
  if (candidate.database)
    database_parts = candidate.compound.split("-", 2)
    id_parts = database_parts[1].split(/_([^_]+)$/, 2)
    database_parts[0] + ": " + id_parts[0] + ", " + id_parts[1]
  else
    return candidate.compound

# Uses ajax to grab a candidate spectra data and then display it
loadCandidate = (btn, timer = null) ->
  query_id = $(btn).attr("data-query_id")
  id = $(btn).attr("data-compound")
  database = $(btn).attr("data-database")

  $.ajax
    url: "/queries/load_compound"
    type: "GET"
    dataType: "JSON"
    data: {'query_id': query_id, 'id': id, 'database': database}
    success: (result) ->
      # If the status is complete we can display it!
      if result.status == "Complete"
        $("[data-compound='" + window.compareCandidate.compound + "']").removeClass("btn-error").addClass("btn-compare").html("Compare")
        $("[data-compound='" + result.compound + "']").removeClass("btn-compare").addClass("btn-error").html("Current")
        $("#comparison-compound").html(parseCompound(result))
        window.compareCandidate = result

        window.retrieving = false
        window.dispatchEvent(new CustomEvent("compare"))
        $("#loading-overlay").fadeOut()
        $('.graphline').click showFragments
        if (timer != null)
          clearInterval(timer)

      # Otherwise we are still waiting for sidekiq to generate the structures
      # So set a timer and keep check again in 5 seconds
      else
        timer = setTimeout(->
          loadCandidate(btn, timer)
          return
        , 5000 )

    error: ->
      alert('Error occured.')
      window.retrieving = false
      $("#loading-overlay").fadeOut()
      $('.graphline').click showFragments
      if (timer != null)
        clearInterval(timer)

# Uses ajax to grab a candidate spectra data and then display it
loadCandidateMoldb = (btn, timer = null) ->
  query_id = $(btn).attr("data-query_id")
  id_low = $(btn).attr("data-compound_low")
  id_med = $(btn).attr("data-compound_med")
  id_high = $(btn).attr("data-compound_high")
  database = $(btn).attr("data-database")
  selected_compound = $(btn).attr("data-compound")
  current_compound = $("#comparison-compound").val()
  nl_spectrum = $(btn).attr("data-compound-type")
  if (nl_spectrum == "true")
    nl_spectrum = true;
  else 
    nl_spectrum = false;
  console.log(current_compound)
  console.log(selected_compound)
  

  $.ajax
    url: "/queries/load_compound_cfmid"
    type: "GET"
    dataType: "JSON"
    data: {'id_low': id_low, 'id_med': id_med, 'id_high': id_high, 'name': selected_compound, 'nl_search': nl_spectrum}
    success: (result) ->
      console.log(result)
      console.log("RESULTS")
      # If the status is complete we can display it!
      # $("[data-compound='" + selected_compound + "']").removeClass("btn-error").addClass("btn-compare").html("Compare")
      console.log(window.compareCandidate.compound)
      $("[data-compound='" + window.compareCandidate.compound + "']").removeClass("btn-error").addClass("btn-compare").html("Compare")
      $("[data-compound='" + result.compound + "']").removeClass("btn-compare").addClass("btn-error").html("Current")
      $("#comparison-compound").html(selected_compound)
      window.compareCandidate = result

      window.retrieving = false
      window.dispatchEvent(new CustomEvent("compare"))
      $("#loading-overlay").fadeOut()
      # $('.graphline').click showFragments
      # if (timer != null)
      #   clearInterval(timer)

      # # Otherwise we are still waiting for sidekiq to generate the structures
      # # So set a timer and keep check again in 5 seconds
      # else
      #   timer = setTimeout(->
      #     loadCandidate(btn, timer)
      #     return
      #   , 5000 )

    error: ->
      alert('Error occured.')
      window.retrieving = false
      $("#loading-overlay").fadeOut()
      $('.graphline').click showFragments
      if (timer != null)
        clearInterval(timer)