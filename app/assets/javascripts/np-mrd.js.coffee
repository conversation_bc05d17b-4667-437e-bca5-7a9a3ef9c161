# Load any reference tooltips
$ ->
  $(".reference-icon-link").tooltip()

# Load any tip tooltips
$ ->
  $(".tips").tooltip()

$ ->
  $('.datatable').dataTable()

  # Customize datatables to change the look
  $('.dataTables_filter label').each ->
    searcher = $(this).find('input').clone(true)
    $(this).html('')
    searcher.appendTo($(this))

  $(".dataTables_filter input").attr('placeholder', 'Search')

$ ->
    $("table").tooltip()

# reset modals loaded from remote when hidden
# allows us to re-use modals
$ ->
  $(document).on 'hidden.bs.modal', (e) ->
    $(e.target).removeData('bs.modal')

$ ->
  $('#load-screen').show()
  # Resize header links font size on window resize
  # The quickfit tolerance param make the text look too small locally but bigger
  # in production, I don't know why...
  $(window).resize () ->
    $('.link-bar.one .link-text').quickfit({max: "60", tolerance: 0.46})
    $('.link-bar.two .link-text').css('font-size', $('.link-bar.one .link-text').css('font-size'))

  $(window).load () ->
    $('.link-bar.one .link-text').quickfit({max: "60", tolerance: 0.46})
    $('.link-bar.two .link-text').css('font-size', $('.link-bar.one .link-text').css('font-size'))

    $('.link-bar.one').animate({
      right: '0%'
    }, 2000)

    $('.link-bar.two').animate({
      right: '0%'
    }, 2000)

    $('#load-screen').hide()

  # Add show loader class to links for MetaboCards using a regex on the url
  # Regex is specific to avoid links to .xml files, etc.
  $("a").filter( ->
    @href.match /natural_products$/
  ).addClass('show-loader')

  $("a").filter( ->
    @href.match /natural_products\/NP\d{7}$/
  ).addClass('show-loader')

  $("a").filter( ->
    @href.match /submissions$/
  ).addClass('show-loader')

  # Show loading gif when clicking on specific links, only if we are NOT holding down a key
  # (i.e. command-click opening a new window)
  $('.show-loader').click (e) ->
    if !e.metaKey
      $('#load-screen').show()

  # Make sure we turn off the loading gif when you use back links
  window.onpageshow = (event) ->
    if event.persisted
      $('#load-screen').hide()

# Truncating text and tables on NP-Card
# Source: https://github.com/jedfoster/Readmore.js/blob/master/README.md
$ ->
  $('.met-desc').readmore
    moreLink: '<a href="#" style="text-align:right;padding-right:10px;font-weight:bold;text-decoration:none;">Read more...</a>'
    lessLink: '<a href="#" style="text-align:right;padding-right:10px;font-weight:bold;text-decoration:none;">Close</a>'
    speed: 1000
    collapsedHeight: 150
    heightMargin: 150

  $('.data-table-container').readmore
    moreLink: '<a href="#" style="text-align:center;font-weight:bold;text-decoration:none;">Show more...</a>'
    lessLink: '<a href="#" style="text-align:center;font-weight:bold;text-decoration:none;">Close</a>'
    speed: 1000
    collapsedHeight: 200
    heightMargin: 200

  $('.acc-num').readmore
    moreLink: '<a href="#" style="padding-left:10px;font-weight:bold;text-decoration:none;">Show more accession numbers</a>'
    lessLink: '<a href="#" style="padding-left:10px;font-weight:bold;text-decoration:none;">Close</a>'
    speed: 1000
    collapsedHeight: 100

$ ->
  $('.comment-icon').tooltip()

# Truncating species list
window.showMore = (more_link, less_link, list_id) ->
  $("#" + more_link).hide()
  $("#" + less_link).show()
  $("." + list_id).css "display", "table-row"
  false

window.showLess = (more_link, less_link, list_id) ->
  $("#" + more_link).show()
  $("#" + less_link).hide()
  $("." + list_id).css "display", "none"
  false

$ ->
  $('#chemical_shift_genus').hide()
  $('#chemical_shift_species').hide()
  $('#chemical_shift_provenance').change (e) ->
    provenance_val = $('#chemical_shift_provenance').val()
    if !(provenance_val == "Other" || provenance_val == "Biotransformation" || provenance_val == "Chemical synthesis")
      $('#chemical_shift_genus').show()
      $('#chemical_shift_species').show()
    else
      $('#chemical_shift_genus').hide()
      $('#chemical_shift_species').hide()

$ ->
  console.log("Starting")
  if !($('#chemical_shift_provenance_verification').val() == "Other" || $('#chemical_shift_provenance_verification').val() == "Biotransformation" || $('#chemical_shift_provenance_verification').val == "Chemical synthesis")
    console.log('INSIDE Verifciaton')
    $('#chemical_shift_genus_verification').show()
  else
    console.log('INSIDE Verifciaton hide')
    $('#chemical_shift_genus_verification').hide()
  $('#chemical_shift_provenance_verification').change (e) ->
    provenance_val_verification = $('#chemical_shift_provenance_verification').val()
    if !(provenance_val_verification == "Other" || provenance_val_verification == "Biotransformation" || provenance_val_verification == "Chemical synthesis")
      $('#chemical_shift_genus_verification').show()
    else
      $('#chemical_shift_genus_verification').hide()

$ ->
  console.log("Starting")

  if !($('#chemical_shift_provenance_edit').val() == "Other" || $('#chemical_shift_provenance_edit').val() == "Biotransformation" || $('#chemical_shift_provenance_edit').val() == "Chemical synthesis")
    $('#chemical_shift_genus_edit').show()
    $('#chemical_shift_species_edit').show()
  else
    $('#chemical_shift_genus_edit').hide()
    $('#chemical_shift_species_edit').hide()
  $('#chemical_shift_provenance_edit').change (e) ->
    provenance_val_edit = $('#chemical_shift_provenance_edit').val()
    if !(provenance_val_edit == "Other" || provenance_val_edit == "Biotransformation" || provenance_val_edit == "Chemical synthesis")
      $('#chemical_shift_genus_edit').show()
      $('#chemical_shift_species_edit').show()
    else
      $('#chemical_shift_genus_edit').hide()
      $('#chemical_shift_species_edit').hide()




$ ->
  $('#submission_genus').hide()
  $('#submission_species').hide()
  $('#submission_provenance').change (e) ->
    provenance_val = $('#submission_provenance').val()
    if !(provenance_val == "Other" || provenance_val == "Biotransformation" || provenance_val == "Chemical synthesis")
      $('#submission_genus').show()
      $('#submission_species').show()
    else
      $('#submission_genus').hide()
      $('#submission_species').hide()


$ ->
  console.log("Starting")
  if !($('#submission_provenance_verification').val() == "Other" || $('#submission_provenance_verification').val() == "Biotransformation" || $('#submission_provenance_verification').val == "Chemical synthesis")
    console.log('INSIDE Verifciaton')
    $('#submission_genus_verification').show()
  else
    console.log('INSIDE Verifciaton hide')
    $('#submission_genus_verification').hide()
  $('#submission_provenance_verification').change (e) ->
    provenance_val_verification = $('#submission_provenance_verification').val()
    if !(provenance_val_verification == "Other" || provenance_val_verification == "Biotransformation" || provenance_val_verification == "Chemical synthesis")
      $('#submission_genus_verification').show()
    else
      $('#submission_genus_verification').hide()


$ ->
  console.log("Starting")

  if !($('#submission_provenance_edit').val() == "Other" || $('#submission_provenance_edit').val() == "Biotransformation" || $('#submission_provenance_edit').val() == "Chemical synthesis")
    $('#submission_genus_edit').show()
    $('#submission_species_edit').show()
  else
    $('#submission_genus_edit').hide()
    $('#submission_species_edit').hide()
  $('#submission_provenance_edit').change (e) ->
    provenance_val_edit = $('#submission_provenance_edit').val()
    if !(provenance_val_edit == "Other" || provenance_val_edit == "Biotransformation" || provenance_val_edit == "Chemical synthesis")
      $('#submission_genus_edit').show()
      $('#submission_species_edit').show()
    else
      $('#submission_genus_edit').hide()
      $('#submission_species_edit').hide()

$ ->
 $('#load-screen').show()
 $.ajax({
  complete: (e) ->
    $('#load-screen').hide()
 })

#$ ->
#  $('.structure-modal-btn').on 'click', () ->
#    if (Jmol)
#      smiles = $(this).data('smiles')
#      Jmol.script(jmolAppletRI, "load SMILES #{smiles}")
#      $("#ri-modal-title").text("Showing structure for #{smiles}")
#    $('.modal-jsmol').modal('show')
#    return false


$ ->
  $('body').on 'click', 'a[data-popup]', (e) ->
    window.open $(this).attr('href'), 'Popup', 'height=600, width=1000'
    e.preventDefault()
    return
$ ->
  $('body').on 'click', 'button[close-window]', (e) ->
    window.close()
  return

$ ->
  $('.ri-modal-btn').on 'click', () ->
    if (typeof Jmol != 'undefined')
      smiles = $(this).data('smiles')
      Jmol.script(jmolAppletRI, "load SMILES #{smiles}; color labels black; font labels 20; set antialiasDisplay; wireframe 15; spacefill 55;label %e;")
      $("#ri-modal-title").text("Showing structure for #{smiles}")
    return

$ ->
  $('#user-prediction-tms-der-table').DataTable({
    pageLength: 10,
    ordering: false,
  });

$ ->
  $('#user-prediction-tbdms-der-table').DataTable({
    pageLength: 10,
    ordering: false,
  });

$ ->
  $('#user-prediction-under-table').DataTable({
    pageLength: 10,
    ordering: false,
  });

$ ->
  $('#database-retention-compounds-under').DataTable({
    pageLength: 10,
    ordering: false,
  });

$ ->
  $('#database-retention-compounds-tms').DataTable({
    pageLength: 10,
    ordering: false,
  });

$ ->
  $('#database-retention-compounds-tbdms').DataTable({
    pageLength: 10,
    ordering: false,
  });

$ ->
  $('#database-retention-compounds').DataTable({
    pageLength: 10,
    ordering: false,
  });

$ ->
  $('#search-results-table').DataTable({
    pageLength: 10,
    ordering: false,
  });

$ ->
  $('.predictions-example-loader').click (evt) ->
    # get smiles
    smiles = $(this).data("smiles")

    # find old iframe
    old = document.getElementsByTagName("iframe")[0];

    # load new marvin and set it up to update the hidden field
    load_marvin_instance("#moldbi-new-draw").then (sketcherInstance) ->
      sketcherInstance.on 'molchange', ->
        sketcherInstance.isEmpty().then ((empty_canvas) ->
          if empty_canvas == true
            $('#moldbi-structure-input').text('').trigger('change')
          else
            sketcherInstance.exportStructure('smiles').then ((source) ->
              $('#moldbi-structure-input').text(source).trigger('change')
            ), (error) ->
              alert "Molecule export failed:" + error
        ), (error) ->
          alert "Empty canvas check failed:" + error
      sketcherInstance.importStructure("smiles", smiles)
      # delete the old iframe
      old.remove()

    # Set the form params
    $('#retention_index_prediction_compound_name').val('1-Methylhistidine').change();
    $('#retention_index_prediction_compound_stationary_phase').val('Semi Standard Non Polar').change();
    $('#retention_index_prediction_compound_derivative_type').val('TMS').change();

$ ->
  $('.search-example-loader').click (evt) ->
    $('#retention_index_compound_ri_compound_name').val('methyl').change();
    $('#retention_index_compound_retention_index_value').val('1750').change();
    $('#retention_index_tolerance').val(3).change();
    $('#retention_index_compound_ri_compound_average_mass').val('150').change();
    $('#mass_tolerance').val('5').change();
    $('#retention_index_compound_compound_derivative_type').val('No-Derivatization').change();
    #$('#tms_count').val('0').change();
    #$('#tbdms_count').val('0').change();
    $('#retention_index_compound_compound_stationary_phase').val('Standard Non Polar').change();