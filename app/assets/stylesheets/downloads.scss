.downloads {
  table {
    th.data-set-col { width: 40%; }
    th.release-col { width: 20%; }
    th.download-col { width: 20%; }
    th.size-col { width: 10%; }
  }
}

.alert-citation {
  margin-top: 2em;
  background: #515151;
  color: white;
  a { color: white; text-decoration: underline;}

  blockquote.primary-citation {
    font-size: 15px;
    border-left: 5px solid silver;
    margin-bottom: 0;
    padding-bottom: 0;
    margin-top: 0;
    padding-top: 0;
    a { color: white; text-decoration: underline;}
  }

  .error {
    background: red;
    font-size: 2.0rem;
    border-radius: 1.0rem;
  }
  
  .success {
    background: green;
    font-size: 2.0rem;
    border-radius: 1.0rem;
  }

  .par-success {
    background:yellow;
    font-size: 2.0rem;
    border-radius: 1.0rem;
    color: black;
  }

}

//.alert-underconstruction {
//  margin-top: 2em;
//  //background: #8EE4AF;
//  font-size: 19px;
//  color: white;
//  //alert {background: #8EE4AF;}
//}

.alert-underconstruction {
  margin-top: 2em;
  background: #515151;
  color: white;
  font-size: 14px;
  a { color: white; text-decoration: underline;}

  blockquote.primary-citation {
    font-size: 15px;
    border-left: 5px solid silver;
    margin-bottom: 0;
    padding-bottom: 0;
    margin-top: 0;
    padding-top: 0;
    a { color: white; text-decoration: underline;}
  }
}