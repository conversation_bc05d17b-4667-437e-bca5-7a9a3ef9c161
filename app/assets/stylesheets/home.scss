// Place all the styles related to the home controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/

// Colors:
$link-hover: #007979;
$banner-main: #00b9c2;
$cfmid-logo-red: #a30000;

@keyframes link-bar-animation-one {
	from {width:0%;}
	to {width:100%;}
}
@keyframes link-bar-animation-two {
	from {width:0%;}
	to {width:85%;}
}
@keyframes link-bar-animation-three {
	from {width:0%;}
	to {width:70%;}
}

* {
	font-family: "Arial";
}

.home-banner {
  position: relative;
  left: -30px;
  top: -10px;
  width: calc(100% + 60px);
  overflow-x: hidden;
  .banner {
    width: 100%;
  }
  .banner-text { // for the shaded area overtop the background image
    font-family: 'Arial';
    position: absolute;
    top: 25.5%;
    height: 51%;
    background-color:rgba(355,355,355,0.8);
    width: 100%;
    .banner-logo {
      height: 90%;
      margin-top: 1%;
      margin-left: 5%;
      float: left;
    }
    .link-bar-area {
			display: flex;
			flex-direction: column;
			justify-content: space-around;
			float: right;
			width: 60%;
			height: 100%;
			padding-top: 1.5%;
			padding-bottom: 1.5%;
			.link-bar {
				display: flex;
				flex-direction: column;
				justify-content: center;
				height: 15%; // vertical size of link bars
				margin-left: auto; // put link bars on right
				background-color: $banner-main;
				white-space: nowrap;
				padding-left: 1%;
				.link-text {
					font-size: 1.5vw;
				}
				&.one {
					width: 100%;
					animation-name: link-bar-animation-one;
					animation-duration: 3s;
				}
				&.two {
					width: 85%;
					animation-name: link-bar-animation-two;
					animation-duration: 3s;
				}
				&.three {
					width: 70%;
					animation-name: link-bar-animation-three;
					animation-duration: 3s;
				}
				a {
					padding: 1% 3%;
					color: white;
					display: table-cell;
					vertical-align: middle;
					&:hover {
						color: $link-hover;
						text-decoration: none;
					}
				}
				span.glyphicon {
					position: relative;
					top: 0.15vw; // push glyphicon down a bit
				}
			}
    }
  }
}

.cfmid-info {
	display: none;
}

.hidden-link {
	display: none;
}

.banner {
  padding: 0px;
}

.citation {
  padding-left: 4em;
  text-indent: -2em;
}

.home-box {
	width: 100%;
	display: flex;
  background-color: $banner-main;
  flex-flow: wrap;
  justify-content: space-between;

	.home-box-citations {
		width: calc(60% - 7px);
		min-width: 450px;
		margin-right: 7px;
		word-wrap: normal;
		word-break: normal;
		white-space: normal;
		color: white;
		padding: 1em 2em;
		margin: 0.5em 0em;
		display: inline-block;
		a {
		color: white;
		}
	}

	.home-box-source {
		width: calc(40% - 7px);
		min-width: 300px;
		max-width: 420px;
		height: calc(100% - 1em);
		margin-left: 7px;
		word-wrap: normal;
		word-break: normal;
		white-space: normal;
		padding: 1em 2em;
		margin: 0.5em 0em;
		display: inline-block;
		color: white;
		a {
		color: white;
		}
	}
}

.home-box-info {
	word-wrap: normal;
	word-break: normal;
	white-space: normal;
	
	padding: 1em 2em;
	
	margin: 0.5em 0em;
	display: inline-block;
}

.cfmid-info {
	
	table.info-table {
		th {
			white-space: nowrap;
			text-align: right;
			vertical-align: top;
			padding-right: 1em;
		}
	}
}

// Funding/support footer
.wishart.support {
	.logos {
	  float: right;
	  margin-left: 1em;
	  .logo {
		display: inline;
		padding-left: 4px;
		padding-right: 4px;
	  }
	  .logo-img {
		vertical-align: bottom;
	  }
	  .logos-bottom {
		.chem-axon {
		  @extend .logo;
		  img {
			height: 100px;
			margin: 0 auto;
			display: block;
			@extend .logo-img;
		  }
		}
	  }
	}
  
	.funding {
	  font-size: 14px;
	  color: #74736E;
	  margin: 0em 0.5em 0em 0em;
	  padding: 0.5em;
	}
  }
  
