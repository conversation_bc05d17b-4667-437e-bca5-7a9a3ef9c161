// Place all the styles related to the Identify controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
* {
	font-family: "Arial";
}

.identify-display-table {
  width: 100%;
  background-color: transparent !important;
  font-size: 14px !important;
  th, td {
    padding: 0.5em 1em 0.5em 0.5em !important;
    border: 1px solid silver;
    position: relative;
    background-color: transparent !important;
  }
  th, .tablesorter-default th, .tablesorter-default thead td {
    border-bottom: 3px double silver;
    white-space: nowrap !important;
  }
  td.score, tr:hover > td.score {
    color: #0404B4;
    text-align: center;
    white-space: nowrap;
  }
  td.image {
    img {
      float: none;
    }
    text-align: center;
  }
  td.compare {
    text-align: center;
  }
  .type-tag {
    position: absolute;
    top: 3px;
    right: 3px;
    padding: 2px 5px;
    border-radius: 4px;
    font-size: 12px;
    color: white;
    &.computed {
      background-color: #ff9933;
    }
    &.experimental {
      background-color: #66ccff;
    }
  }
  .classification {
    padding-left: 10px;
  }
  .display-list {
    list-style: none;
    padding-left: 0px;
    margin-bottom: 0px;
    .hidden-item {
      display: none;
    }
  }
  hr {
    margin-top: 5px;
    margin-bottom: 5px;
    border-top: 1px solid silver;
  }
}

.spectra-label {
  font-weight: normal;
}

input.faux-inline {
  display: inline;
  width: 40%;
	min-width: 120px;
	max-width: 150px;
}

input.small {
  display: inline;
  width: 25%;
	min-width: 90px;
	max-width: 100px;
}


select.faux-inline {
  display: inline;
  width: 40%;
	min-width: 120px;
	max-width: 150px;
}

select.small {
  display: inline;
  width: 35%;
	min-width: 90px;
	max-width: 100px;
}

select.very-small {
  display: inline;
  width: 15%;
}

select.long {
	display: inline;
  width: 60%;
	min-width: 200px;
}

label.block-label {
  display: block;
  text-align: left !important;
}

.database-checkbox {
  .field_with_errors {
    display: inline-block;
  }
  label {
    font-weight: normal;
    padding-left: 5px;
  }
  input {
    margin-top: 0px;
    position: relative;
    top: -1px;
  }
}

#comparison-compound {
  font-weight: bold;
  color: #E63434;
}

.bold {
  font-weight: bold;
}

.predicted-class {
  margin-bottom: 10px;
  padding: 1em;
  border-left: 3px solid silver;
}

.example-loader-esi-1{
    border: 1px solid #00b9c2;
    border-color: #00b9c2;
    color: #00b9c2;
    background-color: rgba(0,0,0,0.0);
}

.example-loader-esi-1:hover, .example-loader-esi-1:focus{
    color: #FFF;
    border-color: #00b9c2;
    background-color: #00b9c2;
}

.example-loader-esi-1:active{
    border: 0px;
}

.example-loader-nl-esi-1{
  border: 1px solid #00b9c2;
  border-color: #00b9c2;
  color: #00b9c2;
  background-color: rgba(0,0,0,0.0);
}

.example-loader-nl-esi-1:hover, .example-loader-nl-esi-1:focus{
  color: #FFF;
  border-color: #00b9c2;
  background-color: #00b9c2;
}

.example-loader-nl-esi-1:active{
  border: 0px;
}

.example-loader-nl-esi-2{
  border: 1px solid #00b9c2;
  border-color: #00b9c2;
  color: #00b9c2;
  background-color: rgba(0,0,0,0.0);
}

.example-loader-nl-esi-2:hover, .example-loader-nl-esi-2:focus{
  color: #FFF;
  border-color: #00b9c2;
  background-color: #00b9c2;
}

.example-loader-nl-esi-2:active{
  border: 0px;
}
.example-loader-esi-2{
    border: 1px solid #00b9c2;
    border-color: #00b9c2;
    color: #00b9c2;
    background-color: rgba(0,0,0,0.0);
}

.example-loader-esi-2:hover, .example-loader-esi-2:focus{
    color: #FFF;
    border-color: #00b9c2;
    background-color: #00b9c2;
}

.example-loader-esi-2:active{
    border: 0px;
}
.example-loader-ei{
    border: 1px solid #00b9c2;
    border-color: #00b9c2;
    color: #00b9c2;
    background-color: rgba(0,0,0,0.0);
}

.example-loader-ei:hover, .example-loader:focus{
    color: #FFF;
    border-color: #00b9c2;
    background-color: #00b9c2;
}

.example-loader-ei:active{
    border: 0px;
}
.example-loader-identify{
    border: 1px solid #00b9c2;
    border-color: #00b9c2;
    color: #00b9c2;
    background-color: rgba(0,0,0,0.0);
}

.example-loader-identify:hover, .example-loader:focus{
    color: #FFF;
    border-color: #00b9c2;
    background-color: #00b9c2;
}

.example-loader-identify:active{
    border: 0px;
}

.type {
  float: center;
  position: center;
  text-align: center;
  font-size: 9px;
  // top: 0;
  padding: 2px 10px;;
  margin: 5px;
  border-radius: 5px;
  color: white;
  text-transform: uppercase;
  line-height: 1.5;
}
.predicted {
  background-color: #3059AC;
}
.experimental {
  background-color: #7b54a0;
}

// .tooltip {
//   position: relative;
//   display: inline-block;
//   border-bottom: 1px dotted black; /* If you want dots under the hoverable text */
// }

// /* Tooltip text */
// .tooltip .tooltiptext {
//   visibility: hidden;
//   width: 120px;
//   background-color: black;
//   color: black;
//   text-align: center;
//   padding: 5px 0;
//   border-radius: 6px;
 
//   /* Position the tooltip text - see examples below! */
//   position: absolute;
//   z-index: 1;
// }

// /* Show the tooltip text when you mouse over the tooltip container */
// .tooltip:hover .tooltiptext {
//   visibility: visible;
// }
// .bs-example{
//   margin: 150px;
// }
// /* Styles for custom tooltip template */
// .tooltip-head{
//   color: #fff;
//   background: #000;
//   padding: 10px 10px 5px;
//   border-radius: 4px 4px 0 0;
//   text-align: center;
//   margin-bottom: -2px; /* Hide default tooltip rounded corner from top */
// }
// .tooltip-head h3{
//   margin: 0;
//   font-size: 18px;
// }
// .tooltip-head i{
//   font-size: 22px;
//   vertical-align: bottom;
// }

