body {
  position: relative;
}
main {
  margin-top: 61px;
}

#load-screen {
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0,0,0,0.5);
  z-index: 5;
  width: 100%;
  height: 100%;
  text-align: center;
  display: none;
  img.loader {
    margin-top: 100px;
    margin-left: auto;
    margin-right: auto;
    width: 200px;
  }
}

.navbar-default {
  background-color: #008080;
  border-color: transparent;
  font-family: 'Maven Pro', sans-serif;
  font-size: 16px;
}
.navbar-default .navbar-brand {
  color: $np-mrd_primary_blue;
}
.navbar-default .navbar-brand:hover, .navbar-default .navbar-brand:focus {
  color: $np-mrd_primary_beige;
}
.navbar-default .navbar-text {
  color: #ffffff;
}
.navbar-default .navbar-nav > li > a {
  color: white;
}

.navbar-default .navbar-nav {
  margin-left: 20px;
}

.navbar-default .navbar-nav > li > a:hover, .navbar-default .navbar-nav > li > a:focus {
  color: $np-mrd_primary_beige;
}
.navbar-default .navbar-nav > .active > a, .navbar-default .navbar-nav > .active > a:hover, .navbar-default .navbar-nav > .active > a:focus {
  color: #008080;
  background-color: $np-mrd_primary_beige;
}
.navbar-default .navbar-nav > .open > a, .navbar-default .navbar-nav > .open > a:hover, .navbar-default .navbar-nav > .open > a:focus {
  color: #008080;
  background-color: $np-mrd_primary_beige;
}
.navbar-default .navbar-toggle {
  border-color: $np-mrd_primary_beige;
}
.navbar-default .navbar-toggle:hover, .navbar-default .navbar-toggle:focus {
  background-color: $np-mrd_primary_beige;
}
.navbar-default .navbar-toggle .icon-bar {
  background-color: #ffffff;
}
.navbar-default .navbar-collapse,
.navbar-default .navbar-form {
  border-color: #ffffff;
}
.navbar-default .navbar-link {
  color: #ffffff;
}
.navbar-default .navbar-link:hover {
  color: #008080;
}

@media (max-width: 767px) {
  .navbar-default .navbar-nav .open .dropdown-menu > li > a {
    color: #ffffff;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
    color: #008080;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {
    color: #008080;
    background-color: $np-mrd_primary_beige;
  }
}

.navbar-brand {
  font-size: 28px !important;
  float: left;
  display: block;
  height: 48px;
  font-weight: bold;
  padding: 15px 5px;
  margin-left: 0px !important;
}

.navbar-icon {
  float: left;
  display: block;
  margin: 10px 0px 10px 0px;
  background-image: image-url('cartoon2.jpg');
  background-size: 40px 40px;
  background-repeat: no-repeat;
  height: 30px;
  width: 40px;
  &:hover {
    background-image: image-url('cartoon2.jpg');
  }
}

// Collapse the navbar before it goes to two lines
@media (max-width: 1009px) {
  .navbar-header {
      float: none;
      margin-right: 0 !important;
      margin-left: 0 !important;
  }
  .navbar-toggle {
      display: block;
  }
  .navbar-collapse.collapse:not(.in) {
      display: none !important;
  }
  .navbar-collapse {
      border-top: 1px solid transparent;
      box-shadow: inset 0 1px 0 rgba(255,255,255,0.1);
  }
  .navbar-nav {
      float: none !important;
      margin: 7.5px -15px;
  }
  .navbar-nav>li {
      float: none;
  }
  .navbar-nav>li>a {
      padding-top: 10px;
      padding-bottom: 10px;
  }
}

.navbar-form {
  .btn-search, select {
    margin-left: .3em;
  }
}

// Fix for navbar input-group width being 100%
// https://github.com/twbs/bootstrap/issues/9950
.navbar-form .input-group-btn,
.navbar-form .input-group-addon {
  width: auto;
}

// Images used for sorting tables
.sort-link {
  white-space: nowrap;
  .img-sort-link {
    margin-left: 0.2em;
  }
}

.home-intro {
  @extend .well;
  overflow: hidden;
}

blockquote.references {
  font-size: 13px;
}

.btn-default {
  color: white !important;
  background-color: $np-mrd_primary_between !important;
  border: 1px solid $np-mrd_primary_green !important;
  &:hover {
    color: white !important;
    background-color: $np-mrd_primary_green !important;
    border: 1px solid $np-mrd_primary_green !important;
  }
}

.wishart-feedback-link > a {
    display: inline-block;
    margin-bottom: 0;
    font-weight: normal;
    text-align: center;
    vertical-align: middle;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    border: 1px solid #f5f5f5;
    white-space: nowrap;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.428571429;
    border-radius: 4px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    color: #fff;
    background-color: #337ab7;
    border-color: #2e6da4;
    padding: 10px 16px;
    font-size: 18px;
    line-height: 1.3333333;
    border-radius: 6px;
  &:hover {
    color: white !important;
    background-color: #337ab7 !important;
    border: 1px solid #2e6da4 !important;
  }
}

.btn-primary, .btn-card {
  color: white;
  background-color: $np-mrd_primary_green;
  border: 1px solid $np-mrd_primary_green;
  &:hover {
    color: white;
    background-color: $np-mrd_primary_green;
    border: 1px solid $np-mrd_primary_green;
  }
}

.btn-submit {
  color: white;
  background-color: #337ab7;
  border: 1px solid #337ab7;
  max-width: 100%;
  font-size: 1.75rem;
  &:hover {
    color: white;
    background-color: #337ab7;
    border: 1px solid #337ab7;
  }
}

.page-header {
  border-bottom: 1px solid $np-mrd_primary_green;
}

.pagination > li > a,
.pagination > li > span {
    color: $np-mrd_primary_between;
}

.pagination > li > a:hover, .pagination > li > a:focus,
.pagination > li > span:hover,
.pagination > li > span:focus {
    color: $np-mrd_primary_green;
    background-color: $np-mrd_primary_blue_light;
}

.pagination > .active > a,
.pagination > .active > a:hover, .pagination > .active > a:focus,
.pagination > .active > span,
.pagination > .active > span:hover,
.pagination > .active > span:focus {
    background-color: $np-mrd_primary_between;
    border-color: $np-mrd_primary_between;
}

.pagination > .disabled > span,
.pagination > .disabled > span:hover,
.pagination > .disabled > span:focus,
.pagination > .disabled > a,
.pagination > .disabled > a:hover,
.pagination > .disabled > a:focus {
    color: $np-mrd_primary_green;
}

.content-table > tbody > tr > th.divider {
  background: $np-mrd_primary_green;
  color: #FFFFFF;
  font-weight: normal;
  text-shadow: none;
}

footer {
  hr {
    border-top: 1px solid $np-mrd_primary_green;
  }
}

.sidenav {
  .affix {
    top: 60px;
  }
}

@media (max-width: $screen-xs-max) {
  .sidenav .affix {
    position: static;
  }
}

#flash_notice {
  padding: 15px;
  background-color: #FFF3CD;
  font-weight: bold;
  font-size: 16px;
  color: #907116;

  & .message {
    font-size: 20px;
  }
}


// Center the spectra viewer
div#spectra-viewer {
  margin-left: auto;
  margin-right: auto;
}

#spectra-container {
  background-color: white;
  padding-top: 5px;
}

div#spectra-container.sticky {
  width: 100%;
  position: fixed;
  top: 50px;
  left: 0;
  z-index: 10;
}

#spectra-container hr {
  margin: 0;
  border-top: 0px rgb(200,200,200) solid;
}

.quality_img{
  padding: 1ex 0 1ex ;
  float: right;
  overflow: auto;
}
.quality_sticky{
  width: 100%;
  height: auto;
}

.table-head-reposition{
  display: block;
  position: relative;
  top: -60px;
  visibility: hidden;
  height: 2px;
}

.css-1dbjc4n {
  height: 200px;
  max-height: 200px;
}

.twitter-timeline {
  height: 100% !important;
}

tr {
  .header-name {
    width: 20%;
  }
}
.predictionlabel {
	font-size: 1.05em
}