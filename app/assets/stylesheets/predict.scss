// Place all the styles related to the Predict controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/

* {
	font-family: "Arial";
}

span.note { font-weight: normal; }

.example-loader-inchi-1{
    border: 1px solid #00b9c2;
    border-color: #00b9c2;
    color: #00b9c2;
    background-color: rgba(0,0,0,0.0);
}

.example-loader-inchi-1:hover, .example-loader-inchi-1:focus{
    color: #FFF;
    border-color: #00b9c2;
    background-color: #00b9c2;
}

.example-loader-inchi-1:active{
    border: 0px;
}

.example-loader-smiles-2{
    border: 1px solid #00b9c2;
    border-color: #00b9c2;
    color: #00b9c2;
    background-color: rgba(0,0,0,0.0);
}

.example-loader-smiles-2:hover, .example-loader-smiles-2:focus{
    color: #FFF;
    border-color: #00b9c2;
    background-color: #00b9c2;
}

.example-loader-smiles-2:active{
    border: 0px;
}
.example-loader-smiles-1{
    border: 1px solid #00b9c2;
    border-color: #00b9c2;
    color: #00b9c2;
    background-color: rgba(0,0,0,0.0);
}

.example-loader-smiles-1:hover, .example-loader-smiles-1:focus{
    color: #FFF;
    border-color: #00b9c2;
    background-color: #00b9c2;
}

.example-loader-smiles-1:active{
    border: 0px;
}