// import the CSS framework
@import "bootstrap";

* {
	font-family: "Arial";
}

// make all images responsive by default
img {
  @extend .img-responsive;
  margin: 0 auto;
  }

.alert {
  margin-top: 2em;
}

.navbar-custom {
  background-color: #00b9c2;
  border: 0 !important;
  background-image: -webkit-gradient(linear, left 0%, left 100%, from(#00eaf5), to(#00b9c2));
  background-image: -webkit-linear-gradient(top, #00eaf5, 0%, #00b9c2, 100%);
  background-image: -moz-linear-gradient(top, #00eaf5 0%, #00b9c2 100%);
  background-image: linear-gradient(to bottom, #00eaf5 0%, #00b9c2 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff00eaf5', endColorstr='#ff00b9c2', GradientType=0);
}
.navbar-custom .container {
  width: auto;
  padding: 0px 10px;
}
.navbar-custom .navbar-brand {
  color: #faffff;
  margin-left: 0px !important;
}
.navbar-custom .navbar-header {
  margin-left: 0px;
  margin-left: 15px;
}

.navbar-brand img {
  width: 25px;
  display: inline-block;
  margin-top: -4px;
  margin-right: 5px;
}

.navbar-custom .navbar-brand:hover,
.navbar-custom .navbar-brand:focus {
  color: #007979;
  background-color: transparent;
}
.navbar-custom .navbar-text {
  color: #faffff;
}
.navbar-custom .navbar-nav > li > a {
  color: #faffff;
}
.navbar-custom .navbar-nav > li > a:hover,
.navbar-custom .navbar-nav > li > a:focus {
  color: #007979;
  background-color: transparent;
}
.navbar-custom .navbar-nav > .active > a,
.navbar-custom .navbar-nav > .active > a:hover,
.navbar-custom .navbar-nav > .active > a:focus {
  color: #007979;
  border-bottom: 2px solid #00cfdb;
  background-color: #00cfdb;
  background-image: -webkit-gradient(linear, left 0%, left 100%, from(#faffff), to(#00cfdb));
  background-image: -webkit-linear-gradient(top, #faffff, 0%, #00cfdb, 100%);
  background-image: -moz-linear-gradient(top, #faffff 0%, #00cfdb 100%);
  background-image: linear-gradient(to bottom, #faffff 0%, #00cfdb 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fffaffff', endColorstr='#ff00cfdb', GradientType=0);
}
.navbar-custom .navbar-nav > .disabled > a,
.navbar-custom .navbar-nav > .disabled > a:hover,
.navbar-custom .navbar-nav > .disabled > a:focus {
  color: #cccccc;
  background-color: transparent;
}
.navbar-custom .navbar-toggle {
  border-color: #dddddd;
}
.navbar-custom .navbar-toggle:hover,
.navbar-custom .navbar-toggle:focus {
  background-color: #dddddd;
}
.navbar-custom .navbar-toggle .icon-bar {
  background-color: #cccccc;
}
.navbar-custom .navbar-collapse,
.navbar-custom .navbar-form {
  border-color: #00979e;
}

.navbar-custom .navbar-nav > .dropdown > a:hover .caret,
.navbar-custom .navbar-nav > .dropdown > a:focus .caret {
  border-top-color: #007979;
  border-bottom-color: #007979;
}
.navbar-custom .navbar-nav > .open > a,
.navbar-custom .navbar-nav > .open > a:hover,
.navbar-custom .navbar-nav > .open > a:focus {
  color: #007979;
  background-color: #00cfdb;
  background-image: -webkit-gradient(linear, left 0%, left 100%, from(#faffff), to(#00cfdb));
  background-image: -webkit-linear-gradient(top, #faffff, 0%, #00cfdb, 100%);
  background-image: -moz-linear-gradient(top, #faffff 0%, #00cfdb 100%);
  background-image: linear-gradient(to bottom, #faffff 0%, #00cfdb 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fffaffff', endColorstr='#ff00cfdb', GradientType=0);
}
.navbar-custom .navbar-nav > .open > a .caret,
.navbar-custom .navbar-nav > .open > a:hover .caret,
.navbar-custom .navbar-nav > .open > a:focus .caret {
  border-top-color: #007979;
  border-bottom-color: #007979;
}
.navbar-custom .navbar-nav > .dropdown > a .caret {
  border-top-color: #faffff;
  border-bottom-color: #faffff;
}
.dropdown-menu {
    border: 1px solid #00cfdb;
  }
@media (max-width: 767px) {
  .navbar-custom .navbar-nav .open .dropdown-menu > li > a {
    color: #faffff;
  }
  .navbar-custom .navbar-nav .open .dropdown-menu > li > a:hover,
  .navbar-custom .navbar-nav .open .dropdown-menu > li > a:focus {
    color: #007979;
    background-color: transparent;
  }
  .navbar-custom .navbar-nav .open .dropdown-menu > .active > a,
  .navbar-custom .navbar-nav .open .dropdown-menu > .active > a:hover,
  .navbar-custom .navbar-nav .open .dropdown-menu > .active > a:focus {
    color: #007979;
    background-color: #00cfdb;
    background-image: -webkit-gradient(linear, left 0%, left 100%, from(#faffff), to(#00cfdb));
    background-image: -webkit-linear-gradient(top, #faffff, 0%, #00cfdb, 100%);
    background-image: -moz-linear-gradient(top, #faffff 0%, #00cfdb 100%);
    background-image: linear-gradient(to bottom, #faffff 0%, #00cfdb 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fffaffff', endColorstr='#ff00cfdb', GradientType=0);
  }
  .navbar-custom .navbar-nav .open .dropdown-menu > .disabled > a,
  .navbar-custom .navbar-nav .open .dropdown-menu > .disabled > a:hover,
  .navbar-custom .navbar-nav .open .dropdown-menu > .disabled > a:focus {
    color: #cccccc;
    background-color: transparent;
  }
}
.navbar-custom .navbar-link {
  color: #faffff;
}
.navbar-custom .navbar-link:hover {
  color: #007979;
}

// custom navbar colors
.navbar-brand {
  font-size: 22px;
  font-family: 'Arial';
  border: 2px solid white;
  padding: 5px;
  margin-top: 0.4em;
  margin-bottom: 0.4em;
  margin-right: 2em;
  height: 34px;
  }

.navbar-collapse {
  font-size: 18px;
  font-family: 'Arial';
}

.navbar-default {
    background-color: #00b9c2;
    border-color: #00cfdb;
}
.navbar-default .navbar-brand {
    color: #faffff;
}
.navbar-default .navbar-brand:hover,
.navbar-default .navbar-brand:focus {
    color: #007979;
}
.navbar-default .navbar-nav > li > a {
    color: #faffff;
}
.navbar-default .navbar-nav > li > a:hover,
.navbar-default .navbar-nav > li > a:focus {
    color: #007979;
}
.navbar-default .navbar-nav > .active > a, 
.navbar-default .navbar-nav > .active > a:hover, 
.navbar-default .navbar-nav > .active > a:focus {
    color: #007979;
    background-color: #00cfdb;
}
.navbar-default .navbar-nav > .open > a, 
.navbar-default .navbar-nav > .open > a:hover, 
.navbar-default .navbar-nav > .open > a:focus {
    color: #007979;
    background-color: #00cfdb;
}
.navbar-default .navbar-nav > .dropdown > a .caret {
    border-top-color: #faffff;
    border-bottom-color: #faffff;
}
.navbar-default .navbar-nav > .dropdown > a:hover .caret,
.navbar-default .navbar-nav > .dropdown > a:focus .caret {
    border-top-color: #007979;
    border-bottom-color: #007979;
}
.navbar-default .navbar-nav > .open > a .caret, 
.navbar-default .navbar-nav > .open > a:hover .caret, 
.navbar-default .navbar-nav > .open > a:focus .caret {
    border-top-color: #007979;
    border-bottom-color: #007979;
}
.navbar-default .navbar-toggle {
    border-color: #00cfdb;
}
.navbar-default .navbar-toggle:hover,
.navbar-default .navbar-toggle:focus {
    background-color: #00cfdb;
}
.navbar-default .navbar-toggle .icon-bar {
    background-color: #faffff;
}
@media (max-width: 767px) {
    .navbar-default .navbar-nav .open .dropdown-menu > li > a {
        color: #faffff;
    }
    .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover,
    .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
        color: #007979;
    }
}

header {
  .banner {
    margin-top: 51px;
    padding: 0;
    .banner-link {
      display: block;
      height: 10px; // Height of "banner", though since we removed the background image, we only get whitespace.
      background-color: white;
      margin: 0;
      padding: 0;
    }
  }
}

table.table {
  thead > tr > th {
    border-top: 2px solid #ddd;
    background-color: #EFEFFF;
  }
  thead > tr.search-row > td {
    border-bottom: 2px solid #ddd;
    background-color: #f2f2f2;
  }
}

.page-header {
  margin-top: 0px;
  border-bottom: 5px double #00b9c2;
}

nav.pagination {
  span.page:not(.gap), span.next, span.last, span.first, span.prev {
    padding: 0rem 0.5rem;
    border: 1px solid #ccc;
    text-align: center;
    display: inline-block;
    &:hover {
      background-color: #EFEFFF;
    }
  }
  span.current {
    color: #4B088A;
    font-weight: bold;
    background-color: #EFEFFF;
  }
}

.badge-queued {
  background-color: #5bc0de;
}
.badge-working {
   background-color: #337ab7;
}
.badge-completed {
  background-color: #5cb85c;
}

.badge-completed-with-errors {
  background-color: #f0ad4e;
}
.badge-failed {
  background-color: #d9534f;
}

main {
  @extend .container;
  background-color: #fff;
  padding-bottom: 80px;
  width: 100%;
  }
section {
  @extend .row;
  margin-top: 20px;
}
