* {
	font-family: "Arial";
}

h4.chart-title {
  clear: both;
}

.chart_container {
  position: relative;
  clear: both;
  background: #fff;
  padding: 20px;
  border: 1px solid #ccc;

  div.chart, div.x_axis {
    position: relative;
    left: 40px;
  }

  div.y_axis {
    position: absolute;
    width: 40px;
  }

  div.x_label {
    visibility: none;
    display: none;
  }

  line.graphline {
    stroke: #52B4FA;
    stroke-width:2;
  }

  div.x_axis_label {
    text-align: center;
    margin-left: 40px;
    position: relative;
    margin-top: -25px;
    font-size: 1em;
  }

  div.y_axis_label {
    top: calc(50% - 30px);
    position: absolute;
    -webkit-transform: rotate(-90deg);
    -moz-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    -o-transform: rotate(-90deg);
    filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
    left: -30px;
  }

  .input_spectra_label {
    text-align: center;
    width: 100%;
    position: absolute;
    top: 20px;
    font-size: 1em;
    color: #52B4FA;
    font-weight: bold;
  }
  .candidate_spectra_label {
    text-align: center;
    width: 100%;
    position: absolute;
    bottom: 66px;
    font-size: 1em;
    color: #E63434;
    font-weight: bold;
  }
}

div.item img.structure {
  float: none;
  padding: 10px;
  background: #fff;
  height: auto;
}
