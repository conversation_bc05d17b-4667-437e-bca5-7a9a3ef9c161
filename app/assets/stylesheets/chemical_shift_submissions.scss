// Place all the styles related to the chemical_shift_submissions controller here.
// You can use Sass (SCSS) here: http://sass-lang.com/

.red-header {
  color: red;
}

table.padded {
  & td {
    padding: 5px;
  }

  & p {
    margin: 0;
  }
}

.chemical-shifts-alert-radio {
  padding-top: 5px;

  & input[type="radio"] {
    margin-left: 10px;
    margin-right: 40px;
  }
}

.grid-container {
  display: grid;
  grid-template-columns: minmax(500px, 30%) 1fr;
  grid-column-gap: 20px;
  width: 100%;
}

.grid-item-table {
  width: 100%;
  overflow-x: auto; // allow horizontal scrolling of responsive table
}

table.chemical-shifts {
  & th {
    background-color: #8EE4AF;
    text-align: center;
    padding: 5px;
  }

  & .grey-col {
    text-align: center;
    background-color: #D3D3D3;
  }

  & .white-col {
    background-color: white;
  }

  & input[type="text"] {
    margin-left: 0;
    width: 100%;
  }

  & .center {
    text-align: center;
  }
}

.chemical-shifts > tbody > tr > td, .chemical-shifts > tbody > tr > th, .chemical-shifts > tfoot > tr > td, .chemical-shifts > tfoot > tr > th, .chemical-shifts > thead > tr > td, .chemical-shifts > thead > tr > th {
  border: 1px solid black;
}

.sticky {
  position: -webkit-sticky;
  position: sticky;
  top: 150px; // encompass for taller navbar (shorter screens)
  min-width: 500px; // ensure image is large enough to see
  max-width: 29vw; // 1 less than 30 to contain within grid (set at 30%)
}

.hide-overflow {
  overflow: hidden;
}

.atom-col-head {
  text-align: center;
  vertical-align: top;
}
.atom-col-head-verification {
  vertical-align: top;
}


table.assignment-report {
  & th {
    #background-color: #8EE4AF;
    text-align: center;
    padding: 0 20px;
  }

  & .center {
    text-align: center;
  }
}