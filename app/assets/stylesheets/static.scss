* {
	font-family: "Arial";
}

.help-header {
  color: #4B088A;
  margin: 1em 0em;
  padding: 1em;
  background-color: #EFEFFF;
  border: 1px solid #D7D7E6;
}

.output {
  padding: 1em 2em;
  background-color: #F2F2F2;
  border: 1px solid #ccc;
  margin-bottom: 10px;
}

.example-indent {
  padding-left: 2em;
  padding-bottom: 1em;
  td {
    height: 1em;
  }
}

.inline-example {
  display: inline-block;
  vertical-align: top;
}

.example-cell {
  vertical-align: top;
  padding-right: 0.5em;
}

.info-cell {
  padding-left: 4em;
  font-style: italic;
  color: #00b9c2;
}

.input-highlight {
  color: #00b9c2;
}

.app-highlight {
  color: #00b9c2;
}

.center {
  text-align: center;
}

.indent {
  margin-left: 2em;
}
