.home-banner {
  position: relative;
  left: -30px;
  top: -10px;
  width: calc(100% + 60px);
  overflow-x: hidden;
  .banner {
    width: 100%;
  }
  .banner-text {
    font-family: '<PERSON>ven Pro', sans-serif;
    position: absolute;
    top: 25%;
    height: 50%;
    background-color:rgba(355,355,355,0.8);
    width: 100%;
    .banner-logo {
      height: 85%;
      margin-top: 2%;
      margin-left: 5%;
      float: left;
    }
    .link-bar {
      height: 20%;
      float: right;
      margin-top: 4%;
      background-color: #008080;
      display: table;
      position: relative;
      white-space: nowrap;
      padding: 1%;
      .link-text {
        width: 40%;
      }
      &.one {
        width: 70%;
        right: -70%;
      }
      &.two {
        width: 60%;
        right: -60%;
      }
      a {
        padding: 1% 3%;
        color: white;
        display: table-cell;
        vertical-align: middle;
        &:hover {
          color: $np-mrd_primary_beige;
          text-decoration: none;
        }
      }
      span.glyphicon {
        position: relative;
        top: 3px;
      }
    }
  }
}

.home-text {
  margin: 15px;
  .version-title {
    font-family: '<PERSON>ven Pro', sans-serif;
    font-weight: bold;
    text-align: center;
    color: #008080;
    font-size: 20px;
    margin-bottom: 10px;
  }
  .wishart-twitter {
    margin-top: 40px;
  }
  .more-about {
    text-align: right;
    margin: 5px 0px;
  }
  .more-intro {
    text-align: justify;
  }
  .citation-box {
    h3 {
      margin-top: 10px;
    }
    margin-top: 25px;
    color: white;
    padding: 15px;
    background-color: $np-mrd_primary_between;
    a {
      color: #008080;
      &:hover {
        color: #008080;
      }
    }
  }
}

.help-well {
  text-align: center;
  .help-header {
    font-weight: bold;
    font-size: 18px;
  }
  iframe {
    padding: 10px;
  }
}

.featurette-image {
  max-width: 95%;
  min-width: 50%;
}

.featurette-right {
  margin: 0.5rem -15px 1.75rem 1rem;
  box-shadow: 1rem 1rem 1rem 0.1rem rgba(0, 0, 0, 0.1);
}
