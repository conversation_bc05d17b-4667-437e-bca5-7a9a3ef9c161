// Place all the styles related to the utilities controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/

* {
	font-family: "Arial";
}

.method {
  margin-bottom: 2em;
}

.form-box {
  white-space: normal;
  border: 1px solid #D7D7E6;
  padding: 1em 2em;
  background-color: #EFEFFF;
  margin-top: 1em;
  margin-bottom: 1em;
}

.modal-box {
  margin: 2rem;
  .modal-header {
    border-bottom: 1px solid #D7D7E6;
  }
  .modal-footer {
    border-top: 1px solid #D7D7E6;
  }
}

.center-or {
  padding: 1em;
  font-weight: bold;
  text-align: center;
  font-size: 16px;
}

.end-row {
  margin-bottom: 0em;
}

.btn-reset {
  margin-right: 0.25em;
}

input, select {
  margin-right: 0.25rem;
}

.checkbox input[type="checkbox"] {
  margin-left: 0px;
  top: -11px;
}

.help-text {
  white-space: normal;
}

.help-block {
  color: black;
  margin-bottom: 0em;
}

.btn-reset {
  background-color: hsl(271, 31%, 32%) !important;
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#7b54a0", endColorstr="#52386a");
  background-image: -khtml-gradient(linear, left top, left bottom, from(#7b54a0), to(#52386a));
  background-image: -moz-linear-gradient(top, #7b54a0, #52386a);
  background-image: -ms-linear-gradient(top, #7b54a0, #52386a);
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #7b54a0), color-stop(100%, #52386a));
  background-image: -webkit-linear-gradient(top, #7b54a0, #52386a);
  background-image: -o-linear-gradient(top, #7b54a0, #52386a);
  background-image: linear-gradient(#7b54a0, #52386a);
  border-color: #52386a #52386a hsl(271, 31%, 28%);
  color: #fff !important;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.26);
  -webkit-font-smoothing: antialiased;
}

.btn-submit {
  background-color: hsl(207, 70%, 31%) !important;
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#2585d4", endColorstr="#175486");
  background-image: -khtml-gradient(linear, left top, left bottom, from(#2585d4), to(#175486));
  background-image: -moz-linear-gradient(top, #2585d4, #175486);
  background-image: -ms-linear-gradient(top, #2585d4, #175486);
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #2585d4), color-stop(100%, #175486));
  background-image: -webkit-linear-gradient(top, #2585d4, #175486);
  background-image: -o-linear-gradient(top, #2585d4, #175486);
  background-image: linear-gradient(#2585d4, #175486);
  border-color: #175486 #175486 hsl(207, 70%, 26.5%);
  color: #fff !important;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.29);
  -webkit-font-smoothing: antialiased;
}

.btn-runtime {
  background-color: hsl(55, 56%, 38%) !important;
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#c9bd40", endColorstr="#978e2a");
  background-image: -khtml-gradient(linear, left top, left bottom, from(#c9bd40), to(#978e2a));
  background-image: -moz-linear-gradient(top, #c9bd40, #978e2a);
  background-image: -ms-linear-gradient(top, #c9bd40, #978e2a);
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #c9bd40), color-stop(100%, #978e2a));
  background-image: -webkit-linear-gradient(top, #c9bd40, #978e2a);
  background-image: -o-linear-gradient(top, #c9bd40, #978e2a);
  background-image: linear-gradient(#c9bd40, #978e2a);
  border-color: #978e2a #978e2a hsl(55, 56%, 34.5%);
  color: #fff !important;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.23);
  -webkit-font-smoothing: antialiased;
}

.btn-download {
  background-color: hsl(130, 50%, 31%) !important;
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#3ebb53", endColorstr="#277634");
  background-image: -khtml-gradient(linear, left top, left bottom, from(#3ebb53), to(#277634));
  background-image: -moz-linear-gradient(top, #3ebb53, #277634);
  background-image: -ms-linear-gradient(top, #3ebb53, #277634);
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #3ebb53), color-stop(100%, #277634));
  background-image: -webkit-linear-gradient(top, #3ebb53, #277634);
  background-image: -o-linear-gradient(top, #3ebb53, #277634);
  background-image: linear-gradient(#3ebb53, #277634);
  border-color: #277634 #277634 hsl(130, 50%, 26.5%);
  color: #fff !important;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.29);
  -webkit-font-smoothing: antialiased;
}

.btn-example {
  background-color: hsl(331, 41%, 33%) !important;
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#a84676", endColorstr="#763152");
  background-image: -khtml-gradient(linear, left top, left bottom, from(#a84676), to(#763152));
  background-image: -moz-linear-gradient(top, #a84676, #763152);
  background-image: -ms-linear-gradient(top, #a84676, #763152);
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #a84676), color-stop(100%, #763152));
  background-image: -webkit-linear-gradient(top, #a84676, #763152);
  background-image: -o-linear-gradient(top, #a84676, #763152);
  background-image: linear-gradient(#a84676, #763152);
  border-color: #763152 #763152 hsl(331, 41%, 29.5%);
  color: #fff !important;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.23);
  -webkit-font-smoothing: antialiased;
}

.btn-error {
  background-color: hsl(350, 80%, 33%) !important;
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#d71737", endColorstr="#971027");
  background-image: -khtml-gradient(linear, left top, left bottom, from(#d71737), to(#971027));
  background-image: -moz-linear-gradient(top, #d71737, #971027);
  background-image: -ms-linear-gradient(top, #d71737, #971027);
  background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #d71737), color-stop(100%, #971027));
  background-image: -webkit-linear-gradient(top, #d71737, #971027);
  background-image: -o-linear-gradient(top, #d71737, #971027);
  background-image: linear-gradient(#d71737, #971027);
  border-color: #971027 #971027 hsl(350, 80%, 29.5%);
  color: #fff !important;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.23);
  -webkit-font-smoothing: antialiased;
}

.btn-rerun {
  @extend .btn-runtime;
}

table.input-list {
  th {
    white-space: nowrap;
  }
  td {
    padding-left: 1em;
  }
}

.processing-box {
  word-wrap: break-word;
  word-break: normal;
  white-space: normal;
  border: 1px solid #B2C3D4;
  padding: 1em 2em;
  background-color: #CEE3F6;
  margin-top: 1em;
  margin-bottom: 1em;
}

.results-box {
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  border: 1px solid #C0D0AF;
  padding: 1em 2em;
  background-color: #E3F6CE;
  margin-top: 1em;
  margin-bottom: 1em;
  overflow: hidden;

  div.query-compound {
    text-align: center;

    img.structure {
      border: 1px solid silver;
      max-height: 200px;
      margin-bottom: 20px;
      padding: 10px;
      background: #fff;
    }
  }

  img.frag-structure {
      float: left;
      border: 1px solid #C0D0AF;
      background-color: white;
      max-height: 96px;
      padding: 2px;
    }

}

.error-box {
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  border: 1px solid #D7B4B4;
  padding: 1em 2em;
  background-color: #F6CECE;
  margin-top: 1em;
  margin-bottom: 1em;
}

.runtime-box {
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  border: 1px solid #D5D5B3;
  padding: 1em 2em;
  background-color: #F5F6CE;
  margin-top: 1em;
  margin-bottom: 1em;
}

.input-box {
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  border: 1px solid #D7D7E6;
  padding: 1em 2em;
  background-color: #EFEFFF;
  margin-top: 1em;
  margin-bottom: 1em;
}

.fun-box {
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  border: 1px solid #D9C8B6;
  padding: 1em 2em;
  background-color: #F6E3CE;
  margin-top: 1em;
  margin-bottom: 1em;
}

.output-header {
  font-weight: bold;
  font-size: 14px;
  margin-top: 1em;
  margin-bottom: 0em;
}

.highlight {
  color: #5F04B4;
}

.spectra-table {
  td {
    padding-right: 1em;
    word-break: keep-all;
    vertical-align: top;
    &:first-child, &:nth-child(2) {
      white-space: nowrap;
    }
  }
}

.fragment-table {
  td {
    padding-right: 1em;
  }
}

.transition-table {
  td {
    padding-right: 1em;
  }
}

.identify-table {
  td {
    padding-right: 1em;
  }
}

.frag {
  color: #B40404;
}

.score {
  color: #0404B4;
}

.id {
  color: #0404B4;
}

.rank {
  font-weight: bold;
  color: black;
  text-align: center;
  padding-right: 1.5em;
}

.btn-padded {
  margin: 0.5em;
}

.btn-padded-left {
  margin-left: 0.25em;
}

.btn-right {
  margin: 0.5em;
}

.text-only {
  padding: 10px;
  font-weight: bold;
  font-size: 16px;
}

.radio-label {
  font-weight: normal;
  margin-right: 1em;
  margin-top: 0.5em;
}

.unpadded-col {
  padding: 0em;
}

.results-help {
  float: left;
  width: 75%;
  word-wrap: normal;
  word-break: normal;
  white-space: normal;
  margin: 1em 0em;
  padding: 1em;
  border-left: 3px solid silver;
}

#fragment-modal {
  .modal-dialog {
    width: 400px;
  }
  .modal-header {
    background-color: #EFEFFF;
    border-bottom: 1px solid #D7D7E6;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
    h3 {
      font-size: 18px;
      margin: 0px;
    }
  }
  .modal-body {
    max-height: 400px;
    max-width: 400px;
    overflow-y: scroll;
  }
  table {
    width: 100%;
    th {
      text-align: center;
      padding: 0em 0.5em;
      border: 1px solid silver;
    }
    td {
      img {
        margin: 0em auto;
      }
      text-align: center;
      padding: 1em;
      border: 1px solid silver;
    }
  }
  .small-structure {
    width: 150px;
  }
}

.nav-pills-custom {
  .active {
    a {
      background-color: #00b9c2 !important;
    } 
  }
  a {
    border: 1px solid #00b9c2;
    border-color: #00b9c2;
    color: #00b9c2;
  }
}

#loading-overlay {
  content: '';
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
}

// Animated loader
#circularG{
margin: 10em auto;
position:relative;
width:128px;
height:128px}

.circularG{
position:absolute;
background-color:#FFFFFF;
width:29px;
height:29px;
-moz-border-radius:19px;
-moz-animation-name:bounce_circularG;
-moz-animation-duration:1.04s;
-moz-animation-iteration-count:infinite;
-moz-animation-direction:linear;
-webkit-border-radius:19px;
-webkit-animation-name:bounce_circularG;
-webkit-animation-duration:1.04s;
-webkit-animation-iteration-count:infinite;
-webkit-animation-direction:linear;
-ms-border-radius:19px;
-ms-animation-name:bounce_circularG;
-ms-animation-duration:1.04s;
-ms-animation-iteration-count:infinite;
-ms-animation-direction:linear;
-o-border-radius:19px;
-o-animation-name:bounce_circularG;
-o-animation-duration:1.04s;
-o-animation-iteration-count:infinite;
-o-animation-direction:linear;
border-radius:19px;
animation-name:bounce_circularG;
animation-duration:1.04s;
animation-iteration-count:infinite;
animation-direction:linear;
}

#circularG_1{
left:0;
top:50px;
-moz-animation-delay:0.39s;
-webkit-animation-delay:0.39s;
-ms-animation-delay:0.39s;
-o-animation-delay:0.39s;
animation-delay:0.39s;
}

#circularG_2{
left:14px;
top:14px;
-moz-animation-delay:0.52s;
-webkit-animation-delay:0.52s;
-ms-animation-delay:0.52s;
-o-animation-delay:0.52s;
animation-delay:0.52s;
}

#circularG_3{
top:0;
left:50px;
-moz-animation-delay:0.65s;
-webkit-animation-delay:0.65s;
-ms-animation-delay:0.65s;
-o-animation-delay:0.65s;
animation-delay:0.65s;
}

#circularG_4{
right:14px;
top:14px;
-moz-animation-delay:0.78s;
-webkit-animation-delay:0.78s;
-ms-animation-delay:0.78s;
-o-animation-delay:0.78s;
animation-delay:0.78s;
}

#circularG_5{
right:0;
top:50px;
-moz-animation-delay:0.91s;
-webkit-animation-delay:0.91s;
-ms-animation-delay:0.91s;
-o-animation-delay:0.91s;
animation-delay:0.91s;
}

#circularG_6{
right:14px;
bottom:14px;
-moz-animation-delay:1.04s;
-webkit-animation-delay:1.04s;
-ms-animation-delay:1.04s;
-o-animation-delay:1.04s;
animation-delay:1.04s;
}

#circularG_7{
left:50px;
bottom:0;
-moz-animation-delay:1.17s;
-webkit-animation-delay:1.17s;
-ms-animation-delay:1.17s;
-o-animation-delay:1.17s;
animation-delay:1.17s;
}

#circularG_8{
left:14px;
bottom:14px;
-moz-animation-delay:1.3s;
-webkit-animation-delay:1.3s;
-ms-animation-delay:1.3s;
-o-animation-delay:1.3s;
animation-delay:1.3s;
}

@-moz-keyframes bounce_circularG{
0%{
-moz-transform:scale(1)}

100%{
-moz-transform:scale(.3)}

}

@-webkit-keyframes bounce_circularG{
0%{
-webkit-transform:scale(1)}

100%{
-webkit-transform:scale(.3)}

}

@-ms-keyframes bounce_circularG{
0%{
-ms-transform:scale(1)}

100%{
-ms-transform:scale(.3)}

}

@-o-keyframes bounce_circularG{
0%{
-o-transform:scale(1)}

100%{
-o-transform:scale(.3)}

}

@keyframes bounce_circularG{
0%{
transform:scale(1)}

100%{
transform:scale(.3)}

}

progress[value] { 
  /* Reset the default appearance */
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: none;

  width: 100%;
  height: 20px;
  color: blue;
}  
progress[value]::-webkit-progress-bar {
  clear: both;
  background-color: #eee;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.25) inset;
}  
progress[value]::-webkit-progress-value {  
  background-image:
    -webkit-linear-gradient(135deg, 
                             transparent 33%, rgba(0, 0, 0, 0.1) 33%, 
                             rgba(0,0, 0, 0.1) 66%, transparent 66%),
    -webkit-linear-gradient(top, 
                             rgba(255, 255, 255, 0.25), 
                             rgba(0, 0, 0, 0.25)),
    -webkit-linear-gradient(left, #00b9c2, #5F04B4);

  border-radius: 2px; 
  background-size: 35px 20px, 100% 100%, 100% 100%;
  -webkit-animation: animate-stripes 5s linear infinite;
  animation: animate-stripes 5s linear infinite;
  
}  
progress[value]::-moz-progress-bar {  
  background-image:
    -moz-linear-gradient(
      135deg, 
      transparent 33%, 
      rgba(0, 0, 0, 0.1) 33%, 
      rgba(0, 0, 0, 0.1) 66%, 
      transparent 66% 
    ),
    -moz-linear-gradient(
      top, 
      rgba(255, 255, 255, 0.25), 
      rgba(0, 0, 0, 0.25)
    ),
    -moz-linear-gradient(
      left, 
      #09c, 
      #f44
    );

  border-radius: 2px; 
  background-size: 35px 20px, 100% 100%, 100% 100%; 

}

@-webkit-keyframes animate-stripes {
   100% { background-position: -100px 0px; }
}

@keyframes animate-stripes {
   100% { background-position: -100px 0px; }
}

.spectra-id-col {
  display: none;
}

.form-group .row {
  margin-left: 0;
  margin-right: 0;
}

#form-submit {
  display: none;
}

.control-label {
	text-align: left;
}