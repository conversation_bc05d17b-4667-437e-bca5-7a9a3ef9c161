class NaturalProductIndex
  include Unearth::Index::Indexable

  set_index_name 'natural_products'

  mapping do
    indexes :id, type: :keyword
    indexes :name, type: :text
    # Index all the ids we want to be able to search with
    indexes :np_mrd_id, type: :text
    indexes :cas, type: :keyword
    indexes :external_links, as: Proc.new { identifire["identifiers"].values.compact.uniq unless !identifired? }, type: :keyword

    # Index the description for full text search
    indexes :description, type: :text

    # Index non-id fields we want to be able to search with
   
    indexes :synonyms, as: Proc.new { synonymify["synonyms"].map { |synonym| synonym["name"] } unless !synonymified? }, type: :text
    indexes :iupac, type: :text
    indexes :chemical_formula, as: 'moldb_formula', type: :text
    indexes :mono_mass, as: 'moldb_mono_mass', type: :double
    indexes :average_mass, as: 'moldb_average_mass', type: :double
    indexes :inchikey, as: 'moldb_inchikey', type: :text
    # Classyfire taxonomy
    indexes :classyfied, as: 'classyfired?', type: :boolean
    indexes :direct_parent,
      as: Proc.new { classyfirecation.try(:direct_parent_name) }, type: :text
    indexes :kingdom,
      as: Proc.new { classyfirecation.try(:kingdom_name) }, type: :text
    indexes :superklass,
      as: Proc.new { classyfirecation.try(:superklass_name) }, type: :text
    indexes :klass,
      as: Proc.new { classyfirecation.try(:klass_name) }, type: :text
    indexes :subklass,
      as: Proc.new { classyfirecation.try(:subklass_name) }, type: :text
    indexes :alternative_parents,
      as: Proc.new { classyfirecation.try(:alternative_parent_names) }, type: :text
    indexes :substituents,
      as: Proc.new { classyfirecation.try(:substituent_names) }, type: :text
    indexes :molecular_framework,
      as: Proc.new { classyfirecation.try(:molecular_framework_name) }, type: :text
    indexes :external_descriptors,
      as: Proc.new { classyfirecation.try(:external_descriptor_annotations) }, type: :text
  end

  def self.indexed_document_scopes
    [NaturalProduct.exported]
  end
end

