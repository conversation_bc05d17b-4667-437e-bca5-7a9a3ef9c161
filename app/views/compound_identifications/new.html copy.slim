body
  = render partial: "shared/view_3D"
  //= render partial: 'submissions/warning'

  div.page-header
    h1 Compound Identification
  br

  p Determines compounds that most closely match given spectra and <PERSON><PERSON>'s RI. The spectra and <PERSON><PERSON>'s RI for each candidate compound (in the provided list) are predicted using a pre-trained model and compared to the input spectra. The top candidates are ranked according to how closely they match and returned in a list.


  .well-content.form-wrapper#variable_html style="font-weight: bold; font-size: 17px;"
    div
    = form_for @compound_identification, url: { controller: 'compound_identifications', action: 'create' }, method: :post do |f|
      .row
        /.tab-content
          #find-candidates class="tab-pane #{ 'active' if params[:submit_or_find] == 'find' }"
            .row
              .col-sm-offset-2.col-sm-2
                = f.label :database, "Select database(s)", class: "control-label"
        /- CompoundIdentification::EI_DATABASES.merge(CompoundIdentification::ESI_DATABASES).sort.to_h.each do |human_name, value|
                  div class=(["database-checkbox", value.parameterize, ("ei" if CompoundIdentification::EI_DATABASES.values.include? value), ("esi" if CompoundIdentification::ESI_DATABASES.values.include? value)].compact.join(' '))
                    = f.check_box :database, { multiple: true, checked: @compound_identification.database.include?(value) }, value, nil
                    = f.label "database-" + value.parameterize, human_name, class: "control-label"
        / = f.select :database, options_for_select((@compound_identification.spectra_type == "EI" ? CompoundIdentification::EI_DATABASES : CompoundIdentification::ESI_DATABASES ), selected: @compound_identification.database || "HMDB" ), {}, {class: "form-control", 'data-esi-databases' => CompoundIdentification::ESI_DATABASES.to_json, 'data-ei-databases' => CompoundIdentification::EI_DATABASES.values.to_json }

        h1 style="font-weight: bold; color: black;" Input Form For Spectra data:

        p.form-control-static.help-text(style="margin-left: 20px !important;")
          | N/B: The spectra should be represented as a list of peaks with the format 'mass intensity' on each line. See an
          = link_to " example peak list file", "examples/example_spec.txt"

        br

        .row
          .col-sm-5
            br
              = f.label :spectra, "Input Spectrum values:"
              = text_area_tag :low_spectra, @low_spectra, class: "form-control", rows: 8, wrap: "off"
            br
              .col-sm
                .row
                  .col-sm-4
                    = link_to "#{glyphicon(:save)} Load Example 1".html_safe, "javascript:void(0)",
                            class: "btn btn-default predictions-example-loader", 'data-smiles': "CN(C)C1",
                            'data-no-turbolink': true, 'data-ri': "CN(C)C"

          .col-sm-5
            = f.label "OR", style: "margin-left: 70px !important;"
            = f.label :input_file, "Upload File:", style: "margin-top: 50px !important; margin-left: 150px !important;"
            .col-sm
              .row
                .col-sm-4
                  = f.file_field :input_file, style: "margin-left: 250px !important;"

          br

        .row
          .col-sm-12
            = f.label :scoring_function, "Scoring Function:", class: "col-sm-2 control-label"
            .col-sm-5
              - selected_functions = @ei_functions if defined?(@ei_functions) && @ei_functions.present?
              - selected_functions ||= @esi_functions if defined?(@esi_functions) && @esi_functions.present?
              = f.select :scoring_function, selected_functions, {}, { class: "form-control", style: "width:20%; margin-left: -15px !important;","data-esi-functions": @esi_functions.to_json, "data-ei-functions": @ei_functions.to_json }

        .row(style="margin-left: 0px !important; margin-top: 10px !important;")
          .col-sm-12
            = f.label :num_results, "Number of Results:", class: "control-label", style: "margin-left: 0px !important;"
            = f.text_field :num_results, value: @compound_identification.num_results.blank? ? 10 : @compound_identification.num_results, class: "form-control", style: "width: 20%; margin-left: 200px !important; margin-top: -35px !important;"
        /.form-group
          .text-right
            = "The number of results to return. Leave blank to return all results."
        /=f.label "The number of results to return. Leave blank to return all results."

        .row(style="margin-left: 0px !important; margin-top: 10px !important;")
          .col-sm-12
            = f.label :retention_index, "Retention Index:", style: "margin-left: 0px !important;"
            = f.text_field :retention_index, placeholder: "1756.35", style: "margin-left: 45px !important;"

        br

        div(style="margin-left: 20px !important; margin-top: 50px !important;")
          #form-buttons
            .col-sm-offset-2.col-sm-10
              = f.button 'Reset' , type: 'submit', :onclick => "submit_button_click()", style: "font-weight:bold;color: white; background-color: #bce8f1;", class: 'btn btn-submit'
              = f.button 'Submit' , type: 'submit', :onclick => "submit_button_click()", style: "font-weight:bold;color: white; background-color: #bce8f1; margin-left:50px;", class: 'btn btn-submit'
        br

      javascript:
        document.addEventListener('DOMContentLoaded', function () {
          const compoundNameField = document.querySelector('#eims_prediction_compound_name');
          const derivativeTypeField = document.querySelector('#eims_prediction_compound_derivative_type');

          document.querySelectorAll('.predictions-example-loader').forEach(function (button) {
            button.addEventListener('click', function () {
              const smilesData = this.getAttribute('data-smiles');
              compoundNameField.value = determineCompoundName(smilesData);
              derivativeTypeField.value = determineDerivativeType(smilesData);
            });
          });

          function determineCompoundName(smilesData) {
            if (smilesData === "O=C(O)O") {
              return "carbonic acid";
            } else if (smilesData === "CCC(C(=O)N)N=C(N)N") {
              return "arginine";
            }
          }

          function determineDerivativeType(smilesData) {
            if (smilesData === "O=C(O)O") {
              return "TMS";
            } else if (smilesData === "CCC(C(=O)N)N=C(N)N") {
              return "TBDMS";
            }
          }

          $(function () { // Save the tab state to a hidden field
              $('#submit-candidates-toggle a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                  $('#submit_or_find').val('submit');
              });
              $('#find-candidates-toggle a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                  $('#submit_or_find').val('find');
              });
              $('#spectra-text-toggle a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                  $('#text_or_file').val('text');
              });
              $('#spectra-file-toggle a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                  $('#text_or_file').val('file');
              });
          });
        });