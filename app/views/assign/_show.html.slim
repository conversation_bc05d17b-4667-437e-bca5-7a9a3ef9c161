= render partial: "assign/assign_input"
- unless @query.runtime.blank?
  #results.runtime-box
    h4
      | Runtime Info: 
      button.btn.btn-xs.btn-runtime.hide-runtime[href="#"]
        | Show 
        span.glyphicon.glyphicon-collapse-down
    .collapse.runtime-collapse
      = simple_format(@query.runtime)
- if @query.parsed_output == "Successful"
  #results.results-box
    h4
      | Results: 
      = link_to 'Download <span class="glyphicon glyphicon-save"></span>'.html_safe, @query.output_file.url, class: 'btn btn-xs btn-download'
    .row
      .col-xs-8
        |  Input spectra are shown below. Peaks for which corresponding fragments have been found are colored red; unassigned peaks are colored blue. Hover over the peaks to see the exact mass and intensity values, along with the highest scoring assigned fragments, if found. Clicking on red spectra lines will show a list of all possible predicted fragments for that peak. A list of all possible matching fragments, with structures and transitions, is shown below the spectra. 
      = render 'shared/query_compound'
    - if @query.spectra_type == "ESI"
      h4.chart-title
        | Low Energy Input MsMs Spectrum (10V), 
        = @query.ion_mode == "positive" ? "M+H" : "M-H"
      #energy0
        - if !@spectra_data[:spectra].has_key?("energy0")
          span[style="font-style: italic; margin-left: 1em;"]
            | No MsMs Spectrum
      h4.chart-title
        | Medium Energy Input MsMs Spectrum (20V), 
        = @query.ion_mode == "positive" ? "M+H" : "M-H"
      #energy1
        - if !@spectra_data[:spectra].has_key?("energy1")
          span[style="font-style: italic; margin-left: 1em;"]
            | No MsMs Spectrum
      h4.chart-title
        | High Energy Input MsMs Spectrum (40V), 
        = @query.ion_mode == "positive" ? "M+H" : "M-H"
      #energy2
        - if !@spectra_data[:spectra].has_key?("energy2")
          span[style="font-style: italic; margin-left: 1em;"]
            | No MsMs Spectrum
    - else
      h4.chart-title
        | Predicted MsMs Spectrum (70eV), 
        = @query.ion_mode == "positive" ? "M+H" : "M-H"
      #energy0
        - if !@spectra_data[:spectra].has_key?("energy0")
          span[style="font-style: italic; margin-left: 1em;"]
            | No MsMs Spectrum
    h4 Peak Table, Fragment Structures and Fragment Transitions
    | Fragment IDs are shown in 
    span.frag red
    | . Corresponding scores for each fragment are in 
    span.score blue
    | .
    = render partial: "assign/assign_output", locals: {query: @query, spectra_data: @spectra_data}

  - @spectra_data[:spectra].each_pair do |energy_level, data|
    javascript:
      mz_plot("##{ energy_level }", #{ data.to_json.html_safe }, "#{ @query.secret_id }" );

- unless @query.error.blank?
  #results.error-box
    h4 Error:
    = simple_format(@query.error)
#fragment-modal.modal.fade.bs-example-modal-sm[tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true"]
