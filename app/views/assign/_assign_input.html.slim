.input-box
  h4 Spectrum Assignment Input Parameters: 
  table.input-list
    tr
      th
        | Parent Compound Structure 
        span.note
          | (
          = @query.compound_format
          |  Format)
      td = @query.compound
    tr
      th Parent Compound Mass
      td = @query.compound_mass
    tr
      th MsMs Spectrum File
      td
        = @query.input_file_file_name
        = link_to 'Download <span class="glyphicon glyphicon-save"></span>'.html_safe, @query.input_file.url, class: 'btn btn-xs btn-reset btn-padded'
    tr
      th Spectra Type
      td = @query.spectra_type
    tr
      th Ion Mode
      td = @query.ion_mode.try(:capitalize)
    tr
      th Mass Tolerance 
      - if @query.ppm_mass_tol > -1
        td
          = @query.ppm_mass_tol
          |  ppm
      - elsif @query.abs_mass_tol > -1
        td
          = @query.abs_mass_tol
          |  Da
    tr
      th Status
      td
        span[class="badge badge-#{@query.status.downcase.gsub(/ /, "-")}"] = @query.status