.page-header
  h3 = title("Peak Assignment")
.method
  | This function annotates the fragment ion peaks in a given MS/MS spectra for a given molecule. Users must provide a chemical structure (SMILES or InChI) and a spectral peak list from an MS/MS experiment. Once the data are submitted, a complete list of feasible fragments is computed by CFM-ID, then the most likely fragments for each spectrum peak are determined using a pre-trained SE-CFM model. The results provide an annotated form of the submitted experimental spectra, with each peak appended with a list of ids corresponding to any fragments with the correct mass, listed in order from most to least likely. A hypothesized fragmentation graph is also provided, which contains all the annotated fragments and the possible paths by which they could have been produced. This is provided in the form of a list of fragments and a list of transitions between those fragments.
.form-box
  = form_for(@assign_query, url: assign_new_path, html: {id: 'assign-form', class: "form-horizontal"}, method: "post", multipart: true) do |f|
    .form-group
      = f.label :compound, class: "col-sm-2 control-label" do
        | Parent Compound Structure
        br
        span.note
          | InChI or SMILES format
      .col-sm-10
        = f.text_field :compound, class: "form-control", placeholder: "Enter an InChI or SMILES string"
        span.help-block
          | InChI strings need to start with "InChI=" and are not expected to have any charge - an additional H+ will be added. Maximum compound size is 200 atoms.
          / a.assign-example-loader href="#" data-example="#{render 'utilities/inchi_example'}" data-update="assign_query_compound" data-example-low="#{render 'utilities/low_spectra_example'}" data-update-low="low_spectra" data-update-medium="medium_spectra" data-update-high="high_spectra" InChI example
          / | , 
          / a.assign-example-loader href="#" data-example="#{render 'utilities/smiles_example'}" data-update="assign_query_compound" data-example-low="#{render 'utilities/low_spectra_example'}" data-update-low="low_spectra" data-example-medium="#{render 'utilities/medium_spectra_example'}" data-update-medium="medium_spectra" data-example-high="#{render 'utilities/high_spectra_example'}" data-update-high="high_spectra" SMILES example
          / | , 
          / a.assign-example-loader href="#" data-example="#{render 'utilities/smiles_example_2'}" data-update="assign_query_compound" data-example-low="#{render 'utilities/low_spectra_example_2'}" data-update-low="low_spectra" data-example-medium="#{render 'utilities/medium_spectra_example_2'}" data-update-medium="medium_spectra" data-example-high="#{render 'utilities/high_spectra_example_2'}" data-update-high="high_spectra" another SMILES example.
          / | , or 
          / a.assign-ei-example-loader href="#" data-example="#{render 'utilities/ei_smiles_example'}" data-update="assign_query_compound" data-example-low="#{render 'utilities/ei_spectra_example'}" data-update-low="low_spectra" an EI spectra example.
    .form-group
      .row
        .col-sm-offset-1.col-sm-1
          = label_tag nil, "Examples", class: "control-label"
        .col-sm-3 
          a.assign-example-loader href="#" class="btn btn-default example-loader-inchi-1" data-example="#{render 'utilities/inchi_example'}" data-update="assign_query_compound" data-example-low="#{render 'utilities/low_spectra_example'}" data-update-low="low_spectra" data-update-medium="medium_spectra" data-update-high="high_spectra" Load InChI Example #1
        .col-sm-3 
          a.assign-example-loader href="#" class="btn btn-default example-loader-smiles-1" data-example="#{render 'utilities/smiles_example'}" data-update="assign_query_compound" data-example-low="#{render 'utilities/low_spectra_example'}" data-update-low="low_spectra" data-example-medium="#{render 'utilities/medium_spectra_example'}" data-update-medium="medium_spectra" data-example-high="#{render 'utilities/high_spectra_example'}" data-update-high="high_spectra" Load SMILES Example #1
        .col-sm-3 
          a.assign-example-loader href="#" class="btn btn-default example-loader-smiles-2" data-example="#{render 'utilities/smiles_example_2'}" data-update="assign_query_compound" data-example-low="#{render 'utilities/low_spectra_example_2'}" data-update-low="low_spectra" data-example-medium="#{render 'utilities/medium_spectra_example_2'}" data-update-medium="medium_spectra" data-example-high="#{render 'utilities/high_spectra_example_2'}" data-update-high="high_spectra" Load SMILES Example #2

    .form-group
      = f.label  :spectra_type, "Spectra Type", class: "col-sm-2 control-label"
      .col-sm-2
        = f.select :spectra_type, Query::SPECTRA_TYPES, {}, {class: "form-control small"}
    .form-group
      = f.label  :ion_mode, "Ion Mode", class: "col-sm-2 control-label"
      .col-sm-2
        = f.select :ion_mode, {Negative: "negative", Positive: "positive"}, {}, {class: "form-control faux-inline"}
    .form-group
      = f.label :spectra_input, "Spectra", class: "col-sm-2 control-label"
      = hidden_field_tag :text_or_file, params[:text_or_file]
      .col-sm-10
        ul.nav.nav-pills.nav-pills-custom
          li id="spectra-text-toggle" class="#{"active" if params[:text_or_file] == 'text'}"
            a[href="#spectra-text" data-toggle="tab"] Input Text
          li.text-only OR
          li id="spectra-file-toggle" class="#{"active" if params[:text_or_file] == 'file'}"
            a[href="#spectra-file" data-toggle="tab"] Load a File
      .tab-content
        #spectra-text class="tab-pane #{"active" if params[:text_or_file] == 'text'}"
          .col-sm-offset-2.col-sm-10
            p.form-control-static.help-text The spectra should be represented as a list of peaks with the format 'mass intensity' on each line, and can be entered directly into the corresponding energy level boxes below. Only centroided spectrum can be entered. Multiple energy levels are optional; only one is required.

          .col-sm-offset-2.col-sm-10
            = label_tag nil, "Input Spectra Text", class: "control-label"
          .col-sm-offset-2.col-sm-3
            = label_tag :low_spectra, "Low Energy", class: "control-label spectra-label"
            = text_area_tag :low_spectra, @low_spectra, class: "form-control", rows: 8, wrap: "off"
          .col-sm-3
            = label_tag :medium_spectra, "Medium Energy", class: "control-label spectra-label"
            = text_area_tag :medium_spectra, @medium_spectra, class: "form-control", rows: 8, wrap: "off"
          .col-sm-3
            = label_tag :high_spectra, "High Energy", class: "control-label spectra-label"
            = text_area_tag :high_spectra, @high_spectra, class: "form-control", rows: 8, wrap: "off"
        #spectra-file class="tab-pane #{"active" if params[:text_or_file] == 'file'}"
          .col-sm-offset-2.col-sm-10
            p.form-control-static.help-text
              | The spectra should be represented as a list of peaks with the format 'mass intensity' on each line. For ESI spectra, 'low','medium', and 'high' or 'energy0', 'energy1', and 'energy2' header lines should begin spectra of different energy levels (in that order) and multiple energy levels are optional (only one is required). EI spectra only need to have one energy level. Spectra may also be in .msp file format, in which case energy levels for ESI spectra should be specified in the "Comment: " field (EI spectra do not need a specified energy level). A corresponding spectra ID must be selected for .msp spectra. See an 
              = link_to "example peak list file", "examples/example_spec.txt"
              |  or an 
              = link_to "example .msp file", "examples/example_spec.msp"
              | .
          .col-sm-offset-2.col-sm-10
            = f.label :input_file, "Load Spectra File", class: "control-label"
            = f.file_field :input_file
          .col-sm-offset-2.col-sm-3.spectra-id-col
            = f.label :spectra_id, "Spectra ID", class: "control-label"
            p.form-control-static.help-text style="display: inline-block" * required for .msp files only
            = f.select :spectra_id, {}, {}, {class: "form-control"}
    .form-group
      = f.label :mass_tol, "Mass Tolerance", class: "col-sm-2 control-label"
      .col-sm-10
        p.form-control-static.help-text The mass tolerance to use when matching peaks within the spectrum comparison.
      .col-sm-3.col-sm-offset-2
        = text_field_tag :mass_tol, nil, { value: @mass_tol, class: "form-control small" }
        = select_tag :mass_tol_units, options_for_select(['ppm', 'Da']), { value: @mass_tol_units, class: "form-control small" }
    .form-group.end-row
      #form-buttons
        .col-sm-offset-2.col-sm-10
          = link_to 'Reset', assign_path, class: 'btn btn-primary btn-reset'
          = f.submit "Submit", class: "btn btn-primary btn-submit", id: "query-submit"
      #form-submit
        .col-sm-offset-2.col-sm-2
          #small-progress
            progress id="progressbar" value="100" max="100"
        .col-sm-8
          span#submitted-text Please wait while your input is validated (this may take a few seconds)
.end-note
  | If you wish to run multiple jobs, input larger query molecules, or customize the computation parameters, you can freely download the 
  = link_to "docker image", "https://hub.docker.com/repository/docker/wishartlab/cfmid",target: "_blank"
  | .
javascript:
  $(function(){ // Save the tab state to a hidden field 
    $('#spectra-text-toggle a[data-toggle="tab"]').on('shown.bs.tab', function (e) { $('#text_or_file').val('text'); }); 
    $('#spectra-file-toggle a[data-toggle="tab"]').on('shown.bs.tab', function (e) { $('#text_or_file').val('file'); }); 
  });
