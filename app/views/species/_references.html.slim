ul.list-unstyled
  // Determine if any references exist
  - articles = species_mapping.articles
  - textbooks = species_mapping.textbooks
  - external_links = species_mapping.external_links
  - if articles.count + textbooks.count + external_links.count > 0
    - articles.each do |article|
      li
        - if article.pubmed_id.present?
          = bio_link_out :pubmed, article.pubmed_id, nil,
            title: article.citation, class: 'tips'
        - elsif article.doi.present?
          = bio_link_out :doi, article.doi, nil,
            title: article.citation, class: 'tips'
        - else
          span.tips title=article.citation
            = truncate(article.citation, length: 100)
    - textbooks.each do |textbook|
      li
        - if textbook.isbn.present?
          = bio_link_out(:open_isbn, textbook.isbn)
        - else
          span.tips title=textbook.title
            = truncate(textbook.title, length: 100)
    - external_links.each do |external_link|
      li
        span.tips title=external_link.name
          = bio_link_to(truncate(external_link.name, length: 100), external_link.url)
  - else
    = nah
