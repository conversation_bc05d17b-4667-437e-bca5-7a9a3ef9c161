.page-header
  h3 = title("Query Status")

- if @query.type == "PredictQuery"
  - time = "a couple minutes, depending on the size of your input molecule"
  - partial = "predict/predict_input"
  - if @query.output_file.blank?
    - action = "Computing spectra"
  - else
    - action = "Generating structures"
- elsif @query.type == "AssignQuery"
  - time = "a couple minutes, depending on the size and complexity of your input molecule"
  - partial = "assign/assign_input"
  - if @query.output_file.blank?
    - action = "Computing fragments and assigning peaks"
  - else
    - action = "Generating structures"
- elsif @query.type == "IdentifyQuery"
  - time = "several minutes, depending on the number of candidates and molecule size"
  - partial = "identify/identify_input"
  - if @query.output_file.blank?
    - action = "Computing spectra and matching candidates"
  - else
    - action = "Generating structures"
- elsif @query.type == "NeutralLossQuery"
  - time = "several minutes, depending on the number of candidates and molecule size"
  - partial = "nl_search/nl_search_input"
  - action = "Computing spectra and searching database"

- if @status == :complete
  .results-box
    h4 Complete!
    p
      | If you are not redirected to your results, 
      = link_to 'click here', query_path(@query.secret_id)
      | .
- elsif @status == :queued
  .processing-box
    h4
      | Queued...
    p
      | You will be redirected when your results are ready. You can also bookmark this page and check back later. 
      br
      | This computation may take 
      = time
      | .
    #spinner-div
- elsif @status == :working
  .processing-box
    h4 Processing...
    h5
      | Currently: 
      span.action-text
        = action
    p
      | You will be redirected when your results are ready. You can also bookmark this page and check back later. 
      br
      | This computation may take 
      = time
      | .
    progress id="progressbar" value="#{ (Time.now - @query.updated_at) < @query.process_time ? Time.now - @query.updated_at : @query.process_time - 1}" max="#{@query.process_time}"
- elsif @status == :failed
  .error-box
    h4 Query Error. Please try again or contact an administrator.
- else
  .error-box
    h4 Query Error. Please try again or contact an administrator.

= render partial: partial

javascript:
  setTimeout(function () { location.reload(true); }, 5000);
  var opts = { lines: 11, // The number of lines to draw 
               length: 25, // The length of each line 
               width: 10, // The line thickness 
               radius: 30, // The radius of the inner circle 
               corners: 1, // Corner roundness (0..1) 
               rotate: 0, // The rotation offset 
               direction: 1, // 1: clockwise, -1: counterclockwise 
               color: '#000', // #rgb or #rrggbb or array of colors 
               speed: 1, // Rounds per second 
               trail: 60, // Afterglow percentage 
               shadow: false, // Whether to render a shadow 
               hwaccel: false, // Whether to use hardware acceleration 
               className: 'spinner', // The CSS class to assign to the spinner 
               zIndex: 2e9, // The z-index (defaults to 2000000000) 
               top: 'auto', // Top position relative to parent in px 
               left: 'auto' // Left position relative to parent in px 
             }; 
    var target = document.getElementById('spinner-div'); 
    var spinner = new Spinner(opts).spin(target); 
    var progressbar = $('#progressbar'), max = progressbar.attr('max'), time = 1000, value = progressbar.val(); 
    var loading = function() { value += 1; addValue = progressbar.val(value); 

    $('.progress-value').html(value + '%'); 

    if (value == max) { clearInterval(animate); } }; 
    
    var animate = setInterval(function() { loading(); }, time); 
