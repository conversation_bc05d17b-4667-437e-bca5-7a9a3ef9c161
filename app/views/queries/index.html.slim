h3 Queries

= paginate @queries

table.table.table-condensed.table-striped
  thead
    = search_form_for @q do |f|
      tr
        th = sort_link(@q, :type, 'Type')
        th = sort_link(@q, :spectra_type, 'Spectra Type')
        th = sort_link(@q, :ion_mode, 'Ion Mode')
        th = sort_link(@q, :created_at, 'Created At')
        th Status
        th
      tr.search-row
        td = f.select :type_eq, Query::TYPES, { include_blank: true }, { class: "form-control input-sm" }
        td = f.select :spectra_type_eq, Query::SPECTRA_TYPES, { include_blank: true }, { class: "form-control input-sm" }
        td = f.select :ion_mode_eq, Query::ION_MODES, { include_blank: true }, { class: "form-control input-sm" }
        td
        td
        td 
          = f.submit "Search", class: "btn btn-submit btn-sm"
          = link_to "Reset", queries_path, class: "btn btn-reset btn-sm"
  tbody
    - @queries.each do |query|
      tr
        td = query.type
        td = query.spectra_type
        td = query.ion_mode
        td = query.created_at
        td
          span[class="badge badge-#{query.status.downcase.gsub(/ /, "-")}"] = query.status
        td
          - if query.secret_id.present?
            = link_to "View", query_path(query.secret_id), class: "btn btn-download btn-sm"
            = link_to "Rerun", query_rerun_path(query.secret_id), class: "btn btn-rerun btn-sm btn-padded-left"
          - else
            = button_tag "Missing", class: "btn btn-submit btn-xs disabled"
          = link_to("Delete", query_path(query.id), :data => { :confirm => "Are you sure?" }, :method => :delete, :class => 'btn btn-error btn-sm btn-padded-left')


= paginate @queries