#loading-overlay
  #circularG
    #circularG_1.circularG
    #circularG_2.circularG
    #circularG_3.circularG
    #circularG_4.circularG
    #circularG_5.circularG
    #circularG_6.circularG
    #circularG_7.circularG
    #circularG_8.circularG

.page-header
  h3 = title("Results")

- if @query.type == "PredictQuery"
  = render 'predict/show'
- elsif @query.type == "AssignQuery"
  = render 'assign/show'
- elsif @query.type == "IdentifyQuery"
  = render 'identify/show'
- elsif @query.type == "NeutralLossQuery"
  = render 'nl_search/show'
- else
  .error-box
    h3 = title("Database Error")
    p Please contact an administrator. 
