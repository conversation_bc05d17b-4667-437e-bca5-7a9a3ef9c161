<div class="alert-underconstruction">
  <div class="alert">
    <p align="center"> &nbsp;<strong><span class="glyphicon glyphicon-info-sign"></span>&nbsp;&nbsp;NP-MRD (Beta) is still undergoing development. Please feel free to explore and use the database. But the interface is still being refined and the deposition might be slow. Please provide us with your feedback.</strong></p>
  </div>
</div>

<div class="page-header">
  <h1>1H NMR Predictor</h1>
</div>

<h2>What is the NMR predictor?</h2>
<div class="well">
  <div class="intro">
    = render :partial => "nmr_preds/intro"
    <p></p>
  </div>
</div>

<!-- <div class="well"> -->
<!--   <%= form_for @nmr_pred do |f| %> -->
<!--   -->
<!--   <div class="center jumbotron heading-jumbo"> -->
<!--     <%= f.label :Upload %> -->
<!--     <%= f.file_field :sdf_file %> -->
<!--   </div> -->
<!--   -->
<!--   <%= f.submit "Predict", style: "text-align:center"  %> -->
<!--<% end %> -->
<!-- </div> -->
  
.well-content.form-wrapper id="variable_html"
  h2 <b>Input form for NMR predictor<b>
  br
  = form_for @nmr_pred, :url => url_for(:controller => 'nmr_preds', :action => 'create')  do |f|
    .row
      .col-sm-5
          = moldbi_structure_drawer '', "structure_input", format: "smiles"
          br
          br
          = link_to "#{glyphicon('play-circle')} MarvinJS Tutorials".html_safe, 'https://www.youtube.com/playlist?list=PLA3Ev2ngKC0TY2p59vJhlYGm-wmrLaWp6', class: 'btn btn-default pull-right', target: '_blank'
      .col-sm-6.hide-overflow
        //= f.fields_for
        br
        br
        br
        br
        br
        br
        br
        .row
          .col-sm-4
            = f.label :solvent, "*Solvent: "
          .col-sm-7
            = f.select :solvent, solvent_collection_for_prediction, {:include_blank => 'Select One'}, {:required => true, :class => 'select'}
        br
        .row
          .col-sm-4
            = f.label :nucleus, "*Prediction type: "
          .col-sm-7
            = f.select :nucleus, nucleus_collection, {:include_blank => 'Select One'}, {:required => true, :class => 'select'}
        br
        br
        br
        br
        br
        .row
          .col-sm-4
            br
          .col-sm-7
            br
            br
            br
            br
            br
            br
            = f.button 'Predict' , type: 'submit', :onclick => "submit_button_click()"
