.page-header: h1 = title("Prediction result")
.well
  br
  .row
    .col-sm-6.hide-overflow
      h4 Submitted structure
      br
      .col-sm-6 id="view_3d" style="z-index:0"
      //- if @chemical_shift_submission.natural_product.threeDmol
      div[class="mol_file" data-mol_file = "#{@mol}"]
      = render partial: "shared/view_3D"
      //- else
      //  = image_tag "#{@threeD_image_url}", class: 'featurette-image'
    .col-sm-6.hide-overflow
      h4 Predicted 1H chemical shifts
      br
      br
      <table>
        <tr>
          <th>Hydrogen &nbsp &nbsp</th>
          <th>Chemical shift(ppm)</th>
          <br />
        </tr>
        <tr>
          - @shift_position.each do |s|
            <tr>
              <td> #{s[1]} &nbsp &nbsp &nbsp &nbsp </td>
              td = s[0] 
            </tr>
        </tr>
      </table>
  
  .row
    .col-sm-6.hide-overflow
      br
      = link_to "<button>Back to Home Page</button>".html_safe, main_app.root_path, :style=> 'color:black;float:left;'
  
    .col-sm-6.hide-overflow 
      br
      = link_to "<button>Predict another compound</button>".html_safe, new_nmr_pred_path, :style=> 'color:black;float:right;'
