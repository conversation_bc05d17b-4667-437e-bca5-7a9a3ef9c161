doctype html
html
  head
    meta name="viewport" content="width=device-width, initial-scale=1.0"
    meta content="text/html; charset=UTF-8" http-equiv="Content-Type"
    title = content_for?(:title) ? "#{'RIpred'}: #{yield(:title)}" : 'RIpred'
    / = wishart_favicons
    = stylesheet_link_tag "application", media: 'all', "data-no-turbolink" => true
    = javascript_include_tag "application", media: 'all', "data-no-turbolink" => true
    = render '/layouts/shared/ie'
    = csrf_meta_tags
    = favicon_link_tag asset_path('cartoon2.jpg')
  body[class="#{params[:controller].parameterize.dasherize}-c \
              #{params[:action].parameterize}-a"
      data-c=params[:controller].parameterize
      data-a=params[:action].parameterize
      data-spy="scroll"
      data-target=".sidenav"]
    header 
      = render 'navigation/navigation'
    br
    main role="main"

      #load-screen
        img.loader src="#{image_path('np-mrd_loader.svg')}"
      / br
      / br
      //== render_maintenance("01:00PM","10:00PM","April 16", "April 21", "NP-MRD")
      / br
      == render_wishart_notices
      == render '/layouts/messages'
      == yield
    footer  
      = render partial: 'shared/footer'
      = render partial: 'layouts/ga_footer'
