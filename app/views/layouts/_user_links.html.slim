// add navigation links to this file
- if user_signed_in? && current_user.role == "admin"
  li class="dropdown #{ 'active' if current_page?(users_path) || current_page?(queries_path) }"
    a.dropdown-toggle[data-toggle="dropdown" href="#"]
      | Administration
      b.caret
    ul.dropdown-menu
      li = link_to 'Queries', queries_path
      li = link_to 'Users', users_path
- if user_signed_in?
    li class="dropdown #{ 'active' if current_page?(edit_user_registration_path)}"
      a.dropdown-toggle[data-toggle="dropdown" href="#"]
        span.glyphicon.glyphicon-user
        b.caret
      ul.dropdown-menu
        li = link_to 'Edit account', edit_user_registration_path
        li = link_to 'Logout', destroy_user_session_path, :method=>'delete'

/ - else
/   li class="#{ 'active' if current_page?(new_user_session_path) } "
/     = link_to 'Login', new_user_session_path
  