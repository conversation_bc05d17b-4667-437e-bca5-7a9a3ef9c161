// add navigation links to this file
li class="dropdown #{ 'active' if current_page?(predict_path) || current_page?(assign_path) || current_page?(identify_path) }" 
  a.dropdown-toggle[data-toggle="dropdown" href="#"]
    span.glyphicon.glyphicon-star-empty
    |  Utilities
    b.caret
  ul.dropdown-menu
    li = link_to 'Spectra Prediction', predict_path
    li = link_to 'Peak Assignment', assign_path
    li = link_to 'Compound Identification', identify_path
li class="#{ 'active' if current_page?(help_path) }"
  = link_to 'Help', help_path
li class="dropdown #{ 'active' if current_page?(publications_path) || current_page?(statistics_path) || current_page?(data_path) }"
  a.dropdown-toggle[data-toggle="dropdown" href="#"]
    |  About
    b.caret
  ul.dropdown-menu
    li = link_to 'Publications', publications_path
    li = link_to 'Statistics', statistics_path
    li = link_to 'Supplementary Data', data_path
    li = link_to 'Versions', versions_path
li class="#{ 'active' if current_page?(contact_path) }"
  = link_to 'Contact Us', contact_path
