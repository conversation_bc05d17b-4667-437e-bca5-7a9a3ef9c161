- # navigation styled for Twitter Bootstrap 3.0
nav.navbar.navbar-custom.navbar-fixed-top
  .navbar-header
    button.navbar-toggle[type="button" data-toggle="collapse" data-target=".navbar-collapse"]
      span.sr-only
        | Toggle navigation
      span.icon-bar
      span.icon-bar
      span.icon-bar
    / = link_to 'CFM-ID', root_path, class: 'navbar-brand'
    = link_to root_path, class: 'navbar-brand' do
      = image_tag "hexagon.svg"
      | CFM-ID
    end
  .collapse.navbar-collapse
    ul.nav.navbar-nav.navbar-left
      = render 'layouts/navigation_links'
    ul.nav.navbar-nav.navbar-right
      = render 'layouts/user_links'
