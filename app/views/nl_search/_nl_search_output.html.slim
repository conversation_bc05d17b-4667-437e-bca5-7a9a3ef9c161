h4 Candidate Rankings
- query_results = query.get_results

- if !query_results.nil?
  .predicted-class
    span.bold Scoring Function: 
    space.score = @query.scoring_function
    - if query.score_with_metadata? && !query_results[:predicted_class].nil?
      br
      span.bold Predicted Class(es): 
      span.score = query_results[:predicted_class].join(", ")
      
  table.identify-display-table
    thead
      - if query.score_with_metadata?
        tr
          th.no-sort rowspan="2" Rank
          th.no-sort colspan="4" Score
          th.no-sort rowspan="2" Structure
          th.no-sort rowspan="2"
            | ID
            hr
            | Name
          th.no-sort rowspan="2"
            | Chemical Formula
            hr
            | Mass
          th.no-sort rowspan="2"
            | InChI
            hr 
            | SMILES
          th.no-sort rowspan="2" Classification
          th.no-sort rowspan="2" Spectrum Type
          th.no-sort rowspan="2" Compare
        tr
          th Overall
          th Similarity
          th Reference
          th Class
      - elsif query.predicted_database.present? || query.experimental_database.present?
        tr
          th Rank
          th Score
          th.no-sort Structure
          th.no-sort
            | ID
            hr
            | Name
          th.no-sort
            | Chemical Formula
            hr
            | Mass
          th.no-sort 
            | InChI
            hr 
            | SMILES
          th.no-sort Classification
          th.no-sort 
            | Spectrum Type
            p style="font-size: 10px; font-weight: normal !important; margin-bottom: 0px"
              | Hover over to 
            p style="font-size: 10px; font-weight: normal !important; margin-top: 0px" 
              | view more information
          th.no-sort Compare
      - else
        tr
          th Rank
          th Score
          th.no-sort Structure
          th.no-sort ID
          th.no-sort Chemical Formula
          th.no-sort 
            | InChI
            hr 
            | SMILES
          th.no-sort Compare
      - first = true
      - query_results[:results].each_with_index do |result, i|
        tr
          td.rank = i + 1
          - if query.score_with_metadata?
            td.score = result[:combined_score]
            td.score = result[:cfm_id_score]
            td.score = result[:reference_score]
            td.score = result[:ancestor_score]
            td.image = get_image(result[:inchi], "frag-structure img-responsive", "Structure", result[:inchi_key]).html_safe
            / td.image = "NA"
            / td.image = get_image(result[:jchem_id], "frag-structure img-responsive", "Structure").html_safe
            / t.image = "<img src=\"" + "http://moldb.wishartlab.com/structures/ms_ms/#{result[:jchem_id]}/peak.svg"+ "\" class=\"#{classname}\" alt=\"#{altname}\" >"
            td
              = link_to_database(result[:database], result[:database_id]).html_safe
              - result[:related].each do |ref|
                br
                = link_to_database(ref[:database], ref[:database_id]).html_safe
              hr
              / span class=("type-tag "  + find_in_database(database_id, "spectra_type", database).try(:downcase)) = find_in_database(database_id, "spectra_type", database)
              = display(find_in_database(result[:database_id], "full_name", result[:database]))
            td
              = pretty_formula(result[:structure])
              hr
              = display(find_in_database(result[:database_id], "neutral_mass", result[:database]))
            td 
              = result[:inchi]
              hr
              = result[:smiles]
            td
              span.bold Direct Parent
              .classification = display(result[:direct_parent])
              span.bold Alternative Parents
              .classification = display_list(result[:alternative_parents])
            td 
              .type class="#{result[:predicted].downcase}" = result[:predicted]
              / br
              / = display(get_spectrum_type(result[:id_low]))
            td.compare
              a href="#" class="btn btn-xs btn-download #{ first ? "btn-error" : "btn-compare" }" data-compound="#{ result[:database_id] }" data-database="#{ result[:database] }" data-compound_low="#{ result[:id_low] }" data-compound_med="#{ result[:id_med] }" data-compound_high="#{ result[:id_high] }" data-compound-type="#{result[:neutral_loss]}" data-query_id="#{ query.secret_id }"
                = first ? "Current" : "Compare"
            - if first
              - first = false
              javascript:
                window.compareCandidate = #{ loadCandidateNew(result[:id_low], result[:id_med], result[:id_high], result[:database_id], result[:neutral_loss]).to_json.html_safe };
          - elsif query.predicted_database.present? || query.experimental_database.present?
            td.score = result[:cfm_id_score]
            td.image = get_image_id(result[:inchi], "frag-structure img-responsive", "Structure", result[:inchi_key]).html_safe
            /td.image = "SAD JCHEM DOWN :("
            td
              = link_to_database(result[:database], result[:database_id]).html_safe
              - result[:related].each do |ref|
                br
                = link_to_database(ref[:database], ref[:database_id]).html_safe
              hr
              / span class=("type-tag "  + find_in_database(database_id, "spectra_type", database).try(:downcase)) = find_in_database(database_id, "spectra_type", database)
              = display(find_in_database(result[:database_id], "full_name", result[:database]))
            td
              = pretty_formula(result[:structure])
              hr
              = display(find_in_database(result[:database_id], "neutral_mass", result[:database]))
            td 
              = result[:inchi]
              hr
              = result[:smiles]
            td
              span.bold Direct Parent
              .classification = display(result[:direct_parent])
              span.bold Alternative Parents
              .classification = display_list(result[:alternative_parents])
            td 
              .type class="#{result[:predicted].downcase}"
                span [title="#{get_spectrum_type(result[:id_low])}" data-toggle="tooltip" data-placement="bottom" style="position: relative;display: inline-block;"] = result[:predicted]
            td.compare
              a href="#" class="btn btn-xs btn-download #{ first ? "btn-error" : "btn-compare" }" data-compound="#{ result[:database_id] }" data-database="#{ result[:database] }" data-compound_low="#{ result[:id_low] }" data-compound_med="#{ result[:id_med] }" data-compound_high="#{ result[:id_high] }" data-compound-type="#{result[:neutral_loss]}" data-query_id="#{ query.secret_id }"
                = first ? "Current" : "Compare"
            - if first
              - first = false
              javascript:
                window.compareCandidate = #{ loadCandidateNew(result[:id_low], result[:id_med], result[:id_high], result[:database_id], result[:neutral_loss]).to_json.html_safe };
          - else
            td.score = result[:cfm_id_score]
            td.image = get_image(result[:structure], "frag-structure img-responsive", "Structure").html_safe
            td.id = result[:id]
            td = get_formula(result[:structure])
            td = result[:structure]
            td.compare
              a href="#" class="btn btn-xs btn-download #{ first ? "btn-error" : "btn-compare" }" data-compound="#{ result[:database_id] }" data-database="#{ result[:database] }" data-compound_low="#{ result[:id_low] }" data-compound_med="#{ result[:id_med] }" data-compound_high="#{ result[:id_high] }" data-compound-type="#{result[:neutral_loss]}" data-query_id="#{ query.secret_id }"
                = first ? "Current" : "Compare"
            - if first
              - first = false
              javascript:
                window.compareCandidate = #{ loadCandidateNew(result[:id_low], result[:id_med], result[:id_high], result[:database_id], result[:neutral_loss]).to_json.html_safe };

javascript:
  $('[data-toggle="tooltip"]').tooltip({container:'body', trigger: 'hover', placement:"right"}); 
