- #= debug params
.page-header
  h3 = title("Neutral Loss Search")

/.method
  p Determines the compounds that most closely match to a given spectra. The spectra for each candidate compound (in the provided list) are predicted using a pre-trained model and compared to the input spectra. The top candidates are ranked according to how closely they match and returned in a list.
.form-box
  = form_for(@nl_search_query, url: nl_search_new_path, html: {id: 'nl_search-form', class: "form-horizontal"}, method: "post", multipart: true) do |f|
    .form-group
      .row
        .col-sm-offset-1.col-sm-1
          = label_tag nil, "Examples", class: "control-label"
        .col-sm-3 
          a href="#" class="btn btn-default example-loader-esi-1" data-mass="#{ render 'utilities/neutral_mass_example_3' }" data-update="identify_query_neutral_mass" data-example-low="#{ render 'utilities/low_spectra_example_3' }"  data-update-low="low_spectra" data-example-medium="#{ render 'utilities/medium_spectra_example_3' }" data-update-medium="medium_spectra" data-example-high="#{ render 'utilities/high_spectra_example_3' }" data-update-high="high_spectra" Load ESI Example #1
        .col-sm-3 
          a href="#" class="btn btn-default example-loader-esi-2" data-mass="#{ render 'utilities/neutral_mass_example_4' }" data-update="identify_query_neutral_mass" data-example-low="#{ render 'utilities/low_spectra_example_4' }" data-update-low="low_spectra" data-example-medium="#{ render 'utilities/medium_spectra_example_4' }" data-update-medium="medium_spectra" data-example-high="#{ render 'utilities/high_spectra_example_4' }" data-update-high="high_spectra" Load ESI Example #2

      .col-sm-offset-2.col-sm-2
        = f.label :database, "Select database(s)", class: "control-label"
        .row-sm-3
          
          = f.label :database, "Experimental", class: "control-label"

          - IdentifyQuery::ESI_EXPERIMENTAL_DATABASES.merge(IdentifyQuery::EI_DATABASES).to_h.each do |human_name, value|
            div class=(["database-checkbox", value.parameterize, ("ei" if IdentifyQuery::EI_DATABASES.values.include? value), ("esi" if IdentifyQuery::ESI_EXPERIMENTAL_DATABASES.values.include? value)].compact.join(' '))
              = f.check_box :database, { multiple: true, checked: @nl_search_query.database.include?(value), data:{toggle:'tooltip', placement:'right', title:"tooltip here"} }, value, nil
              = f.label "database-" + value.parameterize, class: "control-label" do 
                span [title="#{human_name}" data-toggle="tooltip" data-placement="bottom" style="position: relative;display: inline-block;border-bottom: 1px dotted black;"] = human_name
      

javascript:
  $('[data-toggle="tooltip"]').tooltip({container:'body', trigger: 'hover', placement:"right"});  
  $('#submit-candidates-toggle a[data-toggle="tab"]').on('shown.bs.tab', function (e) { $('#submit_or_find_or_nl').val('submit'); }); 
  $('#find-candidates-toggle a[data-toggle="tab"]').on('shown.bs.tab', function (e) { $('#submit_or_find_or_nl').val('find'); }); 
  $('#neutral-loss-toggle a[data-toggle="tab"]').on('shown.bs.tab', function (e) { $('#submit_or_find_or_nl').val('neutral-loss'); }); 
  $('#spectra-text-toggle a[data-toggle="tab"]').on('shown.bs.tab', function (e) { $('#text_or_file').val('text'); }); 
  $('#spectra-file-toggle a[data-toggle="tab"]').on('shown.bs.tab', function (e) { $('#text_or_file').val('file'); });
