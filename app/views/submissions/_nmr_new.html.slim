= form_for @nmr_submission, :url => url_for(:controller => 'submissions', :action => 'create') do |f|
    h2
      b
        | Instructions
    p 
      | INSERT INSTRUCTIONS HERE.
    br


    ol.form-list

      li
        .form-group
          .form-label
            = f.label :nmr_spectrum_type, 'Select the type of NMR spectrum:', class: " control-label"
          .col-sm-9.form-input
            - NmrSubmission::NMR_SPECTRUM_TYPES.each do |nst|
              .radio-div-fixed
                = f.radio_button :nmr_spectrum_type, nst
                = f.label :nmr_spectrum_type, nst, class: 'radio-label'
      br
  
      li
        .form-group
          .form-label
            = f.label :chemical_shift_standard, 'Select Chemical Shift (CS) Reference:', class: " control-label"
          .col-sm-9.form-input
            - NmrSubmission::CHEMICAL_SHIFT_STANDARDS.each do |standard|
              .radio-div-fixed
                = f.radio_button :chemical_shift_standard, standard
                = f.label :chemical_shift_standard, subscript_numbers(standard), class: 'radio-label'
      br

      
      li
        .form-group
          .form-label
            = f.label :spectrometer_frequency, 'NMR Frequency:', class: " control-label"
            span.form-help Provide the frequency of the NMR spectrometer used to collect the spectrum.
          .col-sm-6.form-input
            - NmrSubmission::LIBRARY_FREQUENCIES.each do |freq|
              .radio-div-fixed
                = f.radio_button :spectrometer_frequency, freq
                = f.label :spectrometer_frequency, "#{freq} MHz", class: 'radio-label'
      br

      li
        .form-group
          .form-label
            = f.label :nmr_file, 'Upload Spectrum:', class: 'control-label'
            .form-help
              span.stand-out
                | Please ensure your spectrum has been collected using the 
                | protocol described 
                = link_to 'here', '#'
                | . 
              | The uploaded file must be a ZIP compressed FID folder (
              | <abbr title='Compressed folder must contain a fid and procpar file'>Agilent/Varian</abbr> or 
              | <abbr title='Compressed folder must contain a fid and acqus file'>Bruker</abbr>).
          .col-sm-2.form-input
            = f.file_field :nmr_file
      br

    = f.button 'Submit' , type: 'submit', name: 'state', value: 'post_nmr_return_to_index', :onclick => "submit_button_click()"
