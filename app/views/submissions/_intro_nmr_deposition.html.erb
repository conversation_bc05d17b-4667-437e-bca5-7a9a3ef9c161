<!-- <p style="font-family:arial, sans-serif;font-size: 12;font-weight: 1">
  Add the structure of your natural product by simply pasting a <b>SMILES</b> or <b>InChI</b> string into the <b>MarvinJS</b> structure drawing applet below. Alternatively, you can use the applet to draw your structure or to upload an <b>'.sdf'</b> file. To learn how to draw a structure in MarvinJS, click on the <b>'MarvinJS Tutorials'</b>.
</p>
 -->

<p style="font-family:arial, sans-serif;font-size: 12;font-weight: 1">
  <b>Instructions for Structure to Kovats' Retention Index Prediction:</b><br>
  1) Add the structure of natural product by pasting a <b>SMILES</b> string into the <b>MarvinJS</b> structure drawing applet<br>
  2) Alternatively, the applet can be used to draw a structure or to upload an <b>'.sdf'</b> file<br>
  3) To learn how to draw a structure in MarvinJS, click on the <b>'MarvinJS Tutorials'</b><br>
  4) If a compound name is not added, a <b> 'unique identifier (ID)' </b> will be associated with the compound to map back to it's different derivatized forms.
</p>
