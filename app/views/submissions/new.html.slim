= render partial: "shared/view_3D"
=render partial: 'submissions/warning'
br
h1 Kovats' Retention Index Prediction (RIpred)
br
.well-content.form-wrapper id="variable_html"
  h2 <b>Input Form for RI data<b>
  br
  = form_for @submission, :url => url_for(:controller => 'submissions', :action => 'create'), :remote => true do |f|
    .row
      .col-sm-5
        h2 Provide a Structure
      .col-sm-6
        h2 Fields Marked with '*' are Mandatory
    br
    .row
      .col-sm-5
          = moldbi_structure_drawer '', "structure_input", format: "smiles"
          br
          br
          br
          = link_to "#{glyphicon('play-circle')} MarvinJS Tutorials".html_safe, 'https://www.youtube.com/playlist?list=PLA3Ev2ngKC0TY2p59vJhlYGm-wmrLaWp6', class: 'btn btn-default pull-right', target: '_blank'
      .col-sm-6.hide-overflow
        = f.fields_for :submission_meta_data do |ff|
          .row
            .col-sm-6
              .label-font-size
                = label_tag :compound_name, "Compound Name: "
            .col-sm-5
              = text_field_tag 'compound_name', nil, placeholder: "Common/IUPAC name",  required: true
          br
          .row
            .col-sm-6
              = ff.label :provenance, "*GC Stationary Phase: ",  required: true
            .col-sm-5
              = ff.select :provenance, provenance_collection, {:include_blank => 'Semi Standard Non Polar'}, {:required => true, :class => 'select', :id => "submission_provenance"}
          br
          .row
            .col-sm-6
              = ff.label :physical_state_of_compound, "*Type of Derivatization: "
            .col-sm-5
              = ff.select :physical_state_of_compound, physical_state_of_compound_collection, {:include_blank => 'TMS'}
          br
          .row
            .col-sm-5
              = f.button 'Next' , type: 'submit', name: 'state', value: 'post_np', :onclick => "submit_button_click()"
