h2 <b>Input Form for Natural Product NMR Data</b>
br
= form_for @submission, :remote => true do |f|
  //.well-content
      //=render partial: 'submissions/instructions_spectral_file_submission'
  br
  .row
    .col-sm-3
      = f.label " ", "NP-MRD ID:  "
    .col-sm-2
      = "#{@submission.natural_product.np_mrd_id}".html_safe
  .row
    .col-sm-3
      = f.label " ", "Compound Name:  "
    .col-sm-2
      = "#{@submission.natural_product.name}".html_safe
  .row
    .col-sm-3
      = f.label " ", "Provenance:  "
    .col-sm-2
      = "#{@submission.submission_meta_data.provenance}".html_safe

  - if !(@submission.submission_meta_data.provenance == "Other" || @submission.submission_meta_data.provenance == "Biotransformation" || @submission.submission_meta_data.provenance == "Chemical synthesis")
    .row
      .col-sm-3
        = f.label " ", "Organism Genus Name:  "
      .col-sm-2
        = "#{@submission.submission_meta_data.genus}".html_safe
    .row
      .col-sm-3
        = f.label " ", "Organism Species Name:  "
      .col-sm-2
        = "#{@submission.submission_meta_data.species}".html_safe
  br
  // put headers in a separate cell so that position: sticky will work in Safari
  .row
    .col-sm-4
      h4 The Submitted Structure with Atom Indexing
    .col-sm-8
      h4 Upload Spectral Files
  .row

    .col-sm-4 id="view_3d" style="z-index:0"
      - if @submission.renumberedMol
        div[class="mol_file" data-mol_file = "#{@renumbered_mol}"]
        = render partial: "shared/view_3D"
      - elsif @submission.natural_product.threeDmol
        div[class="mol_file" data-mol_file = "#{@submission.natural_product.threeDmol}"]
        = render partial: "shared/view_3D"
      - else
        = image_tag "#{@threeD_image_url}", class: 'featurette-image'
    .col-sm-8
      .grid-item-table
        br
        p
          | #{glyphicon('info-sign')} Click the <em>Add Another Spectral File</em> button below to get started for multiple <br> spectral file    upload.
        #nmr-submissions
          .table-responsive
            table.table-bordered.table-hover.chemical-shifts
              thead
                tr
                  th.atom-col-head Spectrum Type
                  th.atom-col-head Solvent
                  th.atom-col-head Chemical Shift Reference
                  th.atom-col-head Upload FID File
                  th.atom-col-head Remove The Entry
              tbody.nmr_submissions
                = f.fields_for :nmr_submissions do |nmr_submission|
                  = render 'nmr_submission_fields', f: nmr_submission
          .links
            = link_to_add_association "#{glyphicon('plus-sign')} Add Another Spectral File".html_safe, f, :nmr_submissions, class: 'btn     btn-lg',  data: {"association-insertion-node" => "tbody.nmr_submissions", "association-insertion-method" => "append"}
          br
          br
            = f.button 'Submit and Verify' , type: 'submit', name: 'state', value: 'post_spectral_file', :onclick => "submit_button_click()"