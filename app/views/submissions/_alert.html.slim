h1
  b.red-header = "Alert!"
  b
    = " An identical structure already exists in NP-MRD"
br

= form_tag submissions_path, remote: true do
  // Pass params back to controller for saving
  = hidden_field_tag 'natural_product_id', @natural_product.id
  = hidden_field_tag 'state', 'post_alert'

  // Spectra data from MolDB
  - nmr_data, earliest_creation_date, last_update_date = @natural_product.moldb_nmr_spectra_summaries

  // NP-MRD valid chemical shift submissions
  - if @submissions.any?
    // Spectra summaries
    - submission_meta_data = SubmissionMetaData.where(:submission_id => @submissions.pluck(:id))
    - submission_meta_data.each do |meta_data|
      - nmr_data.push(meta_data.to_s)

    // Dates
    - earliest_creation_date, last_update_date = get_creation_update_dates(@submissions, earliest_creation_date, last_update_date)

  table.table-bordered.padded
    tbody
      tr
        td
          b = "Database Identifier"
        td = link_to @natural_product.np_mrd_id, @natural_product
      tr
        td 
          b = "Initial Entry Date"
        td = nah earliest_creation_date
      tr
        td 
          b = "Last Update"
        td = nah last_update_date
      tr
        td 
          b = "Name"
        td = @natural_product.name
      tr
        td 
          b = "Species Source"
        - if @submissions.any?
          td = submission_meta_data.map{ |x| x.scientific_name }.reject(&:blank?).uniq.join(", ")
        - else
          td = nah
      tr
        td 
          b = "NMR Data"
        - if nmr_data.any?
          td = simple_format nmr_data.uniq.join(";").gsub(";", "\n")
        - else
          td = nah
        
  br

  = "Does your data set provide new experimental data (assignments, field strength, NMR expt., solvent, species of origin)? If so, do you wish to continue?"
  br
  // User submission
  .chemical-shifts-alert-radio
    = label_tag 'user_input', 'Yes'
    = radio_button_tag 'user_input', "Continue", true
    = label_tag 'user_input', 'No'
    = radio_button_tag 'user_input', "Cancel"
  br
  br
  = submit_tag "Submit",:onclick => "submit_button_click()"
