.well
  .intro
    h2 NMR spectra (Real)
    = render "shared/spectra_viewer_r"
.well
  .intro
    h2 Predicted from uploaded assignment
    = render "shared/spectra_viewer", spectra_path: @nmrml_path

.well
  .intro
    h4 Submitted Chemical Shifts
    = render "final_chemical_shifts"
    = image_tag "#{@score_image_path}", class: 'featurette-image'

    table.table-standard
      thead
      .row
        .col-sm-6
          = link_to "Detailed Assignment Report", show_assignment_report_submission_path





.well
  .intro
    h4 Documentation

.downloads
  table.table-standard
    thead
      tr
        th.data-set-col Documentation Description
        th.download-col Download Link
    tbody
      tr
        td Chemical Shift Assignments File
        td = nah link_to "Download CSV", download_submitted_chemical_shift_path
      tr
        td nmrML-NMR Data Exchange Format File
        td = nah link_to "Download nmrML", nmrML_path
      tr
        td Mol File
        td = nah link_to "Download Mol", download_renumbered_mol
      - @submission.nmr_submissions.each do |file|
        tr
          td = "Raw Free Induction Decay File (#{file.nmr_spectrum_type}, #{file.solvent}, #{file.chemical_shift_standard})"
          td = link_to "Download FID", file.nmr_file.url
          br