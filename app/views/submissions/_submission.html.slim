- chemical_shifts.each do	|shift|
	- if shift.batch_submission_id.nil?
		tr
			td.natural-product-link
				//- puts shift.inspect
				- if shift.natural_product_id
					- np = NaturalProduct.find(shift.natural_product_id)
					- puts np
					- if shift.valid
						=  link_to np.np_mrd_id, np, class: 'btn-card'
					- else
						strong
							.btn-card
								= np.np_mrd_id
				- else
					= "No ID"
			td.natural-product-name
				- if np
					- if shift.valid
						strong
							= link_to np.name, np
					- else
						strong
							= np.name
					- if np.valid_thumb?
					    td.natural-product-structure
					    	 = image_tag(np.thumb.url)
				  	- else
					    td.natural-product-structure 
					    	= moldb_vector_thumbnail(np)
				- else
					= "No Name"
					td
		
			td.natural-product-inchi
				-if np
					- if np.structure_resource
						= np.structure_resource.inchi
					- else
						- if np.structure
							= np.structure
						- else
							= "Structure Image Not Available"
				- else
					= "No Structure Provided"
			td
			 	= shift.created_at.strftime("%Y-%m-%d")
			td
				- if shift.valid.present?
					- if shift.valid
						.accepted_status
			 				= "Complete"
			 		- else
			 			.pending_status
			 				= "Pending"
			 	- else
			 			.pending_status
			 				= "Pending"
			td
			 	= "Chemical Shift"
			td
				- if shift.valid.present?
					- if shift.valid
						= link_to "View", np
						/= link_to "View", shift
					- else
						= link_to "Edit", edit_chemical_shift_submission_path(shift)
				- else
					= link_to "Edit", edit_chemical_shift_submission_path(shift)


- submissions.each do |submission|
	tr
		td.natural-product-link
			- puts submission.inspect
			- if submission.natural_product_id
				- np = NaturalProduct.find(submission.natural_product_id)
				- puts np
				- if submission.valid
					=  link_to np.np_mrd_id, np, class: 'btn-card'
				- else
					strong
						.btn-card
							= np.np_mrd_id
			- else
				= "No ID"
		td.natural-product-name
			- if np
				- if submission.valid
					strong
						= link_to np.name, np
				- else
					strong
						= np.name
				- if np.valid_thumb?
				    td.natural-product-structure
				    	 = image_tag(np.thumb.url)
			  	- else
				    td.natural-product-structure 
				    	= moldb_vector_thumbnail(np)
			- else
				= "No Name"
				td
	
		td.natural-product-inchi
			-if np
				- if np.structure_resource
					= np.structure_resource.inchi
				- else
					- if np.structure
						= np.structure
					- else
						= "Structure Image Not Available"
			- else
				= "No Structure Provided"
		td
		 	= (submission.created_at).to_s.split(" ")[0]
		td
			- if submission.valid.present?
				- if submission.valid
					.accepted_status
		 				= "Complete"
		 		- else
		 			.pending_status
		 				= "Pending"
		 	- else
		 		.pending_status
		 			= "Pending"
		td
		 	= "Spectrum"
		td
			- if submission.valid.present?
				- if submission.valid
					= link_to "View", np
				- else
					= link_to "Edit", edit_submission_path(submission)
			- else
				= link_to "Edit", edit_submission_path(submission)


- batch_submissions.each do |batch_submission|
	- batch_upload = batch_submission.batch_upload
	tr
		td.natural-product-link
			= "NA"
		td.natural-product-name
			= "NA"
			td
	
		td.natural-product-inchi
			= "NA"
		td
		 	= (batch_submission.created_at).to_s.split(" ")[0]
		td
		 	//= "Pending"
			- if batch_submission.valid.present?
				- if batch_submission.valid
					.accepted_status
		 				= "Complete"
		 		- else
		 			.pending_status
		 				= "Pending"
		 	- else
		 		.pending_status
		 			= "Pending"
		td
			- if batch_submission.batch_upload
		 		= "Offline Bulk Submission: #{batch_submission.batch_upload.batch_file.original_filename}"
		td
			//= link_to "View/Fill The Form", batch_submission
			- if batch_submission.valid.present?
				- if batch_submission.valid
					= link_to "View", batch_upload
				- else
					= link_to "Edit", batch_submission
			- else
				= link_to "Edit", batch_submission

			


