//<link href="http://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap.min.css" rel="stylesheet" />
//<script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.2.23/angular.min.js"></script>
//<form class="form-horizontal" role="form" enctype="multipart/form-data" id="inputForm" name="inputForm" novalidate>
tr.nested-fields
  td.white-col
    //.select-table
      //= f.select :nmr_spectrum_type, fid_spectrum_type_collection, :style => "width:20px;", required: true
    = f.select :nmr_spectrum_type, fid_spectrum_type_collection ,required: true
  td.white-col
    //.select-table
    = f.select :solvent, solvent_collection,  required: true
  td.white-col
    //.select-table
    = f.select :chemical_shift_standard, chemical_shift_reference_collection, required: true

  td.white-col
    - if f.object.nmr_file.exists?
      .select-table
        = "<b>#{f.object.nmr_file_file_name}</b>".html_safe
        
    - else
      .file-selector
        = f.file_field :nmr_file, as: :file, required: true
          
  td.white-col
    .select-table
      .float-right
        = link_to_remove_association "Remove", f, class: 'btn btn-outline-danger'
