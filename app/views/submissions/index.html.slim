=render partial: 'submissions/warning'
// Display notice and sign out button if redirected from a cancelled submission
- if params[:submission] # Display
  br
  #flash_notice
    .message
      = "Submission cancelled."
    br
    = "Continue with data deposition below or "
    = link_to "#{glyphicon("log-out")} Sign Out".html_safe, main_app.sign_out_path, method: :delete, class: 'btn btn-warning btn-md'
br
- if params[:notice].present?
  .alert-citation.alert
    p #{glyphicon(:'glyphicon-ok')} #{params[:notice]}
// Display intro by default (i.e. not redirected from a cancelled submission)
- else
  .page-header: h1 = title("Data Deposition Form")
  .well
    .intro
      =render partial: 'submissions/intro'

.page-header: h2 = title('Submit Data')
.well
  .intro
    <ul class="nav nav-flex-column">
       <li class="nav-item">
         <a class="nav-link active" href="#{main_app.new_submission_path}">Option 1: <u>Single Structure Kovats' RI Prediction</u></a>
       </li>
    </ul>
.page-header: h2 = title('Past Submissions')
.table-responsive
  table.table.table-striped.table-condensed.table-hover.natural-products
    thead
      = render 'header'
    tbody
      = render partial: 'submissions/submission', locals: {:submissions => @submissions, :chemical_shifts => @chemical_shift_submissions, :batch_submissions => @batch_submissions}
