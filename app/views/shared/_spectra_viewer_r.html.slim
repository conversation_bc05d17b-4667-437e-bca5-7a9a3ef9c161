.alert.alert-info
  | Details in the spectrum can be examined by zooming in on regions of interest
    (Press <em><b>Option or Alt</b></em> and drag around an area; or use a scroll wheel to zoom
    the X-axis and press <em><b>Option or Alt</b></em> while using a scroll wheel to zoom the
    Y-axis).
  a id='spectra-viewer-more-info' href='#' More info...

= render 'shared/spectra_viewer_instructions'
#spectra_list data-spectra_list = @file_list

- @file_list.each_with_index do |file,i|
  h2 "#{@name_list[i]}"
  .row
    .col-sm-6
      .row
        h4 b Spectrum Parameters
      .row
        .col-sm-3
          . Instrument type
          . Frequency
          . Line Width
          . Signal/Noise
          . Sovlent
        .col-sm-3
          . = "#{@param_list[i]["Instrument"]}"
          . = "#{@param_list[i]['Frequency']}"
          . = "#{@param_list[i]['Linewidth']}"
          . = "#{@param_list[i]['S/N']}"
          . = "#{@param_list[i]['Solvent']}"

    .col-sm-6
      b Quality Accessment
      .quality_img
        = image_tag "#{@quality_list[i]}", class: "quality_sticky"

  #spectra-anchor
  #spectra-container
    #spectra-header
    . (id = "spectra-viewer-r#{i}")
  br


