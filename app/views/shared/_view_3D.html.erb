<script>
  function loadScript(url, callback){
    var script = document.createElement("script")
    script.type = "text/javascript";
    console.log("Jsmol loaded")
    if (script.readyState){  //IE
      script.onreadystatechange = function(){
        if (script.readyState == "loaded" ||
            script.readyState == "complete"){
            script.onreadystatechange = null;
            callback();
        }
      };
    }
    else {  //Others
      script.onload = function(){
          callback();
      };
    }
    script.src = url;
    document.getElementsByTagName("head")[0].appendChild(script);
  }
  function run_jsmol(mol_file){
    var Info = {
        width: 400,
        height: 312,
        debug: false,
        color : "#EAF0F2",
        //addSelectionOptions: true,
        use: "HTML5",   // JAVA HTML5 WEBGL are all options
        j2sPath: "/jsmol/j2s", // this needs to point to where the j2s directory is.
        multipleBondSpacing : 0.9,
        zoomlarge: true,
        //script: "set zoomlarge false;set antialiasDisplay;",
        //serverURL: "https://chemapps.stolaf.edu/jmol/jsmol/php/jsmol.php",
        //defaultModel: "$caffeine",
        allowJavaScript: true

      };
      //jmolApplet0 = Jmol.getApplet("jmolApplet0", Info);
      //Jmol.loadFile(jmolApplet0,"/assets/test_mol.mol");""
      Info.script = 'load INLINE "' + mol_file + '"; color labels black; font labels 20; label %i;set antialiasDisplay; wireframe 15; spacefill 55;'
      $("#view_3d").html(Jmol.getAppletHtml("jmolApplet0", Info))
  }
  var mol_file = $('div[data-mol_file]').data('mol_file');
  if (!mol_file){
    $(window).load(function(){
      loadScript("/jsmol/JSmol.min.js", function(){})
    })
  }
  else{
    try {
      run_jsmol(mol_file)
    }
    catch(e){
      loadScript("/jsmol/JSmol.min.js", function(){
        run_jsmol(mol_file)
      })
    }
  }
</script>