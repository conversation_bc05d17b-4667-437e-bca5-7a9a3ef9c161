.home-banner
  = image_tag "home_banner.svg", class: "banner"
  .banner-text
    = image_tag "cfm-id-logo.png", class: "banner-logo"
    .link-bar-area
      .link-bar.one
        .link-text
          = link_to "Spectra Prediction #{glyphicon('chevron-right')}#{glyphicon('chevron-right')}".html_safe, main_app.predict_path
      .link-bar.two
        .link-text
          = link_to "Peak Assignment #{glyphicon('chevron-right')}#{glyphicon('chevron-right')}".html_safe, main_app.assign_path
      .link-bar.three
        .link-text
          = link_to "Compound Identification #{glyphicon('chevron-right')}#{glyphicon('chevron-right')}".html_safe, main_app.identify_path

.page-header
  h3 Welcome to CFM-ID 4.0!

.home-box-info.col-md-12
  p[style="font-weight: bold;"] 
    | Welcome to CFM-ID 4.0! To use the earlier version 3.0, 
    = link_to "click here", "http://cfmid3.wishartlab.com", target: "_blank"
    | .
/p.alert.alert-warning
  | #{glyphicon(:'warning-sign')} 
  | Please note that this site is under contruction and some features are inoperable.
  br
  | - Spectra prediction has been updated to v4.0.5. For certain lipids, the rule-based prediction algorithm introduced in v3.0 will continue to be used.
  br
  | - Peak assignment has been updated to v4.0.5.
  br
  | - Compound identification has been updated to v4.0.5 for the Find Candidates option. <span class="label label-default">New</span>
  br
  | - The database has been updated with over 420,000 predicted ESI spectra using the new v4.0 algorithm.

/ .home-box-info
/   .help-header
/     p 
/       | Welcome to <strong>CFM-ID 4.0</strong>! For use of EI-MS, please use the earlier version CFM-ID 3.0, 
/       = link_to "click here", "http://cfmid3.wishartlab.com", target: "_blank"
/       |.
/   p <strong>CFM-ID</strong> provides a method for accurately and efficiently identifying metabolites in spectra generated by electrospray tandem mass spectrometry (ESI-MS/MS). The program uses <strong>Competitive Fragmentation Modeling</strong> to produce a probabilistic generative model for the MS/MS fragmentation process and machine learning techniques to adapt the model parameters from data. <strong>CFM-ID 4.0</strong> has an updated ESI spectral prediction algorithm using neural networks. In addition, the rule-based fragmenter is extended to acyl carnitines, acylcholines, and polyphenols.
/   = link_to "More", "#", :onclick => "return showMore(\"cfmid-info-more\", \"cfmid-info-less\", \"cfmid-info\")".html_safe, :id => 'cfmid-info-more'
/   = link_to "Less", "#", :onclick => "return showLess(\"cfmid-info-more\", \"cfmid-info-less\", \"cfmid-info\")".html_safe, :id => 'cfmid-info-less', :class => "hidden-link"

/   .cfmid-info
/     br
/     | CFM-ID can be used for:
/     ul
/       li
/         = link_to "Spectra Prediction", predict_path
/         | : 
/         | <strong>Predicting the spectra for a given chemical structure.</strong> This task predicts low/10 eV, medium/20 eV, and high/40 eV energy MS/MS spectra for an input structure provided in SMILES or InChI format. Spectra are predicted using combinatorial fragmentation, except in the case of selected lipids, polyphenols, acylcarnitines and acylcholine in which a rule-based fragmentation approach is implemented. The rule-based fragmentation module is based on a library of 657 rules covering 21 lipid classes, 9 polyphenol classes, and 8 adducts.
/       li
/         = link_to "Peak Assignment", assign_path
/         | : 
/         | <strong>Annotating the peaks in set of spectra given a known chemical structure.</strong> This task takes a set of three input spectra (for ESI spectra, low/10 eV, medium/20 eV, and high/40 eV energy levels) or a single input spectra (for EI spectra, 70eV energy level) in peak list format and a chemical structure in SMILES or InChI format, then assigns a putative fragment annotation to the peaks in each spectrum.
/       li
/         = link_to "Compound Identification", identify_path
/         | : 
/         | <strong>Predicted ranking of possible candidate structures for a target spectrum.</strong> This task takes a set of three input spectra (for ESI spectra, low/10 eV, medium/20 eV, and high/40 eV energy levels) or a single input spectra (for EI spectra, 70eV energy level) in peak list format, and ranks a list of candidate structures according to how well they match the input spectra. This candidate list may be provided by the user, or can be generated from select databases (HMDB, KEGG, LivestockDB, BovineDB, MilkDB, FooDB, MicrobeDB, YeastDB, E.coliDB, PhytoHub, CannabisDB, ContaminantDB, DrugBank). Chemical classes are predicted for each candidate molecule. The original similarity score used in the ranking was computed (Dice or DotProduct) by comparing the predicted spectra of a candidate compound with the input spectra. The new similarity score takes into account candidate molecule metadata (citation frequency and chemical classification) in addition to the original score. Users can choose to use either scoring method.

.home-box
  .home-box-citations
    p
    = render 'shared/citations'
  .home-box-source
    p
    = render 'shared/source_code_links'

hr
.wishart.support
  .logos
    .logos-bottom
      .chem-axon
        = link_to 'https://chemaxon.com/', target: '_blank' do
          = image_tag 'chemaxon.png'
  .funding
    | This project is supported by <a href="https://chemaxon.com/" target="_blank">ChemAxon</a>.


