h3 Users

= paginate @users

table.table.table-condensed.table-striped
  thead
    = search_form_for @q do |f|
      tr
        th = sort_link(@q, :name, 'Username')
        th = sort_link(@q, :email, 'Email')
        th = sort_link(@q, :created_at, 'Registered')
        th = sort_link(@q, :role, 'Role')
        th
      tr.search-row
        td = f.search_field :name_cont, class: "form-control input-sm"
        td = f.search_field :email_cont, class: "form-control input-sm"
        td 
        td = f.select :role_eq, User::USER_ROLES, { include_blank: true }, { class: "form-control input-sm" }
        th 
          = f.submit "Search", class: "btn btn-submit btn-sm"
          = link_to "Reset", users_path, class: "btn btn-reset btn-sm"
  tbody
    - @users.each do |user|
      tr
        td = link_to user.name, user
        td = user.email
        td = user.created_at.to_date
        td = user.role.titleize
        td
          a data-toggle="modal" href="#role-options-#{ user.id }" class="btn btn-download btn-sm" type="button" Change Role
          = render user
          = link_to("Delete user", user_path(user), :data => { :confirm => "Are you sure?" }, :method => :delete, :class => 'btn btn-error btn-sm btn-padded-left') unless user == current_user

= paginate @users