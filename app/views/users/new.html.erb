<br>
<%# <div class="alert-underconstruction">
  <div class="alert">
    <p align="center"> &nbsp;<strong><span class="glyphicon glyphicon-info-sign"></span>&nbsp;&nbsp;R<PERSON><PERSON><PERSON> (Beta) is still undergoing development. Please feel free to explore and use the predictor. Please provide us with your feedback.</strong></p>
  </div>
</div> %>
<div class="page-header"><h1>Register</h1></div>
<div class="well intro">
  <%= render :partial => "users/intro" %>
  <p></p>
</div>

<%= form_for @user do |f| %>
  <%= render 'shared/errors', object: @user %>

  <div class="form-group">
    <%= f.label :email %>
    <%= f.email_field :email, class: 'form-control', placeholder: "Enter email" %>
  </div>

  <div class="form-group">
    <%= f.label :password %>
    <%= f.password_field :password, class: 'form-control', placeholder: "Password" %>
  </div>

  <div class="form-group">
    <%= f.label :password_confirmation %>
    <%= f.password_field :password_confirmation, class: 'form-control', placeholder: "Reset the password" %>
  </div>

  <%= f.submit 'Register', class: 'btn btn-primary btn-lg' %>
<% end %>