div id="role-options-#{ user.id }" class="modal" style="display: none;"
  = simple_form_for user, :url => user_path(user), :html => {:method => :put, :class => 'form-horizontal' } do |f|
    .form-box.modal-box
      .modal-header
        a.close[data-dismiss="modal"] ×
        h3 Change Role
      .modal-body
        = f.input :role, :collection => User::USER_ROLES, :as => :radio_buttons, :label_method => lambda {|t| t.titleize}, :label => false, :item_wrapper_class => 'inline', checked: user.role
      .modal-footer
        = f.submit "Change Role", :class => "btn btn-submit"
        a.btn[data-dismiss="modal" href="#"] Close
