- jumps = %w{ identification physical_properties references }
#jumper.retention_index_compound
  .section-jumps
  	th <b> Jump To Section: </b>
    .btn-group.btn-group-sm role='group'
      - jumps.each do |section|
        = link_to section.humanize, "##{section}", class: 'btn-jump'
      = link_to "XML", retention_index_compound_path(retention_index_compound, format: :xml),
          target: '_blank', class: 'btn-jump'
