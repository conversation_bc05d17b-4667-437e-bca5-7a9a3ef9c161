- cache [natural_product] do
tr
  td.natural-product-link
    = link_to natural_product.np_mrd_id, natural_product, class: 'btn-card'
    .cas = natural_product.cas
  td.natural-product-name
    strong
      = link_to natural_product.name, natural_product
  td.natural-product-species
    ul.list-unstyled[style="margin-bottom:0"]
      - species_list = natural_product.species.pluck(:scientific_name).uniq
      - if species_list.blank?
        // # e.g. All Kingdoms
        li = nah natural_product.origin 
      - else
        - species_list.each_with_index do |species, i|
          - if i < 5
            li = species
          - else
            li class=("hidden-list #{natural_product.np_mrd_id}-species") = species

        - if species_list.size > 5
          - more_link = "#{natural_product.np_mrd_id}-more-species"
          - less_link = "#{natural_product.np_mrd_id}-less-species"
          - list_id = "#{natural_product.np_mrd_id}-species"
          li
            | (
            = link_to "show all", "#", id: more_link, onclick: "return showMore('#{more_link}', '#{less_link}', '#{list_id}')"
            = link_to "show less", "#", id: less_link, onclick: "return showLess('#{more_link}', '#{less_link}', '#{list_id}')", class: "hidden-link"
            | )
  - if natural_product.valid_thumb?
    td.natural-product-structure = image_tag(natural_product.thumb.url)
  - else
    td.natural-product-structure = moldb_vector_thumbnail(natural_product)
  td.weight-value
    = nah html_formula(natural_product.chemical_formula)
    br
    br
    = nah natural_product.moldb_average_mass
    br
    = natural_product.mono_mass
  //td = nah link_to natural_product.name, natural_product
  td = specdb_spectra_list_index(natural_product)
