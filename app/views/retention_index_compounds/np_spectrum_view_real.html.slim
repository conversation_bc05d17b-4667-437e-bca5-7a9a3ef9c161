.page-header: h1 = title("spectrum view ")



    / = render partial: 'chemical_shift_submissions/spectrum_view', locals: {:predicted_spectra_path => @image_path, \
    /   :download_submitted_chemical_shift_path => download_submitted_data_chemical_shift_submission_path, :nmrML_path => download_nmrml_chemical_shift_submission_path}

= render partial: 'spectrum_view_real', locals: {:download_submitted_chemical_shift_path =>download_submitted_chemical_shift_data_submission_path, :nmrML_path => download_nmrml_submission_path}
= link_to "<button>Back</button>".html_safe, natural_product_path, :style=> 'color:black;float:right;'