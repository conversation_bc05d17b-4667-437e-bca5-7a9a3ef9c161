.page-header: h1
  = title("Showing RIpred-Card for #{@retention_index_compound.ri_compound_name} (#{@retention_index_compound.retention_index_compound_id})")

- cache @retention_index_compound, expires: 14.days
  = render "/retention_index_compounds/jumper", retention_index_compound: @retention_index_compound
  

  table.content-table.table.table-condensed.table-bordered
    tbody
      = table_header_divider "Record Information"
      tr
        th Version
        td = Rails.application.config.version
      tr
        th= RetentionIndexCompound.human_attribute_name(:created_at)
        td = @retention_index_compound.created_at
      tr
        th= RetentionIndexCompound.human_attribute_name(:updated_at)
        td = @retention_index_compound.updated_at
      tr
        th  RI-Pred ID
        td = @retention_index_compound.retention_index_compound_id
      tr id="identification" class="table-head-reposition"
      = table_header_divider "Compound Identification", id: 'unknown'
      tr
        th Common Name
        td: strong = nah @retention_index_compound.ri_compound_name

      tr
        th Structure
        td
          - if @retention_index_compound.valid_thumb?
            .structure = image_tag(@retention_index_compound.thumb.url)
      tr
        th Average Molecular Weight
        td = nah @retention_index_compound.ri_compound_average_mass
      tr
        th SMILES
        td = link_to @retention_index_compound.ri_compound_smiles, jsmol_ri_modal_path(@retention_index_compound.ri_compound_smiles), data: {toggle: 'modal', target: '#ri-jsmol', smiles: @retention_index_compound.ri_compound_smiles}, class: "ri-modal-btn"
        /= render partial: 'jsmol/jsmol_modal_target'
        /td = link_to @retention_index_compound.ri_compound_smiles, jsmol_structure_modal_path(@retention_index_compound.ri_compound_smiles), data: {toggle: 'modal', target: '#structure-jsmol', smiles: @retention_index_compound.ri_compound_smiles}, class: "structure-modal-btn"

      tr id="physical_properties" class="table-head-reposition"
      = table_header_divider "Physical Properties", id: 'unknown'
      tr
        th Predicted Properties
        td
          table class="table table-bordered"
            col width = "250"
            col width = "250"
            col width = "500"
            thead
              tr
                th Property
                th Value
                th Reference
            tbody
              tr
                td Retention Index
                td = nah @retention_index_compound.retention_index_value
                //td = nah @retention_index_compound.experimental_melting_point_reference

    tr id="References" class="table-head-reposition"
    = table_header_divider "References", id: 'unknown'
    tr
      th General References
      td.data-table-container = "Accurate Prediction of Gas Chromatographic Kováts Retention Indices"

= render partial: 'jsmol/jsmol_modal_target'