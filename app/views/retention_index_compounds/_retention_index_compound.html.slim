- cache [retention_index_compound] do
tr
  td.retention-index-compound-link
    = link_to retention_index_compound.retention_index_compound_id, retention_index_compound, class: 'btn-card'
    //= link_to retention_index_compound, retention_index_compound.retention_index_compound_id, class: 'btn-card'
  td.retention-index-compound-name
    strong
      = link_to retention_index_compound.ri_compound_name, retention_index_compound
  //td.retention-index-smiles
  //  = link_to retention_index_compound.ri_compound_smiles, jsmol_ri_modal_path(retention_index_compound.ri_compound_smiles),data: {toggle: 'modal', target: '#ri-jsmol', smiles: retention_index_compound.ri_compound_smiles}, class: "ri-modal-btn"
 	//td.retention-index-stationary-phase
  //  = nah retention_index_compound.compound_stationary_phase
  td.retention-index-smiles
    - if retention_index_compound.ri_compound_smiles.present?
      = link_to "Jsmol", jsmol_ri_modal_path(retention_index_compound.ri_compound_smiles), 
        data: {toggle: 'modal', target: '#ri-jsmol', smiles: retention_index_compound.ri_compound_smiles}, class: 'btn btn-primary', style: 'font-size: 1.2rem; margin-right: 1.0em; margin-bottom: 0.5rem;', 'data-no-turbolink': false
      span style="display: inline-block; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; width: 20rem;"
        = link_to retention_index_compound.ri_compound_smiles, jsmol_ri_modal_path(retention_index_compound.ri_compound_smiles),
            data: {toggle: 'modal', target: '#ri-jsmol', smiles: retention_index_compound.ri_compound_smiles}, class: "ri-modal-btn"
    - else
      = nah
  td.retention-index-derivatization-type
    = retention_index_compound.compound_derivative_type
  td.retention-index-avg-mass
    = retention_index_compound.ri_compound_average_mass
    //= retention_index_compounds_smiles_to_mw_convert("#{retention_index_compound.ri_compound_smiles}")
    //= retention_index_compounds_smiles_to_mw_convert_path("#{retention_index_compound.ri_compound_smiles}")
    //= retention_index_compound.ri_compound_average_mass
  td.retention-index-stat-phase
    = retention_index_compound.compound_stationary_phase
  td.retention-index-value
    = retention_index_compound.retention_index_value
