div.page-header
    h1 Retention Index Search
br
.well-content.form-wrapper id="variable_html"
    br
    table.table.table-striped.table-condensed.table-hover.natural-products.depositions#search-results-table
        thead
            tr
            th RIpred ID
            th.header-name Structure Name
            th Chemical Structure 
            th Derivatization Type 
            th Average Mass
            th Stationary Phase
            th Retention Index
        tbody
            -if @retention_index_search_results.present?
                - @retention_index_search_results.each do |result|
                    tr
                        td.retention-index-compound-link
                            = link_to result.retention_index_compound_id, result, class: 'btn-card'
                        td.retention-index-compound-name
                            strong
                                = link_to result.ri_compound_name, result
                        td.retention-index-smiles
                            - if result.ri_compound_smiles.present?
                                = link_to "Jsmol", jsmol_ri_modal_path(result.ri_compound_smiles), 
                                    data: {toggle: 'modal', target: '#ri-jsmol', smiles: result.ri_compound_smiles}, class: 'btn btn-primary', style: 'font-size: 1.2rem; margin-right: 1.0em; margin-bottom: 0.5rem;', 'data-no-turbolink': false
                                span style="display: inline-block; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; width: 20rem;"
                                    = link_to result.ri_compound_smiles, jsmol_ri_modal_path(result.ri_compound_smiles),
                                        data: {toggle: 'modal', target: '#ri-jsmol', smiles: result.ri_compound_smiles}, class: "ri-modal-btn"
                            - else
                                = nah
                        td.retention-index-derivatization-type
                            = result.compound_derivative_type
                        td.retention-index-avg-mass
                            = nah result.ri_compound_average_mass
                        td.retention-index-stat-phase
                            = nah result.compound_stationary_phase
                        td.retention-index-value
                            = result.retention_index_value