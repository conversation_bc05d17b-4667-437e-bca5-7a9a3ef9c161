div.page-header
    h1 Retention Index Search
br
.well-content.form-wrapper id="variable_html" style="font-weight: bold; font-size: 17px;"
  label Search Form for RI data
  br
  br
  = form_for @retention_index_compound, :url => url_for(:controller => 'retention_index_compounds', :action => 'create') do |ff|
    .row
        .col-sm-5
            br
            = moldbi_structure_drawer '', "structure_input", format: "smiles"
            //= link_to "#{glyphicon('play-circle')} MarvinJS Tutorials".html_safe, 'https://www.youtube.com/playlist?list=PLA3Ev2ngKC0TY2p59vJhlYGm-wmrLaWp6', class: 'btn btn-default pull-left', target: '_blank', style: 'margin: 0.5rem 0 0 0;'
            //br
            .col-sm
                = link_to "#{glyphicon('play-circle')} MarvinJS Tutorials".html_safe, 'https://www.youtube.com/playlist?list=PLA3Ev2ngKC0TY2p59vJhlYGm-wmrLaWp6', class: 'btn btn-default pull-left', target: '_blank', style: 'margin: 0 0 0 0;'
            .col-sm
                .row
                    .col-sm-4
                        = link_to "#{glyphicon(:save)} Load Example ".html_safe, "javascript:void(0)",
                        class: "btn btn-default search-example-loader", 'data-smiles': "CN1C=NC(C[C@H](N)C(O)=O)=C1",
                        'data-no-turbolink': true
        .col-sm-7.hide-overflow
            br
            br
            .row
                = ff.label :instruction, "To Operate this Search:"
                ol
                    li Enter a Retention Index value and a Tolerance parameter
                    li Select the GC stationary phase from the pull-down menu (optional)
                    li Select the type of derivatization from the pull-down menu (optional)
                    li Enter the Average Mass and and a Tolerance level for the mass (optional)
                    li Enter any number of TMS and/or TBDMS derivatives (optional) 
                    li You can optionally draw a structure in the white box on the left as a search query
                    li Click the "Search" button
                br
            .row
                .col-sm-3
                    = ff.label :compound_name_label, "Compound Name:", class: "predictionlabel"
                .col-sm-3
                    = ff.text_field :ri_compound_name
            .row
                .col-sm-3
                    = ff.label :retention_index_label, "*Retention Index:", class: "predictionlabel"
                .col-sm-3
                    = ff.number_field :retention_index_value, {min: 0, step: 0.001, required: true}
            .row
                .col-sm-3
                    = ff.label :retention_index_tolerance_label, "*Tolerance (%):", class: "predictionlabel"
                .col-sm-3
                    = number_field_tag :retention_index_tolerance, {}, min: 1, max: 10, required: true
            .row
                .col-sm-3
                    = ff.label :average_mass_label, "Average Mass:", class: "predictionlabel"
                .col-sm-3
                    = ff.number_field :ri_compound_average_mass, {min: 0, step: 0.001}
            .row
                .col-sm-3
                    = ff.label :mass_tolerance_label, "Tolerance ± (Da):", class: "predictionlabel"
                .col-sm-3
                    = number_field_tag :mass_tolerance, {}, min: 0.0, max: 10, step: 0.1
            .row
                .col-sm-3
                    = ff.label :derivatization_type_label, "Derivatization Type:", class: "predictionlabel"
                .col-sm-3
                    = ff.select :compound_derivative_type, physical_state_of_compound_collection
            //.row
            //    .col-sm-3
            //        = ff.label :derivative_count_label, "Number of Derivatives:", class: "predictionlabel"
            //    .col-sm-1
            //        = number_field_tag :tms_count, {}, min: 0, max: 4, step: 1, placeholder: "TMS", class: "predictionlabel"
            //    .col-sm-2
            //        = number_field_tag :tbdms_count, {}, min: 0, max: 4, step: 1, placeholder: "TBDMS", class: "predictionlabel"
            .row
                .col-sm-3
                    = ff.label :column_type_label, "Column Type:" , class: "predictionlabel"
                .col-sm-3
                    = ff.select :compound_stationary_phase, provenance_collection, {:class => 'select', :id => "submission_provenance", :include_blank => 'Select One'}
            br
            = ff.button 'Search' , type: 'submit', :onclick => "submit_button_click()", class: 'btn btn-submit'