<div class="alert-underconstruction">
  <div class="alert">
    <p align="center"> &nbsp;<strong><span class="glyphicon glyphicon-info-sign"></span>&nbsp;&nbsp;NP-MRD (Beta) is still undergoing development. Please feel free to explore and use the database. But the interface is still being refined and the deposition might be slow. Please provide us with your feedback.</strong></p>
  </div>
</div>
.page-header: h1 = title("NP-Chemical Shift Offline Bulk Deposition (Natural Product Chemical Shift Offline Bulk Deposition)")

.well
  .intro
    = "Please Download the generated Excel format file to complete the submission at your convenience."

.downloads
  table.table-standard
    thead
      tr
        th.data-set-col Documentation Description
        th.download-col Download Link
    tbody
      tr
        td Excel File for Each of The Submitted Compounds
        td = nah link_to "Download Excel", downloadexcel_batch_upload_path
      //tr
        //td nmrML-NMR Data Exchange Format File
        //td = nah link_to "Download nmrML", nmrML_path

.well-content.form-wrapper id="variable_html"
  .intro

    <p> When you are ready, please upload the <strong>'.zip'</strong> Excel file to complete your submission. Please do not change the file name.</p><br>

  = form_for @batch_submission do |f|
    
    = f.fields_for :batch_uploads do |ff|
      .file-selector
        = ff.file_field :excel_file, as: :file, required: true
    br
    = f.submit "Submit Excel File", style: "text-align:center"


br
br
= link_to "<button>Back</button>".html_safe, submissions_path, :style=> 'color:black;float:right;'