/ <div class="alert-underconstruction">
/   <div class="alert">
/     <p align="center"> &nbsp;<strong><span class="glyphicon glyphicon-info-sign"></span>&nbsp;&nbsp;R<PERSON><PERSON> (Beta) is still undergoing development. Please feel free to explore and use the predictor. Please provide us with your feedback.</strong></p>
/   </div>
/ </div>
br
h1 Bulk submission of structures for prediction of Gas Chromatography Kovats' Retention Index 
br
.well-content.form-wrapper id="variable_html"
  .well
    = render partial: 'batch_submissions/intro'
  h2 <b>Instructions for Offline Bulk Submission<b>
  br
  .well-content
    = render partial: 'batch_uploads/instructions'


  .row
    .col-sm-7  
      = form_for @batch_submission do |f|
        //= f.label :Upload
        = f.fields_for :batch_uploads do |ff|
          .file-selector
            = ff.file_field :batch_file, as: :file, required: true
        br
        = f.submit "Submit CSV File", style: "text-align:center", :onclick => "submit_button_click()"
    //.col-sm-5
    //  br
    //  br
    //  = "Example CSV File"
 
    