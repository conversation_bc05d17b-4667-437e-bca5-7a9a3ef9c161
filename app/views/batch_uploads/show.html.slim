<div class="alert-underconstruction">
  <div class="alert">
    <p align="center"> &nbsp;<strong><span class="glyphicon glyphicon-info-sign"></span>&nbsp;&nbsp;NP-MRD (Beta) is still undergoing development. Please feel free to explore and use the database. But the interface is still being refined and the deposition might be slow. Please provide us with your feedback.</strong></p>
  </div>
</div>

br
- if params[:notice].present?
  .alert-citation.alert
    p #{glyphicon(:'glyphicon-ok')} #{params[:notice]}

.page-header: h1 = title("NP-Chemical Shift Offline Bulk Deposition (Natural Product Chemical Shift Offline Bulk Deposition)")

.well
  .intro
    = "<b>You have submitted data for the following Natural Products:</b>".html_safe


//br
//.page-header: h2 = title('Past Submissions')
.table-responsive
  table.table.table-striped.table-condensed.table-hover.natural-products
    thead
      tr
        th ID
        th Name
        th Structure
        th InChi Identifier
        th Submission Date
        th Submission Status
        th Entry Type
        th View
    tbody
      - @chemical_shift_submissions.each do |c_s|
        tr
          td.natural-product-link
            - puts c_s.inspect
            - if c_s.natural_product_id
              - np = NaturalProduct.find(c_s.natural_product_id)
              - puts np
              - if c_s.valid
                =  link_to np.np_mrd_id, np, class: 'btn-card'
              - else
                strong
                  .btn-card
                    = np.np_mrd_id
            - else
              = "No ID"
          td.natural-product-name
            - if np
              - if c_s.valid
                strong
                  = link_to np.name, np
              - else
                strong
                  = np.name
              - if np.valid_thumb?
                td.natural-product-structure
                  = image_tag(np.thumb.url)
              - else
                td.natural-product-structure 
                  = moldb_vector_thumbnail(np)
            - else
              = "No Name"
              td
        
          td.natural-product-inchi
            -if np
              - if np.structure_resource
                = np.structure_resource.inchi
              - else
                - if np.structure
                  = np.structure
                - else
                  = "Structure Image Not Available"
            - else
              = "No Structure Provided"
          td
            = (c_s.created_at).to_s.split(" ")[0]
          td
            - if c_s.valid.present?
              - if c_s.valid
                .accepted_status
                  = "Complete"
              - else
                .pending_status
                  = "Pending/Failed"
            - else
              .pending_status
                = "Pending/Failed"
          td
            = "Spectrum"
          td
            - if c_s.valid.present?
              - if c_s.valid
                = link_to "View", np
              //- else
                //= link_to "Edit", edit_submission_path(submission)
            - else
              = "NA"
= link_to "<button>Back</button>".html_safe, submissions_path, :style=> 'color:black;float:right;'