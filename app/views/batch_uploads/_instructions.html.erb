<p style="font-family:arial, sans-serif;font-size: 10;font-weight: 1">
  1) Submit a <strong>CSV</strong> file with multiple structures. Each line has the format: <b>'Compound Name', 'Smiles', 'Stationary Phase', 'Derivatization Type', 'Highest no. of derivatized groups to attach'</b> (e.g.<i>L-Alanine, C[C@H](N)C(O)=O, SemiStdNP, TMS, 4</i>) <%= link_to "<button><b>Example CSV File</b></button>".html_safe, example_batch_upload_csv_path, :style=> 'color:#008080;float:middle;' %><br>
  2) Accepted stationary phases are: SemiStdNP, StdNP, StdP<br>
  3) Accepted derivatives types are: No Derivatization, TMS, TBDMS, TMS_and_TBDMS <br>
  4) Download the Excel file<br>
  5) Fill up the Excel file with required information<br>
  6) Submit back the filled up <strong> Zipped Excel</strong> file<br> <%= link_to "<button><b>Multiple Structure Submission Tutorial for Kovats' RI Prediction</b></button>".html_safe, tutorial_batch_upload_path, :style=> 'color:#008080;float:right;' %><br>
</p>
