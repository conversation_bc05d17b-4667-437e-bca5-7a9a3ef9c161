h4 Peak Table and Fragment Structures
| Fragment IDs are shown in 
span.frag red
| . Corresponding scores for each fragment are in 
span.score blue
| .
- structuredir = "public/structures/" + spectrum.id.to_s
.output-header
  | Spectra Peaks and Possible Matching Fragments for 
  span.highlight = spectrum.database_molecule.source_id
table.spectra-table
  - spectra = false
  - fragments = false
  - start = true
  - frag_count = 0
  - File.readlines(spectrum.get_peak_list_file).each do |line|
    - if !spectra
      - if line =~ /^\s*([-+]?[0-9]*\.?[0-9]+)\s+([-+]?[0-9]*\.?[0-9]+)\s+((?:[\d-]+\s*)*)(?:\((.*)\))*$/
        tr
          td = $1
          td= $2
          td.frag= $3
          td.score = $4
      - elsif line =~ /^([A-Za-z]+[\d]*\s*)$/
        tr
          th colspan="2" = line.delete!("\n")
      - elsif line.blank?
        | </table>
        - spectra = true
        .output-header Fragments Generated
        | <table class="assign-fragments-table">
        | <tr><th>Structure</th><th>ID</th><th>Mass</th><th>SMILES</th></tr>
    - elsif !fragments
      - if frag_count >= 100
        | </table>
        | ...
        br
        | There are over 100 fragments, 
        = link_to 'download the results', query.output_file.url
        |  to view the full list.
        - fragments = true
      - elsif line.blank?
        | </table>
        - fragments = true
      - elsif line =~ /^(\d+)\s+([-+]?[0-9]*\.?[0-9]+)\s+(\S+)$/
        - frag_count = frag_count + 1
        - id = $1
        - mass = $2
        - structure = $3
        tr
          td.image = get_image(structuredir + "/" + id + ".png", "frag-structure img-responsive", "Structure").html_safe
          td.frag = id
          td = mass
          td = structure