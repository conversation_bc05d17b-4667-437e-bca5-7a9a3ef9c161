.input-box
  h4 Spectrum Prediction Input Parameters: 
  table.input-list
    tr
      th
        | Parent Compound Structure 
        span.note
          | (
          = @query.compound_format
          |  Format)
      td = @query.compound
    tr
      th Parent Compound Mass
      td = @query.compound_mass
    tr
      th Spectra Type
      td = @query.spectra_type
    tr
      th Ion Mode
      td = @query.ion_mode.try(:capitalize)
    tr
      th Adduct Type
      td = @query.adduct_type
    tr
      th Probability Threshold
      td = @query.threshold
    tr
      th Status
      td 
        span[class="badge badge-#{@query.status.downcase.gsub(/ /, "-")}"] = @query.status