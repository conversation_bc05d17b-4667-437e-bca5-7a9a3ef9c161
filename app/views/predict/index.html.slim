.page-header
  h3 = title("Spectra Prediction")
.method
  p This function predicts QToF MS/MS spectra for multiple collision energies for a given input small molecule. Spectra are computed for low (10 eV), medium (20 eV) and high (40 eV) collision energy levels and are represented by a list of 'mass intensity' pairs, each corresponding to a peak in the spectrum.
.form-box
  = form_for(@predict_query, url: predict_new_path, html: {id: 'predict-form', class: "form-horizontal"}, method: "post") do |f|
    .form-group
      = f.label :compound, class: "col-sm-2 control-label" do
        | Parent Compound Structure
        br
        span.note
          | InChI or SMILES format
      .col-sm-10
        = f.text_field :compound, class: "form-control", placeholder: "Enter an InChI or SMILES string"
        span.help-block
          | InChI strings need to start with "InChI=" and are not expected to have any charge - an additional proton will be added or removed. Maximum compound size is 200 atoms.
          / a.example-loader href="#" data-example="#{render 'utilities/inchi_example'}" data-update="predict_query_compound" InChI example
          / | , 
          / a.example-loader href="#" data-example="#{render 'utilities/smiles_example'}" data-update="predict_query_compound" SMILES example
          / | , or another 
          / a.example-loader href="#" data-example="#{render 'utilities/smiles_example_2'}" data-update="predict_query_compound" SMILES example
          / | .
    .form-group
      .row
        .col-sm-offset-1.col-sm-1
          = label_tag nil, "Examples", class: "control-label"
        .col-sm-3 
          a.example-loader href="#" class="btn btn-default example-loader-inchi-1" data-example="#{render 'utilities/inchi_example'}" data-update="predict_query_compound" Load InChI Example #1
        .col-sm-3 
          a.example-loader href="#" class="btn btn-default example-loader-smiles-1" data-example="#{render 'utilities/smiles_example'}" data-update="predict_query_compound" Load SMILES Example #1
        .col-sm-3 
          a.example-loader href="#" class="btn btn-default example-loader-smiles-2" data-example="#{render 'utilities/smiles_example_2'}" data-update="predict_query_compound" Load SMILES Example #2

    .form-group
      .col-sm-offset-2.col-sm-2
        = f.label  :spectra_type, "Spectra Type", class: "control-label"
        = f.select :spectra_type, Query::SPECTRA_TYPES, {}, {class: "form-control"}
      .col-sm-2
        = f.label :ion_mode, "Ion Mode", class: "control-label"
        = f.select :ion_mode, {Negative: "negative", Positive: "positive"}, {}, {class: "form-control"}
      .col-sm-2
        - selected_adducts = @predict_query.ion_mode == "positive" ? @positive_adducts : @negative_adducts
        = f.label :adduct_type, "Adduct Type", class: "control-label"
        = f.select :adduct_type, selected_adducts, {}, {class: "form-control", 'data-positive-adducts' => @positive_adducts.to_json, 'data-negative-adducts' => @negative_adducts.to_json}
    / .form-group
    /   = f.label :threshold, "Probability Threshold", class: "col-sm-2 control-label"
    /   .col-sm-2
    /     = f.text_field :threshold, value: @predict_query.threshold.blank? ? 0.001 : @predict_query.threshold, class: "form-control"
    = f.hidden_field :threshold, value: 0.001
      / p.form-control-static
      /   | The probability below which to prune unlikely fragmentations in the spectrum prediction.
    .form-group.end-row
      #form-buttons
        .col-sm-offset-2.col-sm-10
          = link_to 'Reset', predict_path, class: 'btn btn-primary btn-reset'
          = f.submit "Submit", class: "btn btn-primary btn-submit", id: "query-submit"
      #form-submit
        .col-sm-offset-2.col-sm-2
          #small-progress
            progress id="progressbar" value="100" max="100"
        .col-sm-8
          span#submitted-text Please wait while your input is validated (this may take a few seconds)
.end-note
  | If you wish to run multiple jobs, input larger query molecules, or customize the computation parameters, you can freely download the 
  = link_to "docker image", "https://hub.docker.com/repository/docker/wishartlab/cfmid",target: "_blank"
  | .
