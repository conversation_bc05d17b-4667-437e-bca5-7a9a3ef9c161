= render partial: "predict/predict_input"
- unless @query.runtime.blank?
  .runtime-box
    h4
      | Runtime Info: 
      button.btn.btn-xs.btn-runtime.hide-runtime[href="#"]
        | Show 
        span.glyphicon.glyphicon-collapse-down
    .collapse.runtime-collapse
      = simple_format(@query.runtime)
- if @query.parsed_output == "Successful"
  .results-box
    h4 Results: 
    ul.results-tabs.nav.nav-tabs
      li
        a#computed-tab[href="#computed" data-toggle="tab"] Computed Results
      - if @experimental_data.present?
        li
          a#experimental-tab[href="#experimental" data-toggle="tab"] Experimental Results
    .tab-content
      #computed.tab-pane.active
        h4
          | Computed Results: 
          = link_to 'Download <span class="glyphicon glyphicon-save"></span>'.html_safe, @query.output_file.url, class: 'btn btn-xs btn-download'
        .row
          .col-xs-10
            |  Predicted spectra are shown below. Peaks for which corresponding fragments have been found are colored red; unassigned peaks are colored blue. Hover over the peaks to see the exact mass and intensity values, along with the highest scoring assigned fragments, if found. Clicking on red spectra lines will show a list of all possible predicted fragments for that peak. A list of all possible matching fragments is shown below the spectra. 
          = render 'shared/query_compound'
        - if @query.spectra_type == "ESI"
          h4.chart-title
            | Predicted Low Energy MsMs Spectrum (10V), 
            = @query.adduct_type
          #energy0
            - if !@spectra_data[:spectra].has_key?("energy0")
              span[style="font-style: italic; margin-left: 1em;"] No MsMs Spectrum
          h4.chart-title
            | Predicted Medium Energy MsMs Spectrum (20V), 
            = @query.adduct_type
          #energy1
            - if !@spectra_data[:spectra].has_key?("energy1")
              span[style="font-style: italic; margin-left: 1em;"] No MsMs Spectrum
          h4.chart-title
            | Predicted High Energy MsMs Spectrum (40V), 
            = @query.adduct_type
          #energy2
            - if !@spectra_data[:spectra].has_key?("energy2")
              span[style="font-style: italic; margin-left: 1em;"] No MsMs Spectrum
        - else
          h4.chart-title
            | Predicted MsMs Spectrum (70eV), 
            = @query.adduct_type
          #energy0
            - if !@spectra_data[:spectra].has_key?("energy0")
              span[style="font-style: italic; margin-left: 1em;"] No MsMs Spectrum
        h4 Peak Table and Fragment Structures
        | Fragment IDs are shown in 
        span.frag red
        | . Corresponding scores for each fragment are in 
        span.score blue
        | .
        = render partial: "predict/predict_output", locals: {query: @query, spectra_data: @spectra_data}
        javascript:
          var options = { 
            xMin: "#{ @spectra_data[:spectra].map{|e,data|data}.flatten.map{|h|h[:x]}.min - 10 }", 
            xMax: "#{ @spectra_data[:spectra].map{|e,data|data}.flatten.map{|h|h[:x]}.max + 10 }"
          };
        - @spectra_data[:spectra].each_pair do |energy_level,data|
          javascript:
            mz_plot("##{ energy_level }", #{ data.to_json.html_safe }, "#{ @query.secret_id }", options );
      - if @experimental_spectra.present?
        #experimental.tab-pane.active
          h4 Experimental Results: 
          .row
            .col-xs-8
              |  Experimental spectra are shown below. Peaks for which corresponding fragments have been found are colored red; unassigned peaks are colored blue. Hover over the peaks to see the exact mass and intensity values, along with the highest scoring assigned fragments, if found. Clicking on red spectra lines will show a list of all possible predicted fragments for that peak. A list of all possible matching fragments is shown below the spectra. 
            = render 'shared/query_compound'
          - @experimental_spectra.each do |spectrum|
            h4.chart-title = spectrum.name
            h5
              | Source: 
              = link_to_database(spectrum.database_molecule.source, spectrum.database_molecule.source_id).html_safe
            h5
              | File: 
              = link_to 'Download <span class="glyphicon glyphicon-save"></span>'.html_safe, spectrum.get_peak_list_url, class: 'btn btn-xs btn-download'
            div id=("spectra-" + spectrum.id.to_s)
              javascript:
                mz_plot("#spectra-#{ spectrum.id }", #{ @experimental_data[spectrum.id].to_json.html_safe }, "#{ spectrum.id }" );
            = render partial: "predict/experimental_output", locals: {spectrum: spectrum}
        // We select the starting tab down here because we need everything to render on screen first in order to draw the graphs properly (because we need the dimensions of the elements)
        javascript:
          $('.results-tabs a#experimental-tab').tab('show');
      - else
        javascript:
          $('.results-tabs a#computed-tab').tab('show');

- unless @query.error.blank?
  .error-box
    h4 Error:
    = simple_format(@query.error)
#fragment-modal.modal.fade.bs-example-modal-sm[tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true"]
