.output-header
  | Spectra Peaks and Possible Matching Fragments for 
  span.highlight = query.compound
table.spectra-table
  - spectra_data[:spectra].each_pair do |energy_level,data|
    tr
      th colspan="2" = energy_level
    - data.each do |peak|
      tr
        td = peak[:x]
        td = peak[:y]
        td.frag= peak[:structure_ids].join(" ")
        td.score = peak[:scores].join(" ")

.output-header Fragments Generated

table.assign-fragments-table
  tr
    th Structure
    th ID
    th Mass
    th SMILES
  - spectra_data[:structures].each_pair do |id,structure|
    - if id.to_i <= 99
      tr
        td.image = get_image(structure[1], "frag-structure img-responsive", "Structure").html_safe
        td.frag = id
        td = structure[0]
        td = structure[1]

- if spectra_data[:structures]["100"].present?
  | ...
  br
  | There are over 100 fragments, 
  = link_to 'download the results', query.output_file.url
  |  to view the full list.