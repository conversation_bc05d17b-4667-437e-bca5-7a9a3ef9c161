br
<div class="alert-underconstruction">
  <div class="alert">
    <p align="center"> &nbsp;<strong><span class="glyphicon glyphicon-info-sign"></span>&nbsp;&nbsp;NP-MRD (Beta) is still undergoing development. Please feel free to explore and use the database. But the interface is still being refined and the deposition might be slow. Please provide us with your feedback.</strong></p>
  </div>
</div>
.page-header: h1= title 'Searching NP-MRD'

.search-help-bar
  = form_tag unearth.search_path, method: :get, class: 'form-inline' do
    = hidden_field_tag :searcher, 'natural_products'
    .form-group
      = label_tag :query, 'Query', class: "sr-only"
      = text_field_tag :query, params[:query],
          class: 'search-query form-control input-ms', placeholder: 'Search'
    / .form-group
    /   = label_tag :searcher, 'Search type', class: "sr-only"
    /   = select_tag :searcher,
    /       options_for_select(scope_search_types, params[:searcher]),
    /       class: 'form-control input-ms'
    = button_tag "#{glyphicon(:search)} Search".html_safe,
        class: 'btn btn-default btn-md btn-search'
br
.well-content
  p
    ' NP-MRD supports advanced searching using a powerful search engine based on the
    ' #{link_to 'Lucene query language', 'http://lucene.apache.org/java/docs/index.html'}.
    ' NP-MRD text search supports boolean logic
    ' (<span class="bool">AND</span>, <span class="bool">OR</span>,
    ' <span class="bool">NOT</span> operations).  To match a string exactly,
    ' place quotes around your search term (for example "acetic acid" will only
    ' match the acetic followed by acid, it will not match acetic
    ' or acid alone). You can also search using "wild cards" by inserting a "*"
    ' in your search term. For example, searching for "acet*" will
    ' match all words starting with "acet".
    ' In addition, text search supports parenthetical groupings, and prepended
    ' +plus and -minus operators.
    br
  table.table-standard.search-fields
    thead
      tr
        th Example
        th Description
    tbody
      tr
        td= link_to 'Strychnine',
              unearth.search_path(searcher: 'natural_products', query: 'Strychnine')
        td
          ' Find all entries containing Strychnine
          ' in the NP-Card
      tr
        td= link_to '(histidine OR poultry) AND NOT glycolylneuraminic',
              unearth.search_path(searcher: 'natural_products', query: '(histidine OR poultry) AND NOT glycolylneuraminic')
        td Find all natural products containing histidine, poultry or both, but not containing glycolylneuraminic
      tr
        td= link_to '"acetic acid"', unearth.search_path(searcher: 'natural_products', query: '"acetic acid"')
        td
          ' Find all natural products where the <strong>entire term</strong>
          ' "acetic acid" is found. In other words, don't match "acetic" or "acid" alone.
