br
/ <div class="alert-underconstruction">
/   <div class="alert">
/     <p align="center"> &nbsp;<strong><span class="glyphicon glyphicon-info-sign"></span>&nbsp;&nbsp;R<PERSON><PERSON><PERSON> (Beta) is still undergoing development. Please feel free to explore and use the predictor. Please provide us with your feedback.</strong></p>
/   </div>
/ </div>
.page-header: h1 = title "NP-MRD Statistics"

.row
  .col-sm-3
    .sidenav
      ul.nav.nav-pills.nav-stacked data-spy='affix' data-offset-top="100"
        li.nav-header.strong.h2 Categories
        li.active= link_to "Natural Product Statistics", '#natural-product-stats'
        li = link_to 'Spectra Statistics', '#spectra-stats'
        li = link_to 'Reference Statistics', '#reference-stats'

  .col-sm-8
    - cache 'statistics', expires_in: 7.days do
      h2 id="natural-product-stats" = "Natural Product Statistics"
      table.table.table-striped.table-condensed
        thead
          tr
            th width="80%" Description
            th width="20%" Count
        tbody
          tr
            th Total Natural Products
            td = NaturalProduct.count
      br
      h2 id="spectra-stats" = "Spectra Statistics"
      table.table.table-striped.table-condensed
        thead
          tr
            th width="80%" Description
            th width="20%" Count
        tbody
          tr
            th Total NMR Spectra
            td = @specdb_stats.attributes["total_nmr_spectra"]
          tr
            th Total Natural Products with NMR Spectra
            td = @specdb_stats.attributes["total_compounds_with_nmr_spectra"]
          tr
            th Total Natural Products with NMR 1D Spectra
            td = @specdb_stats.attributes["total_compounds_with_one_d_nmr_spectra"]
          tr
            th Total Natural Products with NMR 2D Spectra
            td = @specdb_stats.attributes["total_compounds_with_two_d_nmr_spectra"]

      br
      h2 id="reference-stats" = "Reference Statistics"
      table.table.table-striped.table-condensed
        thead
          tr
            th width="80%" Description
            th width="20%" Count
        tbody
          tr
            th Total Number of References
            td = (CiteThis::Article.count + CiteThis::ExternalLink.count + CiteThis::Textbook.count)
