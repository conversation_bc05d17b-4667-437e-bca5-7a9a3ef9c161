<div class="alert-underconstruction">
  <div class="alert">
    <p align="center"> &nbsp;<strong><span class="glyphicon glyphicon-info-sign"></span>&nbsp;&nbsp;NP-MRD (Beta) is still undergoing development. Please feel free to explore and use the database. But the interface is still being refined and the deposition might be slow. Please provide us with your feedback.</strong></p>
  </div>
</div>
- smile_download = Rails.root.join("public","system","downloads","current","smiles.csv.gz")
.page-header: h1 = title("Downloads")

//= render "simple/download_terms"

.downloads
  //h2 Natural Product and Protein Data (in XML format)
  //table.table-standard
  //  thead
  //    tr
  //      th.data-set-col Data Set
  //      th.release-col Released on
  //      th.download-col Download Link
  //      th.size-col File Size
  //  tbody
  //    tr
  //      td All Natural Products
  //      td = nah download_created_at('npmrd_natural_products.zip')
  //      td = link_to_download('Download', 'npmrd_natural_products')
  //      td = nah file_size('npmrd_natural_products.zip')

  h2 Structures (in <a href="http://en.wikipedia.org/wiki/Chemical_table_file#SDF">SDF Format</a>)
  table.table-standard
    thead
      tr
        th.data-set-col Data Set
        th.release-col Released on
        th.download-col Download Link
        th.size-col File Size
    tbody
      tr
        td Natural Product Structures
        td = nah download_created_at('structures.zip')
        td = link_to_download('Download', 'structures')
        td = nah file_size('structures.zip')

  h2 Structures (in <a href="https://en.wikipedia.org/wiki/Simplified_molecular-input_line-entry_system">Smiles Format</a>)
  table.table-standard
    thead
      tr
        th.data-set-col Data Set
        th.release-col Released on
        th.download-col Download Link
        th.size-col File Size
    tbody
      tr
        td Natural Product Structures ( <a href="https://en.wikipedia.org/wiki/Comma-separated_values">CSV</a>)
        td = nah download_created_at('smiles.csv.gz', version: 'current', compressed: false)
        //td = link_to_download('Download', 'smiles.csv.gz',compressed: false)
        td = link_to "#{glyphicon(:download)} Download".html_safe, smiles_path, class: 'btn btn-default btn-xs'
        td = nah file_size('smiles.csv.gz', version: 'current', compressed: false)


  h2 Spectra
  table.table-standard
    thead
      tr
        th.data-set-col Data Set
        th.release-col Released on
        th.download-col Download Link
        th.size-col File Size
    tbody
      tr
        td NMR Spectra FID Files
        td = nah download_created_at('spectral_data/NP-MRD_fid_files.zip', version: 'current', compressed: false)
        td = link_to "#{glyphicon(:download)} Download".html_safe, 'http://specdb.wishartlab.com/downloads/exports/hmdb/fid_files/NP-MRD_fid_files.zip',
                class: 'btn btn-default btn-xs'
        td = nah file_size('spectral_data/NP-MRD_fid_files.zip', version: 'current', compressed: false)
      tr
        td Raw NMR Spectra Peaklist Files (TXT)
        td = nah download_created_at('spectral_data/NP-MRD_nmr_peak_lists.zip', version: 'current', compressed: false)
        td = link_to "#{glyphicon(:download)} Download".html_safe, 'http://specdb.wishartlab.com/downloads/exports/hmdb/peak_lists_txt/NP-MRD_nmr_peak_lists.zip',
                class: 'btn btn-default btn-xs'
        td = nah file_size('spectral_data/NP-MRD_nmr_peak_lists.zip', version: 'current', compressed: false)
      tr
        td NMR Spectra Files (XML)
        td = nah download_created_at('spectral_data/NP-MRD_nmr_spectra.zip', version: 'current', compressed: false)
        td = link_to "#{glyphicon(:download)} Download".html_safe, 'http://specdb.wishartlab.com/downloads/exports/hmdb/spectra_xml/NP-MRD_nmr_spectra.zip',
                class: 'btn btn-default btn-xs'
        td = nah file_size('spectral_data/NP-MRD_nmr_spectra.zip', version: 'current', compressed: false)
