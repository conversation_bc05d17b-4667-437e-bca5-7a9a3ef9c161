- #= debug params
.page-header
  h3 = title("Compound Identification")
.method
  p This function determines the compounds that most closely match to a given MS/MS spectrum. The input MS/MS spectra (at one or more collision energies) are compared to in silico predicted MS/MS spectra and/or experimental MS/MS spectra as chosen by the user. The top candidates are ranked according to how closely they match and returned in a list. Users may view the matching compounds and their scores in a table and the similarity of the observed spectra to the matched spectra using an MS mirror plot.
.form-box
  = form_for(@identify_query, url: identify_new_path, html: {id: 'identify-form', class: "form-horizontal"}, method: "post", multipart: true) do |f|
    .form-group
      = f.label :candidates_input, "Candidate Compounds", class: "col-sm-2 control-label"
      = hidden_field_tag :submit_or_find_or_nl, params[:submit_or_find_or_nl]
      .col-sm-10
        ul.nav.nav-pills.nav-pills-custom
          /li id="submit-candidates-toggle" class="#{ 'active' if params[:submit_or_find_or_nl] == 'submit' }" 
            a[href="#submit-candidates" data-toggle="tab"] Submit Candidates
          /li.text-only OR
          li id="find-candidates-toggle" class="#{ 'active' if params[:submit_or_find_or_nl] == 'find' }" 
            a[href="#find-candidates" data-toggle="tab"] Find Candidates
          li.text-only OR
          li id="neutral-loss-toggle" class="#{ 'active' if params[:submit_or_find_or_nl] == 'neutral-loss' }" 
            a[href="#find-candidates" data-toggle="tab"] Find Neutral Loss Candidates
      .tab-content
        #submit-candidates class="tab-pane #{ 'active' if params[:submit_or_find_or_nl] == 'submit' }"
          .col-sm-offset-2.col-sm-10
            p.form-control-static.help-text
              | The candidates should be represented as a list of compounds in the format 'ID SMILES_or_InChI' on each line. The list can have a maximum of 100 compounds. Maximum individual compound size is 200 atoms.
          .row
            .col-sm-offset-1.col-sm-1
              = label_tag nil, "Examples", class: "control-label"
            .col-sm-3   
              a href="#" class="btn btn-def example-loader-identify" data-example="#{ render 'utilities/candidates_example' }" data-update="identify_query_candidates" data-example-low="#{ render 'utilities/low_spectra_example' }" data-update-low="low_spectra" data-example-medium="#{ render 'utilities/medium_spectra_example' }" data-update-medium="medium_spectra" data-example-high="#{ render 'utilities/high_spectra_example'} " data-update-high="high_spectra" Load an example
          .form-group[style="margin-left: 0px;"]
            .col-sm-offset-2.col-sm-4
              = f.label :candidates, "Input Text", class: "control-label"
              = f.text_area :candidates, class: "form-control", rows: 8, wrap: "off"
            .col-sm-1
              .center-or OR 
            .col-sm-4
              = f.label :candidates_file, "Load a File", class: "control-label"
              = f.file_field :candidates_file
          .form-group[style="margin-left: 0px"]
            = f.label  :spectra_type, "Spectra Type", class: "col-sm-2 control-label"
            .col-sm-2
              = f.select :spectra_type, Query::SPECTRA_TYPES, {}, {class: "form-control small"}
          .form-group[style="margin-left: 0px"]
            = f.label  :ion_mode, "Ion Mode", class: "col-sm-2 control-label"
            .col-sm-2
              = f.select :ion_mode, {Negative: "negative", Positive: "positive"}, {}, {class: "form-control faux-inline"}
        #find-candidates class="tab-pane #{ 'active' if params[:submit_or_find_or_nl] == 'find' }"
          .col-sm-offset-2.col-sm-10
            #find-candidates-description
              p.form-control-static.help-text
                |  When querying a database, the parent ion mass and adduct type will be used to approximate the molecular weight of the query compound. This molecular weight will then be used to select the most likely candidate compounds from the database (using the candidate mass tolerance), against which the query spectra are compared. The maximum candidate compound size is 200 atoms.
            #find-nl-description
              p.form-control-static.help-text
                |  When querying a database, the neutral loss spectra will be calculated from the user supplied MS/MS spectra and parent ion mass. A spectral spectral match will then be performed between this neutral loss spectra and all calculated neutral loss MS/MS spectra in the selected databases. 

          #find-candidates-examples
            .row
              .col-sm-offset-1.col-sm-1
                = label_tag nil, "Examples", class: "control-label"
              .col-sm-3 
                a href="#" class="btn btn-default example-loader-esi-1" data-mass="#{ render 'utilities/ei_spectra_example' }" data-update="identify_query_neutral_mass" data-example-low="#{ render 'utilities/ei_spectra_example' }"  data-update-low="low_spectra" data-example-medium="#{ render 'utilities/medium_spectra_example_3' }" data-update-medium="medium_spectra" data-example-high="#{ render 'utilities/high_spectra_example_3' }" data-update-high="high_spectra" Load Example #1
              .col-sm-3 
                a href="#" class="btn btn-default example-loader-esi-2" data-mass="#{ render 'utilities/neutral_mass_example_4' }" data-update="identify_query_neutral_mass" data-example-low="#{ render 'utilities/low_spectra_example_4' }" data-update-low="low_spectra" data-example-medium="#{ render 'utilities/medium_spectra_example_4' }" data-update-medium="medium_spectra" data-example-high="#{ render 'utilities/high_spectra_example_4' }" data-update-high="high_spectra" Load Example #2
          #find-nl-examples 
            .row
              .col-sm-offset-1.col-sm-1
                = label_tag nil, "Examples", class: "control-label"
              .col-sm-3 
                a href="#" class="btn btn-default example-loader-nl-esi-1" data-mass="#{ render 'utilities/neutral-loss-examples/neutral_mass_example_1' }" data-update="identify_query_neutral_mass" data-example-low="#{ render 'utilities/neutral-loss-examples/low_spectra_example_1' }"  data-update-low="low_spectra" data-example-medium="#{ render 'utilities/neutral-loss-examples/medium_spectra_example_1' }" data-update-medium="medium_spectra" data-example-high="#{ render 'utilities/neutral-loss-examples/high_spectra_example_1' }" data-update-high="high_spectra" Load ESI Example #1
              .col-sm-3 
                a href="#" class="btn btn-default example-loader-nl-esi-2" data-mass="#{ render 'utilities/neutral-loss-examples/neutral_mass_example_2' }" data-update="identify_query_neutral_mass" data-example-low="#{ render 'utilities/neutral-loss-examples/low_spectra_example_2' }" data-update-low="low_spectra" data-example-medium="#{ render 'utilities/neutral-loss-examples/medium_spectra_example_2' }" data-update-medium="medium_spectra" data-example-high="#{ render 'utilities/neutral-loss-examples/high_spectra_example_2' }" data-update-high="high_spectra" Load ESI Example #2
            / .col-sm-3
              / a href="#" class="btn btn-default example-loader-ei" data-mass="#{ render 'utilities/ei_neutral_mass_example' }" data-update="identify_query_neutral_mass" data-example-low="#{ render 'utilities/ei_spectra_example' }" data-update-low="low_spectra" Load EI Example
          .col-sm-offset-2.col-sm-10
            = f.label :database, "Select database(s)", class: "control-label"
            p.form-control-static.help-text
              | You can find candidates for experimental and/or predicted spectra.
            
          .col-sm-offset-2.col-sm-3
            .row-sm-4
              /= f.label :database, "Select database(s)", class: "control-label"
              = f.label :predicted_database, "Predicted Spectra", class: "control-label"
              - IdentifyQuery::EI_DATABASES.merge(IdentifyQuery::EI_DATABASES).to_h.each do |human_name, value|
                div class=(["database-checkbox", value.parameterize, ("ei" if IdentifyQuery::EI_DATABASES.values.include? value), ("esi" if IdentifyQuery::ESI_PREDICTED_DATABASES.values.include? value)].compact.join(' '))
                  = f.check_box :predicted_database, { multiple: true, checked: @identify_query.predicted_database.include?(value) }, value, nil
                  = f.label "database-" + value.parameterize, human_name, class: "control-label" do
                    - database_description = DatabaseDescription.find_by(name: human_name)
                    - description = database_description.get_description if !database_description.nil?
                    | #{human_name} 
                    i [title="#{description}" data-toggle="tooltip" data-placement="bottom" style="position: relative;display: inline-block;"] = glyphicon('question-sign')      
          / .col-sm-3
          /   .row-sm-3
          /     = f.label :experimental_database, "Experimental Spectra", class: "control-label"
          /     - IdentifyQuery::EI_EXPERIMENTAL_DATABASES.merge(IdentifyQuery::EI_DATABASES).to_h.each do |human_name, value|
          /       div class=(["database-checkbox", value.parameterize, ("ei" if IdentifyQuery::EI_DATABASES.values.include? value), ("esi" if IdentifyQuery::ESI_EXPERIMENTAL_DATABASES.values.include? value)].compact.join(' '))
          /         = f.check_box :experimental_database, { multiple: true, checked: @identify_query.experimental_database.include?(value) }, value, nil
          /         = f.label "database-" + value.parameterize, class: "control-label" do
          /           - database_description = DatabaseDescription.find_by(name: human_name)
          /           - description = database_description.get_description if !database_description.nil?
          /           | #{human_name} 
          /           i [title="#{description}" data-toggle="tooltip" data-placement="bottom" style="position: relative;display: inline-block;"] = glyphicon('question-sign')
          /           / i = glyphicon('question-sign')
                    
          /     / = f.select :database, options_for_select((@identify_query.spectra_type == "EI" ? IdentifyQuery::EI_DATABASES : IdentifyQuery::ESI_DATABASES ), selected: @identify_query.database || "HMDB" ), {}, {class: "form-control", 'data-esi-databases' => IdentifyQuery::ESI_DATABASES.to_json, 'data-ei-databases' => IdentifyQuery::EI_DATABASES.values.to_json }

          .col-sm-2
            .row-sm-3
              = label_tag  :adduct_search_spectra_type, "Spectra Type", class: "control-label"
              .row-sm-3
              / = select_tag :adduct_search_spectra_type, options_for_select({ESI: "ESI"}, @identify_query.spectra_type), {class: "form-control small"}
              = select_tag :adduct_search_spectra_type, options_for_select({EI: "EI" }, @identify_query.spectra_type), {class: "form-control faux-inline"}
            .row-sm-3
              = label_tag  :adduct_search_ion_mode, "Column Type", class: "control-label"
              .row-sm-3
              = select_tag :adduct_search_ion_mode, options_for_select({Negative: "negative", Positive: "positive"}, @identify_query.ion_mode), {class: "form-control faux-inline"}
            .row-sm-3
              - selected_adducts = @identify_query.ion_mode == "positive" ? @positive_adducts : @negative_adducts
              = f.label :adduct_type, "Derivative Type", class: "control-label"
              .row-sm-3
              = select_tag :adduct_type, options_for_select({None: "none", TMS: "tms", TBDMS: "tbdms", 'TMS & TBDMS': 'both'}, @identify_query.adduct_type), {class: " form-control faux-inline", 'data-positive-adducts' => @positive_adducts.to_json, 'data-negative-adducts' => @negative_adducts.to_json}
          .col-sm-2
            .row-sm-3
              = f.label :parent_ion_mass, "Retention Index", class: "control-label block-label"
              .row-sm-3
                = f.text_field :parent_ion_mass, class: "form-control faux-inline"
                / = select_tag :parent_ion_mass_type, options_for_select(IdentifyQuery::MASS_TYPES, selected: @identify_query.parent_ion_mass_type), { class: "form-control faux-inline" }
            #candidates-find
              .row-sm-3
                = f.label :candidate_mass_tol, "Retention Index Tolerance", class: "control-label block-label"
                .row-sm-3
                  = text_field_tag :candidate_mass_tol, nil, { value: '2%', class: "form-control small" }
                  / = text_field_tag :candidate_mass_tol, nil, { value: @candidate_mass_tol, class: "form-control small" }
                  / = select_tag :candidate_mass_tol_units, options_for_select(['ppm', 'Da']), { value: @candidate_mass_tol_units, class: "form-control small" }
              / .row-sm-3
              / = f.label :candidate_limit, "Candidate Limit", class: "control-label"
              / .row-sm-3
              / = f.text_field :candidate_limit, value: @identify_query.candidate_limit.blank? ? 100 : @identify_query.candidate_limit, class: "form-control small"
        #neutral-loss class="tab-pane #{ 'active' if params[:submit_or_find_or_nl] == 'neutral-loss' }" 
          .col-sm-offset-2.col-sm-10
            p.form-control-static.help-text
              |  When calculating the neutral loss, the parent ion mass will be used to calculate the 

          .row
            .col-sm-offset-1.col-sm-1
              = label_tag nil, "Examples", class: "control-label"
            .col-sm-3 
              a href="#" class="btn btn-default example-loader-nl-esi-1" data-mass="#{ render 'utilities/neutral_mass_example_3' }" data-update="identify_query_neutral_mass" data-example-low="#{ render 'utilities/low_spectra_example_3' }"  data-update-low="low_spectra" data-example-medium="#{ render 'utilities/medium_spectra_example_3' }" data-update-medium="medium_spectra" data-example-high="#{ render 'utilities/high_spectra_example_3' }" data-update-high="high_spectra" Load ESI Example #1
            /.col-sm-3 
              a href="#" class="btn btn-default example-loader-esi-2" data-mass="#{ render 'utilities/neutral_mass_example_4' }" data-update="identify_query_neutral_mass" data-example-low="#{ render 'utilities/low_spectra_example_4' }" data-update-low="low_spectra" data-example-medium="#{ render 'utilities/medium_spectra_example_4' }" data-update-medium="medium_spectra" data-example-high="#{ render 'utilities/high_spectra_example_4' }" data-update-high="high_spectra" Load ESI Example #2
          
        /   .col-sm-offset-2.col-sm-10
        /     = f.label :database, "Select database(s)", class: "control-label"
        /     p.form-control-static.help-text
        /       | You can find candidates for experimental and/or predicted spectra.
            
        /   .col-sm-offset-2.col-sm-3
        /     .row-sm-3
        /       = f.label :experimental_database, "Experimental Spectra", class: "control-label"
        /       - IdentifyQuery::ESI_EXPERIMENTAL_DATABASES.merge(IdentifyQuery::EI_DATABASES).to_h.each do |human_name, value|
        /         div class=(["database-checkbox", value.parameterize, ("ei" if IdentifyQuery::EI_DATABASES.values.include? value), ("esi" if IdentifyQuery::ESI_EXPERIMENTAL_DATABASES.values.include? value)].compact.join(' '))
        /           = f.check_box :experimental_database, { multiple: true, checked: @identify_query.experimental_database.include?(value) }, value, nil
        /           = f.label "database-" + value.parameterize, class: "control-label" do
        /             | #{human_name} 
        /             i = glyphicon('question-sign')
        /       / = f.select :database, options_for_select((@identify_query.spectra_type == "EI" ? IdentifyQuery::EI_DATABASES : IdentifyQuery::ESI_DATABASES ), selected: @identify_query.database || "HMDB" ), {}, {class: "form-control", 'data-esi-databases' => IdentifyQuery::ESI_DATABASES.to_json, 'data-ei-databases' => IdentifyQuery::EI_DATABASES.values.to_json }
        /   .col-sm-3
        /     .row-sm-4
        /       /= f.label :database, "Select database(s)", class: "control-label"
        /       = f.label :predicted_database, "Predicted Spectra", class: "control-label"
        /       - IdentifyQuery::ESI_PREDICTED_DATABASES.merge(IdentifyQuery::EI_DATABASES).to_h.each do |human_name, value|
        /         div class=(["database-checkbox", value.parameterize, ("ei" if IdentifyQuery::EI_DATABASES.values.include? value), ("esi" if IdentifyQuery::ESI_PREDICTED_DATABASES.values.include? value)].compact.join(' '))
        /           = f.check_box :predicted_database, { multiple: true, checked: @identify_query.predicted_database.include?(value) }, value, nil
        /           = f.label "database-" + value.parameterize, human_name, class: "control-label" do
        /             | #{human_name} 
        /             i = glyphicon('question-sign')
        /   .col-sm-2
        /     .row-sm-3
        /       = label_tag  :adduct_search_spectra_type, "Spectra Type", class: "control-label"
        /       .row-sm-3
        /       = select_tag :adduct_search_spectra_type, options_for_select({ESI: "ESI"}, @identify_query.spectra_type), {class: "form-control small"}
        /       / = select_tag :adduct_search_spectra_type, options_for_select({ESI: "ESI", EI: "EI" }, @identify_query.spectra_type), {class: "form-control faux-inline"}
        /     .row-sm-3
        /       = label_tag  :adduct_search_ion_mode, "Ion Mode", class: "control-label"
        /       .row-sm-3
        /       = select_tag :adduct_search_ion_mode, options_for_select({Negative: "negative", Positive: "positive"}, @identify_query.ion_mode), {class: "form-control faux-inline"}
        /     .row-sm-3
        /       - selected_adducts = @identify_query.ion_mode == "positive" ? @positive_adducts : @negative_adducts
        /       = f.label :adduct_type, "Adduct Type", class: "control-label"
        /       .row-sm-3
        /       = f.select :adduct_type, selected_adducts, {selected: @identify_query.adduct_type}, {class: " form-control faux-inline", 'data-positive-adducts' => @positive_adducts.to_json, 'data-negative-adducts' => @negative_adducts.to_json}
        /   .col-sm-2
        /     .row-sm-3
        /       = f.label :parent_ion_mass, "Parent Ion Mass", class: "control-label block-label"
        /       .row-sm-3
        /         = f.text_field :parent_ion_mass, class: "form-control faux-inline"
        /         = select_tag :parent_ion_mass_type, options_for_select(IdentifyQuery::MASS_TYPES, selected: @identify_query.parent_ion_mass_type), { class: "form-control faux-inline" }    

    .form-group
      =f.label :spectra_input, "Spectra", class: "col-sm-2 control-label"
      / = hidden_field_tag :text_or_file, params[:text_or_file]
      / .col-sm-10
      /   ul.nav.nav-pills.nav-pills-custom
      /     li id="spectra-text-toggle" class="#{ 'active' if params[:text_or_file] == 'text' }"
      /       a[href="#spectra-text" data-toggle="tab"] Input Text
      /     li.text-only OR
      /     li id="spectra-file-toggle" class="#{ 'active' if params[:text_or_file] == 'file' }"
      /       a[href="#spectra-file" data-toggle="tab"] Load a File
      .tab-content
        #spectra-text class="tab-pane #{'active' if params[:text_or_file] == 'text' }"
          .col-sm-offset-2.col-sm-10
            p.form-control-static.help-text The spectra should be represented as a list of peaks with the format 'mass intensity' on each line, and can be entered directly into the corresponding energy level boxes below. Only centroided spectrum can be entered. Multiple energy levels are optional; only one is required. 
          .col-sm-offset-2.col-sm-10
            = label_tag nil, "Input Spectra Text", class: "control-label"
          .col-sm-offset-2.col-sm-3
            = label_tag :low_spectra, "Low Energy", class: "control-label spectra-label"
            = text_area_tag :low_spectra, @low_spectra, class: "form-control", rows: 8, wrap: "off"
          / .col-sm-3
          /   = label_tag :medium_spectra, "Medium Energy", class: "control-label spectra-label"
          /   = text_area_tag :medium_spectra, @medium_spectra, class: "form-control", rows: 8, wrap: "off"
          / .col-sm-3
          /   = label_tag :high_spectra, "High Energy", class: "control-label spectra-label"
          /   = text_area_tag :high_spectra, @high_spectra, class: "form-control", rows: 8, wrap: "off"
        #spectra-file class="tab-pane #{ 'active' if params[:text_or_file] == 'file' }"
          .col-sm-offset-2.col-sm-10
            p.form-control-static.help-text
              | The spectra should be represented as a list of peaks with the format 'mass intensity' on each line. For ESI spectra, 'low','medium', and 'high' or 'energy0', 'energy1', and 'energy2' header lines should begin spectra of different energy levels (in that order) and multiple energy levels are optional (only one is required). EI spectra only need to have one energy level. Spectra may also be in .msp file format, in which case energy levels for ESI spectra should be specified in the "Comment: " field (EI spectra do not need a specified energy level). A corresponding spectra ID must be selected for .msp spectra. See an 
              = link_to "example peak list file", "examples/example_spec.txt"
              |  or an 
              = link_to "example .msp file", "examples/example_spec.msp"
              | .
          / .col-sm-offset-2.col-sm-10
          /   = f.label :input_file, "Load Spectra File", class: "control-label"
          /   = f.file_field :input_file
          .col-sm-offset-2.col-sm-3.spectra-id-col
            = f.label :spectra_id, "Spectra ID", class: "control-label"
            p.form-control-static.help-text style="display: inline-block" * required for .msp files only
            = f.select :spectra_id, {}, {}, {class: "form-control"}
    .form-group
      / = f.label :scoring_function, "Scoring Function", class: "col-sm-2 control-label"
      / .col-sm-2
      /   - selected_functions = @identify_query.spectra_type == "EI" ? @ei_functions : @esi_functions
      /   = f.select :scoring_function, selected_functions, {}, { class: "form-control long", "data-esi-functions": @esi_functions.to_json, "data-ei-functions": @ei_functions.to_json}
    .form-group
      = f.label :num_results, "Number of Results", class: "col-sm-2 control-label"
      .col-sm-10
        p.form-control-static.help-text The number of results to return. Leave blank to return all results. 
      .col-sm-2.col-sm-offset-2
        = f.text_field :num_results, value: @identify_query.num_results.blank? ? 10 : @identify_query.num_results, class: "form-control small"
    / .form-group
    /   =f.label :mass_tol, "Mass Tolerance", class: "col-sm-2 control-label"
    /   .col-sm-10
    /     p.form-control-static.help-text The mass tolerance to use when matching peaks within the spectrum comparison. 
      / .col-sm-2.col-sm-offset-2
      /   = f.label :ppm_mass_tol, "In ppm", class: "control-label"
      /   = f.text_field :ppm_mass_tol, value: @identify_query.ppm_mass_tol.blank? ? 10.0 : @identify_query.ppm_mass_tol, class: "form-control small"
      / .col-sm-1
      /   .center-or
      /     | OR 
      / .col-sm-2
      /   = f.label :abs_mass_tol, "In daltons", class: "control-label"
      /   = f.text_field :abs_mass_tol, value: @identify_query.abs_mass_tol.blank? ? 0.01 : @identify_query.abs_mass_tol, class: "form-control small"
      .col-sm-3.col-sm-offset-2
        = text_field_tag :mass_tol, nil, { value: @mass_tol, class: "form-control small" }
        = select_tag :mass_tol_units, options_for_select(['ppm', 'Da']), { value: @mass_tol_units, class: "form-control small" }
    / .form-group
    /   = f.label :threshold, "Probability Threshold", class: "col-sm-2 control-label"
    /   .col-sm-2
    /     = f.text_field :threshold, value: @identify_query.threshold.blank? ? 0.001 : @identify_query.threshold, class: "form-control small"
    = f.hidden_field :threshold, value: 0.001
    /   p.form-control-static
    /     | The probability below which to prune unlikely fragmentations in the spectrum prediction.
    .form-group.end-row
      #form-buttons
        .col-sm-offset-2.col-sm-10
          = link_to 'Reset', identify_path, class: 'btn btn-primary btn-reset'
          = f.submit "Submit", class: "btn btn-primary btn-submit", id: "query-submit"
      #form-submit
        .col-sm-offset-2.col-sm-2
          #small-progress
            progress id="progressbar" value="100" max="100"
        .col-sm-8
          span#submitted-text Please wait while your input is validated (this may take a few seconds)
/ .end-note
/   | If you wish to run multiple jobs, search more candidates/larger candidate molecules, or customize the computation parameters, you can freely download the 
/   = link_to "docker image", "https://hub.docker.com/repository/docker/wishartlab/cfmid",target: "_blank"
/   | .
javascript:
  $('[data-toggle="tooltip"]').tooltip({container:'body', trigger: 'hover', placement:"right"});  
  $('#find-nl-examples').hide();
  $('#find-nl-description').hide();
  $('#submit-candidates-toggle a[data-toggle="tab"]').on('shown.bs.tab', function (e) { $('#submit_or_find_or_nl').val('submit'); }); 
  $('#find-candidates-toggle a[data-toggle="tab"]').on('shown.bs.tab', function (e) { 
    $('#submit_or_find_or_nl').val('find'); 
    $('#candidates-find').show();
    $('#find-candidates-examples').show();
    $('#find-nl-examples').hide();
    $('#find-nl-description').hide();
    $('#find-candidates-description').show();

  }); 
  $('#neutral-loss-toggle a[data-toggle="tab"]').on('click.bs.tab', function (e) { 
    $('#submit_or_find_or_nl').val('neutral-loss'); 
    $('#candidates-find').hide();
    $('#find-candidates-examples').hide();
    $('#find-nl-examples').show();
    $('#find-nl-description').show();
    $('#find-candidates-description').hide();
  }); 
  $('#spectra-text-toggle a[data-toggle="tab"]').on('shown.bs.tab', function (e) { $('#text_or_file').val('text'); }); 
  $('#spectra-file-toggle a[data-toggle="tab"]').on('shown.bs.tab', function (e) { $('#text_or_file').val('file'); });
