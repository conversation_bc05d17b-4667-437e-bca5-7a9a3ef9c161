= render partial: "identify/identify_input"
- if !@query.runtime.blank?
  #results.runtime-box
    h4
      | Runtime Info: 
      button.btn.btn-xs.btn-runtime.hide-runtime[href="#"]
        | Show 
        span.glyphicon.glyphicon-collapse-down
    .collapse.runtime-collapse = simple_format(@query.runtime)
- if @query.parsed_output == "Successful"
  #results.results-box
    h4
      | Results: 
      = link_to 'Download <span class="glyphicon glyphicon-save"></span>'.html_safe, @query.output_file.url, class: 'btn btn-xs btn-download'
    .results-help
      p Input spectra are shown below in blue. If a database was queried, candidate spectra are overlayed on top for comparison. The top ranking candidate spectra is shown by default; to compare other database candidates use the "Compare" buttons on the list of ranked candidate compounds that follow the spectra.
      p
        | The candidate spectra currently shown (in red) is 
        span#comparison-compound
        | .
    - if @query.spectra_type == "ESI"
      h4.chart-title
        | Low Energy Input MsMs Spectrum (10V), 
        = @query.ion_mode == "positive" ? "M+H" : "M-H"
      #energy0
        - if !@spectra_data.has_key?("energy0")
          span[style="font-style: italic; margin-left: 1em;"] No Spectra
      h4.chart-title
        | Medium Energy Input MsMs Spectrum (20V), 
        = @query.ion_mode == "positive" ? "M+H" : "M-H"
      #energy1
        - if !@spectra_data.has_key?("energy1")
          span[style="font-style: italic; margin-left: 1em;"] No Spectra
      h4.chart-title
        | High Energy Input MsMs Spectrum (40V), 
        = @query.ion_mode == "positive" ? "M+H" : "M-H"
      #energy2
        - if !@spectra_data.has_key?("energy2")
          span[style="font-style: italic; margin-left: 1em;"] No Spectra
    - else
      h4.chart-title
        | Predicted MsMs Spectrum (70eV), 
        = @query.adduct_type
      #energy0
        - if !@spectra_data.has_key?("energy0")
          span[style="font-style: italic; margin-left: 1em;"] No MsMs Spectrum
    = render partial: "identify/identify_output", locals: {query: @query}

  - @spectra_data.each_pair do |energy_level,data|
    javascript:
      mz_plot("##{ energy_level }", #{ data.to_json.html_safe }, "#{ @query.secret_id }", { mirror: true });

- if !@query.error.blank?
  #results.error-box
    h4 Error:
    = simple_format(@query.error)
#fragment-modal.modal.fade.bs-example-modal-sm[tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true"]
