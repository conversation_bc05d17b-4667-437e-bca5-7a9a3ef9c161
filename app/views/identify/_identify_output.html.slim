h4 Candidate Rankings
- query_results = query.get_results
- if query.score_with_metadata?
  .predicted-class
    span.bold Predicted Class(es): 
    span.score = query_results[:predicted_class].join(", ")
table.identify-display-table
  thead
    - if query.score_with_metadata?
      tr
        th.no-sort rowspan="2" Rank
        th.no-sort colspan="4" Score
        th.no-sort rowspan="2" Structure
        th.no-sort rowspan="2"
          | ID
          hr
          | Name
        th.no-sort rowspan="2"
          | Chemical Formula
          hr
          | Mass
        th.no-sort rowspan="2" Classification
        th.no-sort rowspan="2" Compare
      tr
        th Overall
        th Similarity
        th Reference
        th Class
    - elsif query.database.present?
      tr
        th Rank
        th Score
        th.no-sort Structure
        th.no-sort
          | ID
          hr
          | Name
        th.no-sort
          | Chemical Formula
          hr
          | Mass
        th.no-sort InChI/SMILES
        th.no-sort Compare
    - else
      tr
        th Rank
        th Score
        th.no-sort Structure
        th.no-sort ID
        th.no-sort Chemical Formula
        th.no-sort InChI/SMILES
        th.no-sort Compare
  tbody
    - first = true
    - query_results[:results].each_with_index do |result, i|
      tr
        td.rank = i + 1
        - if query.score_with_metadata?
          td.score = result[:combined_score]
          td.score = result[:cfm_id_score]
          td.score = result[:reference_score]
          td.score = result[:ancestor_score]
          td.image = get_image(result[:structure], "frag-structure img-responsive", "Structure").html_safe
          td
            = link_to_database(result[:database], result[:database_id]).html_safe
            - result[:related].each do |ref|
              br
              = link_to_database(ref[:database], ref[:database_id]).html_safe
            hr
            / span class=("type-tag "  + find_in_database(database_id, "spectra_type", database).try(:downcase)) = find_in_database(database_id, "spectra_type", database)
            = display(find_in_database(result[:database_id], "full_name", result[:database]))
          td
            = get_formula(result[:structure])
            hr
            = display(find_in_database(result[:database_id], "neutral_mass", result[:database]))
          td
            span.bold Direct Parent
            .classification = display(result[:direct_parent])
            span.bold Alternative Parents
            .classification = display_list(result[:alternative_parents])
          td.compare
            a href="#" class="btn btn-xs btn-download #{ first ? "btn-error" : "btn-compare" }" data-compound="#{ result[:id] }" data-database="#{ result[:database] }" data-query_id="#{ query.secret_id }"
              = first ? "Current" : "Compare"
          - if first
            - first = false
            javascript:
              window.compareCandidate = #{ loadCandidate(query.secret_id, result[:id], result[:database]).to_json.html_safe };
        - elsif query.database.present?
          td.score = result[:cfm_id_score]
          td.image = get_image(result[:structure], "frag-structure img-responsive", "Structure").html_safe
          td
            = link_to_database(result[:database], result[:database_id]).html_safe
            - result[:related].each do |ref|
              br
              = link_to_database(ref[:database], ref[:database_id]).html_safe
            hr
            / span class=("type-tag "  + find_in_database(database_id, "spectra_type", database).try(:downcase)) = find_in_database(database_id, "spectra_type", database)
            = display(find_in_database(result[:database_id], "full_name", result[:database]))
          td
            = get_formula(result[:structure])
            hr
            = display(find_in_database(result[:database_id], "neutral_mass", result[:database]))
          td = result[:structure]
          td.compare
            a href="#" class="btn btn-xs btn-download #{ first ? "btn-error" : "btn-compare" }" data-compound="#{ result[:id] }" data-database="#{ result[:database] }" data-query_id="#{ query.secret_id }"
              = first ? "Current" : "Compare"
          - if first
            - first = false
            javascript:
              window.compareCandidate = #{ loadCandidate(query.secret_id, result[:id], result[:database]).to_json.html_safe };
        - else
          td.score = result[:cfm_id_score]
          td.image = get_image(result[:structure], "frag-structure img-responsive", "Structure").html_safe
          td.id = result[:id]
          td = get_formula(result[:structure])
          td = result[:structure]
          td.compare
            a href="#" class="btn btn-xs btn-download #{ first ? "btn-error" : "btn-compare" }" data-compound="#{ result[:id] }" data-query_id="#{ query.secret_id }"
              = first ? "Current" : "Compare"
          - if first
            - first = false
            javascript:
              window.compareCandidate = #{ loadCandidate(query.secret_id, result[:id], "").to_json.html_safe };
