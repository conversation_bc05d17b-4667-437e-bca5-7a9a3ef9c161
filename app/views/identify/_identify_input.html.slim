.input-box
  h4 Compound Identification Input Parameters: 
  table.input-list
    tr
      th Spectrum File
      td
        = @query.input_file_file_name
        = link_to 'Download <span class="glyphicon glyphicon-save"></span>'.html_safe, @query.input_file.url, class: 'btn btn-xs btn-reset btn-padded'
    tr
      th Candidates File
      td
        = @query.candidates_file_file_name
        = link_to 'Download <span class="glyphicon glyphicon-save"></span>'.html_safe, @query.candidates_file.url, class: 'btn btn-xs btn-reset btn-padded'
    - if !@query.database.present?
      tr
        th Predicted Candidate Spectra File
        td
          = @query.candidate_spectra_file_file_name
          = link_to 'Download <span class="glyphicon glyphicon-save"></span>'.html_safe, @query.candidate_spectra_file.url, class: 'btn btn-xs btn-reset btn-padded'
    - if @query.database.present?
      tr
        th Database(s)
        td = @query.database.map { |d| d.gsub("_Deriv", " Derivatized") }.join(", ")
    - if @query.database.present?
      tr
        th Parent Ion Mass
        td
          = @query.parent_ion_mass_string
    - if @query.database.present?
      tr
        th Adduct Type
        td = @query.adduct_type
    - if @query.database.present?
      tr
        th Candidate Mass Tolerance 
        - if @query.candidate_ppm_mass_tol > -1
          td
            = @query.candidate_ppm_mass_tol
            |  ppm
        - elsif @query.candidate_abs_mass_tol > -1
          td
            = @query.candidate_abs_mass_tol
            |  Da
    - if @query.candidate_limit.present?
      tr
        th Candidate Limit
        td = @query.candidate_limit
    tr
      th Spectra Type
      td = @query.spectra_type
    tr
      th Ion Mode
      td = @query.ion_mode.try(:capitalize)
    tr
      th Number of Results
      td = @query.num_results
    tr
      th Mass Tolerance 
      - if @query.ppm_mass_tol > -1
        td
          = @query.ppm_mass_tol
          |  ppm
      - elsif @query.abs_mass_tol > -1
        td
          = @query.abs_mass_tol
          |  Da
    tr
      th Probability Threshold
      td = @query.threshold
    tr
      th Scoring Function
      td = @query.scoring_function
    tr
      th Status
      td
        span[class="badge badge-#{@query.status.downcase.gsub(/ /, "-")}"] = @query.status

