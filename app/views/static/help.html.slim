.page-header
  h3 = title("Help")
ul#help-tabs.nav.nav-tabs
  li.active
    a[href="#predict" data-toggle="tab"] Spectra Prediction
  li
    a[href="#assign" data-toggle="tab"] Peak Assignment
  li
    a[href="#identify" data-toggle="tab"] Compound Identification
  li
    a[href="#browser_compliance" data-toggle="tab"] Browser Compliance
  li
    a[href="#hcd_cid_spectra" data-toggle="tab"] HCD vs CID Spectra
  li
    a[href="#performance" data-toggle="tab"] Performance
  li
    a[href="#docker_image" data-toggle="tab"] Docker Image
.tab-content
  #predict.tab-pane.active
    h4.help-header Spectra Prediction
    p The Spectra Prediction utility predicts the spectra for a given input molecule.
    p

    h4 Submit Prediction Query

    .row
      .col-lg-12
        = image_tag "Spectra_prediction_input.jpeg", alt: "Figure 1", width: 10000
    
    h5 Submit Prediction Query Steps:
    p
      ol 
        li Enter InChI or SMILES
        li Select desired spectra type, ion mode and adduct type. 
        li Submit prediction job to server
    p Note: InChI strings need to start with "InChI=" and input molecule should not has any charges. 
    
    h5 Defitions of Inputs:
    p
      span.input-highlight InChI/SMILES:
      |  The molecule must be represented in either 
      a[href="http://en.wikipedia.org/wiki/International_Chemical_Identifier" target="_blank"] InChI format
      |  or 
      a[href="http://en.wikipedia.org/wiki/Simplified_molecular-input_line-entry_system" target="_blank"] SMILES format
      | . InChI strings need to start with "InChI=" and are not expected to have any charge - an additional H+ will be added. InChI strings need to contain AT LEAST the main layer with its chemical formula and atom connections sublayers for proper computation. 
    .example-indent
      table
        tr
          td Examples:
          td
            | CN1CCC[C@H]1c2cccnc2
        tr
          td
          td
            | InChI=1S/C10H14N2/c1-12-7-3-5-10(12)9-4-2-6-11-8-9/h2,4,6,8,10H,3,5,7H2,1H3
    p
      span.input-highlight Spectra Type:
      |  The type of spectra, either ESI (Electrospray Ionization) or EI (Electron Ionization/Impact).

    p
      span.input-highlight Ion Mode:
      |  Indicates whether the precursor ion has a positive or negative adduct.

    p
      span.input-highlight Adduct:
      |  Indicates the specific adduct used.
    
    h4 Spectra Prediction Results

    .row
      .col-lg-12
        = image_tag "Spectra_prediction_result_part1.jpeg", alt: "Figure 1", width: 10000
        p 
          b Results Part 1: 
          | Spectra are computed for low (10 eV), medium (20 eV) and high (40 eV) collision energy levels and are represented by a list of 'mass intensity' pairs, each corresponding to a peak in the spectra. Each peak in predicted spectra has a m/z value, an intensity value, and one or more possible fragment ion structure(s).
      
      .col-lg-12
        = image_tag "Spectra_prediction_result_part2.jpeg", alt: "Figure 1", width: 10000
        p 
          b Results Part 2: 
          | A detailed list of all predicted fragment can be found down the page. Each fragment can be linked back to predicted spectra by its fragement id.

  
  #assign.tab-pane
    h4.help-header Peak Assignment
    p The Peak Assignment utility annotates the peaks in a provided set of spectra given a known molecule. The complete list of feasible fragments is computed, then the most likely fragments for each spectrum peak are determined using a pre-trained model. 
    
    h4 Submit Peak Assigment Query:

    .row
      .col-lg-12
        = image_tag "Spectra_assignment_input.jpeg", alt: "Figure 1", width: 10000
    
    h5 Peak Assignment Query Steps:
    p
      ol 
        li Enter InChI or SMILES
        li Select desired spectra type, ion mode and adduct type. 
        li Enter spectra data, the spectra should be represented as a list of peaks with the format 'm/z intensity' on each line. Multiple energy levels are optional; only one is required.
        li Select mass tolerance to use when matching peaks within the spectrum comparison.
        li Submit job to server
    
    h5 Defitions of Inputs:
    p
      span.input-highlight InChI/SMILES:
      |  The molecule must be represented in either 
      a[href="http://en.wikipedia.org/wiki/International_Chemical_Identifier" target="_blank"] InChI format
      |  or 
      a[href="http://en.wikipedia.org/wiki/Simplified_molecular-input_line-entry_system" target="_blank"] SMILES format
      | . InChI strings need to start with "InChI=" and are not expected to have any charge - an additional H+ will be added. InChI strings need to contain AT LEAST the main layer with its chemical formula and atom connections sublayers for proper computation. 
    .example-indent
      table
        tr
          td.example-cell[rowspan="2"] Examples:
          td 
            | Oc1ccc(CC(NC(=O)C(N)CO)C(=O)NC(CC(O)=O)C(O)=O)cc1
        tr
          td
            | InChI=1S/C16H21N3O8/c17-10(7-20)14(24)18-11(5-8-1-3-9(21)4-2-8)15(25)19-12(16(26)27)6-13(22)23/h1-4,10-12,20-21H,5-7,17H2,(H,18,24)(H,19,25)(H,22,23)(H,26,27)
    p
      span.input-highlight Spectra:
      |  The spectra should be represented as a list of peaks with the format 'mass intensity' on each line. For ESI spectra, 'low','medium', and 'high' or 'energy0', 'energy1', and 'energy2' header lines should begin spectra of different energy levels (in that order) and multiple energy levels are optional (only one is required). EI spectra only need to have one energy level. Spectra may also be in .msp file format, in which case energy levels for ESI spectra should be specified in the "Comment: " field (EI spectra do not need a specified energy level). A corresponding spectra ID must be selected for .msp spectra. .msp files must have an "ID" and "Num peaks" attributes for each spectra.
    .example-indent.inline-example
      table.spectra-table
        tr
          td.example-cell[rowspan="36"] Example peak list format:
          td[colspan="2"] low
        tr
          td 87.054687
          td 7.567280
        tr
          td 105.069174
          td 1.791050
        tr
          td 136.07616
          td 13.081500
        tr
          td 160.076289
          td 2.225420
        tr
          td 178.084616
          td 5.319120
        tr
          td 223.106608
          td 100.000000
        tr
          td 251.10173
          td 40.722900
        tr
          td 297.107567
          td 3.945980
        tr
          td 384.140384
          td 11.216900
        tr
          td[colspan="2"] medium
        tr
          td 60.044545
          td 2.476820
        tr
          td 87.056965
          td 9.632580
        tr
          td 119.046086
          td 2.367850
        tr
          td 135.066335
          td 1.865000
        tr
          td 136.077192
          td 46.373600
        tr
          td 160.074417
          td 6.652730
        tr
          td 178.08705
          td 20.078100
        tr
          td 223.109344
          td 100.000000
        tr
          td 251.108668
          td 3.127750
        tr
          td 297.113687
          td 1.892360
        tr
          td[colspan="2"] high
        tr
          td 42.033909
          td 3.047230
        tr
          td 60.043746
          td 26.520300
        tr
          td 70.027268
          td 3.162400
        tr
          td 87.056272
          td 18.342000
        tr
          td 91.054494
          td 23.516200
        tr
          td 119.04828
          td 15.711000
        tr
          td 121.063402
          td 7.273900
        tr
          td 133.06551
          td 5.039960
        tr
          td 135.066238
          td 3.626030
        tr
          td 136.074907
          td 100.000000
        tr
          td 160.074409
          td 26.458000
        tr
          td 178.085454
          td 12.211700

    .example-indent.inline-example
      table.spectra-table
        tr
          td.example-cell[rowspan="36"] Example .msp format:
        tr
          td[colspan="2"] Name: Diazirine
        tr
          td[colspan="2"] NISTNO: 305841
        tr
          td[colspan="2"] ID: ID_3
        tr
          td[colspan="2"] Num peaks: 12
        tr
          td[colspan="2"] Comment: energy0
        tr
          td 12
          td 108.00
        tr
          td 13
          td 228.99
        tr
          td 14
          td 999.00
        tr
          td 15
          td 21.98
        tr
          td 26
          td 17.98
        tr
          td 27
          td 58.05
        tr
          td 28
          td 178.04
        tr
          td 29
          td 22.98
        tr
          td 40
          td 17.98
        tr
          td 41
          td 108.00
        tr
          td 42
          td 431.01
        tr
          td 43
          td 7.99
        tr
          td[colspan="2"]
        tr
          td[colspan="2"] Name: Methane, diazo-
        tr
          td[colspan="2"] NISTNO: 57
        tr
          td[colspan="2"] ID: ID_4
        tr
          td[colspan="2"] Num peaks: 12
        tr
          td[colspan="2"] Comment: energy1
        tr
          td 12
          td 110.10
        tr
          td 13
          td 220.30
        tr
          td 14
          td 999.00
        tr
          td 15
          td 25.18
        tr
          td 26
          td 12.59
        tr
          td 27
          td 58.25
        tr
          td 28
          td 179.34
        tr
          td 29
          td 20.48
        tr
          td 40
          td 21.98
        tr
          td 41
          td 110.10
        tr
          td 42
          td 424.82
        tr
          td 43
          td 10.99

    p
      span.input-highlight Spectra Type:
      |  The type of spectra, either ESI (Electrospray Ionization) or EI (Electron Ionization/Impact).

    p
      span.input-highlight Ion Mode:
      |  Indicates whether the precursor ion has a positive or negative adduct.

    p
      span.input-highlight Mass Tolerance:
      |  The mass tolerance to use when matching peaks within the dot product comparison. The default value is 10.0 ppm. 

    h4 Peak Assigment Results:

    .row
      .col-lg-12
        = image_tag "Spectra_assignment_results.png", alt: "Figure 1", width: 10000
    p 
      b Results: 
      | Input spectra are shown in the plot. Peaks for which corresponding fragments have been found are colored red; unassigned peaks are colored blue. Hover over the peaks to see the exact mass and intensity values, along with the highest scoring assigned fragments, if found. More detailed information can be found father down the page. Note that, in the proposed fragment annotations result, the charge is located on an atom. Whilst this may be a true representation of the charged mass fragment, this is not necessarily the case. Currently, CFM-ID determines the charge location by finding a possible solution of electron configuration that 1. Met valence requirements for each atom, 2. Uses exactly the amount of electrons in the fragment. Thus, there will be multiple possible charge locations that meet this requirement, in this case CFM-ID picks the first electron configuration it found.

  #identify.tab-pane
    h4.help-header Compound Identification
    p The Compound Identification function determines the compounds that most closely match to a given MS/MS spectrum. The input MS/MS spectra (at one or more collision energies) are compared to in silico predicted MS/MS spectra and/or experimental MS/MS spectra as chosen by the user. The top candidates are ranked according to how closely they match and returned in a list. Users may view the matching compounds and their scores in a table and the similarity of the observed spectra to the matched spectra using an MS mirror plot.
    
    h4 Compound Identification Query

    .row
      .col-lg-12
        = image_tag "compound_id_input_3.jpeg", alt: "Figure 1", width: 10000

    h5 Compound Identification Steps:
    p
      ol 
        li Select find candidate or find neutral loss candidates option. The neutral loss option will allow you to enter a spectrum, then the corresponding neutral loss spectrum will be calculated based off of the parent ion mass. 
        li Select desired candidate databases. Both experimental and predicted databases are available.
        li Select desired spectra type, ion mode and adduct type. 
        li Enter parent ion information.
        li Select mass tolerance for candidates retirive.
        li Enter spectra data, the spectra should be represented as a list of peaks with the format 'm/z intensity' on each line. Multiple energy levels are optional; only one is required. This function only accepts centroid spectrum.
        li Select scoring function for ranking.
        li Select mass tolerance to use when matching peaks within the spectrum comparison.
        li Submit job to server
    

    /h4 Submit Candidates Compound Identification Query

    /.row
      .col-lg-12
        = image_tag "compound_id_input_2.jpeg", alt: "Figure 2", width: 10000    

    /h5 Submit Candidates Compound Identification Steps:
    /p
      ol 
        li Select submit candidates option
        li Select desired candidate databases. 
        li Select desired spectra type, ion mode and adduct type. 
        li Enter spectra data, the spectra should be represented as a list of peaks with the format 'm/z intensity' on each line. Multiple energy levels are optional; only one is required.
        li Select scoring function for ranking.
        li Select mass tolerance to use when matching peaks within the spectrum comparison.
        li Submit job to server
     
    h5 Defitions of Inputs:
    p
      span.input-highlight Spectra:
      |  The spectra should be represented as a list of peaks with the format 'mass intensity' on each line. Only centroided spectrum can be entered. For ESI spectra, 'low','medium', and 'high' or 'energy0', 'energy1', and 'energy2' header lines should begin spectra of different energy levels (in that order) and multiple energy levels are optional (only one is required). EI spectra only need to have one energy level. Spectra may also be in .msp file format, in which case energy levels for ESI spectra should be specified in the "Comment: " field (EI spectra do not need a specified energy level). A corresponding spectra ID must be selected for .msp spectra. .msp files must have an "ID" and "Num peaks" attributes for each spectra.
    .example-indent.inline-example
      table.spectra-table
        tr
          td.example-cell[rowspan="36"] Example:
          td[colspan="2"] low
        tr
          td 87.054687
          td 7.567280
        tr
          td 105.069174
          td 1.791050
        tr
          td 136.07616
          td 13.081500
        tr
          td 160.076289
          td 2.225420
        tr
          td 178.084616
          td 5.319120
        tr
          td 223.106608
          td 100.000000
        tr
          td 251.10173
          td 40.722900
        tr
          td 297.107567
          td 3.945980
        tr
          td 384.140384
          td 11.216900
        tr
          td[colspan="2"] medium
        tr
          td 60.044545
          td 2.476820
        tr
          td 87.056965
          td 9.632580
        tr
          td 119.046086
          td 2.367850
        tr
          td 135.066335
          td 1.865000
        tr
          td 136.077192
          td 46.373600
        tr
          td 160.074417
          td 6.652730
        tr
          td 178.08705
          td 20.078100
        tr
          td 223.109344
          td 100.000000
        tr
          td 251.108668
          td 3.127750
        tr
          td 297.113687
          td 1.892360
        tr
          td[colspan="2"] high
        tr
          td 42.033909
          td 3.047230
        tr
          td 60.043746
          td 26.520300
        tr
          td 70.027268
          td 3.162400
        tr
          td 87.056272
          td 18.342000
        tr
          td 91.054494
          td 23.516200
        tr
          td 119.04828
          td 15.711000
        tr
          td 121.063402
          td 7.273900
        tr
          td 133.06551
          td 5.039960
        tr
          td 135.066238
          td 3.626030
        tr
          td 136.074907
          td 100.000000
        tr
          td 160.074409
          td 26.458000
        tr
          td 178.085454
          td 12.211700

    .example-indent.inline-example
      table.spectra-table
        tr
          td.example-cell[rowspan="36"] Example .msp format:
        tr
          td[colspan="2"] Name: Diazirine
        tr
          td[colspan="2"] NISTNO: 305841
        tr
          td[colspan="2"] ID: ID_3
        tr
          td[colspan="2"] Num peaks: 12
        tr
          td[colspan="2"] Comment: energy0
        tr
          td 12
          td 108.00
        tr
          td 13
          td 228.99
        tr
          td 14
          td 999.00
        tr
          td 15
          td 21.98
        tr
          td 26
          td 17.98
        tr
          td 27
          td 58.05
        tr
          td 28
          td 178.04
        tr
          td 29
          td 22.98
        tr
          td 40
          td 17.98
        tr
          td 41
          td 108.00
        tr
          td 42
          td 431.01
        tr
          td 43
          td 7.99
        tr
          td[colspan="2"]
        tr
          td[colspan="2"] Name: Methane, diazo-
        tr
          td[colspan="2"] NISTNO: 57
        tr
          td[colspan="2"] ID: ID_4
        tr
          td[colspan="2"] Num peaks: 12
        tr
          td[colspan="2"] Comment: energy1
        tr
          td 12
          td 110.10
        tr
          td 13
          td 220.30
        tr
          td 14
          td 999.00
        tr
          td 15
          td 25.18
        tr
          td 26
          td 12.59
        tr
          td 27
          td 58.25
        tr
          td 28
          td 179.34
        tr
          td 29
          td 20.48
        tr
          td 40
          td 21.98
        tr
          td 41
          td 110.10
        tr
          td 42
          td 424.82
        tr
          td 43
          td 10.99
    /p
      span.input-highlight Submit Candidates:
      |  The candidates should be represented as a list of compounds in the format 'ID SMILES_or_InChI' on each line. The list can have a maximum of 100 compounds. The compounds must be represented in proper 
      a[href="http://en.wikipedia.org/wiki/International_Chemical_Identifier" target="_blank"] InChI format
      |  or 
      a[href="http://en.wikipedia.org/wiki/Simplified_molecular-input_line-entry_system" target="_blank"] SMILES format
      | . InChI strings need to start with "InChI=" and are not expected to have any charge - an additional H+ will be added. InChI strings need to contain AT LEAST the main layer with it's chemical formula and atom connections sublayers for proper computation.
    /.example-indent
      table.spectra-table
        tr
          td.example-cell[rowspan="21"] Example:
          td 7156455
          td
            | CC(C)N1C(=O)C2C(CCN2S(C)(=O)=O)N(Cc2cccc(F)c2)C1=O
          td
        tr
          td 485776
          td
            | CSCC(=O)NCC1CN(c2ccc(N3CCOCC3)c(F)c2)C(=O)O1
          td
        tr
          td 485687
          td
            | CC(=O)NNCC1CN(c2ccc(C3CCS(=O)CC3)c(F)c2)C(=O)O1
          td
        tr
          td 45556239
          td
            | O=C(NC1CC1)N1CCC2(CC1)OCCN2S(=O)(=O)c1ccc(F)cc1
          td
        tr
          td 19459759
          td
            | Cc1cc(C(F)(F)Cl)n2nc(C(=O)NC3CC4CCC(C3)N4C)cc2n1
          td
        tr
          td 59444507
          td
            | Cc1cc(CN(CC(=O)O)CC(=O)O)nc(CN(CC(=O)O)CC(=O)O)c1
          td
        tr
          td 58984199
          td
            | C=CC(=O)OCCn1c(=O)n(CCOC)c(=O)n(CCOC(=O)C=C)c1=O
          td
        tr
          td 58753253
          td
            | NC(CO)C(=O)NC(CC(=O)O)C(=O)NC(Cc1ccc(O)cc1)C(=O)O
          td
        tr
          td 54199399
          td
            | NC(CN(CC(=O)O)CC(=O)O)(c1ccccc1)N(CC(=O)O)CC(=O)O
          td
        tr
          td 45644415
          td
            | CNC(=O)NC(=O)COC(=O)C1C(C(=O)OC)=C(C)NC(C)=C1C(=O)OC
          td
        tr
          td 44585322
          td
            | COc1cc(C(=O)NCC(=O)NCC(=O)NCC(=O)O)cc(OC)c1OC
          td
        tr
          td 36010709
          td
            | COc1cc(C(=O)NCC(=O)OC(C)C(=O)NC(N)=O)cc(OC)c1OC
          td
        tr
          td 21494927
          td
            | Nc1ccccc1C(C(=O)O)N(CCN(CC(=O)O)CC(=O)O)CC(=O)O
          td
        tr
          td 21273011
          td
            | NC(C(=O)O)(c1ccccc1)N(CCN(CC(=O)O)CC(=O)O)CC(=O)O
          td
        tr
          td 20147059
          td
            | Nc1ccc(C(C(=O)O)N(CCN(CC(=O)O)CC(=O)O)CC(=O)O)cc1
          td
        tr
          td 18232127
          td
            | NC(Cc1ccc(O)cc1)C(=O)NC(CO)C(=O)NC(CC(=O)O)C(=O)O
          td
        tr
          td 18231916
          td
            | NC(Cc1ccc(O)cc1)C(=O)NC(CC(=O)O)C(=O)NC(CO)C(=O)O
          td
        tr
          td 18224136
          td
            | NC(CO)C(=O)NC(Cc1ccc(O)cc1)C(=O)NC(CC(=O)O)C(=O)O
          td
        tr
          td 18219720
          td
            | NC(CC(=O)O)C(=O)NC(Cc1ccc(O)cc1)C(=O)NC(CO)C(=O)O
          td
    p
      span.input-highlight Database:
      |  Instead of providing a candidate list, one can be generated from a selected database. Additional input options for generating a compound list from a database are:

    p.indent
      span.input-highlight Parent Ion Mass:
      |  The parent ion mass of the compound used in the mass spectrometry.
    p.indent
      span.input-highlight Adduct Type:
      |  The adduct type used in the mass spectrometry.
    p.indent
      span.input-highlight Candidate Mass Tolerance:
      |  The mass tolerance to use when identifying candidate compounds in the database. The default value is 100.0 ppm.
    p.indent
      span.input-highlight Candidate Limit:
      |  The maximum number of candidates to return. The maximum and default value is 100.

    p
      span.input-highlight Spectra Type:
      |  The type of spectra, either ESI (Electrospray Ionization) or EI (Electron Ionization/Impact).

    p
      span.input-highlight Ion Mode:
      |  Indicates whether the precursor ion has a positive or negative adduct.

    p
      span.input-highlight Number of Results:
      |  The number of results to return, with the default value being 10. If left blank, all results wil be returned.

    p
      span.input-highlight Mass Tolerance:
      |  The mass tolerance to use when matching peaks within the dot product comparison. The default value is 10.0 ppm.

    p
      span.input-highlight Scoring Function:
      |  The type of scoring function to use when comparing spectra. The options are 
      a[href="http://en.wikipedia.org/wiki/Sørensen–Dice_coefficient"] Dice
      |  and 
      a[href="http://en.wikipedia.org/wiki/Dot_product"] DotProduct
      | . 

    h4 Compound Identification Results:

    .row
      .col-lg-12
        = image_tag "compound_id_results.jpeg", alt: "Figure 3", width: 10000
    p 
      b Results: 
      | Input spectra are shown in blue and candidate spectra are shown in red. If a database was queried, candidate spectra are overlayed on top for comparison. The top ranking candidate spectra is shown by default; to compare other database candidates use the "Compare" buttons on the list of ranked candidate compounds that follow the spectra.
    
  #browser_compliance.tab-pane
    h4.help-header Browser Compliance
    .table-responsive
      table.table.table-striped
        tr
          th OS
          th Version
          th Chrome
          th Firefox
          th Microsoft Edge
          th Safari
        tr
          td Linux
          td Mint 20.1
          td 95.0.4638
          td 89.0.2
          td N/A
          td N/A
        tr
          td MacOS
          td BigSur 11.6
          td 96.0.4664.93
          td 95.0.2
          td N/A
          td 14.1.2 (16611.********)
        tr
          td Windows
          td 10
          td 96.0.4664.110
          td 95.0.1
          td 96.0.1054.57
          td N/A
  #hcd_cid_spectra.tab-pane
    h4.help-header HCD vs CID Spectra
    p High-energy C-trap dissociation (HCD) is considered a more gentle fragmentation process than CID, that is an HCD spectrum typically has more unique fragments across the entire mass-to-charge ratio range than their CID counterparts. However, for the same molecule, CID and HCD spectra in similar collision energy are having a lot of fragments in common. Recall that CFM-ID is trained on CID data, thus its predicted spectra are less similar to Orbitrap spectra than QToF spectra (Yields lower Dice or/and Dot Product score).  From our experience, CFM-ID predicted spectra are still very useful to determine compounds with Orbitrap data. In the CASMI 2016 experiments, we first determine the true collision energy of a given spectrum from its NCE value by the equation provided by Thermo Fisher, then compared this spectrum with the closest CID collision energy spectra. 
  #performance.tab-pane
    h4.help-header Performance
    = render partial: 'performance'
  #docker_image.tab-pane
    h4.help-header Docker Image
    h4 What is Included?
    p
      ol 
        li Latest CFM-ID 4 MSML machine learning model
        li C++ Runtime for CFM-ID 4 MSML
        li Latest CFM-ID 4 MSRB rule based extension
        li Java Runtime for CFM-ID 4 MSRB
    h4 What is not Included?
    p
      ol 
        li CFM-ID in-silico spectra libaray
        li CFM-ID experimental spectra libaray
    h4 What can it do?
    p
      ol 
        li Predict spectra for give molecule structure
        li Annotate spectrum for given moleucle structure and its spectrum
        li Idenity molecule from a given spectrum and user provided candidate list
    h4 How to use it?
    p Please refer to the user guide on <a href="https://hub.docker.com/r/wishartlab/cfmid" target='_blank' </a> DockerHub page.
