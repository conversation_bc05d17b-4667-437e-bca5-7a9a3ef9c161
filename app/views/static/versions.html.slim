.page-header
  h3 = title("CFM-ID Versions")

.table-responsive
  table.table.table-striped
    tr
      th Version
      th Description
      th Docker Image
      th Source Code/Downloadable
    tr
      td 4.0
      td Upgraded machine learned model and enhanced rule-based extension for predicting ESI-MS/MS spectrum.
      td 
        = link_to 'Visit DocHub', 'https://hub.docker.com/r/wishartlab/cfmid', target:'_blank'
      td 
        = link_to 'MSML 4.4.7/', 'https://bitbucket.org/wishartlab/cfm-id-code/src', target:'_blank'
        = link_to 'MSRB 1.1.4', 'https://bitbucket.org/wishartlab/msrb-fragmenter/downloads/msrb-fragmenter-1.1.4.jar', target:'_blank'
    tr
      td 3.0
      td Augmented with rules to help predict ESI-MS/MS spectra for specific classes of compounds.
      td 
        = link_to 'Visit DocHub', 'https://hub.docker.com/r/wishartlab/cfmid', target:'_blank'
      td 
        = link_to 'MSML 2.4.3/', 'https://bitbucket.org/wishartlab/cfm-id-code/get/CFM-ID_2.4.3.tar.gz', target:'_blank'
        = link_to 'MSRB 1.1.0', 'https://bitbucket.org/wishartlab/msrb-fragmenter/downloads/msrb-fragmenter-1.1.0.jar', target:'_blank'
    tr
      td 2.0
      td First published version of CFM-ID, using machine learned model to predict EI-MS (or ESI-MS/MS) spectrum from given compound. Also used to predict the compound from a given spectrum.
      td N/A
      td 
        = link_to 'MSML 2.4.3', 'https://bitbucket.org/wishartlab/cfm-id-code/get/CFM-ID_2.4.3.tar.gz', target:'_blank'

