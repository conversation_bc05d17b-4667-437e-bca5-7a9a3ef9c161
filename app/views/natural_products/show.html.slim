.page-header: h1
  = title("Showing NP-Card for #{@natural_product.name} (#{@natural_product.np_mrd_id})")

- cache @natural_product, expires: 14.days
  = render "/natural_products/jumper", natural_product: @natural_product

  table.content-table.table.table-condensed.table-bordered
    tbody
      = table_header_divider "Record Information"
      tr
        th Version
        td = Rails.application.config.version
      tr
        th= NaturalProduct.human_attribute_name(:created_at)
        td = @natural_product.created_at
      tr
        th= NaturalProduct.human_attribute_name(:updated_at)
        td = @natural_product.updated_at
      tr
        th  NP-MRD ID
        td = @natural_product.np_mrd_id
      tr
        th Secondary Accession Numbers
        td.acc-num = list(@natural_product.accession_numbers, empty: "None")

      tr id="identification" class="table-head-reposition"
      = table_header_divider "Natural Product Identification", id: 'unknown'
      tr
        th Common Name
        td: strong = nah @natural_product.name
      tr
        th Description
        td.met-desc = nah text_with_linked_references(@natural_product.get_description)

      tr
        th Structure
        td
          - if @natural_product.valid_thumb?
            .structure = image_tag(@natural_product.thumb.url)
          - else
            .structure = moldb_vector_thumbnail(@natural_product)
          .structure-links = moldb_structure_links(@natural_product)
      tr
        th Synonyms
        - if @natural_product.synonymified?
          td.data-table-container
            = moldb_synonyms_table(@natural_product)
        - else
          td = nah
      tr
        th Chemical Formula
        td = nah html_formula @natural_product.chemical_formula
      tr
        th Average Molecular Weight
        td = nah @natural_product.average_mass
      tr
        th Monoisotopic Molecular Weight
        td = nah @natural_product.mono_mass
      tr
        th IUPAC Name
        td = nah @natural_product.iupac
      tr
        th Traditional Name
        td = nah @natural_product.traditional_iupac
      tr
        th CAS Registry Number
        td = nah @natural_product.cas
      tr
        th SMILES
        td
          - if @natural_product.has_structure?
            .wrap= @natural_product.moldb_smiles
          - else
            = nah
      tr
        th InChI Identifier
        td: .wrap = nah @natural_product.inchi
      tr
        th InChI Key
        td = nah @natural_product.inchikey

      tr id="spectra" class="table-head-reposition"
      = table_header_divider "Spectra",id: 'unknown'
      tr
      th
      td.data-table-container = specdb_spectra_list(@natural_product)

      tr id="species" class="table-head-reposition"
      = table_header_divider "Species of Origin", id: 'species'

      - species_mappings = @natural_product.ordered_species_mappings
      tr
        th Species
        td.data-table-container
          - if species_mappings.any?
            table.table.table-condensed.table-striped.species-mappings
              thead
                tr
                  th Species Name
                  th Source
                  th Reference
              tbody
                - species_mappings.each do |species_mapping|
                  tr
                    // td = species_mapping.species.scientific_name
                    td = link_to species_mapping.species.scientific_name, "https://www.ncbi.nlm.nih.gov/Taxonomy/Browser/wwwtax.cgi?id=#{species_mapping.species.scientific_name_taxid}"
                    td = species_mapping.source
                    td
                      ul.list-unstyled
                        = nah species_mapping_references(species_mapping)
          - else
            // # e.g. All Kingdoms
            = nah @natural_product.origin

      tr id="taxonomy" class="table-head-reposition"
      = table_header_divider "Chemical Taxonomy", id: 'unknown'
      - if @natural_product.classyfired?
        = moldb_classification_table(@natural_product)
      - else
        tr
          th Classification
          td = nah nil, 'Not classified'

      tr id="physical_properties" class="table-head-reposition"
      = table_header_divider "Physical Properties", id: 'unknown'
      tr
        th State
        td = nah @natural_product.state
      tr
        th Experimental Properties
        td
          table class="table table-bordered"
            col width = "250"
            col width = "250"
            col width = "500"
            thead
              tr
                th Property
                th Value
                th Reference
            tbody
              tr
                td Melting Point
                td = nah @natural_product.experimental_melting_point
                td = nah @natural_product.experimental_melting_point_reference
              tr
                td Boiling Point
                td = nah @natural_product.experimental_boiling_point
                td = nah @natural_product.experimental_boiling_point_reference
              tr
                td Water Solubility
                - if @natural_product.experimental_water_solubility.present?
                    td = nah @natural_product.experimental_water_solubility.gsub(@natural_product.experimental_water_solubility.split(" ")[0], @natural_product.experimental_water_solubility.split(" ")[0].to_f.smart_round_to_s)
                - else
                    td = nah @natural_product.experimental_water_solubility
                td = nah @natural_product.experimental_water_solubility_reference
              tr
                td LogP
                td = nah @natural_product.experimental_logp
                td = nah @natural_product.experimental_logp_reference
      tr
        th Predicted Properties
        - if @natural_product.has_structure?
            td
              = moldb_properties_table(@natural_product)
        - else
            td = nah
    tr id="user_submitted_data" class="table-head-reposition"
    = table_header_divider "User Submitted Data", id: 'unknown'
    tr
      th
      td.data-table-container = render partial: 'natural_products/user_submitted_data'


    tr id="links" class="table-head-reposition"
    = table_header_divider "External Links", id: 'unknown'
    - if !@natural_product.wrangler_identifiers.blank?
      tr
        th HMDB ID
        td = nah bio_link_out :hmdb, @natural_product.wrangler_identifiers["hmdb_id"]
      tr
        th DrugBank ID
        td = nah bio_link_out :drugbank, @natural_product.wrangler_identifiers["drugbank_id"]
      tr
        th Phenol Explorer Compound ID
        td = nah bio_link_out :phenol_compound, @natural_product.wrangler_identifiers["phenol_id"]
      tr
        th FoodDB ID
        td = nah bio_link_out :foodb_compound, @natural_product.wrangler_identifiers["foodb_id"]
      tr
        th KNApSAcK ID
        td = nah bio_link_out :knapsack, @natural_product.wrangler_identifiers["knapsack_id"]
      tr
        th Chemspider ID
        td = nah bio_link_out :chemspider, @natural_product.wrangler_identifiers["chemspider_id"]
      tr
        th KEGG Compound ID
        td = nah bio_link_out :kegg_compound, @natural_product.wrangler_identifiers["kegg_id"]
      tr
        th BioCyc ID
        td = nah bio_link_out :biocyc, @natural_product.wrangler_identifiers["meta_cyc_id"]
      tr
        th BiGG ID
        td = nah bio_link_out :bigg, @natural_product.wrangler_identifiers["bigg_id"]
      tr
        th Wikipedia Link
        td = nah bio_link_out :wikipedia, @natural_product.wrangler_identifiers["wikipedia_id"]
      tr
        th METLIN ID
        td = nah bio_link_out :metlin, @natural_product.wrangler_identifiers["metlin_id"]
      tr
        th PubChem Compound
        td = nah bio_link_out :pubchem_compound, @natural_product.wrangler_identifiers["pubchem_id"]
      tr
        th PDB ID
        td = nah bio_link_out :pdb_ligand, @natural_product.wrangler_identifiers["pdbe_id"]
      tr
        th ChEBI ID
        td = nah bio_link_out :chebi, @natural_product.wrangler_identifiers["chebi_id"]
    - else
      tr
        th External Links
        td = nah


    tr id="References" class="table-head-reposition"
    = table_header_divider "References", id: 'unknown'
    tr
      th General References
      td.data-table-container = nah full_reference_list(@natural_product)
