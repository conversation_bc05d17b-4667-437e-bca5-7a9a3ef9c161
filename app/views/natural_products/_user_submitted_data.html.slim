- @chemical_shift_submission = @natural_product.chemical_shift_submissions.where(:valid => true).order("created_at DESC")
- @submission = @natural_product.submissions.where(:valid => true).order("created_at DESC")
- if @chemical_shift_submission.exists? or @submission.exists?
  table.table-inner
    thead
      tr
        th.head-medium Spectrum Type
        th.head-medium Description
        th.head-medium Depositor ID
        th.head-medium Deposition Date
        th.head-medium View
        th.head-medium Reference
        th.head-medium Download

    tbody
        - if @chemical_shift_submission.exists?
            - @chemical_shift_submission.each do |cs|
                tr
                    td = "#{cs.chemical_shift_submission_meta_data.spectrum_type}"+" NMR"
                    td = "Chemical Shift Assignments/Simulated spectra from assignment"
                    td = "#{User.find(cs.user_id).email.split("@")[0].capitalize}"
                    td = "#{cs.created_at.strftime("%Y-%m-%d")}"
                    td = nah link_to "Predicted Spectrum", spectrum_view_natural_product_path(cs)
                    - if cs.chemical_shift_submission_meta_data.literature_reference_type == "PMID"
                        td = nah link_to "PubMed:#{cs.chemical_shift_submission_meta_data.literature_reference} #{glyphicon('new-window')}".html_safe, "https://www.ncbi.nlm.nih.gov/pubmed/#{cs.chemical_shift_submission_meta_data.literature_reference}".html_safe, target: '_blank', rel: "nofollow"
                    - elsif cs.chemical_shift_submission_meta_data.literature_reference_type == "DOI"
                        td = nah link_to "DOI:#{cs.chemical_shift_submission_meta_data.literature_reference} #{glyphicon('new-window')}".html_safe, "https://doi.org/#{cs.chemical_shift_submission_meta_data.literature_reference}".html_safe, target: '_blank', rel: "nofollow"
                    - else
                        td = nah
                    td
                        = nah link_to "CSV", download_submitted_data_chemical_shift_submission_path(cs)
                        = " / "
                        = nah link_to "nmrML", download_nmrml_from_outside_chemical_shift_submission_path(cs)
        - if @submission.exists?
            - @submission.each do |s|
                tr
                    td = "#{s.submission_meta_data.spectrum_type}"+" NMR"
                    td = "Chemical Shift Assignments/Simulated spectra from assignment"
                    td = "#{User.find(s.user_id).email.split("@")[0].capitalize}"
                    td = "#{s.created_at.strftime("%Y-%m-%d")}"
                    td = nah link_to "Predicted Spectrum", submission_spectrum_view_natural_product_path(:spectrum_type => 'simulation',:id => s.id)
                    //td = nah
                    - if s.submission_meta_data.literature_reference_type == "PMID"
                        td = nah link_to "PubMed:#{s.submission_meta_data.literature_reference} #{glyphicon('new-window')}".html_safe, "https://www.ncbi.nlm.nih.gov/pubmed/#{s.submission_meta_data.literature_reference}".html_safe, target: '_blank', rel: "nofollow"
                    - else
                        td = nah
                    td
                        = nah link_to "CSV", download_submitted_chemical_shift_data_submission_path(s)
                        = " / "
                        = nah link_to "nmrML", download_nmrml_from_outside_submission_path(s)

                - s.nmr_submissions.each do |file|
                    tr
                        td = "#{file.nmr_spectrum_type}"+" NMR"
                        td = "Chemical Shift Assignments/Spectral File"
                        td = "#{User.find(s.user_id).email.split("@")[0].capitalize}"
                        td = "#{s.created_at.strftime("%Y-%m-%d")}"
                        td = nah link_to "Spectrum", submission_spectrum_view_natural_product_path(:spectrum_type => 'real',:id => s.id,:spectrum_id => file.id)
                        - if s.submission_meta_data.literature_reference_type == "PMID"
                            td = nah link_to "PubMed:#{s.submission_meta_data.literature_reference} #{glyphicon('new-window')}".html_safe, "https://www.ncbi.nlm.nih.gov/pubmed/#{s.submission_meta_data.literature_reference}".html_safe, target: '_blank', rel: "nofollow"
                        - else
                            td = nah
                        td
                            = nah link_to "CSV", download_submitted_chemical_shift_data_submission_path(s)
                            = " / "
                            = nah link_to "nmrML", download_nmrml_from_outside_submission_path(s)
                            = " / "
                            = link_to "FID", file.nmr_file.url
        - else
            - puts "some problem in loading user submitted data"