<% if cookies.kind_of? ActionDispatch::Cookies::<PERSON><PERSON><PERSON>ar %>
  <% if cookies && cookies['cookie_eu_consented'] != 'true' %>
    <div class="cookies-eu js-cookies-eu" style="height: 10%; text-align: center; padding-top: 5px" <%= "dir=rtl" if I18n.exists?('cookies_eu.direction', I18n.locale) && I18n.t("cookies_eu.direction") == "rtl"  %>>
      <span class="cookies-eu-content-holder" style="font-size: 20px;"><%= 'CFM-ID requires the use of cookies. By continuing to use this site, you agree to our use of cookies.' %></span>
      <span class="cookies-eu-button-holder">
      <button class="cookies-eu-ok js-cookies-eu-ok" style="font-size: 20px; height: 30px"> <%= t('cookies_eu.ok') %> </button>
        <% if defined?(link).present? %>
        <a href="<%= link %>" class="cookies-eu-link" target="<%= defined?(target).present? ? target : '' %>"> <%= t('cookies_eu.learn_more') %> </a>
      <% end %>
      </span>
    </div>
  <% end %>
<% else %>
  <% raise Exception.new "'cookies' is a reserved Rails class, please rename your method" %>
<% end %>