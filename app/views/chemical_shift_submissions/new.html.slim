= render partial: "shared/view_3D"
= render partial: 'submissions/warning'
h1 NP-Chemical Shift Deposition (Natural Product Chemical Shift Deposition)
br
.well-content.form-wrapper id="variable_html"
  h2 <b>Input Form for Natural Product Chemical Shift Data<b>
  br
  = form_for @chemical_shift_submission, :url => url_for(:controller => 'chemical_shift_submissions', :action => 'create'), :remote => true do |f|
    .well-content
      = render partial: 'chemical_shift_submissions/intro'
    br
    .well-content
      = render partial: 'chemical_shift_submissions/instructions'
    .row
      .col-sm-5
        h2 Provide a Structure
      .col-sm-6
        h2 Fields Marked with '*' are Mandetory

    br
    .row
      .col-sm-5
          = moldbi_structure_drawer '', "structure_input", format: "smiles"
          br
          br
          br
          = link_to "#{glyphicon('play-circle')} MarvinJS Tutorials".html_safe, 'https://www.youtube.com/playlist?list=PLA3Ev2ngKC0TY2p59vJhlYGm-wmrLaWp6', class: 'btn btn-default pull-right', target: '_blank'
      .col-sm-6.hide-overflow
        = f.fields_for :chemical_shift_submission_meta_data do |ff|
          .row
            .col-sm-6
              .label-font-size
                = label_tag :compound_name, "*Compound Name: "
            .col-sm-5
              = text_field_tag 'compound_name', nil, placeholder: "Common/IUPAC name", required: true
          br
          .row
            .col-sm-6
              = ff.label :provenance, "*Provenance: ",  required: true
            .col-sm-5
              = ff.select :provenance, provenance_collection, {:include_blank => 'Select One'}, {:required => true, :class => 'select', :id => "chemical_shift_provenance"}
          br
          #chemical_shift_genus
            .row
              .col-sm-6
                = ff.label :genus, "*Organism Genus Name: "
              .col-sm-5
                = ff.text_field :genus, class: "form-control-plaintext", placeholder: "Genus name"
            br
          #chemical_shift_species
            .row
              .col-sm-6
                = ff.label :genus, "*Organism Species Name: "
              .col-sm-5
                = ff.text_field :species, class: "form-control-plaintext", placeholder: "Species name"
          br
          .row
            .col-sm-6
              = ff.label :physical_state_of_compound, "Physical State of Compound: "
            .col-sm-5
              = ff.select :physical_state_of_compound, physical_state_of_compound_collection, {:include_blank => 'Select One'}
          br
          .row
            .col-sm-6
              = ff.label :melting_point, "Melting Point(°C): "
            .col-sm-5
              = ff.text_field :melting_point
          br
          .row
            .col-sm-6
              = ff.label :boiling_point, "Boiling Point(°C): "
            .col-sm-5
              = ff.text_field :boiling_point  
          br    
          .row
            .col-sm-6
              = ff.label :literature_reference_type, "*Literature Reference Type: ",  required: true
            .col-sm-5
              = ff.select :literature_reference_type, literature_reference_types_collection, {:include_blank => 'Select One'}, {:required => true, :class => 'select'}
          br
          .row
            .col-sm-6
              = ff.label :literature_reference, "*Literature Reference: "
            .col-sm-5
              = ff.text_field :literature_reference, class: "form-control-plaintext", placeholder: "PubMed ID/DOI/Book", required: true
          br
          .row
            .col-sm-5
              = f.button 'Next' , type: 'submit', name: 'state', value: 'post_np', :onclick => "submit_button_click()"
