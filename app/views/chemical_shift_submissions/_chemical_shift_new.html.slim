h2 <b>Input Form for Natural Product Chemical Shift Data</b>
= form_for @chemical_shift_submission, :remote => true do |f|
  br
  .row
    .col-sm-3
      = f.label " ", "NP-MRD ID:  "
    .col-sm-2
      = "#{@chemical_shift_submission.natural_product.np_mrd_id}".html_safe
  .row
    .col-sm-3
      = f.label " ", "Compound Name:  "
    .col-sm-2
      = "#{@chemical_shift_submission.natural_product.name}".html_safe

  .row
    .col-sm-3
      = f.label " ", "Provenance:  "
    .col-sm-2
      = "#{@chemical_shift_submission.chemical_shift_submission_meta_data.provenance}".html_safe

  - if !(@chemical_shift_submission.chemical_shift_submission_meta_data.provenance == "Other" || @chemical_shift_submission.chemical_shift_submission_meta_data.provenance == "Biotransformation" || @chemical_shift_submission.chemical_shift_submission_meta_data.provenance == "Chemical synthesis")
    .row
      .col-sm-3
        = f.label " ", "Organism Genus Name:  "
      .col-sm-2
        = "#{@chemical_shift_submission.chemical_shift_submission_meta_data.genus}".html_safe
    .row
      .col-sm-3
        = f.label " ", "Organism Species Name:  "
      .col-sm-2
        = "#{@chemical_shift_submission.chemical_shift_submission_meta_data.species}".html_safe
  br
  br
  .well-content
    = render partial: 'chemical_shift_submissions/ambiguity_handling'
  br
  .well-content
    = render partial: 'chemical_shift_submissions/custom_numbering_handling'
  br
  .row
    .col-sm-4
      h4 Submitted Structure with Atom Indexing
    .col-sm-8
      h4 Enter Chemical Shift Data into the Table

  br
  .row
    .col-sm-4 id="view_3d" style="z-index:0"
      - if @chemical_shift_submission.renumberedMol
        div[class="mol_file" data-mol_file = "#{@chemical_shift_submission.renumberedMol}"]
        = render partial: "shared/view_3D"
      - elsif @chemical_shift_submission.natural_product.threeDmol
        div[class="mol_file" data-mol_file = "#{@chemical_shift_submission.natural_product.threeDmol}"]
        = render partial: "shared/view_3D"
      - else
        = image_tag "#{@threeD_image_url}", class: 'featurette-image'
    // put headers in a separate cell so that position: sticky will work in Safari
    .col-sm-8
      .grid-item-table
        = render partial: 'chemical_shift_submissions/shift_table', locals: {:atom_symbol => @atom_symbol, :chemical_shifts => @chemical_shifts, :f => f}
        br
        br
         = f.button 'Save and Verify' , type: 'submit', name: 'state', value: 'post_chemical_shift', :onclick => "submit_button_click()"
