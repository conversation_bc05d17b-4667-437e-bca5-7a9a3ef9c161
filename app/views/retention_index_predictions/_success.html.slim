br
- if params[:notice].present?
  .alert-citation.alert
    p #{glyphicon(:'glyphicon-ok')} #{params[:notice]}

.page-header: h1 = title("Kovats' Retention Index Prediction (RIpred)")
//.well
//  .intro
//    h4 Spectrum View (Real)
//= "under construction"

//.well
//  .intro
//    h4 Spectrum View (Predicted Using Uploaded Data)

/ = render partial: 'chemical_shift_submissions/spectrum_view', locals: {:download_submitted_chemical_shift_path => download_submitted_data_chemical_shift_submission_path, :nmrML_path => download_nmrml_chemical_shift_submission_path, :percentage_of_assigned_atoms => @percentage_of_assigned_atoms, :percentage_of_good_true_values => @percentage_of_good_true_values, :percentage_of_assigned_jcoupling => @percentage_of_assigned_jcoupling, :percentage_of_assigned_multiplet => @percentage_of_assigned_multiplet}
.alert-citation.alert style="text-align: center"
  p #{glyphicon(:'glyphicon-ok')} Submission Deposited Successfully!
  br
  p You will be redirected to the Browse Page in 5 Seconds
  meta HTTP-EQUIV="REFRESH" content="5; #{main_app.retention_index_compounds_path}"

= image_tag("np-mrd_loader.svg", class: 'loader', style: 'display: block; margin-left: auto; margin-right: auto;')