= render partial: "shared/view_3D"
= render partial: 'submissions/warning'

div.page-header
  h1 Kovats' Retention Index Prediction (RIpred)
br

.well-content.form-wrapper id="variable_html" style="font-weight: bold; font-size: 17px;"
  div
    h1 <b>Input Form for RI data</b>
  = form_for @retention_index_prediction, :url => url_for(:controller => 'retention_index_predictions', :action => 'create') do |ff|
    / .row
    /   .col-sm-5
    /     h2 Provide a Structure
    /   .col-sm-6
    /     h2 Fields Marked with '*' are Mandatory
    .row
      .col-sm-5
        br
          = moldbi_structure_drawer '', "structure_input", format: "smiles"
          .col-sm
            = link_to "#{glyphicon('play-circle')} MarvinJS Tutorials".html_safe, 'https://www.youtube.com/playlist?list=PLA3Ev2ngKC0TY2p59vJhlYGm-wmrLaWp6', class: 'btn btn-default pull-left', target: '_blank', style: 'margin: 0 0 0 0;'
          .col-sm
            .row
              .col-sm-4
                = link_to "#{glyphicon(:save)} Load Example 1".html_safe, "javascript:void(0)",
                    class: "btn btn-default predictions-example-loader", 'data-smiles': "CN1C=NC(C[C@H](N)C(O)=O)=C1",
                    'data-no-turbolink': true
          br
      .col-sm-7.hide-overflow
        br
        br
        .row
          .col-sm-7
            = ff.label :instruction, "To Operate this Prediction:"
            ol
              li Draw the structure in the white box on the left
              li Input the compound name in the corresponding box
              li Select the GC stationary phase from the pull-down menu
              li Select the type of derivatization from the pull-down menu
              li Click the "Predict" button
            br
            br
            .row
              .col-sm-5
                = ff.label :compound_name_label, "*Compound Name: ", class: "predictionlabel"
              .col-sm-3
                = ff.text_field :compound_name, placeholder: "Common/IUPAC name",  required: true
            br
            .row
              .col-sm-5
                = ff.label :compound_stationary_phase_label, "*GC Stationary Phase: ",  class: "predictionlabel"
              .col-sm-3
                = ff.select :compound_stationary_phase, provenance_collection, {:class => 'select', :id => "submission_provenance"}
            br
            .row
              .col-sm-5
                = ff.label :compound_derivative_type_lable, "*Type of Derivatization: ", class: "predictionlabel"
              .col-sm-5
                = ff.select :compound_derivative_type, physical_state_of_compound_collection
            br
            .row
              .col-sm-4
                = ff.button 'Predict' , type: 'submit', :onclick => "submit_button_click()", class: 'btn btn-submit'
          .col-sm-2