h2 <b>Input Form for Natural Product Chemical Shift Data</b>
= form_for @chemical_shift_submission, :remote => true do |f|
  br
  table.padded
    tbody
      tr
        td
          b = "NP-MRD ID:"
        td = @chemical_shift_submission.natural_product.np_mrd_id
      tr
        td
          b = "Compound Name:"
        td = @chemical_shift_submission.natural_product.name
      tr
        td
          b = "Provenance:"
        td = @chemical_shift_submission.chemical_shift_submission_meta_data.provenance

      - if !(@chemical_shift_submission.chemical_shift_submission_meta_data.provenance == "Other" || @chemical_shift_submission.chemical_shift_submission_meta_data.provenance == "Biotransformation" || @chemical_shift_submission.chemical_shift_submission_meta_data.provenance == "Chemical synthesis")

        tr
          td
            b = "Organism Genus Name:"
          td = @chemical_shift_submission.chemical_shift_submission_meta_data.genus
        tr
          td
            b = "Organism Species Name:"
          td = @chemical_shift_submission.chemical_shift_submission_meta_data.species
            br


  = f.fields_for :chemical_shift_submission_meta_data do |ff|
    br
    .row
      .col-sm-6
        h2 Submitted Structure
      .col-sm-6
        h2 Fields Marked with '*' are Mandetory
    .row
      .col-sm-6
        br
        .col-sm-6 id="view_3d" style="z-index:0"
          - if @chemical_shift_submission.renumberedMol
            div[class="mol_file" data-mol_file = "#{@chemical_shift_submission.renumberedMol}"]
            = render partial: "shared/view_3D"
          - elsif @chemical_shift_submission.natural_product.threeDmol
            div[class="mol_file" data-mol_file = "#{@chemical_shift_submission.natural_product.threeDmol}"]
            = render partial: "shared/view_3D" 
          - else
            = image_tag "#{@threeD_image_url}", class: 'featurette-image'
      .col-sm-6.hide-overflow
        br
        br
        br
        .row
          .col-sm-6
            = ff.label :solvent, "*Solvent:"
          .col-sm-6
            = ff.select :solvent, solvent_collection, {:include_blank => 'Select One'}, {:required => true, :class => 'select'}

        // Spectrum type cannot be edited once set
        - if !@chemical_shift_submission_meta_data.spectrum_type.present?
          br
          .row
            .col-sm-6
              = ff.label :spectrum_type, "*Spectrum Type:"
            .col-sm-6
              = ff.select :spectrum_type, spectrum_type_collection, {:include_blank => 'Select One'}, {:required => true, :class => 'select'}
        - else
          br
          .row
            .col-sm-6
              = ff.label :spectrum_type, "*Spectrum Type:"
            .col-sm-6
              = ff.text_field :spectrum_type, :readonly => true
        br
        .row
          .col-sm-6
            = ff.label :spectrometer_frequency, "*Spectrometer Frequency:"
          .col-sm-6
            = ff.select :spectrometer_frequency, spectrometer_frequency_collection, {:include_blank => 'Select One'}, {:required => true, :class => 'select'}
        br
        .row
          .col-sm-6
            = ff.label :temperature, "Temperature(°C):"
          .col-sm-6
            = ff.text_field :temperature, class: "form-control-plaintext", placeholder: "20"
        br
        .row
          .col-sm-6
            = ff.label :chemical_shift_standard, "*Chemical Shift Reference:"
          .col-sm-6
            = ff.select :chemical_shift_standard, chemical_shift_reference_collection, {:include_blank => 'Select One'}, {:required => true, :class => 'select'}
        br
        .row
          .col-sm-3
            = ff.button 'Next' , type: 'submit', name: 'state', value: 'post_meta', :onclick => "submit_button_click()"

    

