- if params[:notice].present?
  .alert-citation.alert
    p #{glyphicon(:'glyphicon-ok')} #{params[:notice]}
- else
  .page-header: h1 = title("RI Prediction Form")
  .well
    .intro
      =render partial: 'retention_index_predictions/intro'
.well style="display: inline-block"
    .intro 
        h2 style="padding: 15px;"
            = link_to "<b>Kovats' Retention Index Prediction (RIPred)</b>".html_safe, main_app.new_retention_index_prediction_path,
                        class: 'btn btn-primary', style: 'font-size: 2.0rem;', 'data-no-turbolink': false

.page-header
  h2 style="font-size: 1.5em"
    = title('Prediction')
    - if params[:compound_name_prefix].present?
      = link_to "Download All".html_safe, retention_index_predictions_download_path(compound_name_prefix: params[:compound_name_prefix], phase: params[:phase], type: params[:type], smile: params[:smile], download_type: ""), 
        class: 'btn btn-primary', style: 'font-size: 1.5rem; margin-left: 1.0em; margin-bottom: 0.5rem', 'data-no-turbolink': false, target: :_blank
ul#submission_tabs.nav.nav-tabs
  li.active: a data-toggle="tab" href="#tms-der-predictions" TMS-Derivatization
  li: a data-toggle="tab" href="#tbdms-der-predictions" TBDMS-Derivatization
  li: a data-toggle="tab" href="#under-predictions" No-Derivatization

= render partial: 'jsmol/jsmol_modal_target'
.tab-content
  #tms-der-predictions.tab-pane.active
    - if params[:compound_name_prefix].present?
      = link_to "Download TMS".html_safe, retention_index_predictions_download_path(compound_name_prefix: params[:compound_name_prefix], phase: params[:phase], type: params[:type], smile: params[:smile], download_type: "tms"), 
        class: 'btn btn-primary', style: 'font-size: 1.5rem; margin-bottom: 1.0rem', 'data-no-turbolink': false, target: :_blank
    .table-responsive
        table.table.table-striped.table-condensed.table-hover.natural-products.depositions#user-prediction-tms-der-table
            thead
                = render 'header'
            tbody
                - if params[:type].eql?("TMS") || params[:type].eql?("TMS_AND_TBDMS")
                  = render partial: 'retention_index_predictions/prediction', locals: {:retention_index_prediction_submissions => @tms_der_predictions}
  #tbdms-der-predictions.tab-pane
    - if params[:compound_name_prefix].present?
      = link_to "Download TBDMS".html_safe, retention_index_predictions_download_path(compound_name_prefix: params[:compound_name_prefix], phase: params[:phase], type: params[:type], smile: params[:smile], download_type: "tbdms"), 
        class: 'btn btn-primary', style: 'font-size: 1.5rem; margin-bottom: 1.0rem', 'data-no-turbolink': false, target: :_blank
    .table-responsive
        table.table.table-striped.table-condensed.table-hover.natural-products.depositions#user-prediction-tbdms-der-table
            thead
                = render 'header'
            tbody
                - if params[:type].eql?("TBDMS") || params[:type].eql?("TMS_AND_TBDMS")
                  = render partial: 'retention_index_predictions/prediction', locals: {:retention_index_prediction_submissions => @tbdms_der_predictions}
  #under-predictions.tab-pane
    - if params[:compound_name_prefix].present?
      = link_to "Download No-Derivatization".html_safe, retention_index_predictions_download_path(compound_name_prefix: params[:compound_name_prefix], phase: params[:phase], type: params[:type], smile: params[:smile], download_type: "no-deriv"), 
        class: 'btn btn-primary', style: 'font-size: 1.5rem; margin-bottom: 1.0rem', 'data-no-turbolink': false, target: :_blank
    .table-responsive
        table.table.table-striped.table-condensed.table-hover.natural-products.depositions#user-prediction-under-table
            thead
                = render 'header'
            tbody
                = render partial: 'retention_index_predictions/prediction', locals: {:retention_index_prediction_submissions => @under_predictions}