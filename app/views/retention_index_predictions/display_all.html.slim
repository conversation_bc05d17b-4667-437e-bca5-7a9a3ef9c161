- if params[:notice].present?
  .alert-citation.alert
    p #{glyphicon(:'glyphicon-ok')} #{params[:notice]}

.page-header: h2 = title('Browsing Retention Index Predictions')
ul#submission_tabs.nav.nav-tabs
  li.active: a data-toggle="tab" href="#der-predictions" Derivatization
  li: a data-toggle="tab" href="#under-predictions" No-Derivatization
= render partial: 'jsmol/jsmol_modal_target'
.tab-content
  #der-predictions.tab-pane.active
    .table-responsive
        table.table.table-striped.table-condensed.table-hover.natural-products.depositions#total-prediction-der-table
            thead
                = render 'header'
            tbody
                = render partial: 'retention_index_predictions/prediction', locals: {:retention_index_prediction_submissions => @der_predictions}
  #under-predictions.tab-pane
    .table-responsive
        table.table.table-striped.table-condensed.table-hover.natural-products.depositions#total-prediction-under-table
            thead
                = render 'header'
            tbody
                = render partial: 'retention_index_predictions/prediction', locals: {:retention_index_prediction_submissions => @under_predictions}