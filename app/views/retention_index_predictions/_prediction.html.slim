-if retention_index_prediction_submissions.present?
	- retention_index_prediction_submissions.each do |prediction|
		tr
			td.natural-product-source_name 
				= "#{params[:compound_name_prefix]}#{prediction.compound_name}"
			td.retention-index-smiles
				- if prediction.compound_smiles.present?
					= link_to "Jsmol", jsmol_ri_modal_path(prediction.compound_smiles), 
						data: {toggle: 'modal', target: '#ri-jsmol', smiles: prediction.compound_smiles}, class: 'btn btn-primary', style: 'font-size: 1.2rem; margin-right: 1.0em; margin-bottom: 0.4rem; margin-top: 0.4rem;', 'data-no-turbolink': false
					= link_to "Download".html_safe, retention_index_predictions_mol_download_path(mol_smile: "#{prediction.compound_smiles}", name: "#{params[:compound_name_prefix]}#{prediction.compound_name}.mol"), id: 'download_mol_button', class: 'btn btn-primary',
						style: 'font-size: 1.2rem; margin-right: 1.0em; margin-bottom: 0.4rem; margin-top: 0.4rem;'
					span style="display: inline-block; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; width: 20rem; vertical-align: middle;"
						= link_to prediction.compound_smiles, jsmol_ri_modal_path(prediction.compound_smiles),
							data: {toggle: 'modal', target: '#ri-jsmol', smiles: prediction.compound_smiles}, class: "ri-modal-btn"
				- else
					= nah
			td = prediction.compound_stationary_phase
			td = prediction.compound_derivative_type
			td = prediction.predicted_RI
			td = "#{(prediction.created_at).to_s.split(" ")[0]} | #{(prediction.created_at).to_s.split(" ")[1]}"