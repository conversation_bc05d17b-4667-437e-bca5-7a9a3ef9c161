br
- if params[:notice].present?
  .alert-citation.alert
    p #{glyphicon(:'glyphicon-ok')} #{params[:notice]}

.page-header: h1 = title("Kovats' Retention Index Prediction (RIpred)")

.alert-citation.alert style="text-align: center"
  - if flash[:status] == 2
    p.success #{glyphicon(:'glyphicon-ok')} Prediction Successful!
  - elsif flash[:status] == 1
    p.par-success #{glyphicon(:'glyphicon-ok')} Nonviable Derivatization, Prediction Partially Successful for Base compound!
  - else
    p.error #{glyphicon(:'glyphicon-ok')} Prediction Failed
  br
  p You will be redirected to the Prediction Page in 4 Seconds
  meta HTTP-EQUIV="REFRESH" content="4; #{main_app.retention_index_predictions_path(type: params[:retention_index_prediction][:compound_derivative_type], phase: params[:retention_index_prediction][:compound_stationary_phase], smile: params[:structure_input], compound_name_prefix: params[:retention_index_prediction][:compound_name])}"

= image_tag("np-mrd_loader.svg", class: 'loader', style: 'display: block; margin-left: auto; margin-right: auto;')