.well
  .intro
    h4 Spectrum View (Predicted Using Uploaded Data)
    = render "shared/spectra_viewer", spectra_path: @nmrml_path

.well
  .intro
    h4 Submitted Chemical Shifts
    = render "final_chemical_shifts"
    = image_tag "#{@score_image_path}", class: 'featurette-image'

    table.table-standard
      thead
      .row
        .col-sm-6
          = link_to "Detailed Assignment Report", show_assignment_report_chemical_shift_submission_path


//.downloads
//  table.table-standard
//    thead
//      tr
//        th.data-set-col Quality Index
//        th.download-col Value(%)
//    tbody
//      tr
//        td No. of Atoms With Chemical Shift Data
//        td = @percentage_of_assigned_atoms
//      tr
//        td No. of Deposited Atoms With True Chemical Shift Data Similar to Predicted Values(Difference With Predcted Value < 0.20ppm)
//        td = @percentage_of_good_true_values
//
//      tr
//        td No. of Deposited Jcoupling Constant Data
//        td = @percentage_of_assigned_jcoupling
//
//      tr
//        td No. of Deposited Multiplet Data
//        td = @percentage_of_assigned_multiplet

.well
  .intro
    h4 Documentation

.downloads
  table.table-standard
    thead
      tr
        th.data-set-col Documentation Description
        th.download-col Download Link
    tbody
      tr
        td Chemical Shift Assignments File
        td = nah link_to "Download CSV", download_submitted_chemical_shift_path
      tr
        td nmrML-NMR Data Exchange Format File
        td = nah link_to "Download nmrML", nmrML_path
      tr
        td Structure File
        td = nah link_to "Download Mol", download_renumbered_mol