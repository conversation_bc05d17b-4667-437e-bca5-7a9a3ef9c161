.table-responsive
  table.table-bordered.table-hover.chemical-shifts
    thead
      tr
        th.atom-col-head Atom Type
        th.atom-col-head Atom No.
        th.atom-col-head Custom Atom No.
        th.atom-col-head Predicted Shift<br>( ppm )
        th.atom-col-head Measured Shift<br>( ppm )
        th.atom-col-head Predicted Multiplet Type
        th.atom-col-head Measured Multiplet Type
        th.atom-col-head Predicted J-Coupling<br>( Hz )
        th.atom-col-head Measured J-Coupling<br>( Hz )
    tbody
      - chemical_shifts.each do |cs|
        = f.fields_for :chemical_shifts, cs do |cs_f|
          // Uneditable fields
          = cs_f.hidden_field :atom_symbol
          = cs_f.hidden_field :atom_id
          = cs_f.hidden_field :chemical_shift_pred
          = cs_f.hidden_field :multiplet_pred
          = cs_f.hidden_field :jcoupling_pred

          tr
            td.grey-col
              b
                = cs.atom_symbol
            td.grey-col
              b
                = cs.atom_id
            td.grey-col
              = cs_f.text_field :custom_atom_id
            td.grey-col
              = cs.chemical_shift_pred
            td.white-col
              = cs_f.text_field :chemical_shift_true
            td.grey-col
              = cs.multiplet_pred
            td.white-col
              - if cs.multiplet_pred.length > 3
                = cs_f.text_field :multiplet_true, placeholder: "m"
              - else
                = cs_f.text_field :multiplet_true, placeholder: "#{cs.multiplet_pred}"
            td.grey-col
              = cs.jcoupling_pred
            td.white-col
              = cs_f.text_field :jcoupling_true, placeholder: "NA"
