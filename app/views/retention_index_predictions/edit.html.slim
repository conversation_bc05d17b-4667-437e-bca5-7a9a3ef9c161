br
br
h1 NP-Chemical Shift Deposition (Natural Product Chemical Shift Deposition)
br
.well-content.form-wrapper id="variable_html"
  h2 <b>Update Form for Natural Product Chemical Shift Data<b>
  br
  = form_for @chemical_shift_submission, :url => url_for(:controller => 'chemical_shift_submissions', :action => 'update'), :remote => true do |f|
    br
    .row
      .col-sm-6
        h2 Submitted Structure
      .col-sm-6
        h2 Fields Marked with '*' are Mandetory
    br
    .row
      .col-sm-6
        br
        .col-sm-6 id="view_3d" style="z-index:0"
          - if @chemical_shift_submission.renumberedMol
            div[class="mol_file" data-mol_file = "#{@chemical_shift_submission.renumberedMol}"]
            = render partial: "shared/view_3D"
          - elsif @chemical_shift_submission.natural_product.threeDmol
            div[class="mol_file" data-mol_file = "#{@chemical_shift_submission.natural_product.threeDmol}"]
            = render partial: "shared/view_3D" 
          - else
            = image_tag "#{@threeD_image_url}", class: 'featurette-image'
      .col-sm-6.hide-overflow
        = f.fields_for :chemical_shift_submission_meta_data do |ff|
          .row
            .col-sm-6
              .label-font-size
                = "Compound Name: "
            .col-sm-5
              .label-font-size
                = @chemical_shift_submission.natural_product.name
          br
          .row
            .col-sm-6
              = ff.label :provenance, "*Provenance: ",  required: true
            .col-sm-5
              = ff.select :provenance, provenance_collection, {:include_blank => 'Select One'}, {:required => true, :class => 'select', :id => "chemical_shift_provenance_edit"}
          br
          #chemical_shift_genus_edit
            .row
              .col-sm-6
                = ff.label :genus, "*Organism Genus Name: "
              .col-sm-5
                = ff.text_field :genus, class: "form-control-plaintext", placeholder: "Genus name"
            br
          #chemical_shift_species_edit
            .row
              .col-sm-6
                = ff.label :genus, "*Organism Species Name: "
              .col-sm-5
                = ff.text_field :species, class: "form-control-plaintext", placeholder: "Species name"
          br
          .row
            .col-sm-6
              = ff.label :physical_state_of_compound, "Physical State of Compound: "
            .col-sm-5
              - if @chemical_shift_submission.chemical_shift_submission_meta_data.physical_state_of_compound.empty?
                = ff.select :physical_state_of_compound, physical_state_of_compound_collection, {:include_blank => 'Select One'}
              - else
                = ff.select :physical_state_of_compound, physical_state_of_compound_collection
          br
          .row
            .col-sm-6
              = ff.label :melting_point, "Melting Point(°C): "
            .col-sm-5
              = ff.text_field :melting_point
          br
          .row
            .col-sm-6
              = ff.label :boiling_point, "Boiling Point(°C): "
            .col-sm-5
              = ff.text_field :boiling_point 
          br
          .row
            .col-sm-6
              = ff.label :literature_reference_type, "Literature Reference Type: "
            .col-sm-5
              = ff.select :literature_reference_type, literature_reference_types_collection, {:include_blank => 'Select One'}, {:required => true, :class => 'select'}
          br
          .row
            .col-sm-6
              = ff.label :literature_reference, "Literature Reference: "
            .col-sm-5
              = ff.text_field :literature_reference, class: "form-control-plaintext", placeholder: "PubMed ID/DOI/Book", required: true
          br
          .row
            .col-sm-3
              = f.button 'Next' , type: 'submit', name: 'state', value: 'post_np', :onclick => "submit_button_click()"
