h1
  b
    = "Thank you for submitting to NP-MRD, #{current_user.email.gsub(/@[^\s]+/,"").upcase}. Please verify your submission."
br
br
h2
  b
    = "Verification Form for Natural Product Chemical Shift Data"
br
= form_for @chemical_shift_submission do |f|

  table.padded
    tbody
      tr
        td
          b = "Creation Date:"
        td = @chemical_shift_submission.created_at.strftime("%Y-%m-%d")
      tr
        td
          b = "NP-MRD ID:"
        td = @natural_product.np_mrd_id
      tr
        td
          b = "Compound Name:"
        td = @natural_product.name
      tr
        td
          b = "Spectrum Type:"
        td = @chemical_shift_submission.chemical_shift_submission_meta_data.spectrum_type
  br
  = f.fields_for :chemical_shift_submission_meta_data do |ff|
    .row
      .col-sm-6
        .col-sm-6
          = ff.label :provenance, "*Provenance: ",  required: true
        .col-sm-6
          = ff.select :provenance, provenance_collection, {:include_blank => 'Select One'}, {:required => true, :class => 'select', :id => "chemical_shift_provenance_verification"}
      .col-sm-6
        .col-sm-6
          = ff.label :temperature, "Temperature(°C):"
        .col-sm-6
          = ff.text_field :temperature
    #chemical_shift_genus_verification
      br
      .row
        .col-sm-6
          .col-sm-6
            = ff.label :genus, "*Organism Genus Name:"
          .col-sm-6
            = ff.text_field :genus
        .col-sm-6
          .col-sm-6
            = ff.label :species, "*Organism Species Name:"
          .col-sm-6
            = ff.text_field :species
    br
    .row
      .col-sm-6
        .col-sm-6
          = ff.label :physical_state_of_compound, "Physical State of Compound:"
        .col-sm-6
          - if @meta_data.physical_state_of_compound.empty?
            = ff.select :physical_state_of_compound, physical_state_of_compound_collection, {:include_blank => 'Select One'}
          - else
            = ff.select :physical_state_of_compound, physical_state_of_compound_collection

          - if @meta_data.physical_state_of_compound.nil?
            .validation_message
              = "* No selection provided"

      .col-sm-6
        .col-sm-6
          = ff.label :melting_point, "Melting Point(°C):"
        .col-sm-6
          = ff.text_field :melting_point

          //- if @meta_data.melting_point == "NA"
          //  .validation_message
          //    = "* No melting point provided"
    br
    .row
      .col-sm-6
        .col-sm-6
          = ff.label :boiling_point, "Boiling Point(°C):"
        .col-sm-6
          = ff.text_field :boiling_point

          //- if @meta_data.boiling_point == "NA"
          //  .validation_message
          //    = "* No boiling point provided"
    br
    .row

      .col-sm-6
        .col-sm-6
          = ff.label :solvent, "Solvent:"
        .col-sm-6
          = ff.select :solvent, solvent_collection

          - if @meta_data.chemical_shift_standard == "TMS" && @meta_data.solvent == "H2O(Solvent-Peak)"
            .validation_message
              = "* Not compatible with chemical shift reference"

    br
    .row
      .col-sm-6
        .col-sm-6
          = ff.label :literature_reference_type, "Literature Reference Type:"
        .col-sm-6
          = ff.select :literature_reference_type, literature_reference_types_collection, {:include_blank => 'Select One'}, {:required => true, :class => 'select'}
    br
    .row
      .col-sm-6
        .col-sm-6
          = ff.label :spectrometer_frequency, "Spectrometer Frequency:"
        .col-sm-6
          = ff.select :spectrometer_frequency, spectrometer_frequency_collection
    br
    .row
      .col-sm-6
        .col-sm-6
          = ff.label :literature_reference, "Literature Reference:"
        .col-sm-6
          = ff.text_field :literature_reference, required: true

          - if (@meta_data.literature_reference_type == "PMID") && (is_number?(@meta_data.literature_reference) == false)
            .validation_message
              = "* PubMed IDs must only contain numbers"
          - elsif (@meta_data.literature_reference_type == "DOI") && (@meta_data.literature_reference.match(/10.\d{4}.+/) == nil)
            .validation_message
              = "* DOIs must begin with a prefix with the form 10.NNNN, where NNNN is a four digit number"

    br
    .row
      .col-sm-6
        .col-sm-6
          = ff.label :chemical_shift_standard, "Chemical Shift Reference:"
        .col-sm-6
          = ff.select :chemical_shift_standard, chemical_shift_reference_collection

          - if @meta_data.chemical_shift_standard == "TMS" && @meta_data.solvent == "H2O(Solvent-Peak)"
            .validation_message
              = "* Not compatible with solvent"
          - elsif (@meta_data.chemical_shift_standard == "DSS" || @meta_data.chemical_shift_standard == "TSP") && (@meta_data.solvent == "DMSO" || @meta_data.solvent == "CHCL3(Solvent-Peak)" || @meta_data.solvent == "Acetone(Solvent-Peak)" || @meta_data.solvent == "Methane(Solvent-Peak)")
              .validation_message
                = "* Not compatible with solvent"
  br
  br
  .row
    .col-sm-4
      h4 Submitted Structure with Atom Indexing
    .col-sm-8
      h4 Verify the Chemical Shift Data in the Table

  br
  .row
    .col-sm-4 id="view_3d" style="z-index:0"
        - @css = ChemicalShiftSubmission.find(@chemical_shift_submission.id)
        - if @css.renumberedMol

          div[class="mol_file" data-mol_file = "#{@css.renumberedMol}"]
          = render partial: "shared/view_3D"
        - elsif @css.natural_product.threeDmol
          div[class="mol_file" data-mol_file = "#{@css.natural_product.threeDmol}"]
          = render partial: "shared/view_3D"
        - else
          = image_tag "#{@threeD_image_url}", class: 'featurette-image'
    // put headers in a separate cell so that position: sticky will work in Safari
    .col-sm-8
      .grid-item-table
        = render partial: 'chemical_shift_submissions/shift_table_verification', locals: {:atom_symbol => @atom_symbol, :chemical_shifts => @chemical_shifts, :f => f}
      .grid-item
        = link_to "Quality Score rules", score_rules_chemical_shift_submission_path, 'data-popup' => true
      .grid-item
        = "Assignment Quality Score "
        = image_tag "#{@score_image_path}", class: 'featurette-image'
      .grid-item
        br
        = "I verify that the above information is correct."
        br
        // User submission
        .chemical-shifts-alert-radio
          = label_tag 'user_input', 'Yes'
          = radio_button_tag 'user_input', "Continue", true
          = label_tag 'user_input', 'No'
          = radio_button_tag 'user_input', "Cancel"

        br
        .row
          .col-sm-8
            = f.button 'Verified' , type: 'submit', name: 'state', value: 'post_verification', :onclick => "submit_button_click()"

javascript:
  if (($('#chemical_shift_provenance_verification').val() != "Other") || ($('#chemical_shift_provenance_verification').val() != "Biotransformation") || ($('#chemical_shift_provenance_verification').val() != "Chemical synthesis")){
    $('#chemical_shift_genus_verification').hide()
  } else {
    $('#chemical_shift_genus_verification').show()
  }
  $('#chemical_shift_provenance_verification').change(function () {
    if ($('#chemical_shift_provenance_verification').val() == "Other") {
      $('#chemical_shift_genus_verification').hide()
    } else if ($('#chemical_shift_provenance_verification').val() == "Biotransformation") {
      $('#chemical_shift_genus_verification').hide()
    } else if ($('#chemical_shift_provenance_verification').val() == "Chemical synthesis") {
      $('#chemical_shift_genus_verification').hide()
    } else {
      $('#chemical_shift_genus_verification').show()
    }
  })


