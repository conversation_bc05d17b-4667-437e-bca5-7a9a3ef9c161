.page-header
  h3 
    | Edit 
    = resource_name.to_s.humanize

.form-box
  = simple_form_for(resource, :as => resource_name, :url => registration_path(resource_name), :html => { :method => :put, :class => 'form-vertical' }) do |f|
    = f.error_notification
    = display_base_errors resource
    .form-group
      = f.input :name, :autofocus => true, input_html: { class: "form-control" }
    .form-group
     = f.input :email, :required => true, input_html: { class: "form-control" }
    .form-group
      = f.input :password, :autocomplete => "off", :hint => "leave it blank if you don't want to change it", :required => false, input_html: { class: "form-control" }
    .form-group
      = f.input :password_confirmation, :required => false, input_html: { class: "form-control" }
    .form-group
      = f.input :current_password, :hint => "we need your current password to confirm your changes", :required => true, input_html: { class: "form-control" }
    .form-group.end-group
      = f.button :submit, 'Update', :class => 'btn-submit'
      