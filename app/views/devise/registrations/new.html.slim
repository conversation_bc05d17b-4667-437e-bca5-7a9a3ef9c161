.page-header
  h3 Sign up

.form-box
  = simple_form_for(resource, :as => resource_name, :url => registration_path(resource_name), :html => {:class => 'form-vertical' }) do |f|
    = f.error_notification
    = display_base_errors resource
    .form-group
      = f.input :name, :autofocus => true, input_html: { class: "form-control" }
    .form-group
      = f.input :email, :required => true, input_html: { class: "form-control" }
    .form-group
      = f.input :password, :required => true, input_html: { class: "form-control" }
    .form-group
      = f.input :password_confirmation, :required => true, input_html: { class: "form-control" }

    .form-group.end-row
      = f.button :submit, 'Sign up', :class => 'btn-submit'
      br
      = render "devise/shared/links"
