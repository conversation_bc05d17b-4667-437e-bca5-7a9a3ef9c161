.page-header
  h3 Sign in

.form-box
  = simple_form_for(resource, :as => resource_name, :url => session_path(resource_name), :html => {:class => 'form-vertical' }) do |f|
    .form-group
      = f.input :email, :autofocus => true, input_html: { class: "form-control" }
    .form-group
      = f.input :password, input_html: { class: "form-control" }
    .checkbox
      = f.label :remember_me
      = f.check_box :remember_me, class: "form-control", :as => :boolean if devise_mapping.rememberable?

    .form-group.end-row
      = f.button :submit, "Sign in", :class => 'btn-submit'
      br
      = render "devise/shared/links"
