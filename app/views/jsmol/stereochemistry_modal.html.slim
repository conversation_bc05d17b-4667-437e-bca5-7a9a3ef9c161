.modal-header
  button.close aria-hidden="true" data-dismiss="modal" type="button"  &times;
  h4.modal-title Showing Stereo Labels for #{@metabolite.name}
.modal-body
  - if @metabolite_structure.nil?
    p Not Available
  - else
    .text-center#stereochemistry-view
      #view-stereochemistry-3d [data-structure="#{@metabolite_structure}" data-rs_configs="#{@rs_labels}"
        style="width:720px;margin-left:auto;margin-right:auto;z-index: 0; position: relative;"]
      = render partial: 'view_stereochemistry'
    .text-center
      .btn-group.btn-group-lg.btn-group-toggle
        = link_to "Hide Labels", "javascript:toggleLabelsStereochemistry()", class: 'btn btn-danger', id: "toggle-labels",
                title: 'Hide Labels'
.modal-footer
  - unless @metabolite_structure.nil?
    = link_to "Export as PNG", "javascript:downloadJsmolStereochemistry('#{@metabolite.name.gsub(" ", "_")}')",
            class: 'btn btn-default', title: 'Download Image'
  button.btn.btn-default data-dismiss="modal" type="button"  Close