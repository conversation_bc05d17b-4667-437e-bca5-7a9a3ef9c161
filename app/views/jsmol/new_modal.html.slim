.modal-header
  button.close aria-hidden="true" data-dismiss="modal" type="button"  &times;
  h4.modal-title Showing structure for #{@metabolite.name}
.modal-body
  - if @metabolite_structure.nil?
    p Not Available
  - else
    .text-center#structure-view
      #view-3d [data-structure="#{@metabolite_structure}"
        style="width:720px;margin-left:auto;margin-right:auto;z-index: 0; position: relative;"]
      = render partial: 'view_structure'
    .text-center
      .btn-group.btn-group-lg.btn-group-toggle
        = link_to "Hide Labels", "javascript:toggleLabels()", class: 'btn btn-danger', id: "toggle-labels",
                title: 'Hide Labels'
        = link_to "Hide Hydrogens", "javascript:toggleHydrogens()", class: 'btn btn-danger', id: "toggle-H",
                title: 'Hide Hydrogens'
        = link_to "Recenter", "javascript:recenter()", class: 'btn btn-info', title: 'Recenter'
        = link_to glyphicon('fullscreen'), "javascript:zoomFit()", class: 'btn btn-info',
                title: 'Zoom to Fit', id: 'fs-jsmol'
.modal-footer
  - unless @metabolite_structure.nil?
    = link_to "Export as PNG", "javascript:downloadJsmol('#{@metabolite.name.gsub(" ", "_")}')",
            class: 'btn btn-default', title: 'Download Image'
  button.btn.btn-default data-dismiss="modal" type="button"  Close