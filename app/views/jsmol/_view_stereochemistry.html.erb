<script>
    function loadScript(url, callback){
        let script = document.createElement("script")
        script.type = "text/javascript";

        if (script.readyState){  //IE
            script.onreadystatechange = function(){
                if (script.readyState === "loaded" ||
                    script.readyState === "complete"){
                    script.onreadystatechange = null;
                    callback();
                }
            };
        }
        else {  //Others
            script.onload = function(){
                callback();
            };
        }
        script.src = url;
        document.getElementsByTagName("head")[0].appendChild(script);
    }

    function run_jsmol(structure){
        // Height and width of the JSmol applet
        const width = 720, height = 562

        const load_type = "SMILES"

        const labels = "label %a;";
        const background = "background white;";

        let Info = {
            width: width,
            height: height,
            debug: false,
            color : "#EAF0F2",
            use: "HTML5",   // JAVA HTML5 WEBGL are all options
            j2sPath: "/jsmol/j2s", // this needs to point to where the j2s directory is.
            multipleBondSpacing : 0.9,
            zoomlarge: true,
            allowJavaScript: true

        };

        Info.script = 'load ' + load_type + ' "' +
            structure +
            '"; color labels black; font labels 20; set antialiasDisplay; wireframe 15; spacefill 55;' +
            labels + background + stereochemistryLabels()
        $("#view-stereochemistry-3d").html(Jmol.getAppletHtml('jmolApplet3', Info))

    }

    const structure = $('#stereochemistry-view > #view-stereochemistry-3d').data('structure')

    try {
        run_jsmol(structure)
    }
    catch(e){
        loadScript("/jsmol/JSmol.min.js", function(){
            run_jsmol(structure)
        })
    }
</script>