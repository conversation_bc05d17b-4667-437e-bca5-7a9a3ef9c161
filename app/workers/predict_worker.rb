require 'open3'

class PredictWorker
  include Sidekiq::Worker
  include PredictHelper
  sidekiq_options retry: true

  def perform(query_id)

    sleep(5) # Wait because if sidekiq fails too quickly the job is left "hanging" (marked queued)
    query = PredictQuery.find_by_id(query_id)

    # Set up directories to save the results
    outputdir = File.join("public/queries", query.secret_id, "output")
    FileUtils.mkdir_p(File.join(Rails.root, outputdir)) unless File.directory?(File.join(Rails.root, outputdir))
    output_file_basename = 'output.txt'
    output_file = File.join(outputdir, output_file_basename)

    # Docker-related paths and variables - used if cfmid.yml contains 'use_cfmid_docker: true'
    use_docker = CFMID_CONFIG[:use_cfmid_docker] || false
    docker_img = CFMID_CONFIG[:docker_img] || 'tmic/cfmid:latest'
    output_file_for_docker = File.join('/root/output', output_file_basename) # Path to give to the program running within the Docker container
    outputdir_for_docker = File.join(CFMID_CONFIG[:host_queries_path], query.secret_id, "output") # Used as argument to Docker's -v option

    run_cfmid = false
    error = nil
    runtime = nil

    # 1) Check if there is already precomputed spectra for this compound
    # If so, make this the output file
    # puts "STEP 1"
    check_for_precomputed(output_file, query)
    # 2) If no precomputed found, then:
    # For ESI, we run the fragmenter first
    if query.compound_is_inchi?
      smiles = Jchem.structure_to_smiles(query.compound)
    else
      smiles = query.compound
    end
    begin
      if !safe_file_exist(output_file) && query.spectra_type == "ESI"
        # puts "STEP 2"
        logger.debug('STEP 2')
        # Need to make the input into SMILES if it's not already, for the fragmenter

        if use_docker
          # Call Dockerized version
          query_string = "java -jar msrb-fragmenter.jar -ismi \'#{smiles}\' -a \'#{query.adduct_type}\' -o #{output_file_for_docker}"
          query_string = "docker run --rm=true -v #{outputdir_for_docker}:/root/output -i #{docker_img} sh -c \"#{query_string}\""
          logger.debug(query_string)
        else
          query_string = "java -jar #{Rails.root}/lib/fragmenter/msrb-fragmenter-1-0-8.jar -ismi \"#{smiles}\" -a \"#{query.adduct_type}\" -o \"#{Rails.root}/#{output_file}\""
        end

        stdin, stdout, stderr = Open3.popen3(query_string)

        error = stderr.gets(nil).to_s
        runtime = stdout.gets(nil).to_s

        stdin.close
        stdout.close
        stderr.close

        # Remove content from stderr that does not reflect an error.
        error.gsub!('c: /root/output', '')

        # Statuses 1-2 mean the fragmenter was successful so an output file was produced
        # Status 3 means the fragmenter could only produce results for other adducts
        # Status 4 means the compound is a lipid we don't support, so stop and return an error
        # Status 5 means the compound is not a lipid, so run CFM-ID combinatorial prediction method
        status = runtime.match(/STATUS REPORT = (\d)/)&.captures&.first
        if [1,2].include?(status.to_i)
          run_cfmid = false
          # Update the output file name to match what the fragmenter produces
          output_file += "_#{query.adduct_type}.log"
        elsif status.to_i == 3
          # NOTE set true for now ,  let cfmid msml do the rest
          run_cfmid = true
          File.delete(output_file) if safe_file_exist(output_file)
          # TO DO: Other adducts will be generated, return links to these files?
          error = "The adduct specified is not covered for the lipid class of the query compound."
        elsif status.to_i == 4
          # NOTE set true for now , let cfmid msml do the rest
          run_cfmid = true
          File.delete(output_file) if safe_file_exist(output_file)
          error = "Invalid chemical class. The query compound belongs to a lipid class that is neither covered in the current version of the fragmenter nor suitable for the combinatorial prediction method."
        elsif status.to_i == 5
          run_cfmid = true
          File.delete(output_file) if safe_file_exist(output_file)
          #error = "This compound is not handled by MSRB-Fragmenter"
        end
      end
    rescue Exception => e
      run_cfmid = false
      error += e.message
      File.delete(output_file) if safe_file_exist(output_file)
    end
    
    # 3) If the fragmenter didn't produce anything, we can still look for existing spectra
    # using the neutral adduct (we'll then add the peak in)
    # This step was removed Jan 21, 2020 when the database was updated to replace
    # 'Neutral' adduct types with [M+H]+, [M-H]- or [M]+ adduct types.

    # 4) If we still haven't found anything, run cfm-predict unless the fragmenter
    # told us not to
    begin
      if run_cfmid && !safe_file_exist(output_file)
        # puts "STEP 4"
        logger.debug('STEP 4')
        # Run the query
        error = nil
        runtime = nil

        if use_docker
          # Call Dockerized version
          query_string = "cfm-predict '#{smiles}' #{query.threshold} #{query.param_file} #{query.config_file} 1 #{output_file_for_docker} #{query.post_process?}"
          query_string = "docker run --rm=true -v #{outputdir_for_docker}:/root/output -i #{docker_img} sh -c \"#{query_string}\""
          logger.debug(query_string)
        else
          query_string = "cfm-predict \"#{smiles}\" #{query.threshold} #{query.param_file} #{query.config_file} 1 #{output_file} #{query.post_process?}"
        end

        if CFMID_CONFIG[:extra_params]
          query_string = CFMID_CONFIG[:extra_params] + " " + query_string
        end
        stdin, stdout, stderr = Open3.popen3(query_string)

        cfm_error = stderr.gets(nil).to_s
        cfm_runtime = stdout.gets(nil).to_s

        stdin.close
        stdout.close
        stderr.close

        # Combine fragmenter output with cfm-id output, if present
        error = cfm_error
        runtime = cfm_runtime
      end
    rescue Exception => e
      error += e.message
    end
    if error.blank?
      if query.update_attributes(runtime: runtime, error: error, output_file: File.new(output_file) )
        parsed_output = create_predict_output(output_file, query)
        if (parsed_output == "Failed") || !query.update_attributes( parsed_output: parsed_output )
          if query.error.nil?
            query.update_attributes( error: "Error parsing output, please try again or contact an administrator." )
          end
          return nil
        end
        # Then it was successful!
      else
        if query.error.nil?
          query.update_attributes( runtime: "Runtime Error: #{runtime}", error: "Server error, please try again or contact an administrator. #{error}" )
        end
        return nil
      end
    else
      query.update_attributes( runtime: "#{runtime} #{query_string}", error: error )
      return nil
    end
  end
end
