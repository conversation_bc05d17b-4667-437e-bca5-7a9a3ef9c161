class GenerateMolExcelWorker
  include Sidekiq::Worker
  require 'fileutils'
  require 'open-uri'
  require 'zip'
  require 'csv'
  sidekiq_options retry: false

  def perform(backend_dir,nmr_pred_dir,draw_mol_script_basename,backend_batch_dir,excel_generator_script_with_path,fpath,project_name,list_of_input_files_for_excel_generator,batch_values,batch_upload_user_id,batch_upload_id,batch_directory)
    # Do something
    @backend_dir = backend_dir
    puts("@backend_dir in job = #{@backend_dir}")
    @nmr_pred_dir = nmr_pred_dir
    puts("@nmr_pred_dir in job = #{@nmr_pred_dir}")
    @draw_mol_script_basename = draw_mol_script_basename
    puts("@draw_mol_script_basename in job = #{@draw_mol_script_basename}")
    @backend_batch_dir = backend_batch_dir
    puts("@backend_batch_dir in job = #{@backend_batch_dir}")
    @excel_generator_script_with_path = excel_generator_script_with_path
    puts("@excel_generator_script_with_path in job = #{@excel_generator_script_with_path}")
    @fpath = fpath
    puts("@fpath in job = #{@fpath}")
    @project_name = project_name
    puts("@project_name in job = #{@project_name}")
    @list_of_input_files_for_excel_generator = list_of_input_files_for_excel_generator
    puts("@list_of_input_files_for_excel_generator in job = #{@list_of_input_files_for_excel_generator}")
    @batch_values = batch_values
    puts("@batch_values in job = #{@batch_values}")
    @batch_upload_user_id = batch_upload_user_id
    puts("@batch_upload_user_id in job = #{@batch_upload_user_id}")
    @batch_upload_id = batch_upload_id
    puts("@batch_upload_id in job = #{@batch_upload_id}")
    @batch_directory = batch_directory
    puts("@batch_directory in job = #{@batch_directory}")
    @var = 1
    puts("@list_of_input_files_for_excel_generator  in job = #{@list_of_input_files_for_excel_generator }")
    CSV.open("#{@list_of_input_files_for_excel_generator}", "wb") do |csv|  
      @batch_values.each do |b_v|
        @smiles = b_v[1]
        #creating mol, imgae and prediction file
        @path_to_temp_model, @path_to_temp_image, @path_to_csv, @compound_name = BatchUpload.DrawMol(@smiles,@batch_directory,@backend_dir,@nmr_pred_dir,@draw_mol_script_basename,@batch_upload_id,b_v[0],@batch_upload_user_id,@var)
        #creating mol, imgae and prediction file
        puts("@path_to_temp_model  in job = #{@path_to_temp_model}")
        puts("@path_to_temp_image  in job = #{@path_to_temp_image}")
        puts("@path_to_csv@path_to_csv  in job = #{@path_to_csv}")
        puts("@compound_name  in job = #{@compound_name}")
        csv<<["#{@compound_name}","#{@path_to_csv}","#{@path_to_temp_image}"]
        @var = @var + 1
      end
    end
    puts("@var = #{@var}")
    BatchUpload.GenerateExcel(@project_name,@excel_generator_script_with_path, @list_of_input_files_for_excel_generator, @batch_directory)
  end
end