require 'open3'
require 'fileutils'

class NeutralLossWorker
  include Sidekiq::Worker
  include NeutralLossHelper
  sidekiq_options backtrace: true, retry:true
  def perform(query_id, params)

    sleep(5) # Wait because if sidekiq fails too quickly the job is left "hanging" (marked queued)
    start_time = Time.now
    query = NeutralLossQuery.find_by_id(query_id)

    # Set up directories to save the results
    outputdir = File.join("public/queries", query.secret_id, "output")
    FileUtils.mkdir_p(File.join(Rails.root, outputdir)) unless File.directory?(File.join(Rails.root, outputdir))
    output_file_basename = 'output.txt'
    output_file = File.join(Rails.root, outputdir, output_file_basename)
    File.new(File.join(Rails.root, outputdir, output_file_basename), 'w+')
    
    

    
    spectrum_results = []
    if params["submit_or_find_or_nl"] == "find"
      spectrum_results = Specdb::MsMsFindCandidatesSearch.find_candidates_search(params)
    elsif params["submit_or_find_or_nl"] == "neutral-loss"
      params[:energy_level] = 10
      spectrum_results_low = Specdb::MsMsFindCandidatesSearch.nl_search(params)
      # # puts "LOW SPECTRUM RESULTS: #{spectrum_results_low}"
      params[:energy_level] = 20
      spectrum_results_med = Specdb::MsMsFindCandidatesSearch.nl_search(params)
      puts "MED SPECTRUM RESULTS: #{spectrum_results_med}"
      params[:energy_level] = 40
      spectrum_results_high = Specdb::MsMsFindCandidatesSearch.nl_search(params)
      puts "LOW #{spectrum_results_low.length}"
      puts "Med #{spectrum_results_med.length}"
      puts "High #{spectrum_results_high.length}"
      spectrum_results = spectrum_results_low + spectrum_results_med + spectrum_results_high

    end
    puts "BEFORE SPECTUM RESULTS"
    spectrum_results.each do |t|
      puts "#{t.database_id} - #{t.database}"

    end
    runtime = Time.now - start_time
    error = ""
    # # puts spectrum_results[0]
    # # puts spectrum_results[1]
    # # puts spectrum_results[2]
    # puts spectrum_results.empty?
    # puts spectrum_results.blank?
    # puts spectrum_results.nil?
    # if !spectrum_results.empty?
    if !spectrum_results.empty?
      parsed_output = make_result_file(spectrum_results, output_file)
    else 
      parsed_output = "Failed"
    end
    puts spectrum_results.empty?
    puts "INSIDE THE EMPTY QUEries"
    if query.update_attributes( runtime: runtime, output_file: File.new(File.join(Rails.root, outputdir, output_file_basename)), error: error)
      puts "here"
      # parsed_output = make_result_file(spectrum_results)
      if (parsed_output == "Failed") || !query.update_attributes( parsed_output: parsed_output )
        if query.error.nil?
          query.update_attributes( error: "Error parsing output, please try again or contact an administrator." )
        end
        return nil
      end
      # Then it was successful!
    else
      if query.error.nil?
        query.update_attributes( runtime: runtime, error: "Server error, please try again or contact an administrator." )
      end
      return nil
    end
    # # There are no errors but the output file is NOT present
    # elsif error.blank? && !safe_file_exist(output_file)
    #   if !query.update_attributes( runtime: runtime, error: "Runtime error - check that your input are valid." )
    #     return nil
    #   end
    # # There are errors, and we failed to update the query??
    # elsif !query.update_attributes( runtime: runtime, error: "#{error}, Server error, please try again or contact an administrator."  )
    #   return nil
    # end

    
    
    # temp_results = []
    # databases = Hash.new{|h,k| h[k] = Array.new}
    # puts "MAKEING FILE"
    # # []
    # id_low = nil
    # id_med = nil
    # id_high = nil
    # spectrum_results.each do |f|
    #   puts f
    #   next if f.scoring_function == 0
    #   if f.collision_energy_voltage == 10
    #     id_low = f.id
    #   elsif f.collision_energy_voltage == 20
    #     id_med = f.id
    #   else
    #     id_high = f.id
    #   end
    #   if databases.key?(f.database)
    #     # puts databases[f.database][0]
    #     # puts f.scoring_function.to_f
    #     new_scoring_function = databases[f.database][3].to_f + f.scoring_function.to_f
          
    #     databases[f.database] = [id_low, id_med, id_high, new_scoring_function, f.formula, f.database, f.database_id, f.inchi_key, "#{f.predicted}"]
    #     # databases[f.database][0] = databases[f.database][0][0] + f.scoring_function

    #   else 
    #     databases[f.database] = [id_low, id_med, id_high, f.scoring_function, f.formula, f.database, f.database_id, f.inchi_key, "#{f.predicted}"]
    #   end
    #   # temp_results << [id_low, id_med, id_high, f.scoring_function, f.formula, f.database, f.database_id, f.inchi_key, "#{f.predicted}"]
    # end

    # databases.each do |h, f|
    #   temp_results << "#{f[0]} #{f[1]} #{f[2]} #{f[3]} #{f[5]}-#{f[6]} #{f[7]} #{f[8]}"
    # end


    # # my_file = 
    # puts File.join(Rails.root, outputdir, output_file_basename)
    # # # my_file.write "HELLO MAN"
    # File.open(File.join(Rails.root, outputdir, output_file_basename), "w+") do |t|
    #   t.puts "HELLO MOTHER FUCKER"
    # end
    # temp_results.each do |u|
    #   my_file.write u
    #   my_file.write "\n"
    # end
    # puts "Done SIDEKIQ WORKER"
    # puts output_file
    # make_result_file(spectrum_results, File.join(Rails.root, "public/queries", query.secret_id, output_file_basename))
    
    # # temp_results = []
    # # databases = Hash.new{|h,k| h[k] = Array.new}

    # # # []
    # # spectrum_results.each do |f|
    # #   next if f.scoring_function == 0
    # #   if databases.key?(f.database)
    # #     # puts databases[f.database][0]
    # #     # puts f.scoring_function.to_f
    # #     new_scoring_function = databases[f.database][1].to_f + f.scoring_function.to_f
    # #     databases[f.database] = [f.id, new_scoring_function, f.formula, f.database, f.database_id, f.inchi_key, "#{f.predicted}"]
    # #     # databases[f.database][0] = databases[f.database][0][0] + f.scoring_function

    # #   else 
    # #     databases[f.database] = [f.id, f.scoring_function, f.formula, f.database, f.database_id, f.inchi_key, "#{f.predicted}"]
    # #   end
    # #   # temp_results << [f.compound_id, f.scoring_function, f.formula, f.database, f.database_id, f.inchi_key, f.predicted, f.collision_energy_voltage]
    # # end

    # # databases.each do |h, f|
    # #   temp_results << "#{f[0]} #{f[1]} #{f[3]}-#{f[4]} #{f[5]} #{f[6]} #{f[7]}"
    # # end


    # # my_file = File.open("/Users/<USER>/CFMID/cfm-id/public/output", "w")
    # # temp_results.each do |u|
    # #   my_file.write u
    # #   my_file.write "\n"
    # # end

    # parsed_output = "Successful"
  
    #   # puts(spectrum_results)

    # # @search.each do |f|
    # #   puts f.compound.cas
    # #   puts f.scoring_function
    # # end

    # # Steps for the Neutral Loss Search
    # # 1. Call SpecDB Search
    #   # a. @search = Specdb::NmrOneDSearch.create(search_params)
    
  end

end
