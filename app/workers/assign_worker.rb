class AssignWorker
  include Sidekiq::Worker
  include AssignHelper
  sidekiq_options backtrace: true

  def perform(query_id)

    sleep(15) # Wait because if sidekiq fails too quickly the job is left "hanging" (marked queued)
    query = AssignQuery.find_by_id(query_id)

    # Set up directories to save the results
    outputdir = File.join("public/queries", query.secret_id, "output")
    FileUtils.mkdir_p(File.join(Rails.root, outputdir)) unless File.directory?(File.join(Rails.root, outputdir))
    output_file_basename = 'output.txt'
    output_file = File.join(outputdir, output_file_basename)

    # Docker-related paths and variables - used if cfmid.yml contains 'use_cfmid_docker: true'
    use_docker = CFMID_CONFIG[:use_cfmid_docker] || false
    docker_img = CFMID_CONFIG[:docker_img] || 'tmic/cfmid:latest'
    output_file_for_docker = File.join('/root/output', output_file_basename) # Path to give to the program running within the Docker container
    outputdir_for_docker = File.join(CFMID_CONFIG[:host_queries_path], query.secret_id, "output") # Used as argument to Docker's -v option

    # Set up log file
    logfile = "public/queries/" + query.secret_id + "/log.txt"
    log = open(logfile, 'a')
    if query.compound_is_inchi?
      smiles = Jchem.structure_to_smiles(query.compound)
    else
      smiles = query.compound
    end

    # Build query command
    if use_docker
      # Call Dockerized version
      query_string = "cfm-annotate \'#{smiles}\' #{query.input_file.path} #{query.print_spectra_id} #{query.ppm_mass_tol} #{query.abs_mass_tol} #{query.param_file} #{query.config_file} #{output_file_for_docker}"
      query_string = "docker run --rm=true -v #{outputdir_for_docker}:/root/output -v #{CFMID_CONFIG[:host_system_path]}:/cfmid/public/system -i #{docker_img} sh -c \"#{query_string}\""
      logger.debug(query_string)
    else
      query_string = "cfm-annotate \"#{smiles}\" #{query.input_file.path} #{query.print_spectra_id} #{query.ppm_mass_tol} #{query.abs_mass_tol} #{query.param_file} #{query.config_file} #{output_file}"
    end

    if CFMID_CONFIG[:extra_params]
      query_string = CFMID_CONFIG[:extra_params] + " " + query_string
    end
    log.write(query.secret_id + "\n")
    log.write(query_string + "\n")
    log.flush

    # Run the query with timeout
    # Note: A separate timeout may be set in the param_config.txt file (e.g. /apps/cfmid/project/shared/data/trained_models/esi_msms_models/metab_se_cfm/param_config.txt).
    timeout = 570 # in seconds
    begin
      # stdout, stderr pipes
      rout, wout = IO.pipe
      rerr, werr = IO.pipe
      stdout, stderr = nil

      pid = Process.spawn(query_string, pgroup: true, :out => wout, :err => werr)
      log.write("cfm-annotate pid = " + pid.to_s + "\n")
      log.flush

      Timeout.timeout(timeout) do
        Process.waitpid(pid)
        if $?.exitstatus != 0
          log.write("cfm-annotate exited with non-zero exit status\n")
          log.write("  exit status: " + ($?.exitstatus).to_s + "\n")
          log.flush
          if !query.update_attributes( error: "Runtime error - check that your inputs are valid." )
            return nil
          end
          return
        end

        # close write ends so we can read from them
        wout.close
        werr.close

        stdout = rout.readlines.join
        stderr = rerr.readlines.join

        log.write("stdout:\n")
        log.write(stdout + "\n\n")
        log.write("stderr:\n")
        log.write(stderr + "\n\n")
        log.flush
        log.close
      end

    rescue Timeout::Error
      log.write("cfm-annotate killed due to timeout\n")
      log.flush
      log.close
      query.update_attributes( error: "Job timed out. Was your input too large? Please try again or contact an administrator." )
      Process.kill(-9, pid)
      Process.detach(pid)
      return
    ensure # will be run even if return above
      wout.close unless wout.closed?
      werr.close unless werr.closed?
      # dispose the read ends of the pipes
      rout.close
      rerr.close
    end


    # Additional error checking
    if stderr.blank?
      if query.update_attributes( runtime: stdout, error: stderr, output_file: File.new(output_file) )
        parsed_output = create_assign_output(output_file, query)
        if (parsed_output == "Failed") || !query.update_attributes( parsed_output: parsed_output )
          if query.error.nil?
            query.update_attributes( error: "Error parsing output, please try again or contact an administrator." )
          end
          return nil
        end
        # Then it was successful!
      else
        if query.error.nil?
          query.update_attributes( error: "Server error, please try again or contact an administrator." )
        end
        return nil
      end
    elsif stderr.blank? && !safe_file_exist(output_file)
      if !query.update_attributes( runtime: stdout, error: "Runtime failure - check that your inputs are valid." )
        return nil
      end
    elsif !query.update_attributes( runtime: stdout, error: stderr )
      return nil
    end
  end
end