require 'open3'

class IdentifyWorker
  include Sidekiq::Worker
  include IdentifyHelper
  sidekiq_options backtrace: true, retry:true
  def perform(query_id)

    sleep(5) # Wait because if sidekiq fails too quickly the job is left "hanging" (marked queued)
    query = IdentifyQuery.find_by_id(query_id)

    # Set up directories to save the results
    outputdir = File.join("public/queries", query.secret_id, "output")
    FileUtils.mkdir_p(File.join(Rails.root, outputdir)) unless File.directory?(File.join(Rails.root, outputdir))
    output_file_basename = 'output.txt'
    output_file = File.join(outputdir, output_file_basename)

    # Docker-related paths and variables - used if cfmid.yml contains 'use_cfmid_docker: true'
    use_docker = CFMID_CONFIG[:use_cfmid_docker] || false
    docker_img = CFMID_CONFIG[:docker_img] || 'tmic/cfmid:latest'
    output_file_for_docker = File.join('/root/output', output_file_basename) # Path to give to the program running within the Docker container
    outputdir_for_docker = File.join(CFMID_CONFIG[:host_queries_path], query.secret_id, "output") # Used as argument to Docker's -v option

    candidate_spectra_file = nil

    # Run the query
    begin
      if use_docker
        # Call Dockerized version
        if query.database.blank?
          # # candidate_spectra_file = outputdir + "/candidate_spectra.msp"
          # candidate_spectra_file = "/root/output/candidate_spectra.msp"
          # query_string = "cfm-id #{query.input_file.path} \"#{query.print_spectra_id}\" #{query.candidates_file.path} #{query.num_results} #{query.ppm_mass_tol} #{query.abs_mass_tol} #{query.threshold} #{query.param_file} #{query.config_file} #{query.cfm_id_scoring_function} #{query.post_process?} #{output_file_for_docker} #{candidate_spectra_file}"
          candidate_spectra_file = outputdir + "/candidate_spectra.msp"
          candidate_spectra_file_for_docker = '/root/output/candidate_spectra.msp'
          query_string = "cfm-id #{query.input_file.path} \"#{query.print_spectra_id}\" #{query.candidates_file.path} #{query.num_results} #{query.ppm_mass_tol} #{query.abs_mass_tol} #{query.threshold} #{query.param_file} #{query.config_file} #{query.cfm_id_scoring_function} #{query.post_process?} #{output_file_for_docker} #{candidate_spectra_file_for_docker}"
        else
          query_string = "cfm-id-precomputed #{query.input_file.path} \"#{query.print_spectra_id}\" #{query.candidates_file.path} #{query.num_results} #{query.ppm_mass_tol} #{query.abs_mass_tol} #{query.cfm_id_scoring_function} #{output_file_for_docker}"
        end
        query_string = "docker run --rm=true -v #{outputdir_for_docker}:/root/output -v #{CFMID_CONFIG[:host_system_path]}:/cfmid/public/system -v #{CFMID_CONFIG[:host_cmpd_id_path]}:/cfmid/public/spectra -i #{docker_img} sh -c \"#{query_string}\""
        logger.debug(query_string)
      else
        if query.database.blank?
          candidate_spectra_file = outputdir + "/candidate_spectra.msp"
          query_string = "cfm-id #{query.input_file.path} \"#{query.print_spectra_id}\" #{query.candidates_file.path} #{query.num_results} #{query.ppm_mass_tol} #{query.abs_mass_tol} #{query.threshold} #{query.param_file} #{query.config_file} #{query.cfm_id_scoring_function} #{query.post_process?} #{output_file} #{candidate_spectra_file}"
        else
          query_string = "cfm-id-precomputed #{query.input_file.path} \"#{query.print_spectra_id}\" #{query.candidates_file.path} #{query.num_results} #{query.ppm_mass_tol} #{query.abs_mass_tol} #{query.cfm_id_scoring_function} #{output_file}"
        end
      end

      if CFMID_CONFIG[:extra_params]
        query_string = CFMID_CONFIG[:extra_params] + " " + query_string
      end
      stdin, stdout, stderr = Open3.popen3(query_string)

      error = stderr.gets(nil)
      runtime = stdout.gets(nil)

      stdin.close
      stdout.close
      stderr.close
    rescue Exception => e
      error = e.message
    end
    logger.debug("ERROR => #{error}") if error

    # There are no errors and the output file is present
    if error.blank?
      if query.update_attributes( runtime: runtime, error: error,
        output_file: File.new(output_file), candidate_spectra_file: (candidate_spectra_file.present? ? File.new(candidate_spectra_file) : nil))
        parsed_output = create_identify_output(output_file, query)
        if (parsed_output == "Failed") || !query.update_attributes( parsed_output: parsed_output )
          if query.error.nil?
            query.update_attributes( error: "Error parsing output, please try again or contact an administrator." )
          end
          return nil
        end
        # Then it was successful!
      else
        if query.error.nil?
          query.update_attributes( runtime: runtime, error: "Server error, please try again or contact an administrator." )
        end
        return nil
      end
    # There are no errors but the output file is NOT present
    elsif error.blank? && !safe_file_exist(output_file)
      if !query.update_attributes( runtime: runtime, error: "Runtime error - check that your input are valid." )
        return nil
      end
    # There are errors, and we failed to update the query??
    elsif !query.update_attributes( runtime: runtime, error: "#{error}, Server error, please try again or contact an administrator."  )
      return nil
    end
  end
end
