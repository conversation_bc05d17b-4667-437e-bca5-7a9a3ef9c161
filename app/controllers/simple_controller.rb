class SimpleController < ApplicationController

	def home
	end

	def about
	end

	def downloads
	end

	def statistics
    @specdb_stats = Specdb::Statistic.all( :params => { :database => 'NP-MRD' } ).first
	end

  def contact
  end

  def textquery
  end

  def utilities
  end

  def download_smiles
    send_file "#{Rails.root.join('public','system','downloads','current','smiles.csv.gz').to_s}", :type=>"application/zip", :filename => "smiles.csv.gz", :disposition => 'attachment'
  end

end
