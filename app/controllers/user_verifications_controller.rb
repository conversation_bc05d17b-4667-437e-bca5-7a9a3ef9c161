class UserVerificationsController < ApplicationController

  before_filter :load_user_using_perishable_token
  
  def show
    if @user
      @user.verify!
      flash[:notice] = "Thanks for verifying your account. Please Sign In with your newly created account and password to continue to submit data."
    end
    redirect_to(sign_in_path) # we can add here the logged in path by providing user id and password
  end
  
  private
  
  def load_user_using_perishable_token
    @user = User.find_using_perishable_token(params[:id])
    flash[:notice] = 'Unable to find your account' unless @user
  end

end