class UsersController < ApplicationController
  
  def new
    @user = User.new
  end

  def create
    @user = User.new(users_params)
    if @user.save
      flash[:success] = "Thank you for registration!\nWe will send you an email with confirmation. Please check your e-mail inbox and confirm your account."
      @user.deliver_verification_instructions!
      redirect_to sign_in_path
    else
      render :new
    end
  end

  private

  def users_params
    params.require(:user).permit(:email, :password, :password_confirmation).to_h
  end
end
