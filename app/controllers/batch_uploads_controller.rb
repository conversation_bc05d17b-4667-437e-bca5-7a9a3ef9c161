class BatchUploadsController < ApplicationController
  include Shared::BatchSubmissionLoader
  include Shared::BatchUploadLoader
  require 'fileutils'
  require 'open-uri'
  require 'zip'


  def index

  end

  def new
    if UserSession.try(:find).nil?
      redirect_to sign_in_path
    end
    @batch_upload = BatchUpload.new

  end

  def show
    load_batch_upload_object_without_user_session
  end

  def create
    @batch_upload = BatchUpload.new(batch_upload_params)
    @batch_upload.save
    # @user_session_id = session[:session_id]
    @user_session_id = session.id
    print("user_session_id = #{@user_session_id}")
    
    #create session directory if not
    @session_directory=Rails.root.join('public','downloads',"#{@user_session_id}")
    puts "Creating session_directory = #{@session_directory}"
    Dir.mkdir @session_directory unless File.exists?(@session_directory)
    @batch_directory = Rails.root.join('public','downloads',"#{@user_session_id}","batch_upload")
    print("batch_directory = #{@batch_directory}")
    Dir.mkdir @batch_directory unless File.exists?(@batch_directory)

    #save data in table
    @batch_upload.user_session_id = "#{@user_session_id}"
    @batch_upload.user_id = current_user.id
    
    @batch_upload.save!
    session[:batch_id] = @batch_upload.id

    
  
    @captured_file = @batch_upload.batch_file.path # file name with path
    puts("@captured_file = #{@captured_file}")
    print("captured_file=#{@captured_file}")

    Zip::File.open(@captured_file) do |zip_file|
      # zip_file.first do |f|
      @fpath = File.join(@batch_directory, zip_file.first.name)
      if @fpath.end_with?(".csv")
        temp = @fpath.split("/")[-1]
        temp = temp.split(".csv")[0]
        @fpath = @fpath.split("#{temp}")[0]
        temp = "#{@batch_upload.id}"+"_#{temp}_#{current_user.id}.csv"
        @fpath = "#{@fpath}"+"#{temp}"
      else
        puts("Not a valid file")
        return
      end
      print("@fpath = #{@fpath}")
      zip_file.extract(zip_file.first, @fpath) unless File.exist?(@fpath)
      # end
    end


    @batch_values = BatchUpload.ParseCSV(@fpath,@batch_directory,@user_session_id)

    
    
    @var = 1
    @backend_dir="backend"
    @nmr_pred_dir="nmr-pred"
    @draw_mol_script_basename="draw_mol.py"
    @backend_batch_dir = "batch_upload"
    @excel_generator_script_with_path = Rails.root.join("#{@backend_dir}","#{@backend_batch_dir}","excel_form_generation_current.py")
    puts("@excel_generator_script_with_path = #{@excel_generator_script_with_path}")
    #create a csv file with each generated file information which will be the input in the excel
    fpath_split = @fpath.split(".csv")[0]
    @project_name = @fpath.split("/")[-1].split(".csv")[0]
    @list_of_input_files_for_excel_generator = "#{fpath_split}_list_of_input_files_for_excel_generator.csv"
    puts("list_of_input_files_for_excel_generator = #{@list_of_input_files_for_excel_generator}")
    CSV.open("#{@list_of_input_files_for_excel_generator}", "wb") do |csv|  
      @batch_values.each do |b_v|
        @smiles = b_v[1]
        #creating mol, imgae and prediction file
        @path_to_temp_model, @path_to_temp_image, @path_to_csv, @compound_name = BatchUpload.DrawMol(@smiles,@batch_directory,@backend_dir,@nmr_pred_dir,@draw_mol_script_basename,@batch_upload.id,b_v[0],@batch_upload.user_id,@var)
        #creating mol, imgae and prediction file
        csv<<["#{@compound_name}","#{@path_to_csv}","#{@path_to_temp_image}"]
        @var = @var + 1
      end
    end
    execl_file_with_path = BatchUpload.GenerateExcel(@project_name,@excel_generator_script_with_path, @list_of_input_files_for_excel_generator, @batch_directory)
    session[:execl_file_with_path] = "#{execl_file_with_path}"
    respond_to do |format|
      format.html
    end
  end
    
    # @msg = "under construction"

  def downloadexcel
    load_batch_upload_folder_name(params[:id])
    
    print("@execl_file_with_path = #{@execl_file_with_path}")
    send_file "#{@execl_file_with_path}", :type=>"application/excel", :filename => "#{@folder_name}.xlsx", :disposition => 'attachment'

  end

  private
 
  def batch_upload_params
    params.require(:batch_upload).permit(:batch_file)
  end



end
