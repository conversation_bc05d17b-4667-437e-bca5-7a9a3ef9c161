class PredictController < ApplicationController
  include ApplicationHelper
  include PredictHelper

  before_filter :set_adduct_variables

  def index
    @predict_query = PredictQuery.new

    # Set defaults
    @predict_query.ion_mode    = "positive"
  end

  def new
    predict_params = get_predict_params

    predict_params[:compound].strip!
    predict_params[:threshold].strip!
    predict_params[:type] = "PredictQuery"
    predict_params[:user] = user_signed_in? ? current_user : nil
    predict_params[:param_file]  = get_param_file(predict_params[:spectra_type], predict_params[:ion_mode])
    predict_params[:config_file] = get_config_file(predict_params[:spectra_type], predict_params[:ion_mode])

    @predict_query = PredictQuery.new(predict_params)

    if @predict_query.compound.downcase == "cat"
      redirect_to(cat_path) and return
    end

    if @predict_query.compound.downcase == "puppy"
      redirect_to(puppy_path) and return
    end

    if @predict_query.valid? && @predict_query.save
      job_id = PredictWorker.perform_async(@predict_query.id)
    else
      if @predict_query.errors.any?
        if @predict_query.errors[:threshold]
          @predict_query.threshold = 0.001
        end
        errors = process_errors(@predict_query.errors.full_messages).html_safe
      else
        errors = "Input error, please check your input values or contact an administrator."
      end
      flash.now[:alert] = errors
      render(:action => 'index') and return
    end

    if !job_id.nil? && PredictQuery.find_by_id(@predict_query.id).update_attributes(job_id: job_id)
      redirect_to(query_status_path(PredictQuery.find_by_id(@predict_query.id).secret_id))
    else
      redirect_to(predict_path, notice: "Application error, please contact an administrator.") and return
    end
  end

  def get_predict_params
    params.require(:predict_query).permit(:type, :compound, :threshold, :ion_mode, :adduct_type, :spectra_type,
                  :param_file, :config_file, :user)
  end

  private

  def set_adduct_variables
    @positive_adducts = IdentifyQuery::POSITIVE_ADDUCTS
    @negative_adducts = IdentifyQuery::NEGATIVE_ADDUCTS
  end
end
