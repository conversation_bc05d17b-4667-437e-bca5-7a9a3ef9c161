class AssignController < ApplicationController
  include ApplicationHelper
  include As<PERSON><PERSON>elper

  def index
    params[:text_or_file] ||= 'text'
    @assign_query = AssignQuery.new
    @mass_tol = 10.0
    @mass_tol_units = "ppm"

    # Set defaults
    @assign_query.ion_mode    = "positive"
  end

  def new
    assign_params = get_assign_params

    assign_params[:compound].strip!
    assign_params[:type] = "AssignQuery"
    assign_params[:user] = user_signed_in? ? current_user : nil
    assign_params[:param_file]  = get_param_file(assign_params[:spectra_type], assign_params[:ion_mode])
    assign_params[:config_file] = get_config_file(assign_params[:spectra_type], assign_params[:ion_mode])

    # Use spectra file if both file and text input are given
    if params[:text_or_file] == "file"
      params[:low_spectra] = ""
      params[:medium_spectra] = ""
      params[:high_spectra] = ""
      assign_params[:spectra] = ""
    elsif params[:text_or_file] == "text"
      spectra_text = ""
      if params[:low_spectra].present?
        # spectra_text << "energy0\n" << remove_header(params[:low_spectra]).strip + "\n"
        spectra_text_temp = "energy0\n" << remove_header(params[:low_spectra]).strip + "\n"
        spectra_text << convert_scientific_notation(spectra_text_temp, 'energy0')
      end
      if params[:medium_spectra].present?
        # spectra_text << "energy1\n" << remove_header(params[:medium_spectra]).strip + "\n"
        spectra_text_temp = "energy1\n" << remove_header(params[:medium_spectra]).strip + "\n"
        spectra_text << convert_scientific_notation(spectra_text_temp, 'energy1')
      end
      if params[:high_spectra].present?
        # spectra_text << "energy2\n" << remove_header(params[:high_spectra]).strip + "\n"
        spectra_text_temp = "energy2\n" << remove_header(params[:high_spectra]).strip + "\n"
        spectra_text << convert_scientific_notation(spectra_text_temp, 'energy2')
      end
      assign_params[:input_file] = ""
      assign_params[:spectra] = spectra_text
    end

    # Setting mass tolerance
    if params[:mass_tol_units] == "ppm"
      assign_params[:ppm_mass_tol] = params[:mass_tol]
      assign_params[:abs_mass_tol] = -1
    else
      assign_params[:abs_mass_tol] = params[:mass_tol]
      assign_params[:ppm_mass_tol] = -1
    end

    @assign_query = AssignQuery.new(assign_params)
    @mass_tol = params[:mass_tol]
    @mass_tol_units = params[:mass_tol_units]
    @low_spectra = params[:low_spectra]
    @medium_spectra = params[:medium_spectra]
    @high_spectra = params[:high_spectra]

    if @assign_query.compound.downcase == "cat"
      redirect_to(cat_path) and return
    end

    if @assign_query.compound.downcase == "puppy"
      redirect_to(puppy_path) and return
    end

    # First check validity, then convert spectra to proper file format, then save
    if @assign_query.valid? && @assign_query.convert_spectra && @assign_query.save
      job_id = AssignWorker.perform_async(@assign_query.id)
    else
      if @assign_query.errors.any?
        if @assign_query.errors[:ppm_mass_tol] || @assign_query.errors[:abs_mass_tol]
          @mass_tol = 10.0
          @mass_tol_units = "ppm"
        end
        errors = process_errors(@assign_query.errors.full_messages).html_safe
      else
        errors = "Input error, please check your input values or contact an administrator."
      end
      flash.now[:alert] = errors
      render(:action => 'index') and return
    end

    if !job_id.nil? && AssignQuery.find_by_id(@assign_query.id).update_attributes(job_id: job_id)
      redirect_to(query_status_path(AssignQuery.find_by_id(@assign_query.id).secret_id))
    else
      redirect_to(assign_path, notice: "Application error, please contact an administrator.") and return
    end
  end

  def get_assign_params
    params.require(:assign_query).permit(:type, :compound, :input_file, 
                  :spectra, :ppm_mass_tol, :abs_mass_tol, 
                  :ion_mode, :spectra_type, :spectra_id, :param_file, 
                  :config_file, :user)
  end

end
