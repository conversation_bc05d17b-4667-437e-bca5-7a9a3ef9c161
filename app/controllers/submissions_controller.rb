class SubmissionsController < ApplicationController
	include Shared::SubmissionLoader
	include Shared::NaturalProductLoader
  # include SubmissionLoader
	require 'fileutils'
  require 'open-uri'
	 skip_before_action :verify_authenticity_token
	def new
		if UserSession.try(:find).nil?
			redirect_to sign_in_path
		end
		@submission = Submission.new()
    @submission.build_submission_meta_data
	end
	############## create action starts #####################
  def create
    status = "pending"
    case params[:state]
    when "post_np"
      # Grab from params (if submissions exist for the compound, cannot save metadata until after the alert)
      @natural_product, newly_created = load_create_natural_product(nil, params[:compound_name], params[:structure_input])
      @input_smiles = @natural_product.smiles
      session[:structure_input] = @input_smiles
      session[:genus] = params[:submission][:submission_meta_data_attributes][:genus]
      session[:species] = params[:submission][:submission_meta_data_attributes][:species]
      session[:physical_state_of_compound] = params[:submission][:submission_meta_data_attributes][:physical_state_of_compound]
      session[:melting_point] = params[:submission][:submission_meta_data_attributes][:melting_point]
      session[:boiling_point] = params[:submission][:submission_meta_data_attributes][:boiling_point]
      session[:literature_reference] = params[:submission][:submission_meta_data_attributes][:literature_reference]
      session[:literature_reference_type] = params[:submission][:submission_meta_data_attributes][:literature_reference_type]
      session[:provenance] = params[:submission][:submission_meta_data_attributes][:provenance]
      # Check to see if natural product already existed in NP-MRD
      if newly_created
        save_new_chemical_shift_submission
      else # Display alert
        @submissions = Submission.where(natural_product_id: @natural_product.id, valid: true)
        @chemical_shift_submissions = ChemicalShiftSubmission.where(natural_product_id: @natural_product.id, valid: true)
        @page = "alert"
      end
    when "post_alert"
      # User chooses to save or cancel the submission
      if params[:user_input] == "Continue"
        save_new_chemical_shift_submission
      elsif params[:user_input] == "Cancel"
        status = "cancelled"
      end
    end
    unless status == "pending"
      if status == "cancelled"
        respond_to do |format|
          format.js { render ajax_redirect_to("/submissions?submission=cancelled") }
        end
      end
    else
      respond_to do |format|
        format.js
      end
    end
  end
######################### Create action finished ######################################

##################### index action starts ####################
  def index
		if UserSession.try(:find).nil?
			redirect_to sign_in_path
		else
	   	load_submission_index_objects_by_user
	    respond_to do |format|
	     	format.html
	     end
    end
	end
######################### Index action finished ######################################

############## Edit action starts #####################
  def edit
    @submission = Submission.find(params[:id])

    if !@submission.submission_meta_data
      @submission.build_submission_meta_data
    end

    @natural_product = NaturalProduct.find(@submission.natural_product_id)


    temp_image_basename="#{@submission.id}" + "_#{@natural_product.np_mrd_id}_" + "#{current_user.id}" + "_temp_3D.png"
    @temp_mol_basename="#{@submission.id}" + "_#{@natural_product.np_mrd_id}_" + "#{current_user.id}" + "_temp_3D.mol"
    csv_basename="#{@submission.id}" + "_#{@natural_product.np_mrd_id}_" + "#{current_user.id}" + "_mol.csv"
    @threeD_image_url = File.join("/downloads","#{@submission.user_session_id}",temp_image_basename)
    @threeD_mol_url = File.join("/downloads","#{@submission.user_session_id}",@temp_mol_basename)
    @mol_csv_url = File.join("public","downloads","#{@submission.user_session_id}",csv_basename)

    session[:threeD_image] = @threeD_image_url
    session[:mol_csv_url] = @mol_csv_url

    session[:threeD_mol_name] = @temp_mol_basename
    session[:threeD_mol_url] = @threeD_mol_url
  end
######################### Edit action finished ######################################

############## Update action starts #####################
  def update
    status = "pending"
    case params[:state]
    when "post_np"
      # Save meta data
      @submission = Submission.find(params[:id])
      @submission.update(params.require(:submission).permit!)
      # Store in session
      natural_product = NaturalProduct.find(@submission.natural_product_id)
      session[:structure_input] = session[:structure_input]
      session[:natural_product_id] = natural_product.id
      session[:natural_product_np_mrd_id] = natural_product.np_mrd_id
      session[:submission_id] = @submission.id
      session[:structure_input] = session[:structure_input]
      @threeD_image_url = session[:threeD_image]
      # Pass to metadata page
      @submission_meta_data = @submission.submission_meta_data
      @page = "metadata"
    when "post_meta"
      # Save meta data
      @submission = Submission.find(params[:id])
      @submission.update(params.require(:submission).permit!)
      @submission.save!

      if @submission.submission_meta_data.temperature.nil? or @submission.submission_meta_data.temperature.empty?
        @submission.submission_meta_data.update(:temperature => "20")
      end
      if @submission.submission_meta_data.melting_point.empty?
        @submission.submission_meta_data.update(:melting_point => "NA")
      end
      if @submission.submission_meta_data.boiling_point.empty?
        @submission.submission_meta_data.update(:boiling_point => "NA")
      end

      # Store in session
      session[:natural_product_id] = session[:natural_product_id]
      session[:submission_id] = session[:submission_id]
      session[:threeD_image] = session[:threeD_image]
      session[:mol_csv_url] = session[:mol_csv_url]
      session[:spectrum_type] = params[:submission][:submission_meta_data_attributes][:spectrum_type]
      session[:solvent] = params[:submission][:submission_meta_data_attributes][:solvent]
      session[:spectrometer_frequency] = params[:submission][:submission_meta_data_attributes][:spectrometer_frequency]
      session[:temperature] = params[:submission][:submission_meta_data_attributes][:temperature]
      session[:chemical_shift_standard] = params[:submission][:submission_meta_data_attributes][:chemical_shift_standard]
      session[:submission] = params[:submission]
      session[:structure_input] = session[:structure_input]


  #    #### generating predictions using nmrpred ##########
  #    ### It will return back the file with path that has predicrion result###
  #    if session[:solvent] == "H2O" or session[:solvent] == "CHCl3" or session[:solvent] == "DMSO" and session[:spectrum_type] == "1D-1H"
  #      if session[:solvent] == "H2O"
  #        @sol = "D2O"
  #      elsif session[:solvent] == "CDCl3"
  #        @sol = "CDCL3"
  #      else
  #        @sol = session[:solvent]
  #      end
  #      @spec_type = "1H"
  #      @s_id = session[:submission_id]
  #      @prediction_result_file_with_path = NmrPred.create_agent(session[:structure_input],@sol,@spec_type,@c_s_s_id)
  #      @prediction_result_file_with_path = @prediction_result_file_with_path.gsub(".txt",".csv")
  #      puts("@prediction_result_file_with_path = #{@prediction_result_file_with_path}")
  #    end





      @threeD_image_url = session[:threeD_image]
      @page = "chemical_shift_new"
      ##### take the atoms and symbol for the submitted molecule
      preparation_for_bulding_shift_table(session[:mol_csv_url],session[:submission_id],session[:spectrum_type])
    when "post_chemical_shift"
      session[:structure_input] = session[:structure_input]
      session[:submission_id] = session[:submission_id]
      session[:natural_product_id] = session[:natural_product_id]
      session[:threeD_image] = session[:threeD_image]
      session[:mol_csv_url] = session[:mol_csv_url]
      session[:spectrum_type] = session[:spectrum_type]
      session[:threeD_mol_name] = session[:threeD_mol_name]
      session[:threeD_mol_url] = session[:threeD_mol_url]
      shift_hash = params[:submission]["chemical_shifts_attributes"]
      s = Submission.find(session[:submission_id])
      if ChemicalShift.exists?(:submission_id => session[:submission_id])
        for key, values in shift_hash do
          if ChemicalShift.where({:submission_id => session[:submission_id], :atom_id => values["atom_id"]} ).present?
            cs = ChemicalShift.where({:submission_id => session[:submission_id], :atom_id => values["atom_id"]} ).first
            for column, value in values do
              if column != "id"
                cs[column] = value
                cs.save!
              end
            end
            s = Submission.find(session[:submission_id])
            s.valid = false
            s.save!
          end
        end

      else
        for key, values in shift_hash do
          cs = ChemicalShift.new()
          for column, value in values do
            cs[column] = value
          end
          s.chemical_shifts << cs
          s.save!
          s = Submission.find(session[:submission_id])
          s.valid = false
          s.save!
        end
        ChemicalShift.conditional_save(s)
      end
      @natural_product = NaturalProduct.find(s.natural_product_id)
      @meta_data = s.submission_meta_data
      @submission = Submission.find(session[:submission_id])
      # preparation_for_bulding_shift_table(session[:mol_csv_url],session[:submission_id],session[:spectrum_type])


      ######## create new order of custom numbering ####
      returned_atom_order = Submission.new_atom_order(session[:submission_id])
      puts("returned_atom_order = #{returned_atom_order}")
      returned_atom_order_string = returned_atom_order.map(&:inspect).join(',')
      np = NaturalProduct.find((s.natural_product_id).to_i)
      puts("outside np = #{np}")
      temp_mol_basename="#{s.id}" + "_#{np.np_mrd_id}_" + "#{s.user_id}" + "_temp_3D.mol"
      temp_mol_basename_out="#{s.id}" + "_#{np.np_mrd_id}_" + "#{s.user_id}" + "_temp_3D_out.mol"
      threeD_mol_url = Rails.root.join("public","downloads","#{s.user_session_id}","#{temp_mol_basename}")
      threeD_mol_url_out = Rails.root.join("public","downloads","#{s.user_session_id}","#{temp_mol_basename_out}")
      puts("outside threeD_mol_url = #{threeD_mol_url}")
      puts("outside threeD_mol_url_out = #{threeD_mol_url_out}")
      renumbering_script_path = "#{Rails.root}/public/python/renumber_atoms.py"
      renumbering_script_log_path = Rails.root.join("public","downloads","#{s.user_session_id}","renumbering.txt")
      python_path = PYTHON_ENV["#{Rails.env}"]['python_path']
      python_renumbering_command=""
      python_renumbering_command+="#{python_path} "
      python_renumbering_command+="#{renumbering_script_path} "
      python_renumbering_arguments=""
      python_renumbering_arguments+="-i '#{threeD_mol_url}' "
      python_renumbering_arguments+="-o '#{threeD_mol_url_out}' "
      python_renumbering_arguments+="-n '#{returned_atom_order_string}' "
      python_renumbering_arguments+=" > '#{renumbering_script_log_path}' "
      python_renumbering_command+="#{python_renumbering_arguments} "
      puts "Python log is:"
      puts "#{renumbering_script_log_path}"
      puts "Running atom renumbering Python script:"
      puts "#{python_renumbering_command}"

      `#{python_renumbering_command}`
      stdin,stdout,stderr = Open3.popen3("#{PYTHON_ENV["#{Rails.env}"]['python_path']} #{Rails.root}/public/python/rdkit_mol_to_mol_renumbering.py '#{threeD_mol_url_out}'")
      @renumbered_mol = stdout.gets(nil).to_s
      puts("renumbered_mol = #{@renumbered_mol}")
      s.renumberedMol = @renumbered_mol
      s.save!
      session[:renumbered_mol] = @renumbered_mol
      puts("saved renumbered in chemical shift submissions table")
      # `#{PYTHON_ENV["#{Rails.env}"]['python_path']} #{Rails.root}/public/python/renumber_atoms.py -i #{threeD_mol_url} -o #{threeD_mol_url_out} -n #{returned_atom_order_string}" `
      #################################################
      @chemical_shifts = s.chemical_shifts
      calc_assignment_score


      # puts "@chemical shift ............. #{@chemical_shifts}"
      # puts "s.chemical_shifts .....#{s.chemical_shifts.inspect()}"
      s.chemical_shifts << @chemical_shifts
      s.save!




      @page = "spectrum_file_upload"
      session[:submission_id] = session[:submission_id]
      session[:natural_product_id] = session[:natural_product_id]
      session[:threeD_image] = session[:threeD_image]
      session[:mol_csv_url] = session[:mol_csv_url]
      session[:spectrum_type] = session[:spectrum_type]
      session[:spectrometer_frequency] = session[:spectrometer_frequency]
      session[:nmr_spectrum_type] = session[:spectrum_type]
      session[:nmr_solvent] = @meta_data.solvent
      session[:nmr_chemical_shift_standard] = @meta_data.chemical_shift_standard
      if !@submission.nmr_submissions.exists?
        @submission.nmr_submissions.create(:submission_id => session[:submission_id], :spectrometer_frequency => session[:spectrometer_frequency],:nmr_spectrum_type => session[:nmr_spectrum_type], :chemical_shift_standard => session[:nmr_chemical_shift_standard], :solvent => session[:nmr_solvent])
      end
    when "post_spectral_file"
      session[:submission_id] = session[:submission_id]
      session[:natural_product_id] = session[:natural_product_id]
      session[:threeD_image] = session[:threeD_image]
      session[:mol_csv_url] = session[:mol_csv_url]
      session[:spectrum_type] = session[:spectrum_type] # this is in submission_meta_data table
      session[:renumbered_mol] = session[:renumbered_mol]
      @submission = Submission.find(session[:submission_id])
      @submission.update(params.require(:submission).permit!)

      @natural_product = NaturalProduct.find(@submission.natural_product_id)
      preparation_for_bulding_shift_table(session[:mol_csv_url],session[:submission_id],session[:spectrum_type])
      @meta_data = @submission.submission_meta_data
      preparation_for_bulding_shift_table(session[:mol_csv_url],session[:submission_id],session[:spectrum_type])

      @new_renumbered_mol = session[:renumbered_mol]

      session_name = session[:session_name]
      score_image_name = "#{session_name}_assignment_score.svg"
      @score_image_path = File.join("/downloads","#{session.id}",score_image_name)




############################
      @page = "verification"
    when "post_verification"
      @submission = Submission.find(params[:id])
      # User chooses whether or not to verify the submission
      if params[:user_input] == "Continue"
        @submission.update(params.require(:submission).permit!)
        @submission.valid = true # Finished submission mark as valid
        @submission.save!
        if @submission.submission_meta_data.physical_state_of_compound.nil? or @submission.submission_meta_data.physical_state_of_compound.empty?
          @submission.submission_meta_data.update(:physical_state_of_compound => "NA")
        end

        # Export natural product
        natural_product = @submission.natural_product
        natural_product.export = true
        natural_product.save!

        # save or create the reference. for now only PMID
        Submission.Generate_Reference_Pubmed(params[:id])

        status = "verified"
      elsif params[:user_input] == "Cancel"
        status = "cancelled"
      end
    end
    unless status == "pending"  # if something != something then execute the codes
      if status == "verified"
        redirect_to action: :show, notice: 'Submission saved successfully!'
      elsif status == "cancelled"
        redirect_to action: :show, notice: 'Verification still required!'
      end
    else
      respond_to do |format|
        format.js
      end
    end
  end

######################### Update action finished ######################################
############## Show action starts #####################
  def show
    process_data
    session_name = session[:session_name]
    nmrml_name  = "#{session_name}.nmrml"
    # @nmrml_path = File.join("/downloads","#{session[:session_id]}",nmrml_name)
    @nmrml_path = File.join("/downloads","#{session.id}",nmrml_name)
    @file_list = session[:file_list]
    @name_list = session[:name_list]
    @quality_list = session[:sp_quality_img_list]
    @param_list = session[:param_list]

    score_image_name = "#{session_name}_assignment_score.svg"
    @score_image_path = File.join("/downloads","#{session.id}",score_image_name)

    if UserSession.try(:find).nil?
      redirect_to sign_in_path
    else
      load_submission_object_by_user

      @chemical_shifts = @submission.chemical_shifts
      calc_assignment_score
      @submission.chemical_shifts = @chemical_shifts
      @submission.save!
      assignment_report_file_name = "#{session_name}_assignment_report.txt"
      @assignment_report_file_path = File.join("public/downloads","#{session.id}",assignment_report_file_name)
      Submission.save_assignment_report(@submission,file_path = @assignment_report_file_path)

      respond_to do |format|
        format.html
      end
    end

    runNMRpred()
    generate_nmrml()
  end
######################## Show action finished ######################################

############## Custom "Download" action starts #####################
  def download_submitted_chemical_shift_data
    load_submission_object_without_user_session
    file_name = Submission.Download_file(@submission)
    send_file "#{Rails.root.join('public','downloads',file_name).to_s}", :type=>"application/csv", :filename => "#{file_name}", :disposition => 'attachment'
  end
######################## Custom "Download" action finished ######################################
  def download_renumbered_mol
    load_submission_object_without_user_session
    temp_mol_basename_out="#{@submission.id}" + "_#{@natural_product.np_mrd_id}_" + "#{@submission.user_id}" + "_temp_3D_out.mol"
    # threeD_mol_url_out = Rails.root.join("public","downloads","#{@chemical_shift_submission.user_session_id}","#{temp_mol_basename_out}")
    send_file "#{Rails.root.join('public','downloads',@submission.user_session_id,temp_mol_basename_out).to_s}", :type=>"application/mol", :filename => "#{temp_mol_basename_out}", :disposition => 'attachment'
  end

############## Method used by the create action starts #####################
  def save_new_chemical_shift_submission
    # After alert
    if params[:natural_product_id]
      @natural_product = NaturalProduct.find(params[:natural_product_id])
      @input_smiles = @natural_product.moldb_smiles
      @submission = Submission.new(chemical_shift_submission_from_session)
    else # No alert
      @submission = Submission.new(params.require(:submission).permit!)
    end

    # Save chemical shift submission and metadata
    @submission.user_id = current_user.id
    # puts"before submission save, the session id = #{session[:session_id]}"
    puts"before submission save, the session id = #{session.id}"
    # @submission.user_session_id = session[:session_id]
    @submission.user_session_id = session.id
    @submission.natural_product_id = @natural_product.id
    @submission.save!
    puts @submission.inspect
    session[:submission_id] = @submission.id


    # Store in session
    session[:submission_id] = @submission.id
    session[:natural_product_id] = @natural_product.id
    session[:natural_product_name] = @natural_product.name
    session[:natural_product_np_mrd_id] = @natural_product.np_mrd_id
    session[:user_id] = @submission.user_id
    session[:input_smiles_key] = session[:structure_input]

    #puts "@input_smiles ....................#{@input_smiles}"
    puts "INPUT SMILES #{session[:structure_input]}"
    smiles=session[:input_smiles_key]
    session_name=session[:submission_id].to_s + "_#{session[:natural_product_np_mrd_id]}_" + "#{session[:user_id]}"
    session[:session_name] = session_name

    # session_id=session[:session_id]
    session_id=session.id
    # puts "session_id= #{session[:session_id]}"
    puts "session_id= #{session.id}"

    session_directory=Rails.root.join('public','downloads',"#{session_id}")
    puts "Creating session_directory = #{session_directory}"
    Dir.mkdir session_directory unless File.exists?(session_directory)
    backend_dir="backend"
    nmr_pred_dir="nmr-pred"
    draw_mol_script_basename="draw_mol.py"
    parseuserassignment_script_basename="parseuserassignment.py"

    non_canonical_mol_basename,
    non_canonical_base_image_basename,
    non_canonical_equiv_image_basename,
    canonical_mol_basename,
    canonical_base_image_basename,
    canonical_equiv_image_basename,
    non_canonical_mol_abs_path,
    non_canonical_base_image_abs_path,
    non_canonical_equiv_image_abs_path,
    canonical_mol_abs_path,
    canonical_base_image_abs_path,
    canonical_equiv_image_abs_path=runDrawMol(smiles,
                session_directory,
                backend_dir,
                nmr_pred_dir,
                draw_mol_script_basename)

    ## creating 3D sdf file #####
    ## Python script arguments:
    #"-s" - smiles string , required if flags are used at all
    #"-mol" - basename of the output mol file, optional
    #"-mol_path" - absolute path to output mol file   , optional
    #"-cs" - basename of the output chemical shift file, optional
    #"-cs_path" - absolute path to output chemical shift file, optional
    #"-mol_img" - basename of the output image with molecule, optional
    #"-mol_img_path" - absolute path to the output image with molecule, optional
    #"-odir"  - relative path to the output direcrtory, optional
    #"-odir_path" - absolute path to the output direcrtory, optional

    # config/python_paths.yml
    python_drawmolrdkit_path=PYTHON_ENV["#{Rails.env}"]['python_path']

    drawmolrdkit_script=Rails.root.join('public', 'python','drawmolrdkit.py')
    output_dir_for_mol=session_directory
    python_log_basename="#{session_name}_nmrpred_initial_predictions.log"
    python_log_abs_path=Rails.root.join("#{session_directory}",python_log_basename)

    input_mol_file = session[:submission_id].to_s + "_#{session[:natural_product_np_mrd_id]}_" + "#{session[:user_id]}" + "_draw_mol_output.mol"
    path_to_input_mol_file = Rails.root.join("#{session_directory}","#{input_mol_file}")

    temp_model_basename=session[:submission_id].to_s + "_#{session[:natural_product_np_mrd_id]}_" + "#{session[:user_id]}" + "_temp_3D.mol"
    temp_image_basename=session[:submission_id].to_s + "_#{session[:natural_product_np_mrd_id]}_" + "#{session[:user_id]}" + "_temp_3D.png"
    csv_basename=session[:submission_id].to_s + "_#{session[:natural_product_np_mrd_id]}_" + "#{session[:user_id]}" + "_mol.csv"

    path_to_temp_model=Rails.root.join("#{session_directory}","#{temp_model_basename}")
    path_to_temp_image=Rails.root.join("#{session_directory}","#{temp_image_basename}")
    path_to_csv=Rails.root.join("#{session_directory}","#{csv_basename}")
    nmr_pred_location=Rails.root.join("#{backend_dir}", "#{nmr_pred_dir}")

    puts "path_to_temp_model = #{path_to_temp_model}"
    puts "path_to_temp_image = #{path_to_temp_image}"
    puts "path_to_csv = #{path_to_csv}"
    puts "output_dir_for_mol = #{output_dir_for_mol}"
    puts "nmr_pred_location = #{nmr_pred_location}"
    puts "python_drawmolrdkit_path = #{python_drawmolrdkit_path}"
    puts "drawmolrdkit_script = #{drawmolrdkit_script}"

    python_drawmolrdkit_command=""
    python_drawmolrdkit_command+="#{python_drawmolrdkit_path} "
    python_drawmolrdkit_command+="#{drawmolrdkit_script} "
    python_drawmolrdkit_arguments=""
    python_drawmolrdkit_arguments+="-s '#{smiles}' "
    python_drawmolrdkit_arguments+="-sid '#{session_name}' "
    python_drawmolrdkit_arguments+="-odir_path '#{session_directory}' "
    python_drawmolrdkit_arguments+="-mol_path '#{path_to_temp_model}' "
    python_drawmolrdkit_arguments+="-cs_path '#{path_to_csv}' "
    python_drawmolrdkit_arguments+="-mol_img_path '#{path_to_temp_image}' "
    python_drawmolrdkit_arguments+="-nmrpred_loc '#{nmr_pred_location}' "
    python_drawmolrdkit_arguments+="-input_mol '#{path_to_input_mol_file}' " #USE THIS AS INPUT TO NMRPRED
    python_drawmolrdkit_arguments+="-input_img '#{non_canonical_base_image_abs_path}' " #COPY THIS AND USE AS DISPLAY
    python_drawmolrdkit_arguments+=" > '#{python_log_abs_path}' "
    python_drawmolrdkit_command+="#{python_drawmolrdkit_arguments} "

    puts "Python log is:"
    puts "#{python_log_abs_path}"
    puts "Running drawmolrdkit Python script:"
    puts "#{python_drawmolrdkit_command}"

    `#{python_drawmolrdkit_command}`

    @threeD_image_url = File.join("/downloads","#{session_id}",temp_image_basename)
    session[:threeD_image] = @threeD_image_url
    @mol_csv_url = File.join("public","downloads","#{session_id}",csv_basename)
    session[:mol_csv_url] = @mol_csv_url


    @page = "metadata"
    @submission_meta_data = @submission.submission_meta_data
  end

  # form_for returns js requests which doesn't allow the html redirect
  def ajax_redirect_to(redirect_uri)
    { js: "window.location.replace('#{redirect_uri}');" }
  end

  ###### used by "save_new_chemical_shift_submission" starts #########
  def runDrawMol(smiles, session_directory, backend_dir, nmr_pred_dir, draw_mol_script_basename)
    puts "Running runDrawMol() function"
    puts "smiles = #{smiles}"
    puts "session_directory = #{session_directory}"
    #script_arguments:
    # if sys.argv[1] in ('--mol', '--smiles', '--molstring'): molfiletype = sys.argv[1][2:]
    #if sys.argv[i] == "--optmol": optmol = True
    #  if sys.argv[i] == "--writemol": writemol = True #write to prefix_output.mol
    #  if sys.argv[i] == "--outputpath": outputpath = sys.argv[i+1]
    #  if sys.argv[i] == "--outputprefix": outputprefix = sys.argv[i+1] #write to prefix_*.png
    #  if sys.argv[i] == "--size": size = (int(sys.argv[i+1]), int(sys.argv[i+2]))
    #  if sys.argv[i] == "--showstereo": showstereo = True
    #  if sys.argv[i] == "--showequiv": showequiv = True
    #  if sys.argv[i] == "--canonicalorder": canonicalorder = True
    #  if sys.argv[i] == "--filetype": filetype = sys.argv[i+1]

    python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
    #draw_mol_script=Rails.root.join('backend', 'nmr-pred','draw_mol.py')
    draw_mol_script=Rails.root.join("#{backend_dir}", "#{nmr_pred_dir}","#{draw_mol_script_basename}")
    puts "draw_mol_script = #{draw_mol_script}"
    session_name=session[:submission_id].to_s + "_#{session[:natural_product_np_mrd_id]}_" + "#{session[:user_id]}"
    # session_id=session[:session_id]
    session_id=session.id

    draw_mol_log_basename="#{session_name}_draw_mol.log"
    outputprefix="#{session_name}_draw_mol"
    outputprefix_canonical="#{session_name}_draw_mol_canonical"
    draw_mol_log_abs_path=Rails.root.join("#{session_directory}",draw_mol_log_basename)
    draw_mol_log_abs_path_canonical=Rails.root.join("#{session_directory}","canonical_#{draw_mol_log_basename}")
    puts "draw_mol_log_abs_path = #{draw_mol_log_abs_path}"

    draw_mol_command=""
    draw_mol_command+="#{python_path} "
    draw_mol_command+="#{draw_mol_script} "

    draw_mol_arguments=""
    draw_mol_arguments+="--smiles '#{smiles}' "
    draw_mol_arguments+="--outputpath '#{session_directory}' "
    draw_mol_arguments+="--writemol "
    draw_mol_arguments+="--optmol "
    draw_mol_arguments+="--showstereo "
    draw_mol_arguments+="--showequiv "

    draw_mol_arguments_non_canonical=draw_mol_arguments
    draw_mol_arguments_non_canonical+="--outputprefix '#{outputprefix}' "
    draw_mol_arguments_non_canonical+=" > '#{draw_mol_log_abs_path}' "
    draw_mol_command_non_canonical=draw_mol_command
    draw_mol_command_non_canonical+="#{draw_mol_arguments_non_canonical} "
    puts "draw_mol_command_non_canonical: #{draw_mol_command_non_canonical}"
    `#{draw_mol_command_non_canonical}`

    draw_mol_arguments_canonical=draw_mol_arguments
    draw_mol_arguments_canonical+="--canonicalorder "
    draw_mol_arguments_canonical+="--outputprefix '#{outputprefix_canonical}' "
    draw_mol_arguments_canonical+=" > '#{draw_mol_log_abs_path_canonical}' "
    draw_mol_command_canonical=draw_mol_command
    draw_mol_command_canonical+="#{draw_mol_arguments_canonical} "
    puts "draw_mol_command_canonical: #{draw_mol_command_canonical}"
    # bll: these output files appear to not be currently used,
    #      and we want to avoid running the command twice.
    #      there is still ongoing discussion on the best/easiest way to get predictable numbering,
    #      so the output could also change
    # `#{draw_mol_command_canonical}`

    non_canonical_mol_basename="#{outputprefix}_output.mol"
    non_canonical_base_image_basename="#{outputprefix}_2d.png"
    non_canonical_equiv_image_basename="#{outputprefix}_equiv.png"

    canonical_mol_basename="#{outputprefix_canonical}_output.mol"
    canonical_base_image_basename="#{outputprefix_canonical}_2d.png"
    canonical_equiv_image_basename="#{outputprefix_canonical}_equiv.png"


    non_canonical_mol_abs_path=Rails.root.join("#{session_directory}",non_canonical_mol_basename)
    non_canonical_base_image_abs_path=Rails.root.join("#{session_directory}",non_canonical_base_image_basename)
    non_canonical_equiv_image_abs_path=Rails.root.join("#{session_directory}",non_canonical_equiv_image_basename)

    canonical_mol_abs_path=Rails.root.join("#{session_directory}",canonical_mol_basename)
    canonical_base_image_abs_path=Rails.root.join("#{session_directory}",canonical_base_image_basename)
    canonical_equiv_image_abs_path=Rails.root.join("#{session_directory}",canonical_equiv_image_basename)

    puts "non_canonical_mol_abs_path: #{non_canonical_mol_abs_path}"
    puts "non_canonical_base_image_abs_path: #{non_canonical_base_image_abs_path}"
    puts "non_canonical_equiv_image_abs_path: #{non_canonical_equiv_image_abs_path}"
    puts "canonical_mol_abs_path: #{canonical_mol_abs_path}"
    puts "canonical_base_image_abs_path: #{canonical_base_image_abs_path}"
    puts "canonical_equiv_image_abs_path: #{canonical_equiv_image_abs_path}"

    return non_canonical_mol_basename,
    non_canonical_base_image_basename,
    non_canonical_equiv_image_basename,
    canonical_mol_basename,
    canonical_base_image_basename,
    canonical_equiv_image_basename,
    non_canonical_mol_abs_path,
    non_canonical_base_image_abs_path,
    non_canonical_equiv_image_abs_path,
    canonical_mol_abs_path,
    canonical_base_image_abs_path,
    canonical_equiv_image_abs_path
  end
  ####### user by "save_new_chemical_shift_submission" ends"



############## Method used by the create action finished #####################
###########################################################



  def runNMRpred()
    # output from nmrpred --writeassignmenttable will generally have
    # extra atoms especially if there are unassigned couplings

    # default options and files:
    # --pred1hcsv         'results/example_1h_shifts.txt'
    # --pred1hcoupcsv     'results/example_1h_couplings.txt'
    # --userinput         'results/example_assignmenttable_manual.txt'
    # --write1hshift      'results/example_parsed1hshifts.txt'
    # --write1hcoup       'results/example_parsed1hcoup.txt'

    # for simulation, after running script:
    # nmrpred.py --smiles CCO --noprediction --input1h results/example_parsed1hshifts.txt --input1hcoup results/example_parsed1hcoup.txt --plot1h

    puts "Running function runNMRpred()"
    python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
    backend_dir="backend"
    nmr_pred_dir="nmr-pred"
    parseuserassignment_script_basename="parseuserassignment.py"
    nmrpred_script_basename="nmrpred.py"

    #smiles=NaturalProduct.find_by(np_mrd_id: session[:natural_product_np_mrd_id]).moldb_smiles


    session_name=session[:submission_id].to_s + "_#{session[:natural_product_np_mrd_id]}_" + "#{@current_user.id}"
    # session_id=session[:session_id]
    session_id=session.id
    spectrum_type=session[:spectrum_type]
    parseuserassignment_script=Rails.root.join("#{backend_dir}", "#{nmr_pred_dir}","#{parseuserassignment_script_basename}")
    nmrpred_script=Rails.root.join("#{backend_dir}", "#{nmr_pred_dir}","#{nmrpred_script_basename}")
    nmrpred_dir=Rails.root.join("#{backend_dir}", "#{nmr_pred_dir}")


    session_directory=Rails.root.join('public','downloads',"#{session_id}")

    inputmol_basename = predicted_shifts_basename="#{session_name}_output.mol"
    inputmol_path=Rails.root.join("#{session_directory}","#{inputmol_basename}")

    predicted_shifts_basename="#{session_name}_1h_shifts.txt"
    predicted_13c_shifts_basename="#{session_name}_13c_shifts.txt"
    predicted_couplings_basename="#{session_name}_1h_couplings.txt"
    assignmenttable_manual_basename="#{session_name}_assignmenttable.txt"
    parsed_shifts_basename="#{session_name}_parsed_1h_shifts.txt"
    parsed_13c_shifts_basename="#{session_name}_parsed_13c_shifts.txt"
    parsed_couplings_basename="#{session_name}_parsed_1h_couplings.txt"
    parseuserassignment_log_basename="#{session_name}_parseuserassignment.log"
    user_assignmenttable_basename="#{session_name}_user_assignmenttable.txt"
    nmrpred_log_basename="#{session_name}_nmrepred.log"

    predicted_shifts_path=Rails.root.join("#{session_directory}","#{predicted_shifts_basename}")
    predicted_13c_shifts_path=Rails.root.join("#{session_directory}","#{predicted_13c_shifts_basename}")
    predicted_couplings_path=Rails.root.join("#{session_directory}","#{predicted_couplings_basename}")
    assignmenttable_manual_path=Rails.root.join("#{session_directory}","#{assignmenttable_manual_basename}")
    parsed_shifts_path=Rails.root.join("#{session_directory}","#{parsed_shifts_basename}")
    parsed_13c_shifts_path=Rails.root.join("#{session_directory}","#{parsed_13c_shifts_basename}")
    parsed_couplings_path=Rails.root.join("#{session_directory}","#{parsed_couplings_basename}")
    parseuserassignment_log_path=Rails.root.join("#{session_directory}","#{parseuserassignment_log_basename}")
    user_assignmenttable_path=Rails.root.join("#{session_directory}","#{user_assignmenttable_basename}")
    nmrpred_log_path=Rails.root.join("#{session_directory}","#{nmrpred_log_basename}")

    puts "Creating session_directory = #{session_directory}"
    puts "parseuserassignment_script = #{parseuserassignment_script}"
    puts "session_directory = ",session_directory
    puts "backend_dir= ",backend_dir
    puts "nmr_pred_dir = ",nmr_pred_dir
    puts "parseuserassignment_script_basename = ",parseuserassignment_script_basename
    puts "predicted_shifts_path = ",predicted_shifts_path
    puts "predicted_13c_shifts_path = ",predicted_13c_shifts_path
    puts "predicted_couplings_path = ",predicted_couplings_path
    puts "assignmenttable_manual_path = ",assignmenttable_manual_path
    puts "parsed_shifts_path = ",parsed_shifts_path
    puts "parsed_13c_shifts_path = ",parsed_13c_shifts_path
    puts "parsed_couplings_path = ",parsed_couplings_path
    puts "parseuserassignment_log_path = ",parseuserassignment_log_path
    puts "user_assignmenttable_path = ",user_assignmenttable_path
    puts "nmrpred_script = ",nmrpred_script
    #puts "input_smiles =  #{smiles}"
    puts "inputmol_path =  #{inputmol_path}"
    puts "spectrum_type =  #{spectrum_type}"


    user_input_command=""
    user_input_command+="#{python_path} "
    user_input_command+="#{parseuserassignment_script} "

    user_input_arguments=""
    user_input_arguments+="--userinput %s " % user_assignmenttable_path

    user_input_arguments+="--pred1hcsv %s " % predicted_shifts_path
    user_input_arguments+="--pred1hcoupcsv %s " % predicted_couplings_path
    user_input_arguments+="--write1hshift %s " % parsed_shifts_path
    user_input_arguments+="--write1hcoup %s " % parsed_couplings_path

    if spectrum_type=="1D-13C" or spectrum_type=="2D-1H-13C"
       user_input_arguments+="--pred13ccsv %s " % predicted_13c_shifts_path
       user_input_arguments+="--write13cshift %s " % parsed_13c_shifts_path
     end

    user_input_arguments+=" > %s " % parseuserassignment_log_path

    user_input_command+=user_input_arguments

    Submission.save_user_table(@submission,file_path=user_assignmenttable_path)

    puts "user_input_command : #{user_input_command}"
    `#{user_input_command}`

    # nmrpred_prefix="nmrpred"
    nmrpred_prefix = "#{session_name}"

    nmrpred_command=""
    nmrpred_command+="#{python_path} "
    nmrpred_command+="#{nmrpred_script} "
    nmrpred_arguments=""
    #nmrpred_arguments+=" --smiles '#{smiles}' "
    nmrpred_arguments+=" --mol %s "  % inputmol_path
    nmrpred_arguments+=" --noprediction "

    nmrpred_arguments+=" --input1h %s "  % parsed_shifts_path
    nmrpred_arguments+=" --input1hcoup %s "  % parsed_couplings_path
    nmrpred_arguments+=" --plot1h "

    if spectrum_type==="1D-13C" or spectrum_type==="2D-1H-13C"
      nmrpred_arguments+=" --input13c %s "  % parsed_13c_shifts_path
      nmrpred_arguments+=" --plot13c "
    end

    #if spectrum_type==="2D-1H-13C"
    #  nmrpred_arguments+=" --mergepeaks "
    #end
    nmrpred_arguments+=" --outputpath %s "  % session_directory
    nmrpred_arguments+=" --outputprefix %s "  % nmrpred_prefix
    nmrpred_arguments+=" > %s " % nmrpred_log_path

    working_directory=Dir.pwd
    puts "working_directory : #{working_directory}"
    Dir.chdir(nmrpred_dir)
    puts "Changed to NMRpred directory: #{nmrpred_dir}"

    nmrpred_command+=nmrpred_arguments

    puts "nmrpred_ command : #{nmrpred_command}"
    `#{nmrpred_command}`

    puts "Changed to back to working directory: #{working_directory}"
    Dir.chdir(working_directory)


  end

  def generate_nmrml()
    puts "Running function generate_nmrml()"
    python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
    backend_dir="backend"
    nmr_pred_dir="nmr_ml"
    nmrml_creator_script_basename="nmrml_creator.py"
    session_name=session[:submission_id].to_s + "_#{session[:natural_product_np_mrd_id]}_" + "#{session[:user_id]}"
    # session_id=session[:session_id]
    session_id=session.id
    nmrml_creator_script=Rails.root.join("#{backend_dir}", "#{nmr_pred_dir}","#{nmrml_creator_script_basename}")
    session_directory=Rails.root.join('public','downloads',"#{session_id}")
    natural_product_name=session[:natural_product_name]
    #New arguments, August 12th 2020:
    genus=session[:genus]
    species=session[:species]
    literature_reference=session[:literature_reference]
    solvent=session[:solvent]
    spectrum_type=session[:spectrum_type]
    spectrometer_frequency=session[:spectrometer_frequency]
    temperature=session[:temperature]
    chemical_shift_standard=session[:chemical_shift_standard]
    literature_reference_type=session[:literature_reference_type]
    physical_state_of_compound=session[:physical_state_of_compound]
    melting_point=session[:melting_point]
    boiling_point=session[:boiling_point]

    puts "genus=  #{genus}"
    puts "natural_product_name=  #{natural_product_name} "
    puts "species=  #{species} "
    puts "literature_reference= #{literature_reference} "
    puts "solvent= #{solvent} "
    puts "session_id = #{session_id}"
    puts "spectrum_type = #{spectrum_type}"
    puts "spectrometer_frequency = #{spectrometer_frequency}"
    puts "temperature = #{temperature}"
    puts "chemical_shift_standard = #{chemical_shift_standard}"
    puts "literature_reference_type = #{literature_reference_type}"
    puts "physical_state_of_compound = #{physical_state_of_compound}"
    puts "melting_point = #{melting_point}"
    puts "boiling_point = #{boiling_point}"


    nmrpred_1h_peaklist_abs_path=Rails.root.join("#{session_directory}","#{session_name}_1h_peaklist.txt")
    nmrpred_output_mol_abs_path=Rails.root.join("#{session_directory}","#{session_name}_output.mol")
    nmrml_creator_log_abs_path=Rails.root.join("#{session_directory}","#{session_name}_creator.log")
    # New NMRpred inputs August 12th 2020
    nmrpred_param_abs_path=Rails.root.join("#{session_directory}","#{session_name}_1h_params.txt")
    nmrpred_fid_abs_path=Rails.root.join("#{session_directory}","#{session_name}_1h_fid.txt")
    nmrpred_spectrum_abs_path=Rails.root.join("#{session_directory}","#{session_name}_1h_spectrum.txt")
    nmrpred_13c_peaklist_abs_path=Rails.root.join("#{session_directory}","#{session_name}_13c_peaklist.txt")
    nmrpred_13c_param_abs_path=Rails.root.join("#{session_directory}","#{session_name}_13c_params.txt")
    nmrpred_13c_fid_abs_path=Rails.root.join("#{session_directory}","#{session_name}_13c_fid.txt")
    nmrpred_13c_spectrum_abs_path=Rails.root.join("#{session_directory}","#{session_name}_c13_spectrum.txt")

    nmrml_basename="#{session_name}.nmrML"
    nmrml_url="public/downloads/#{session_name}.nmrML"
    nmrml_abs_path=Rails.root.join("#{session_directory}","#{nmrml_basename}")

    session[:nmrml_basename] = nmrml_basename
    session[:nmrml_url] = nmrml_url
    session[:nmrml_abs_path] = nmrml_abs_path

    puts "nmrml_creator_script = #{nmrml_creator_script}"
    puts "session_directory = #{session_directory}"
    puts "natural_product_name = #{natural_product_name}"
    puts "nmrml_basename = #{nmrml_basename}"
    puts "nmrml_abs_path = #{nmrml_abs_path}"

    nmrml_command=""
    nmrml_command+="#{python_path} "
    nmrml_command+="#{nmrml_creator_script} "
    nmrml_arguments=""
    nmrml_arguments+=" -mol #{nmrpred_output_mol_abs_path} "
    nmrml_arguments+=" -pl #{nmrpred_1h_peaklist_abs_path} "
    nmrml_arguments+=" -output_path #{nmrml_abs_path} "
	nmrml_arguments+=' -name "%s" ' % natural_product_name
    #New arguments, August 12th 2020
    nmrml_arguments+=" -genus '#{genus}' "
    nmrml_arguments+=" -solvent '#{solvent}' "
    nmrml_arguments+=" -species '#{species}' "
    nmrml_arguments+=" -freq #{spectrometer_frequency} "
    nmrml_arguments+=" -ref '#{literature_reference}' "
    nmrml_arguments+=" -standard #{chemical_shift_standard} "
    nmrml_arguments+=" -temp #{temperature} "
    nmrml_arguments+=" -spec_type '#{spectrum_type}' "
    nmrml_arguments+=" -param_path #{nmrpred_param_abs_path} "
    nmrml_arguments+=" -fid_path #{nmrpred_fid_abs_path} "
    nmrml_arguments+=" -spec_path #{nmrpred_spectrum_abs_path} "

    nmrml_arguments+=" -13C_pl #{nmrpred_13c_peaklist_abs_path} "
    nmrml_arguments+=" -13C_param_path #{nmrpred_13c_param_abs_path} "
    nmrml_arguments+=" -13C_fid_path #{nmrpred_13c_fid_abs_path} "
    nmrml_arguments+=" -13C_spec_path #{nmrpred_13c_spectrum_abs_path} "

    nmrml_arguments+=" -ref_type '#{literature_reference_type}' "
    nmrml_arguments+=" -phys_state '#{physical_state_of_compound}' "
    nmrml_arguments+=" -melt_point '#{melting_point}' "
    nmrml_arguments+=" -boil_point '#{boiling_point}' "

    nmrml_arguments+=" > #{nmrml_creator_log_abs_path} "
    nmrml_command+=nmrml_arguments

    puts "nmrml_command = #{nmrml_command}"
    `#{nmrml_command}`

    #puts session.to_hash
  end

  def download_nmrml()
    load_submission_object_by_user
    nmrml_basename=session[:nmrml_basename]
    nmrml_url=session[:nmrml_url]
    nmrml_abs_path=session[:nmrml_abs_path]["path"]
    # session_id=session[:session_id]
    session_id=session.id
    puts "nmrml_basename = #{nmrml_basename}"
    puts "nmrml_abs_path = #{nmrml_abs_path}"
    puts "nmrml_url = #{nmrml_url}"
    puts "session_id = #{session_id}"
    file_name=nmrml_basename

    #file_name = ChemicalShiftSubmission.Download_file(@chemical_shift_submission)
    send_file "#{nmrml_abs_path.to_s}", :type=>"application/csv", :filename => "#{file_name}", :disposition => 'attachment'
  end

  def download_nmrml_from_outside
    load_submission_object_without_user_session
    file_name = "#{@submission.id}_#{@natural_product.np_mrd_id}_#{@submission.user_id}.nmrML"
    nmrml_abs_path = Rails.root.join("public", "downloads", "#{@submission.user_session_id}","#{file_name}").to_s
    send_file "#{nmrml_abs_path}", :type=>"application/csv", :filename => "#{file_name}", :disposition => 'attachment'
  end

  def download_spectra_image_from_outside
    load_submission_object_without_user_session
    file_name = "#{@submission.id}_#{@natural_product.np_mrd_id}_#{@submission.user_id}_1h_1d.png"
    image_abs_path = Rails.root.join("public", "downloads", "#{@submission.user_session_id}","#{file_name}").to_s
    send_file "#{image_abs_path}", :type=>"application/png", :filename => "#{file_name}", :disposition => 'attachment'
  end


 def process_data
    load_submission_object_without_user_session
    pub_dir     = Rails.root.join("public")
    script_dir  = Rails.root.join("backend","magmet")
    python_path = PYTHON_ENV["#{Rails.env}"]['python_path']
    script      = Rails.root.join("backend","magmet/processing_np_3.py")
    file_list = []
    name_list = []
    quality_list = []
    param_list = []
    @submission.nmr_submissions.each do |file|
      solvent = "D2O"
      spectrum_type = "spectrum_type"
      reference = "DSS"
      fid_path = file.nmr_file.path

      sp_url = file.nmr_file.url
      sp_dir = File.dirname(sp_url)

      if file.solvent
        solvent = file.solvent
      end
      if file.nmr_spectrum_type
        spectrum_type = file.nmr_spectrum_type
      end
      if file.chemical_shift_standard
        reference = file.chemical_shift_standard
      end
      if file.spectrometer_frequency
        frequency = file.spectrometer_frequency
      else
        frequency = 'undefined'
      end

      process_np = "#{python_path} #{script} -m #{script_dir}/ -i #{fid_path} -o #{fid_path} -f #{frequency} -ref #{reference}"
  	  process_np+=" -sptype #{spectrum_type} -sol '#{solvent}' "
      `#{process_np}`
      sp_name = "#{spectrum_type}_#{solvent}_#{reference}_#{frequency}.json"
      sp_quality_name = "#{spectrum_type}_#{solvent}_#{reference}_#{frequency}.svg"
      sp_name_out = "#{spectrum_type}_#{solvent}_#{reference}_#{frequency}"
  	  sp_out_path ="#{sp_dir}/#{sp_name}"
      sp_quality_path = "#{sp_dir}/#{sp_quality_name}"
      name_list.append(sp_name_out)
      file_list.append(sp_out_path)
      quality_list.append(sp_quality_path)
      param_list.append(read_param_sp(File.join("public",sp_out_path)))

    end
    session[:name_list] = name_list
    session[:file_list] = file_list
    session[:sp_quality_img_list] = quality_list
    session[:param_list] = param_list
  end


############## Method used by the update  action starts #####################
  def preparation_for_bulding_shift_table(session_mol_csv,session_submission_id,spectrum_type)
    ##### take the atoms and symbol for the submitted molecule
    @atom_symbol = Submission.ReadAtoms(session_mol_csv) # it is a two dimentional array [[atom1,symbol1],[atom2,symbol2]]
    @chemical_shifts = Array.new
    if ChemicalShift.exists?(:submission_id => session_submission_id)
      @chemical_shifts_relations = ChemicalShift.where(:submission_id => session_submission_id)
      @chemical_shifts_relations.each do |c|
        if spectrum_type == "1D-1H" or spectrum_type == "1D-1H-DEPT90" or spectrum_type =="2D-1H-1H-COSY" or spectrum_type == "2D-1H-1H-TOCSY" or spectrum_type == "2D-1H-1H-ROESY"
          if c.atom_symbol == "C"
            next
          else
            @chemical_shifts.push(c)
          end
        elsif spectrum_type == "1D-13C" or spectrum_type == "1D-13C-DEPT90" or spectrum_type == "2D-13C-13C-COSY" or spectrum_type == "2D-13C-13C-INADEQUATE"
          if c.atom_symbol == "H"
            next
          else
            @chemical_shifts.push(c)
          end
        else
          @chemical_shifts.push(c)
        end
      end
      @submission = Submission.find(session_submission_id)
    else
      @atom_symbol.each do |atom|
        if spectrum_type == "1D-1H" or spectrum_type == "1D-1H-DEPT90" or spectrum_type == "2D-1H-1H-COSY" or spectrum_type == "2D-1H-1H-TOCSY" or spectrum_type == "2D-1H-1H-ROESY"
          if atom[1] == "C"
            next
          else
            c = ChemicalShift.new(atom_id: atom[0], atom_symbol: atom[1], chemical_shift_pred:atom[2],multiplet_pred:atom[3],jcoupling_pred:atom[4])
            @chemical_shifts.push(c)
          end
        elsif spectrum_type == "1D-13C" or spectrum_type == "1D-13C-DEPT90" or spectrum_type == "2D-13C-13C-COSY" or spectrum_type == "2D-13C-13C-INADEQUATE"
          if atom[1] == "H"
            next
          else
            c = ChemicalShift.new(atom_id: atom[0], atom_symbol: atom[1], chemical_shift_pred:atom[2],multiplet_pred:atom[3],jcoupling_pred:atom[4])
            @chemical_shifts.push(c)
          end
        else
          c = ChemicalShift.new(atom_id: atom[0], atom_symbol: atom[1], chemical_shift_pred:atom[2],multiplet_pred:atom[3],jcoupling_pred:atom[4])
          @chemical_shifts.push(c)
        end

      end
      @submission = Submission.find(session_submission_id)
    end
  end
############## Method used by the update  action starts #####################

def calc_assignment_score
  cm_score = 0
  cm_i     = 0
  @chemical_shifts.each do |cs|
    test_list = ['NA',"",nil]
    if test_list.include? cs.chemical_shift_true
      cs.chemical_shift_true = "NA"
    end
    if test_list.include? cs.multiplet_true
      cs.multiplet_true = "NA"
    end
    if test_list.include? cs.jcoupling_true
      cs.jcoupling_true = "NA"
    end
    # if test_list.include? cs.assigned_peaks
    #   cs.assigned_peaks = "NA"
    # end

    if [cs.chemical_shift_true,cs.multiplet_true,cs.jcoupling_true].all? { |e| e != 'NA' }
      cs.assignment_level = "Level-1"
      cs.assignment_score = 100.0.to_s
    elsif [cs.chemical_shift_true,cs.multiplet_true].all? { |e| e != 'NA' } and cs.jcoupling_true == "NA"
      if cs.multiplet_true == 's' or cs.multiplet_true == 'm'
        cs.assignment_level = "Level-1"
        cs.assignment_score = 100.0
      else
        cs.assignment_level = "Level-2"
        cs.assignment_score = 70.0
      end
    elsif cs.chemical_shift_true != 'NA' and cs.jcoupling_true == "NA" and cs.multiplet_true == "NA"
      cs.assignment_level = "Level-3"
      cs.assignment_score = 50.0
    # elsif cs.chemical_shift_true == 'NA'  and cs.assigned_peaks != "NA"
    #   cs.assignment_level = "level-4"
    #   cs.assignment_score = 25.0
    elsif cs.chemical_shift_true== 'NA'
      cs.assignment_level = "Level-4"
      cs.assignment_score = 0.0
    else
      cs.assignment_level = "NA"
      cs.assignment_score = 'NA'
    end
    cm_score = cm_score+cs.assignment_score.to_f
    cm_i     = cm_i+1

  end
  # pub_dir     = Rails.root.join("public")
  script_dir  = Rails.root.join("backend","magmet")
  python_path = PYTHON_ENV["#{Rails.env}"]['python_path']
  script      = Rails.root.join("backend","magmet/assignment_score_plot.py")

  session_name = session[:session_name]
  score_image_name = "#{session_name}_assignment_score.svg"
  score_image_path = Rails.root.join("public","downloads","#{session.id}",score_image_name)
  final_score = cm_score/cm_i
  assignment_plot = "#{python_path} #{script} -s #{final_score} -f #{score_image_path}"
  `#{assignment_plot}`
  session[:score_image_path] = score_image_path
end

def show_assignment_report
  @score_rules = score_rules=[[1,"Yes","Yes","Yes","Level-1","100%"],
                              [2,"Yes","Yes","No","Level-2","75%"],
                              [3,"Yes","No","No","Level-3","50%"],
                              [4,"No","No","No","Level-4","0%"]]

  load_submission_object_by_user
  session_name = session[:session_name]
  session_id   = session.id
  assignment_report_file =  "#{session_name}_assignment_report.txt"
  @assignment_report_file_path = File.join("/downloads","#{session_id}",assignment_report_file)

  score_image_name = "#{session_name}_assignment_score.svg"
  @score_image_path = File.join("/downloads","#{session.id}",score_image_name)

  @chemical_shifts = @submission.chemical_shifts

  render "shared/_assignment_report", :locals => {:file_path => download_assignment_report_submission_path}

end

def download_assignment_report
    load_submission_object_by_user
    session_name = session[:session_name]
    session_id   = session.id
    assignment_report_file =  "#{session_name}_assignment_report.txt"
    assignment_report_file_path = Rails.root.join("public","downloads","#{session_id}",assignment_report_file)
    send_file "#{assignment_report_file_path.to_s}", :type=>"application/csv", :filename => "#{assignment_report_file}", :disposition => 'attachment'
end




############## Method used by the update  action starts #####################

  def chemical_shift_submission_from_session
    {
      "submission_meta_data_attributes" => {
        "provenance" => session[:provenance],
        "genus" => session[:genus],
        "species" => session[:species],
        "physical_state_of_compound" => session[:physical_state_of_compound],
        "melting_point" => session[:melting_point],
        "boiling_point" => session[:boiling_point],
        "literature_reference" => session[:literature_reference],
        "literature_reference_type" => session[:literature_reference_type]
        # "submission_id"	=> session[:submission_id]

      }
    }
  end
end
