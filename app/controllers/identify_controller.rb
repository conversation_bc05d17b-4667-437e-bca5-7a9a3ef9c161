require_dependency "moldbi/search/search_controller"
class IdentifyController < ApplicationController

  include ApplicationHelper
  include IdentifyHelper
  include NeutralLossHelper

  before_filter :set_variables

  POSITIVE_ADDUCTS = IdentifyQuery::POSITIVE_ADDUCTS
  NEGATIVE_ADDUCTS = IdentifyQuery::NEGATIVE_ADDUCTS
  ESI_SCORING_FUNCTIONS = IdentifyQuery::ESI_SCORING_FUNCTIONS
  EI_SCORING_FUNCTIONS = IdentifyQuery::EI_SCORING_FUNCTIONS

  def index
    # default to the find tab
    params[:submit_or_find_or_nl] ||= 'find'
    params[:text_or_file] ||= 'text'
    @identify_query = IdentifyQuery.new

    # Set defaults
    @identify_query.adduct_type = "neutral"
    @identify_query.ion_mode    = "positive"
    @identify_query.database    = IdentifyQuery::ESI_EXPERIMENTAL_DATABASES.values
    @identify_query.experimental_database    = IdentifyQuery::ESI_EXPERIMENTAL_DATABASES.values
    @identify_query.predicted_database    = IdentifyQuery::ESI_PREDICTED_DATABASES.values

    @mass_tol = 10.0
    @mass_tol_units = "ppm"
    @candidate_mass_tol = 10.0
    @candidate_mass_tol_units = "ppm"
  end

  def new
    identify_params = get_identify_params


    # Copy over the ion mode if using the find candidates form
    if params[:submit_or_find_or_nl] == "find" || params[:submit_or_find_or_nl] == "neutral-loss"
      identify_params[:ion_mode] =  params[:adduct_search_ion_mode]
      identify_params[:spectra_type] =  params[:adduct_search_spectra_type]
    end

    identify_params[:type] = "NeutralLossQuery"

    if params[:submit_or_find_or_nl] == "find" || params[:submit_or_find_or_nl] == "neutral-loss"
      identify_params[:type] = "NeutralLossQuery"
    else
      identify_params[:type] = "IdentifyQuery"
    end

    if params[:submit_or_find_or_nl] == "neutral-loss"
      identify_params[:nl_spectrum] = true
    else 
      identify_params[:nl_spectrum] = false
    end

    # identify_params[:user] = user_signed_in? ? current_user : nil
    identify_params[:param_file]  = get_param_file(identify_params[:spectra_type], identify_params[:ion_mode])
    identify_params[:config_file] = get_config_file(identify_params[:spectra_type], identify_params[:ion_mode])

    # Use spectra file if both file and text input are given
    if params[:text_or_file] == "file"
      params[:low_spectra] = ""
      params[:medium_spectra] = ""
      params[:high_spectra] = ""
      identify_params[:spectra] = ""
    elsif params[:text_or_file] == "text"
      spectra_text = ""
      if params[:low_spectra].present?
        # spectra_text << "energy0\n" << remove_header(params[:low_spectra]).strip + "\n"
        spectra_text_temp = "energy0\n" << remove_header(params[:low_spectra]).strip + "\n"
        spectra_text << convert_scientific_notation(spectra_text_temp, 'energy0')
      end
      if params[:medium_spectra].present?
        # spectra_text << "energy1\n" << remove_header(params[:medium_spectra]).strip + "\n"
        spectra_text_temp = "energy1\n" << remove_header(params[:medium_spectra]).strip + "\n"
        spectra_text << convert_scientific_notation(spectra_text_temp, 'energy1')
      end
      if params[:high_spectra].present?
        # spectra_text << "energy2\n" << remove_header(params[:high_spectra]).strip + "\n"
        spectra_text_temp = "energy2\n" << remove_header(params[:high_spectra]).strip + "\n"
        spectra_text << convert_scientific_notation(spectra_text_temp, 'energy2')
      end
      identify_params[:input_file] = ""
      identify_params[:spectra] = spectra_text
    end

    # Use candidates file if both file and text input are given
    if params[:submit_or_find_or_nl] == "submit"
      if !identify_params[:candidates_file].blank?
        identify_params[:candidates] = ""
      end
      identify_params[:database] = [] #Ensure blank if using submitted candidates, this is the flag to determine which program to run
      identify_params[:experimental_database] = []
      identify_params[:predicted_database] = []
    elsif params[:submit_or_find_or_nl] == "find" || params[:submit_or_find_or_nl] == "neutral-loss"
      identify_params[:candidates] = ""
      identify_params[:candidates_file] = ""
    end

    if identify_params[:num_results].blank? || ( identify_params[:num_results] =~ /[\d]+/ && identify_params[:num_results].to_i < 1)
      identify_params[:num_results] = -1
    end

    # Setting mass tolerance
    if params[:mass_tol_units] == "ppm"
      identify_params[:ppm_mass_tol] = params[:mass_tol]
      identify_params[:abs_mass_tol] = -1
    else
      identify_params[:abs_mass_tol] = params[:mass_tol]
      identify_params[:ppm_mass_tol] = -1
    end

    # Setting candidate mass tolerance
    if params[:candidate_mass_tol_units] == "ppm"
      identify_params[:candidate_ppm_mass_tol] = params[:candidate_mass_tol]
      identify_params[:candidate_abs_mass_tol] = nil
    else
      identify_params[:candidate_abs_mass_tol] = params[:candidate_mass_tol]
      identify_params[:candidate_ppm_mass_tol] = nil
    end
    @identify_query = NeutralLossQuery.new(identify_params)
    # if params[:submit_or_find_or_nl] == "neutral-loss"
    #   @identify_query = NeutralLossQuery.new(identify_params)
    # else 
    #   @identify_query = IdentifyQuery.new(identify_params)
    # end
    
    @mass_tol = params[:mass_tol]
    @mass_tol_units = params[:mass_tol_units]
    @candidate_mass_tol = params[:candidate_mass_tol]
    @candidate_mass_tol_units = params[:candidate_mass_tol_units]
    @low_spectra = params[:low_spectra]
    @medium_spectra = params[:medium_spectra]
    @high_spectra = params[:high_spectra]


    if @identify_query.valid? && @identify_query.convert_spectra && @identify_query.save
      # job_id = NeutralLossWorker.perform_async(@identify_query.id, params)
      if params[:submit_or_find_or_nl] == "neutral-loss" || params[:submit_or_find_or_nl] == "find"
        job_id = NeutralLossWorker.perform_async(@identify_query.id, params)
      else
        job_id = IdentifyWorker.perform_async(@identify_query.id)
      end
    else
      if @identify_query.errors.any?
        if @identify_query.errors[:threshold]
          @identify_query.threshold = 0.001
        end
        if @identify_query.errors[:num_results]
          @identify_query.num_results = 10
        end
        if @identify_query.errors[:ppm_mass_tol] || @identify_query.errors[:abs_mass_tol]
          @mass_tol = 10.0
          @mass_tol_units = "ppm"
        end
        if @identify_query.errors[:candidate_ppm_mass_tol] || @identify_query.errors[:candidate_abs_mass_tol]
          @candidate_mass_tol = 10.0
          @candidate_mass_tol_units = "ppm"
        end
        if @identify_query.errors[:candidate_limit]
          @identify_query.candidate_limit = 100
        end
        errors = process_errors(@identify_query.errors.full_messages).html_safe
      else
        errors = "Input error, please check your input values or contact an administrator."
      end
      flash.now[:alert] = errors
      render(:action => 'index') and return
    end


    if params[:submit_or_find_or_nl] == "find" || params[:submit_or_find_or_nl] == "neutral-loss"
      if !job_id.nil? && NeutralLossQuery.find_by_id(@identify_query.id).update_attributes(job_id: job_id)
        redirect_to(query_status_path(NeutralLossQuery.find_by_id(@identify_query.id).secret_id))
      else
        redirect_to(identify_path, notice: process_errors(@identify_query.errors.full_messages).html_safe) and return
      end
    else 
      if !job_id.nil? && IdentifyQuery.find_by_id(@identify_query.id).update_attributes(job_id: job_id)
        redirect_to(query_status_path(IdentifyQuery.find_by_id(@identify_query.id).secret_id))
      else
        redirect_to(identify_path, notice: process_errors(@identify_query.errors.full_messages).html_safe) and return
      end
    end
  end

  def get_identify_params
    params.require(:identify_query).permit(:type,
                  {database: []}, {experimental_database: []}, {predicted_database: []}, 
                  # {experimental_database_find: []}, {predicted_database_find: []},
                  :num_results, :threshold,
                  :ppm_mass_tol,
                  :candidates_file, :candidates, :candidate_limit, 
                  :neutral_mass, :candidate_ppm_mass_tol, 
                  :parent_ion_mass, :parent_ion_mass_find,
                  :parent_ion_mass_type, :parent_ion_mass_type_find, :ion_mode, 
                  :adduct_type, :spectra_type, :spectra_id, :param_file, 
                  :config_file, :user)
  end

  private

  def set_variables
    @positive_adducts = POSITIVE_ADDUCTS
    @negative_adducts = NEGATIVE_ADDUCTS
    @esi_functions = ESI_SCORING_FUNCTIONS
    @ei_functions = EI_SCORING_FUNCTIONS
  end

end

