class RetentionIndexPredictionsController < ApplicationController
  
  include Shared::ChemicalShiftSubmissionLoader
  include Shared::NaturalProductLoader
  include Shared::PredictionLoader

  require 'fileutils'
  require 'open-uri'
  skip_before_action :verify_authenticity_token

  def new
    @retention_index_prediction = RetentionIndexPrediction.new()
    #@chemical_shift_submission.build_chemical_shift_submission_meta_data
  end
############## create action starts #####################

  def create
    retention_index_params = params[:retention_index_prediction]  # Get the input of the users from the page
    #debugger
    if retention_index_params[:compound_derivative_type] == "TMS_AND_TBDMS"
      check_tms = check_retention_index_predictions(retention_index_params[:compound_stationary_phase], "TMS", params[:structure_input])
      #retention_index_params[:compound_derivative_type] = "TMS"
      check_tbdms = check_retention_index_predictions(retention_index_params[:compound_stationary_phase], "TBDMS", params[:structure_input])
      if !check_tms && !check_tbdms
        new_retention_index_prediction(retention_index_params)
      elsif !check_tms && check_tbdms
        retention_index_params[:compound_derivative_type] = "TMS"
        new_retention_index_prediction(retention_index_params)
        retention_index_params[:compound_derivative_type] = "TMS_AND_TBDMS"
      elsif check_tms && !check_tbdms
        retention_index_params[:compound_derivative_type] = "TBDMS"
        new_retention_index_prediction(retention_index_params)
        retention_index_params[:compound_derivative_type] = "TMS_AND_TBDMS"
      end

      check_tms2 = check_retention_index_predictions(retention_index_params[:compound_stationary_phase], "TMS", params[:structure_input])
      check_tbdms2 = check_retention_index_predictions(retention_index_params[:compound_stationary_phase], "TBDMS", params[:structure_input])

      if check_tms2 && check_tbdms2
        flash.now[:status] = 2
      else
        flash.now[:status] = 1
      end
      #!check_tms ? new_retention_index_prediction(retention_index_params) : flash.now[:status] = 1
      #check_tbdms = check_retention_index_predictions(retention_index_params[:compound_stationary_phase], "TBDMS", params[:structure_input])
      ##retention_index_params[:compound_derivative_type] = "TBDMS"
      #!check_tbdms ? new_retention_index_prediction(retention_index_params) : flash.now[:status] = 1
    elsif retention_index_params[:compound_derivative_type] == "TMS"
      check_tms = check_retention_index_predictions(retention_index_params[:compound_stationary_phase], "TMS", params[:structure_input])
      if !check_tms
        new_retention_index_prediction(retention_index_params)
        check_tms2 = check_retention_index_predictions(retention_index_params[:compound_stationary_phase], "TMS", params[:structure_input])
        if !check_tms2
          flash.now[:status] = 1
        end
      else
        flash.now[:status] = 2
      end
      #puts check_tms
      #!check_tms ? new_retention_index_prediction(retention_index_params) : flash.now[:status] = 2
    elsif retention_index_params[:compound_derivative_type] == "TBDMS"
      check_tbdms = check_retention_index_predictions(retention_index_params[:compound_stationary_phase], "TBDMS", params[:structure_input])
      if !check_tbdms
        new_retention_index_prediction(retention_index_params)
        check_tbdms2 = check_retention_index_predictions(retention_index_params[:compound_stationary_phase], "TBDMS", params[:structure_input])
        if !check_tbdms2
          flash.now[:status] = 1
        end
      else
        flash.now[:status] = 2
      end
      #puts check_tbdms
      #!check_tbdms ? new_retention_index_prediction(retention_index_params) : flash.now[:status] = 2
    else
      check_compound = check_retention_index_predictions(retention_index_params[:compound_stationary_phase], retention_index_params[:compound_derivative_type], params[:structure_input])
      !check_compound ? new_retention_index_prediction(retention_index_params) : flash.now[:status] = 2  # Set success flash for next page
    end
    @tester = params[:retention_index_prediction]
    render "show"
  end
######################### Create action finished ######################################

##################### index action starts ####################
  def index()
    load_submission_objects(params[:phase] || "", params[:type] || "", params[:smile] || "")
    respond_to do |format|
      format.html
    end
  end
######################### Index action finished ######################################

############## Edit action starts #####################
  def edit
  end
######################### Edit action finished ######################################

############## Update action starts #####################
  def update
  end

######################### Update action finished ######################################

############## Show action starts #####################
  def show
    respond_to do |format|
      format.html
    end
  end

  def display_all
    load_submission_object_without_user_session
    respond_to do |format|
      format.html
    end
  end

  def download_mol()
	  mol_download(params["mol_smile"], params["name"])
  end

  def download_csv()
    load_submission_objects(params[:phase] || "", params[:type] || "", params[:smile] || "")
	  generate_download_prediction(params[:download_type])
  end
  
  private 
  def create_retention_index_prediction(retention_index_params)
    RetentionIndexPrediction.create!(compound_name: retention_index_params[:compound_name],
      compound_smiles: params[:structure_input],
      original_smile: params[:structure_input],
      compound_stationary_phase: retention_index_params[:compound_stationary_phase],
      compound_derivative_type: retention_index_params[:compound_derivative_type],
      user_session_id: session.id,
    )
  end

  def new_retention_index_prediction(retention_index_params)
    flag1=""
    flag2=""
    phase = ""
    derivative = ['under']  # No-Derivatization always processed, Derivatization might not (needs to be checked)

    # Account for derivatization if selected
	  if retention_index_params[:compound_derivative_type]=="TMS" || retention_index_params[:compound_derivative_type]=="TBDMS" || retention_index_params[:compound_derivative_type]=="TMS_AND_TBDMS"
      flag1="derivatized"
      derivative.push("der")
    else
      flag1="nonderivatized"
    end

    # Set phase flags and determine part of output file name
    if retention_index_params[:compound_stationary_phase]=="Semi Standard Non Polar"
      flag2="SSNP"
      phase = "semistdnp_transformer"
    elsif retention_index_params[:compound_stationary_phase]=="Standard Non Polar"
      flag2="SNP"
      phase = "stdnp_transformer"
    elsif retention_index_params[:compound_stationary_phase]=="Standard Polar"
      flag2="SP"
      phase = "stdpolar_transformer"
    end
    
    complete = 1

    begin  # Error handling for python exceptions and file problems
      @retention_index_prediction = create_retention_index_prediction(retention_index_params)  # Create first item
      @retention_index_prediction.start_processing()  # Run python scripts
      derivative.each do |type|  # Edit/Add all the necessary objects
        check_non = nil
        # Receive output of python scripts
        #csv.foreach: means for each row in that csv
        CSV.foreach("public/python/output_test/#{type}#{phase}/run_0/results/train_results.csv", headers: true) do |line|
          # Check that the python scripts ran properly
          raise "Python Exception" unless !line['Predicted_RI'].nil?
          if (type == "under")
		        check_non = check_retention_index_predictions(@retention_index_prediction[:compound_stationary_phase], "No-Derivatization", @retention_index_prediction[:compound_smiles])
            @retention_index_prediction.update(compound_derivative_type: "No-Derivatization")  # set derivatization type to appropriate setting
            @retention_index_prediction.update(compound_smiles: line['smiles'], compound_name: line['Name'].split(retention_index_params[:compound_name],2)[1])  # Set new smile and name
            @retention_index_prediction.update(predicted_RI: line['Predicted_RI'], label: line['label'])  # Set the retention index and label
          
          elsif (type == "der") and retention_index_params[:compound_derivative_type]=="TMS_AND_TBDMS"
            #retention_index_params[:compound_derivative_type] = "TMS"
            @retention_index_prediction = create_retention_index_prediction(retention_index_params)  # Create new item
            #@retention_index_prediction.update(compound_derivative_type: "TMS")
            @retention_index_prediction.update(compound_derivative_type: line['Derivatization_Type'])
            @retention_index_prediction.update(compound_smiles: line['smiles'], compound_name: line['Name'].split(retention_index_params[:compound_name],2)[1])  # Set new smile and name
            @retention_index_prediction.update(predicted_RI: line['Predicted_RI'], label: line['label'])  # Set the retention index and label

            # #retention_index_params[:compound_derivative_type] = "TBDMS"
            # @retention_index_prediction = create_retention_index_prediction(retention_index_params)  # Create new item
            # @retention_index_prediction.update(compound_derivative_type: "TBDMS")
            # @retention_index_prediction.update(compound_smiles: line['smiles'], compound_name: line['Name'].split(retention_index_params[:compound_name],2)[1])  # Set new smile and name
            # @retention_index_prediction.update(predicted_RI: line['Predicted_RI'], label: line['label'])  # Set the retention index and label

          else
            @retention_index_prediction = create_retention_index_prediction(retention_index_params)  # Create new item
            @retention_index_prediction.update(compound_smiles: line['smiles'], compound_name: line['Name'].split(retention_index_params[:compound_name],2)[1])  # Set new smile and name
            @retention_index_prediction.update(predicted_RI: line['Predicted_RI'], label: line['label'])  # Set the retention index and label  
          end
          #@retention_index_prediction.update(compound_smiles: line['smiles'], compound_name: line['Name'])  # Set new smile and name
          if (check_non && retention_index_params[:compound_derivative_type] != "No-Derivatization") # Destroy if pre-existing
            @retention_index_prediction.destroy()
          end
          complete = 2
        end
        #File.delete("public/python/output_test/#{type}#{phase}/run_0/results/train_results.csv") if File.exist?("public/python/output_test/#{type}#{phase}/run_0/results/train_results.csv")
        puts "printing complete's value:"
        puts complete
      end
      flash.now[:status] = complete # Set success flash for next page
    rescue Errno::ENOENT
      puts "INSIDE ERRNO!"
      @retention_index_prediction.destroy unless @retention_index_prediction[:compound_derivative_type] == "No-Derivatization"
      complete = complete - 1
      flash.now[:status] = complete # Set failure flash for next page
    rescue RuntimeError
      puts "INSIDE RUN TIME ERROR!"
      @retention_index_prediction.destroy
      complete = complete - 1
      flash.now[:status] = complete  # Set failure flash for next page
    rescue ValueError
      puts "bad smiles input found, can't convert it into rdkit mols"
      @retention_index_prediction.destroy
      complete = complete - 1
      flash.now[:status] = complete  # Set failure flash for next page
    end
  end

  def retention_index_predictions_params
    params.require(:retention_index_prediction).permit(:compound_name,:compound_stationary_phase,:compound_derivative_type)
  end

end
