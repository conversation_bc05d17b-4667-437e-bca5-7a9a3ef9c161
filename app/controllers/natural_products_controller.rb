class NaturalProductsController < ApplicationController
  include Shared::NaturalProductLoader
  include Shared::SubmissionLoader

  # include submission_loader


  SORTABLE_COLUMNS = %i(np_mrd_id name moldb_formula moldb_average_mass moldb_mono_mass).freeze

  parse_sort_params(SORTABLE_COLUMNS, default_column: 'np_mrd_id', only: [:index])

  def index
    load_natural_product_index_objects

    respond_to do |format|
      format.html
    end
  end

  def show
    @natural_product = load_natural_product
    if @natural_product.np_mrd_id != params[:id]
      redirect_to @natural_product and return
    end
    respond_to do |format|
      format.html
      format.xml
    end

  rescue ActiveRecord::RecordNotFound
    raise
  end

  def spectrum_view

    @chemical_shift_submission = ChemicalShiftSubmission.where(:id => params[:id]).first
    @natural_product = NaturalProduct.find(@chemical_shift_submission.natural_product_id)
    nmrml_name  = "#{@chemical_shift_submission.id}_#{@natural_product.np_mrd_id}_#{@chemical_shift_submission.user_id}.nmrML"
    @nmrml_path = File.join("/downloads",@chemical_shift_submission.user_session_id,nmrml_name)

    render "np_spectrum_view"

  end

  def submission_spectrum_view
    nmr_spectrum_type_current = params['spectrum_type']
    @submission = Submission.where(:id => params[:id]).first
    @natural_product = NaturalProduct.find(@submission.natural_product_id)
    if nmr_spectrum_type_current == 'simulation'
      nmrml_name  = "#{@submission.id}_#{@natural_product.np_mrd_id}_#{@submission.user_id}.nmrML"
      @nmrml_path = File.join("/downloads",@submission.user_session_id,nmrml_name)
      render "np_spectrum_view"
    elsif nmr_spectrum_type_current == 'real'
      spectrum_current_id = params['spectrum_id']
      nmr_spectrum = @submission.nmr_submissions.where(id: spectrum_current_id).first
      @file_list = []
      @name_list = []
      @quality_list = []
      @param_list = []
      sp_path = nmr_spectrum.nmr_file.url
      sp_dir = File.dirname(sp_path)
      solvent = nmr_spectrum.solvent
      spectrum_type = nmr_spectrum.nmr_spectrum_type
      reference = nmr_spectrum.chemical_shift_standard
      if nmr_spectrum.spectrometer_frequency
        frequency = nmr_spectrum.spectrometer_frequency
      else
        frequency = 'undefined'
      end
      sp_name = "#{spectrum_type}_#{solvent}_#{reference}_#{frequency}.json"
      sp_name_out = "#{spectrum_type}_#{solvent}_#{reference}_#{frequency}"
      sp_quality_name = "#{spectrum_type}_#{solvent}_#{reference}_#{frequency}.svg"
      sp_out_path ="#{sp_dir}/#{sp_name}"
      sp_quality_path = "#{sp_dir}/#{sp_quality_name}"
      @name_list.append(sp_name_out)
      @file_list.append(sp_out_path)
      @quality_list.append(sp_quality_path)
      @param_list.append(read_param_sp(File.join("public",sp_out_path)))
      render "np_spectrum_view_real"

    end
  end
end
