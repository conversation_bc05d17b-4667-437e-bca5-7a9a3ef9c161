class NmrPredsController < ApplicationController
  include Shared::NmrPredLoader
  require 'fileutils'
  require 'open-uri'
  require 'zip'

  def index

  end

  def new
    if UserSession.try(:find).nil?
      redirect_to sign_in_path
    end
    @nmr_pred = NmrPred.new

  end


  def show
  end


  def create
    @nmr_pred = NmrPred.new(nmr_pred_params)
    puts "session[:session_id] = #{session[:session_id]["public_id"]}"
    @nmr_pred.save
    @nmr_pred.user_session_id = session[:session_id]["public_id"]
    @nmr_pred.save
    @user_session_id =  @nmr_pred.user_session_id
    puts "user_session_id = #{@user_session_id}"
    @shift_file = NmrPred.create_agent(params[:structure_input],params[:nmr_pred][:solvent],params[:nmr_pred][:nucleus],@nmr_pred.id)
    #create session directory if not
    @session_directory=Rails.root.join('public','downloads',"#{@user_session_id}")
    puts "Creating session_directory = #{@session_directory}"
    Dir.mkdir @session_directory unless File.exists?(@session_directory)
    @utility_directory = Rails.root.join('public','downloads',"#{@user_session_id}","utility")
    print("utility_directory = #{@utility_directory}")
    Dir.mkdir @utility_directory unless File.exists?(@utility_directory)

    #save data in table
    # @nmr_pred.user_session_id = "#{@user_session_id}"
    @nmr_pred.user_id = current_user.id
    @nmr_pred.smiles = params[:structure_input]
    @nmr_pred.save!
    

#    @fpath = NmrPred.DrawMol(params["structure_input"],"backend","nmr-pred","draw_mol.py",@nmr_pred,@utility_directory) #.mol file with path
#    puts"created captured_file = #{@fpath}"
    stdin,stdout,stderr = Open3.popen3("#{PYTHON_ENV["#{Rails.env}"]['python_path']} #{Rails.root}/public/python/rdkit_smiles_to_mol_nmr_pred.py '#{params[:structure_input]}'")
    @mol = stdout.gets(nil).to_s
  # @captured_file = @nmr_pred.sdf_file.path # file name with path
  # print("captured_file=#{@captured_file}")

  # Zip::File.open(@captured_file) do |zip_file|
  #   # zip_file.first do |f|
  #   @fpath = File.join(@utility_directory, zip_file.first.name)
  #   if @fpath.end_with?(".mol")
  #     temp = @fpath.split("/")[-1]
  #     temp = temp.split(".mol")[0]
  #     @fpath = @fpath.split("#{temp}")[0]
  #     temp = "#{@nmr_pred.id}"+"_#{temp}_#{current_user.id}_utility_H2O_prediction.mol"
  #     @fpath = "#{@fpath}"+"#{temp}"
  #   end
  #   if @fpath.end_with?(".sdf")
  #     temp = @fpath.split("/")[-1]
  #     temp = temp.split(".sdf")[0]
  #     @fpath = @fpath.split("#{temp}")[0]
  #     temp = "#{@nmr_pred.id}"+"_#{temp}_#{current_user.id}_utility_H2O_prediction.mol"
  #     @fpath = "#{@fpath}"+"#{temp}"
  #   end
  #   print("@fpath = #{@fpath}")
  #   zip_file.extract(zip_file.first, @fpath) unless File.exist?(@fpath)
  #   # end
  # end




#    @file_name_prefix = @fpath.split("/")[-1] 
#    @file_name_prefix = @file_name_prefix.split(".mol")[0]#without .mol and without path
#    print("file_name_prefix=#{@file_name_prefix}")
#    @train_file_with_path = Rails.root.join('backend', 'nmr-pred', 'NmrPred', 'get_descriptor', 'train_hmdb_onlybmrb_swapped_COH2_fixed_7point92_with_uncommon_consistentCH2_no_null.csv')
#    NmrPred.FeatureCreation(@fpath,@utility_directory,@file_name_prefix) # .mol file with path, utility directory path, file name without .mol and without path
#    @test_file_with_path = "#{@fpath}".split(".mol")[0]+"_testfile.csv"
#    print("@test_file_with_path = #{@test_file_with_path}")
    # NmrPred.PredictionShift(@train_file_with_path,@test_file_with_path,@utility_directory,@file_name_prefix)
    puts"nmrpred H2O done"
#    @shift_file = "#{@utility_directory}"+"/"+"#{@file_name_prefix}_H2O_prediction.txt_after_swap"
    puts("shift_file = #{@shift_file}")
    @shift_position = NmrPred.PredictionResult(@shift_file,@user_session_id,@nmr_pred.id)
    print("@shift_position = #{@shift_position}")
    @shift_position.pop()
    @shift_position.delete_at(0)
    for i in 0..@shift_position.length-1 do
      if i == 0
        @nmr_pred.atom_id = @shift_position[i][1]
        @nmr_pred.shift_pred = @shift_position[i][0]
        @nmr_pred.save
      else
        nmr_p = NmrPred.create(:user_session_id => "#{@user_session_id}", :nucleus => params[:nucleus], :solvent => params[:solvent], :atom_id => "#{@shift_position[i][1]}", :shift_pred => "#{@shift_position[i][0]}", :smiles => params[:structure_input])
        nmr_p.save
      end
    end





    respond_to do |format|
      format.html
    end
  end
    
    # @msg = "under construction"

  private
 
  def nmr_pred_params
    params.require(:nmr_pred).permit(:solvent,:nucleus)
  end
    
end

