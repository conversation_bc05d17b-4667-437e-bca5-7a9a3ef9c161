class PasswordResetsController < ApplicationController
  before_action :load_user_using_perishable_token, :only => [:edit, :update]
  def new
    render
  end

  def create
    @user = User.find_by_email(params[:email])
    puts "user= #{@user.email}"
    if @user
      @user.deliver_password_reset_instructions!
      #flash[:success] = "Instructions to reset your password have been emailed to you."
      redirect_to(root_path, :notice => 'Instructions have been emailed to you. Please check your email')
    else
      flash[:warning] = "No user was found with that email address"
      render :new
    end
  end

  def edit
    @user = User.find_by(perishable_token: params[:id])
    puts "edit user id = #{@user.id}"
  end

  def update
    @user.password = params[:user][:password]
    @user.password_confirmation = params[:user][:password_confirmation]
    if @user.update_attributes(password_reset_params)
      flash[:notice] = "Password successfully updated!"
      redirect_to root_path
    else
      render :edit
    end
    # @user = User.find_by(perishable_token: params[:id])
    # if @user.update_attributes(password_reset_params)
    #   flash[:success] = "Password successfully updated!"
    #   redirect_to root_path
    # else
    #   render :edit
    # end
  end


  private
  
  def load_user_using_perishable_token
    puts "params[:id]= #{params[:id]}"
    @user = User.find_using_perishable_token(params[:id])
    unless @user
      flash[:notice] = "We're sorry but we could not find your account."
      redirect_to(root_url)
    end
  end

  def password_reset_params
    params.require(:user).permit(:password, :password_confirmation)
  end
end