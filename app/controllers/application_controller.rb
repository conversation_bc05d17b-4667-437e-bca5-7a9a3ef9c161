class ApplicationController < ActionController::Base
	include Wishart::SortParamsParser

	helper Unearth::ExtractableHelper
  helper Unearth::FilterableHelper
  helper Wishart::Engine.helpers
  helper Moldbi::StructureResourcesHelper
  helper Moldbi::<PERSON><PERSON><PERSON><PERSON>
  helper Moldbi::MarvinJs<PERSON>elper
  helper Specdb::RoutesHelper
  helper Specdb::MainAppHelper
  helper TmicBanner::Engine.helpers
  helper CiteThis::CitationHelper

  protect_from_forgery

  rescue_from ActiveRecord::RecordNotFound, with: :render_404

  protect_from_forgery with: :null_session,
  if: Proc.new { |c| c.request.format =~ %r{application/json} }
  # Render 404: Not Found
  def render_404(exception = nil)
    logger.info( clean_backtrace(exception, "Rendering 404 due to exception") )
    if /(jpe?g|png|gif)/i === request.path
      # Images should simply return the error as text
      return render(text: "404 Not Found", status: 404 )
    end
    render_error(404, "Resource Not Found")
  end

  protect_from_forgery
  
  helper_method :current_user
  
  private
  
  def current_user_session
    return @current_user_session if defined?(@current_user_session)
    @current_user_session = UserSession.find
  end
  
  def current_user
    return @current_user if defined?(@current_user)
    return nil if UserSession.try(:find).nil?
    @current_user = current_user_session && current_user_session.record
  end

  protected

  def render_error(code, message)
    respond_to do |format|
      format.html { render template: "/errors/#{code}", layout: 'application', status: code }
      format.json { render json: { error: message }, status: code }
      format.xml { render xml: { error: message }, status: code }
      format.any { render nothing: true, status: code }
    end
    true  # so we can do "render_xxx and return"
  end

  # Returns a "clean" backtrace that is suitable for humans
  def clean_backtrace(exception, preamble="Caught Exception")
    "\n\n#{preamble}:" + "\n\n#{exception.class} (#{exception.message}):\n    " +
            Rails.backtrace_cleaner.clean(exception.backtrace).join("\n    ") + "\n\n"
  end
  def authorize
    unless current_user
      session[:blocked_url] =  request.request_uri
      redirect_to(login_path, :notice => "You must be logged in as a verified user first")
    end
  end
 
  def redirect_back_or_default(uri)
    redirect_to(session[:blocked_url] || uri)
    session[:blocked_url] = nil
  end


end
