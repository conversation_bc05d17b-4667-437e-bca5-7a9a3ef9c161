class JsmolController < ApplicationController
    def new_structure_modal
        @smiles=params[:smiles]
    end

end
# frozen_string_literal: true

# Handles JSmol modal in metabocards
class JsmolController < ApplicationController
    # include Shared::MetaboliteLoader
  
    # Allows modal to render without application layouts
    layout false
  
    def new_modal
      initialize_metabolite
      @metabolite_structure = @metabolite.moldb_smiles
    end
  
    def new_nmr_modal
      initialize_metabolite
      @metabolite_structure = @metabolite.structure_resource.mol_3d
      @assignments = find_assignments @metabolite
    end
  
    def stereochemistry_modal
      initialize_metabolite
      @metabolite_structure = @metabolite.moldb_smiles
      @rs_labels = if @metabolite.rs_stereochemistry.present?
                     @metabolite.rs_stereochemistry.split(',').map { |pair| pair.split(':') }
                   end
    end
  
    def ri_modal
        @smiles = params[:id]
    end
  
    def initialize_metabolite
      @metabolite = load_metabolite
      redirect_to @metabolite and return if @metabolite.hmdb_id != params[:id]
    end
  
    private
  
    # Finds the first assignment table in the spectra for an NP
    # @param [Array] metabolite the natural product we are trying to find a table for
    # @return [String, nil] the url of the first assignment table
    def find_assignments(metabolite)
      metabolite.spectra.each do |spectrum|
        next if spectrum.nil? || spectrum.class.name != 'Specdb::NmrOneD'
  
        spectrum.documents.each do |document|
          if document.class.name == 'Specdb::NmrOneD::Document' && document.description.downcase == 'peak assignments'
            return parse_assignments(open(document.url).read)
          end
        end
      end
      nil
    end
  
    # Parses the TSV assignment table and returns them in an array
    # @param [String] raw_assignments the tsv file in a string
    # @return [Array] key value pairs with all atoms with assignments
    def parse_assignments(raw_assignments)
      assignments = []
      raw_assignments.split("\n").each do |data|
        parsed_data = data.split("\t")
        next if parsed_data[2].blank? || parsed_data[0] == "atom_id"
  
        assignments.push("#{parsed_data[0]},#{parsed_data[2]}")
      end
      assignments
    end
  
  end
  