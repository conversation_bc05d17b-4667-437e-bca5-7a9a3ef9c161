module Shared::RetentionIndexCompoundLoader
  extend ActiveSupport::Concern

  def load_retention_index_compound
    retention_index_compound_id = params[:id]
    if params[:id].include?("RI")
      retention_index_compound = RetentionIndexCompound.find_by(retention_index_compound_id: retention_index_compound_id)
    end

    # raise ActiveRecord::RecordNotFound if retention_index_compound.nil?
    # Moldbi::StructureResource
    # Moldbi::CurationResource
    # structure_thread = Thread.new do
    #   retention_index_compound.has_structure? 
    # end
    # curation_thread = Thread.new do
    #   retention_index_compound.has_curation?
    # end
    # structure_thread.join
    # curation_thread.join
    Rails.cache.fetch([retention_index_compound, 'with-includes']) do
      RetentionIndexCompound.find_by(id: retention_index_compound.id)
    end
  end

  def load_create_natural_product(np_mrd_id, name, structure)
    newly_created = false # flag to indicate if the natural product already existed or was newly created
    if np_mrd_id.present?
      np = NaturalProduct.find_by(np_mrd_id: np_mrd_id)
      return np, newly_created if np.valid?
    end
    if name.present?
      np = NaturalProduct.find_by(name: name)

      return np, newly_created if np
    end
    if structure.present?
      np = NaturalProduct.find_by(moldb_smiles: structure)
      return np, newly_created if np
    end
    # Create new structure
    newly_created = true
    np = NaturalProduct.new(:name => name)
    np.save!
    np.structure = structure
    np.save!
    np.load_threeDmol
    # ISSUE: InChIKey calculated after natural product is saved
    # Check to see if InChIKey already exists
    # candidates = NaturalProduct.where(moldb_inchikey: np.moldb_inchikey)
    # if candidates.count > 1

    #   np.destroy! # remove newly created natural product
    # return candidates.first # return the existing natural product
    #  else
    return np, newly_created
    # end
  end 




  def load_retention_index_compound_index_objects
    @retention_index_compounds = RetentionIndexCompound.
      page(params[:page]).
      order(@sorted_column => @sorted_order)
  end

  def search_retention_index_compounds(name, type, phase, rmin, rmax, mmin, mmax, tmsmin, tmsmax, tbdmsmin, tbdmsmax, tms_derivativenumber,tbdms_derivativenumber, smile)
    query = "(retention_index_value BETWEEN #{rmin} AND #{rmax})"
    query += " AND compound_stationary_phase = '#{phase}'" unless phase.empty?
    query += " AND (ri_compound_average_mass BETWEEN #{mmin} AND #{mmax})" unless mmax == -1
    
    query += " AND ri_compound_name LIKE '%#{name}%'" unless name.empty?
    query += " AND ri_compound_smiles = '#{smile}'" unless smile.empty?

    if (type == "TMS")
      query += " AND (compound_derivative_type = '#{type}')"
      query += " AND (derivative_number BETWEEN #{tmsmin} AND #{tmsmax})" unless tmsmax == -1

    elsif (type == "TBDMS")
      query += " AND (compound_derivative_type = '#{type}')"
      query += " AND (derivative_number BETWEEN #{tbdmsmin} AND #{tbdmsmax})" unless tbdmsmax == -1
    elsif (type == "TMS_AND_TBDMS")
      query += " AND (compound_derivative_type = '#{"TMS"}' OR compound_derivative_type = '#{"TBDMS"}' )"
     
      #query += " AND (derivative_number BETWEEN #{tmsmin} AND #{tmsmax})" unless tmsmax == -1
      #query += " AND (derivative_number BETWEEN #{tbdmsmin} AND #{tbdmsmax})" unless tbdmsmax == -1

      #if (!Float(tms_derivativenumber).nil? and !Float(tbdms_derivativenumber).nil?)
      #  query += " AND (ri_compound_name LIKE '%#{tms_derivativenumber}TMS%' AND ri_compound_name LIKE '%#{tbdms_derivativenumber}TBDMS%')"
      #elsif (!Float(tms_derivativenumber).nil?)
      #  query += " AND ri_compound_name LIKE '%#{tms_derivativenumber}TMS%'"
      #elsif (!Float(tbdms_derivativenumber))
      #  query += " AND ri_compound_name LIKE '%#{tbdms_derivativenumber}TBDMS%'"
      #end
    else
      query += " AND compound_derivative_type = 'Underivatized'"
    end

    @retention_index_search_results = RetentionIndexCompound.where(query)
                                                            .order("created_at DESC")
  end


  
  # def smiles_to_mw_convert(mol_smile)
  #   python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
  #   script_path = Rails.root.join("public", "python", "smiles_to_mol_wt.py")
  #   script_smile = "#{Regexp.escape(mol_smile)}"
  #   #script_smile = mol_smile
  #   #script_smile = "C"
  #   script_command = ""
  #   script_command += "#{python_path} "
  #   script_command += "#{script_path} "
  #   script_command += "#{script_smile}"
  #   #debugger
  #   puts script_command
  #   #mw = `#{script_command}`
  #   stdin,stdout,stderr = Open3.popen3(script_command)
  #   puts stdout.gets(nil).to_s
  #   #send_data mw
  # end
  
  def get_all_filtered_natural_products
    @all_filtered = NaturalProduct.exported.order(@sorted_column => @sorted_order)
    @all_filtered
  end

  def load_natural_product_with_id_param(np_mrd_id)
    np_mrd_id = np_mrd_id
    natural_product = NaturalProduct.exported.find_by(np_mrd_id: np_mrd_id)
    if natural_product.nil?
      if accession = AccessionNumber.for_natural_products.find_by(number: np_mrd_id)
        return accession.element
      end
    end

    raise ActiveRecord::RecordNotFound if natural_product.nil?
    Moldbi::StructureResource
    Moldbi::CurationResource
    structure_thread = Thread.new do
      natural_product.has_structure? 
    end
    curation_thread = Thread.new do
      natural_product.has_curation?
    end
    structure_thread.join
    curation_thread.join
    Rails.cache.fetch([natural_product, 'with-includes']) do
      NaturalProduct.includes(METABOLITE_SHOW_INCLUDES).find_by(id: natural_product.id)
    end
  end
end
