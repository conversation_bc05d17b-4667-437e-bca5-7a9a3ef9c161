module Shared::SubmissionLoader
  extend ActiveSupport::Concern

  #METABOLITE_SHOW_INCLUDES = [
   # :articles, :textbooks, :external_links, :accession_numbers
  #].freeze
  included do
    helper_method :read_param_sp
  end

  def load_submission

  end
  def load_submission_object_by_user
    @submission = Submission.where(:user_id => current_user.id, :id => params[:id]).first
    @natural_product = NaturalProduct.find(@submission.natural_product_id)
  end

  def load_submission_index_objects_by_user
    @submissions = Submission.where(:user_id => current_user.id).order("created_at DESC")
    @chemical_shift_submissions = ChemicalShiftSubmission.where(:user_id => current_user.id).order("created_at DESC")
    @batch_submissions = BatchSubmission.where(:user_id => current_user.id).order("created_at DESC")
    current_user_email = User.find(current_user.id).email
    @current_user_name_from_email = current_user_email.split("@")[0]
  end
  def load_submission_object_without_user_session
    @submission = Submission.where(:id => params[:id]).first
    @natural_product = NaturalProduct.find(@submission.natural_product_id)
  end

  def read_param_sp(f)
    require 'json'
    hs = File.read(f)
    hs1 = JSON.parse(hs)
    out = {}
    out['Instrument']  = hs1['Instrument']
    out['Frequency']   = hs1['Frequency']
    out['Linewidth']   = hs1['Linewidth']
    out['S/N']         = hs1['S/N']
    out['Reference']   = hs1['Reference']
    out['Solvent']     = hs1['Solvent']
    return out
end


end
