module Shared::RetentionIndexPredictionLoader
  extend ActiveSupport::Concern

  def load_retention_index_prediction
    retention_index_prediction_id = params[:id]
    puts "inside loader function"
    puts "#{params[:id]}"
    #if params[:id].include?("RIP")
    ##### populate the db first
    retention_index_compound = RetentionIndexCompound.find_by(retention_index_prediction_id: retention_index_prediction_id)
    #end

    # raise ActiveRecord::RecordNotFound if retention_index_compound.nil?
    # Moldbi::StructureResource
    # Moldbi::CurationResource
    # structure_thread = Thread.new do
    #   retention_index_compound.has_structure? 
    # end
    # curation_thread = Thread.new do
    #   retention_index_compound.has_curation?
    # end
    # structure_thread.join
    # curation_thread.join
    #Rails.cache.fetch([retention_index_prediction, 'with-includes']) do
    #  RetentionIndexPrediction.find_by(id: retention_index_prediction.id)
    #end
    return retention_index_compound
  end

  def load_create_natural_product(np_mrd_id, name, structure)
    newly_created = false # flag to indicate if the natural product already existed or was newly created
    if np_mrd_id.present?
      np = NaturalProduct.find_by(np_mrd_id: np_mrd_id)
      return np, newly_created if np.valid?
    end
    if name.present?
      np = NaturalProduct.find_by(name: name)

      return np, newly_created if np
    end
    if structure.present?
      np = NaturalProduct.find_by(moldb_smiles: structure)
      return np, newly_created if np
    end
    # Create new structure
    newly_created = true
    np = NaturalProduct.new(:name => name)
    np.save!
    np.structure = structure
    np.save!
    np.load_threeDmol
    # ISSUE: InChIKey calculated after natural product is saved
    # Check to see if InChIKey already exists
    # candidates = NaturalProduct.where(moldb_inchikey: np.moldb_inchikey)
    # if candidates.count > 1

    #   np.destroy! # remove newly created natural product
    # return candidates.first # return the existing natural product
    #  else
    return np, newly_created
    # end
  end 

  def load_retention_index_prediction_index_objects
    @retention_index_predictions = RetentionIndexPrediction.
      page(params[:page]).
      order(@sorted_column => @sorted_order)
  end

  def get_all_filtered_natural_products
    @all_filtered = NaturalProduct.exported.order(@sorted_column => @sorted_order)
    @all_filtered
  end

  def load_natural_product_with_id_param(np_mrd_id)
    np_mrd_id = np_mrd_id
    natural_product = NaturalProduct.exported.find_by(np_mrd_id: np_mrd_id)
    if natural_product.nil?
      if accession = AccessionNumber.for_natural_products.find_by(number: np_mrd_id)
        return accession.element
      end
    end

    raise ActiveRecord::RecordNotFound if natural_product.nil?
    Moldbi::StructureResource
    Moldbi::CurationResource
    structure_thread = Thread.new do
      natural_product.has_structure? 
    end
    curation_thread = Thread.new do
      natural_product.has_curation?
    end
    structure_thread.join
    curation_thread.join
    Rails.cache.fetch([natural_product, 'with-includes']) do
      NaturalProduct.includes(METABOLITE_SHOW_INCLUDES).find_by(id: natural_product.id)
    end
  end
end
