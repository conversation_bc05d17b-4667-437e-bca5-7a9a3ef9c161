module Shared::Prediction<PERSON>oader
  extend ActiveSupport::Concern

  #METABOLITE_SHOW_INCLUDES = [
   # :articles, :textbooks, :external_links, :accession_numbers
  #].freeze
  included do
    helper_method :read_param_sp
  end

  def load_retention_index_prediction
  end

  def load_submission_object_without_user_session
    @prediction = RetentionIndexPrediction.where(:id => params[:id]).first
    @under_predictions = RetentionIndexPrediction.where(:compound_derivative_type => "No-Derivatization").order("created_at DESC")
    @der_predictions = RetentionIndexPrediction.where.not(:compound_derivative_type => "No-Derivatization").order("created_at DESC")
  end

  def check_retention_index_predictions(phase, type, smile)
    return RetentionIndexPrediction.where(:compound_derivative_type => type, :compound_stationary_phase => phase, :original_smile => smile).first
  end

  def load_submission_objects(phase="", type="", smile="")
    @under_predictions = RetentionIndexPrediction.where(:compound_derivative_type => "No-Derivatization", :compound_stationary_phase => phase, :original_smile => smile)
    @tms_der_predictions = RetentionIndexPrediction.where(:compound_derivative_type => "TMS", :compound_stationary_phase => phase, :original_smile => smile)
    @tbdms_der_predictions = RetentionIndexPrediction.where(:compound_derivative_type => "TBDMS", :compound_stationary_phase => phase, :original_smile => smile)
  end

  def generate_download_prediction(type="")
    # FileUtils.rm_rf(Dir['public/cache/*'])
    headers = ["Compound Name", "Chemical Structure", "Type of Derivaization", "GC Stationary Phase", "Predicted RI", "Created At"]
    return unless params[:compound_name_prefix].present?
    csv = CSV.generate(headers: true) do |csv|
      csv << headers
      data = (@under_predictions + @tms_der_predictions + @tbdms_der_predictions).uniq
	    data = []
      if (type == "tms")
		    data = @tms_der_predictions
      elsif (type == "tbdms")
		    data = @tbdms_der_predictions
	    elsif (type == "no-deriv")
		    data = @under_predictions
	    else
		    data = (@under_predictions + @tms_der_predictions + @tbdms_der_predictions).uniq
	    end
      data.each do |prediction|
        row = ["#{params[:compound_name_prefix]}#{prediction.compound_name}", prediction.compound_smiles, prediction.compound_derivative_type, prediction.compound_stationary_phase, prediction.predicted_RI, "#{(prediction.created_at).to_s.split(" ")[0]} | #{(prediction.created_at).to_s.split(" ")[1]}"]
        csv << row
      end
    end
    send_data csv, filename: "#{params[:compound_name_prefix]}.csv"
  end

  def mol_download(mol_smile, name="")
	name = "mol_file.mol" unless name
    python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
    script_path = Rails.root.join("public", "python", "rdkit_smiles_to_mol.py")
    script_smile = "#{Regexp.escape(mol_smile)}"
    script_command = ""
    script_command += "#{python_path} "
    script_command += "#{script_path} "
    script_command += "#{script_smile}"
    puts script_command
    mol_file = `#{script_command}`
    send_data mol_file, filename: name
  end

  def read_param_sp(f)
    require 'json'
    hs = File.read(f)
    hs1 = JSON.parse(hs)
    out = {}
    out['Instrument']  = hs1['Instrument']
    out['Frequency']   = hs1['Frequency']
    out['Linewidth']   = hs1['Linewidth']
    out['S/N']         = hs1['S/N']
    out['Reference']   = hs1['Reference']
    out['Solvent']     = hs1['Solvent']
    return out
  end
end
