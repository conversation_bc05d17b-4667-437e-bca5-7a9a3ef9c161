class QueriesController < ApplicationController
  include QueriesHelper
  include IdentifyHelper

  def status
    @query = Query.find_by_secret_id(params[:query_id])
    if !@query.nil?
      @status = get_job_status(@query.job_id)
      if @status == :complete || @query.parsed_output == "Successful"
        # Query was successful
        redirect_to(query_path(@query.secret_id)) and return
      elsif @status == :failed || @query.parsed_output == "Failed"
        # Query failed or error in worker
        if @query.error.nil?
          @query.update_attributes({:error => "Server error, please try again or contact an administrator."})
        end
        redirect_to(query_path(@query.secret_id)) and return
      elsif @status.nil?
        if @query.error.nil?
          @query.update_attributes({:error => "Server error or query timeout, please try again or contact an administrator."})
        end
        redirect_to(query_path(@query.secret_id)) and return
      end
    else
      redirect_to(error_path) and return
    end
  end

  def show
    @query = Query.find_by_secret_id(params[:query_id])
    unless @query.nil?
      @spectra_data = @query.parsed_data
      @status = get_job_status(@query.job_id)
      if !@query.parsed_output.nil?
        if !File.exists?(@query.output_file.path)
          # Catch any results where the files are no longer on the server for some reason
          redirect_to(error_path) and return
        else
          # if it the query is done (successful or failed) then parse data for the view
          @spectra_data = @query.parsed_data
          # Get experimental results for PredictQuery
          if @query.type == "PredictQuery"
            @experimental_data = {}
            @experimental_spectra = @query.has_experimental
            @experimental_spectra.each do |experimental|
              @experimental_data[experimental.id] = @query.parser.parse!(experimental.get_peak_list_file)[:spectra]["energy0"]
            end
          end
        end
      elsif @status == :working || @status == :queued
        # redirect if not complete or queued
        redirect_to(query_status_path(@query.secret_id)) and return
      else
        # job not running but no output, something went wrong
        if @query.error.nil?
          @query.update_attributes({:error => "#{@query.errors}"})
        end
      end
    else
      redirect_to(error_path) and return
    end
  end

  def load_compound
    # Get the compound data
    compound_data = loadCandidate(params[:query_id], params["id"], params["database"])

    respond_to do |format|
      format.json {render json: compound_data.to_json }
    end
  end

  def load_compound_cfmid
    # Get the compound data
    compound_data = loadCandidateNew(params["id_low"], params["id_med"], params["id_high"], params["name"], params["nl_search"])

    respond_to do |format|
      format.json {render json: compound_data.to_json }
    end
  end

  # This is for admins only
  def index
    params[:q] ||= {}
    params[:q][:s] ||= "created_at desc"
    @q = Query.ransack(params[:q])
    @queries = @q.result.page(params[:page])
    authorize! :manage, @queries, :message => 'Not authorized as an administrator.'
  end

  # This is for admins only
  def destroy
    @query = Query.find(params[:id])
    authorize! :manage, @query, :message => 'Not authorized as an administrator.'
    # Also remove all files
    filedir = "public/queries/" + @query.secret_id
    if File.directory?(filedir)
      FileUtils.rm_rf(filedir)
    end
    @query.destroy
    redirect_to queries_path, :notice => "Query deleted."
  end

  # This is for admins only
  def rerun
    @query = Query.find_by_secret_id(params[:query_id])
    authorize! :manage, @query, :message => 'Not authorized as an administrator.'
    @query.update_attributes( parsed_output: nil, error: nil, runtime: nil )
    if @query.type == "PredictQuery"
      job_id = PredictWorker.perform_async(@query.id)
      if !job_id.nil? && PredictQuery.find_by_id(@query.id).update_attributes(job_id: job_id)
        redirect_to(query_status_path(PredictQuery.find_by_id(@query.id).secret_id))
      else
        redirect_to(queries_path, notice: "Application error, please contact an administrator.") and return
      end
    elsif @query.type == "AssignQuery"
      job_id = AssignWorker.perform_async(@query.id)
      if !job_id.nil? && AssignQuery.find_by_id(@query.id).update_attributes(job_id: job_id)
        redirect_to(query_status_path(AssignQuery.find_by_id(@query.id).secret_id))
      else
        redirect_to(queries_path, notice: "Application error, please contact an administrator.") and return
      end
    elsif @query.type == "IdentifyQuery"
      job_id = IdentifyWorker.perform_async(@query.id)
      if !job_id.nil? && IdentifyQuery.find_by_id(@query.id).update_attributes(job_id: job_id)
        redirect_to(query_status_path(IdentifyQuery.find_by_id(@query.id).secret_id))
      else
        redirect_to(queries_path, notice: "Application error, please contact an administrator.") and return
      end
    end
  end

end
