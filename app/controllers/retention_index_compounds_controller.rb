class RetentionIndexCompoundsController < ApplicationController
  
  include Shared::RetentionIndexCompoundLoader
  include Shared::SubmissionLoader

  # include submission_loader

  #change these
  SORTABLE_COLUMNS = %i(retention_index_compound_id name moldb_formula moldb_average_mass moldb_mono_mass).freeze

  parse_sort_params(SORTABLE_COLUMNS, default_column: 'retention_index_compound_id', only: [:index])

  def index
    @retention_index_compounds=load_retention_index_compound_index_objects.distinct
    #load_retention_index_compound_index_objects

    respond_to do |format|
      format.html
    end
  end

  def search
    @retention_index_compound = RetentionIndexCompound.new()
  end

  def create
    retention_index_search = params[:retention_index_compound]
    value = Float(retention_index_search[:retention_index_value])
    tolerance = Float(params[:retention_index_tolerance])
    r_min = value - value * (tolerance / 100)
    r_max = value + value * (tolerance / 100)

    if (retention_index_search[:ri_compound_average_mass].empty? and params[:ri_compound_average_mass].nil?)
      m_min = -1
      m_max = -1
    else
      mass = Float(retention_index_search[:ri_compound_average_mass])
      tolerance = Float(params[:mass_tolerance])
      m_min = mass - tolerance
      m_max = mass + tolerance
    end

    tms_count_min = -1
    tms_count_max = -1
    tbdms_count_min = -1
    tbdms_count_max = -1
    # if (params[:tms_count].empty? or params[:tms_count].nil?)
    #   tms_count_min = -1
    #   tms_count_max = -1
    # else
    #   tmscount = Float(params[:tms_count])
    #   tms_count_min = 0
    #   tms_count_max = Float(params[:tms_count])
    # end

    # if (params[:tbdms_count].empty? or params[:tbdms_count].nil?)
    #   tbdms_count_min = -1
    #   tbdms_count_max = -1
    # else
    #   tbdmscount = Float(params[:tbdms_count])
    #   tbdms_count_min = 0
    #   tbdms_count_max = Float(params[:tbdms_count])
    # end

    search_retention_index_compounds(retention_index_search[:ri_compound_name],
                                     retention_index_search[:compound_derivative_type], 
                                     retention_index_search[:compound_stationary_phase], 
                                     r_min, 
                                     r_max,
                                     m_min,
                                     m_max,
                                     tms_count_min,
                                     tms_count_max,
                                     tbdms_count_min,
                                     tbdms_count_max,
                                     params[:tms_count],
                                     params[:tbdms_count],
                                     params[:structure_input])
    render 'results'
  end



  def show
    @retention_index_compound = load_retention_index_compound
    if @retention_index_compound.retention_index_compound_id != params[:id]
      redirect_to @retention_index_compound and return
    end
    respond_to do |format|
      format.html
      format.xml
    end

  rescue ActiveRecord::RecordNotFound
    raise
  end
 
  def mw_of_smiles()
	  smiles_to_mw_convert(params["mol_smile"])
  end

end
