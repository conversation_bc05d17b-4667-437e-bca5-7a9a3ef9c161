require_dependency "moldbi/search/search_controller"
class NlSearchController < ApplicationController

  include ApplicationHelper
  include NeutralLossHelper

  before_filter :set_variables

  POSITIVE_ADDUCTS = IdentifyQuery::POSITIVE_ADDUCTS
  NEGATIVE_ADDUCTS = IdentifyQuery::NEGATIVE_ADDUCTS
  ESI_SCORING_FUNCTIONS = IdentifyQuery::ESI_SCORING_FUNCTIONS
  EI_SCORING_FUNCTIONS = IdentifyQuery::EI_SCORING_FUNCTIONS

  def index
    # default to the find tab
    params[:submit_or_find_or_nl] ||= 'find'
    params[:text_or_file] ||= 'text'
    @nl_search_query = NeutralLossQuery.new

    # Set defaults
    @nl_search_query.adduct_type = "neutral"
    @nl_search_query.ion_mode    = "positive"
    @nl_search_query.database    = NeutralLossQuery::ESI_DATABASES.values

    @mass_tol = 10.0
    @mass_tol_units = "ppm"
    @candidate_mass_tol = 10.0
    @candidate_mass_tol_units = "ppm"
  end

  def new
    nl_search_params = get_nl_search_params

    nl_search_params[:ion_mode] =  params[:adduct_search_ion_mode]
    nl_search_params[:spectra_type] =  params[:adduct_search_spectra_type]

    nl_search_params[:type] = "NeutralLossQuery"

    nl_search_params[:user] = user_signed_in? ? current_user : nil
    nl_search_params[:param_file]  = get_param_file(nl_search_params[:spectra_type], nl_search_params[:ion_mode])
    nl_search_params[:config_file] = get_config_file(nl_search_params[:spectra_type], nl_search_params[:ion_mode])

    # Use spectra file if both file and text input are given
    if params[:text_or_file] == "file"
      params[:low_spectra] = ""
      params[:medium_spectra] = ""
      params[:high_spectra] = ""
      nl_search_params[:spectra] = ""
    elsif params[:text_or_file] == "text"
      spectra_text = ""
      if params[:low_spectra].present?
        spectra_text << "energy0\n" << remove_header(params[:low_spectra]).strip + "\n"
      end
      if params[:medium_spectra].present?
        spectra_text << "energy1\n" << remove_header(params[:medium_spectra]).strip + "\n"
      end
      if params[:high_spectra].present?
        spectra_text << "energy2\n" << remove_header(params[:high_spectra]).strip + "\n"
      end
      nl_search_params[:input_file] = ""
      nl_search_params[:spectra] = spectra_text
    end

    # Use candidates file if both file and text input are given
    # if params[:submit_or_find_or_nl] == "submit"
    #   if !nl_search_params[:candidates_file].blank?
    #     nl_search_params[:candidates] = ""
    #   end
    #   nl_search_params[:database] = [] #Ensure blank if using submitted candidates, this is the flag to determine which program to run
    # elsif params[:submit_or_find_or_nl] == "find" || params[:submit_or_find_or_nl] == "neutral-loss"
    #   nl_search_params[:candidates] = ""
    #   nl_search_params[:candidates_file] = ""
    # end

    if nl_search_params[:num_results].blank? || ( nl_search_params[:num_results] =~ /[\d]+/ && nl_search_params[:num_results].to_i < 1)
      nl_search_params[:num_results] = -1
    end

    # Setting mass tolerance
    if params[:mass_tol_units] == "ppm"
      nl_search_params[:ppm_mass_tol] = params[:mass_tol]
      nl_search_params[:abs_mass_tol] = -1
    else
      nl_search_params[:abs_mass_tol] = params[:mass_tol]
      nl_search_params[:ppm_mass_tol] = -1
    end

    # Setting candidate mass tolerance
    # if params[:candidate_mass_tol_units] == "ppm"
    #   nl_search_params[:candidate_ppm_mass_tol] = params[:candidate_mass_tol]
    #   nl_search_params[:candidate_abs_mass_tol] = nil
    # else
    #   nl_search_params[:candidate_abs_mass_tol] = params[:candidate_mass_tol]
    #   nl_search_params[:candidate_ppm_mass_tol] = nil
    # end

    @nl_search_query = NeutralLossQuery.new(nl_search_params)
    
    @mass_tol = params[:mass_tol]
    @mass_tol_units = params[:mass_tol_units]
    @candidate_mass_tol = params[:candidate_mass_tol]
    @candidate_mass_tol_units = params[:candidate_mass_tol_units]
    @low_spectra = params[:low_spectra]
    @medium_spectra = params[:medium_spectra]
    @high_spectra = params[:high_spectra]


    # search_results = Specdb::MsMsNlSearch.nl_search(params)
    # make_result_file(search_results)
    # # # get_results()

    # stop

    if @nl_search_query.valid? && @nl_search_query.convert_spectra && @nl_search_query.save
      job_id = NeutralLossWorker.perform_async(@nl_search_query.id, params)

    else
      if @nl_search_query.errors.any?
        if @nl_search_query.errors[:threshold]
          @nl_search_query.threshold = 0.001
        end
        if @nl_search_query.errors[:num_results]
          @nl_search_query.num_results = 10
        end
        if @nl_search_query.errors[:ppm_mass_tol] || @nl_search_query.errors[:abs_mass_tol]
          @mass_tol = 10.0
          @mass_tol_units = "ppm"
        end
        # if @nl_search_query.errors[:candidate_ppm_mass_tol] || @nl_search_query.errors[:candidate_abs_mass_tol]
        #   @candidate_mass_tol = 10.0
        #   @candidate_mass_tol_units = "ppm"
        # end
        # if @nl_search_query.errors[:candidate_limit]
        #   @nl_search_query.candidate_limit = 100
        # end
        errors = process_errors(@nl_search_query.errors.full_messages).html_safe
      else
        errors = "Input error, please check your input values or contact an administrator."
      end
      flash.now[:alert] = errors
      render(:action => 'index') and return
    end

    if !job_id.nil? && NeutralLossQuery.find_by_id(@nl_search_query.id).update_attributes(job_id: job_id)
      redirect_to(query_status_path(NeutralLossQuery.find_by_id(@nl_search_query.id).secret_id))
    else
      redirect_to(nl_search_path, notice: process_errors(@nl_search_query.errors.full_messages).html_safe) and return
    end
  end

  def get_nl_search_params
    params.require(:neutral_loss_query).permit(:type, :input_file, :spectra, 
                  {database: []}, {experimental_database: []}, {predicted_database: []}, :num_results, :threshold,
                  :ppm_mass_tol, :abs_mass_tol, :scoring_function, 
                  :candidates_file, :candidates, :candidate_limit, 
                  :neutral_mass, :candidate_ppm_mass_tol, 
                  :candidate_abs_mass_tol, :parent_ion_mass, 
                  :parent_ion_mass_type, :ion_mode, 
                  :adduct_type, :spectra_type, :spectra_id, :param_file, 
                  :config_file, :user)
  end

  private

  def set_variables
    @positive_adducts = POSITIVE_ADDUCTS
    @negative_adducts = NEGATIVE_ADDUCTS
    @esi_functions = ESI_SCORING_FUNCTIONS
    @ei_functions = EI_SCORING_FUNCTIONS
  end

end

