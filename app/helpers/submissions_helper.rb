module Submissions<PERSON><PERSON><PERSON>
  def literature_reference_types_collection
    %w(PMID DOI Book)
  end

  def solvent_collection
    %w(H2O CDCl3 DMSO Methanol Acetone  Pyridine)
  end

  def spectrometer_frequency_collection
    %w(300MHz 400MHz 500MHz 600MHz 700MHz 800MHz 850MHz 900Mz 1GHz)
  end

  def chemical_shift_reference_collection
     %w(TMS DSS TSP  H2O(Residual-Peak) CHCl3(Residual-Peak)  Ethanol(Residual-Peak)  Methanol(Residual-Peak))
  end

  def spectrum_type_collection
    %w(1 2 3 4 5 6 7 8)
  end

  def fid_spectrum_type_collection
    %w(1D-1H  1D-13C  1D-1H-DEPT90  1D-13C-DEPT90 1D-31P 2D-1H-1H-COSY 2D-1H-13C-COSY 2D-13C-13C-COSY  2D-1H-13C-HSQC 2D-1H-13C-HMQC 2D-1H-13C-HMBC  2D-1H-15N-HMBC 2D-1H-1H-TOCSY 2D-1H-1H-ROESY 2D-13C-13C-INADEQUATE)
  end

  def physical_state_of_compound_collection
    %w(No-Derivatization  TMS  TBDMS TMS_AND_TBDMS)
  end

  def is_number? string
    true if Float(string) rescue false
  end

  def provenance_collection
     ["Semi Standard Non Polar", "Standard Non Polar",  "Standard Polar"]
  end


  def savenmrfile_details_modal
    render '/nmr_submissions/savenmrfile_details_modal'
  end

  def link_to_savenmrfile(submission_id)
    puts"reached link_to_savenmrfile"
    puts"submission_id = #{submission_id}"

    link_to "<b>Upload Spectrum</b>".html_safe,
      main_app.newsavenmrfile_nmr_submission_path(submission_id),
      data: { toggle: 'modal', target: '#savenmrfile-details' },
      class: 'btn-savenmrfile-details'
  end

  # Get the earliest creation date and latest update date from the union of
  # MolDB spectra and valid chemical shift submissions
  def get_creation_update_dates(submissions, earliest_creation_date, last_update_date)
    # Dates from valid chemical shift submissions
    submission_earliest_creation_date = submissions.order(:created_at).first.created_at.strftime("%Y-%m-%d")
    submission_last_update_date = submissions.order(:updated_at).last.updated_at.strftime("%Y-%m-%d")

    # Compare with MolDB dates
    if earliest_creation_date # MolDB spectra exist
      earliest_creation_date = [earliest_creation_date, submission_earliest_creation_date].min
      last_update_date = [last_update_date, submission_last_update_date].max
      return earliest_creation_date, last_update_date
    else # no MolDB spectra exist
      return submission_earliest_creation_date, submission_last_update_date
    end
  end
end
