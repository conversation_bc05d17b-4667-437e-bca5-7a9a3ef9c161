require 'fileutils'
require 'open3'

module AssignHelper
  include Queries<PERSON><PERSON><PERSON>

  def create_assign_output(output_file, query)
    # Create input structure image
    generate_structure(query.compound, "query_compound", query)

    # Parse output
    spectra = false
    fragments = false
    transitions = false
    start = true
    frag_count = 0
    trans_count = 0
    begin
      File.readlines(output_file).each do |line|
        # if there is meta dataline
        next if line[0] =="#"
        if !spectra
          # line is a spectra peak
          if line =~ /^\s*([-+]?[0-9]*\.?[0-9]+)\s+([-+]?[0-9]*\.?[0-9]+)\s+((?:[\d]+\s+)*)$/
            next
          # line in an energy header
          elsif line =~ /^([A-Za-z]+[\d]*\s*)$/
            next
          # end of spectra
          elsif line.blank?
            spectra = true
          end
        elsif !fragments
          # line is the fragment header
          if line =~ /^(\d+)$/
            next
          # end of fragments
          elsif line.blank?
            fragments = true
          # line is a fragment, so generate structure
          elsif line =~ /^(\d+)\s+([-+]?[0-9]*\.?[0-9]+)\s+(\S+)$/
            frag_count = frag_count + 1
            id = $1
            mass = $2
            structure = $3
            generate_structure(structure, id, query)
          end
        elsif !transitions
          # Don't compute more than 100 transitions
          if trans_count >= 100
            break
          # line is transition, so compute structure
          elsif line =~ /^(\d+)\s+(\d+)\s+(\S+)$/
            trans_count = trans_count + 1
            frag1 = $1
            frag2 = $2
            loss = $3
            generate_structure(loss, frag1 + "to" + frag2, query)
          end
        end
      end
    rescue
      return "Failed"
    else
      return "Successful"
    end
  end

end
