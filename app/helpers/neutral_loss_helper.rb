require 'fileutils'
require 'open3'
require 'jchem'

module NeutralLossHelper
  include <PERSON><PERSON>Helper

  def link_to_database(database, database_id)
    case database
    when "hmdb"
      bio_link_out(:hmdb, database_id)
    when "drugbank"
      bio_link_out(:drugbank, database_id)
    when "PhytoHub"
      bio_link_out(:phytohub, database_id)
    when "KEGG"
      bio_link_out(:kegg_compound, database_id)
    when "HMDB_Deriv"
      bio_link_out(:hmdb, DatabaseMolecule.find_by(source: database, source_id: database_id).try(:derivatization_of_source_id))
    when "MassBank"
      bio_link_out(:massbank, database_id)
    when "FAHFA"
      bio_link_out(:mona_spectrum, database_id)
    when "LipidBlast"
      bio_link_out(:mona_spectrum, database_id)
    when "MetaboBASE"
      bio_link_out(:mona_spectrum, database_id)
    when "foodb"
      bio_link_out(:foodb_compound, database_id)
    # when "np-mrd"
    #   bio_link_out(:np_mrd, database_id)
    else
      return database_id
    end
  end

 
  def make_result_file(spectrum_results, output_file)
    puts "STARTING THIS GARBAGE"

    # begin
    # File.new(output_file, 'w')
    temp_results = []
    databases_predicted = Hash.new{|h,k| h[k] = Array.new}
    databases_experimental = Hash.new{|h,k| h[k] = Array.new}
    unique_predicted = Hash.new{|h,k| h[k] = Array.new}
    unique_experimental = Hash.new{|h,k| h[k] = Array.new}
    unique_predicted_volts = Hash.new{|h,k| h[k] = Array.new}
    voltages = []

    
    id_low = nil
    id_med = nil
    id_high = nil
    puts "MAKEING FILE"
    # []
    puts spectrum_results

    spectrum_results.each do |f|
      id = f.id
      formula = f.formula
      database = f.database.strip
      database_id = f.database_id
      inchi_key = f.inchi_key
      predicted = f.predicted
      collision_energy_voltage = f.collision_energy_voltage 
      scoring_function = f.scoring_function 
      jchem_id = f.jchem_id
      puts "#{f.collision_energy_voltage}V - #{f.id} #{f.database.strip} - #{f.scoring_function}"
      
      if predicted
        
        if databases_predicted.key?(database) 
          
          if unique_predicted.key?(database)
            if !unique_predicted[database].index(id)

              if !unique_predicted_volts[database].index(collision_energy_voltage)
                unique_predicted_volts[database] << collision_energy_voltage
                new_scoring_function = databases_predicted[database][3].to_f + scoring_function.to_f
                databases_predicted[database] = [id_low, id_med, id_high, new_scoring_function, formula, database, database_id, inchi_key, "#{predicted}"]
                unique_predicted[database] << id
              end

            end
          end
        else
          unique_predicted[database] = [id]
          unique_predicted_volts[database] = [collision_energy_voltage]
          databases_predicted[database] = [id_low, id_med, id_high, scoring_function, formula, database, database_id, inchi_key, "#{predicted}"]
        end

      else 
        if databases_experimental.key?(database) 
          
          if unique_experimental.key?(database)
            if !unique_experimental[database].index(id)
              new_scoring_function = databases_experimental[database][3].to_f + scoring_function.to_f
            
              databases_experimental[database] = [id_low, id_med, id_high, new_scoring_function, formula, database, database_id, inchi_key, "#{predicted}", jchem_id]
              unique_experimental[database] << id
            end
          end
        else
          unique_experimental[database] = [id]
          databases_experimental[database] = [id_low, id_med, id_high, scoring_function, formula, database, database_id, inchi_key, "#{predicted}", jchem_id]
        end
      end
      voltages << [database, collision_energy_voltage, id, predicted]
    end

    puts databases_predicted

    # # Create final results array
    voltages.each do |t|
      # puts t
      # puts "Databases experimental: #{databases_experimental[t[0]]}"
      # puts "Databases experimental: #{databases_predicted[t[0]]}"
      if t[3] && databases_predicted.key?(t[0])
        if t[1] == 10
          databases_predicted[t[0]][0] = t[2]
        elsif t[1] == 20
          databases_predicted[t[0]][1] = t[2]
        else 
          databases_predicted[t[0]][2] = t[2]
        end
      elsif databases_experimental.key?(t[0])
        if t[1] == 10
          databases_experimental[t[0]][0] = t[2]
        elsif t[1] == 20
          databases_experimental[t[0]][1] = t[2]
        else 
          databases_experimental[t[0]][2] = t[2]
        end
      end
    end

    puts databases_experimental
    puts databases_predicted
    databases_experimental.each do |h, f|
      next if f[3].to_f < 0.0
      temp_results << "#{f[0]} #{f[1]} #{f[2]} #{f[3]} #{f[4]} #{f[5]}-#{f[6]} #{f[7]} #{f[8]} #{f[9]}"
    end
    databases_predicted.each do |h, f|
      next if f[3].to_f < 0.0
      temp_results << "#{f[0]} #{f[1]} #{f[2]} #{f[3]} #{f[4]} #{f[5]}-#{f[6]} #{f[7]} #{f[8]} #{f[9]}"
    end
    # spectrum_results.each do |f|

    # temp_results << [f.id, f.database,f.predicted, f.collision_energy_voltage, f.scoring_function]
    # end
    puts "HERE"
    puts temp_results
    File.open(output_file, "w+") do |t|
      temp_results.each do |f|
        puts f
        t.puts f
        # t.puts "\n"
      end
    end
    # rescue 
    #   File.open(output_file, "w+") do |t|
    #     t.puts "ERROR"
    #   end
    #   return "Failed"
    # else
    #   return "Successful"
    # end

  end



end

def check_if_duplicate(level_value, voltage)
  # if databases_predicted[database][0] && collision_energy_voltage == '10'
  # elsif databases_predicted[database][1] && collision_energy_voltage == '20'
  # elsif databases_predicted[database][2] && collision_energy_voltage == '40'
  # else 
  # end
end

# spectrum_results.each do |f|
    #   next if f.scoring_function == 0
    #   voltages << [f.database, f.collision_energy_voltage, f.id, f.predicted]
    #   if f.predicted
    #     if databases_predicted.key?(f.database) 
    #       # puts databases[f.database][0]
    #       # puts f.scoring_function.to_f
    #       new_scoring_function = databases[f.database][3].to_f + f.scoring_function.to_f
            
    #       databases_predicte[f.database] = [id_low, id_med, id_high, new_scoring_function, f.formula, f.database, f.database_id, f.inchi_key, "#{f.predicted}", f.formula, "#{f.notes}", f.instrument]
    #       # databases[f.database][0] = databases[f.database][0][0] + f.scoring_function

    #     else 
    #       databases_predicte[f.database] = [id_low, id_med, id_high, f.scoring_function, f.formula, f.database, f.database_id, f.inchi_key, "#{f.predicted}", f.formula, "#{f.notes}", f.instrument]

    #     end
    #   else 
    #   end
    #   temp_results << ["[#{f.collision_energy_voltage}, #{f.id}, #{f.scoring_function}, #{f.formula}, #{f.database}, #{f.database_id}, #{f.inchi_key}, '#{f.predicted}', #{f.formula}, '#{f.notes}'', #{f.instrument}]"]
    # end
    # # puts "VOLTAGES"
    # # puts voltages
    # # puts "Databases: #{databases}"
    # voltages.each do |t|
    #   puts t
    #   puts "Databases: #{databases[t[0]]}"
    #   if t[1] == 10
    #     databases[t[0]][0] = t[2]
    #   elsif t[1] == 20
    #     databases[t[0]][1] = t[2]
    #   else 
    #     databases[t[0]][2] = t[2]
    #   end
    # end
    # databases_experimental[database] = [id_low, id_med, id_high, new_scoring_function, formula, database, database_id, inchi_key, "#{predicted}", "#{notes}", instrument]
    