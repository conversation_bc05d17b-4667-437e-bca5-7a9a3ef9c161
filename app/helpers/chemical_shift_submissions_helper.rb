module ChemicalShiftSubmissions<PERSON>elper
  def literature_reference_types_collection
    %w(PMID DOI Book)
  end

  def solvent_collection
    %w(H2O CDCl3 DMSO Methanol Acetone  Pyridine)
  end

  def spectrometer_frequency_collection
    %w(300MHz 400MHz 500MHz 600MHz 700MHz 800MHz 850MHz 900Mz 1GHz)
  end

  def chemical_shift_reference_collection
    %w(TMS DSS TSP  H2O(Residual-Peak) CHCl3(Residual-Peak) Ethanol(Residual-Peak)  Methanol(Residual-Peak))
  end

  def spectrum_type_collection
    %w(1D-1H  1D-13C  2D-1H-13C)
  end

  def physical_state_of_compound_collection
    %w(Solid  Liquid  Gas)
  end

  def is_number? string
    true if Float(string) rescue false
  end
  
  def provenance_collection
     ["Biotransformation", "Chemical synthesis",  "Commercial", "eDNA screening", "Heterologous biosynthesis",  "Isolated from a combinational source", "Isolated from a community",  "Isolated from a field sample", "Shunt products",  "Other"]
  end


  # Get the earliest creation date and latest update date from the union of
  # MolDB spectra and valid chemical shift submissions
  def get_creation_update_dates(chemical_shift_submissions, earliest_creation_date, last_update_date)
    # Dates from valid chemical shift submissions
    submission_earliest_creation_date = chemical_shift_submissions.order(:created_at).first.created_at.strftime("%Y-%m-%d")
    submission_last_update_date = chemical_shift_submissions.order(:updated_at).last.updated_at.strftime("%Y-%m-%d")

    # Compare with MolDB dates
    if earliest_creation_date # MolDB spectra exist
      earliest_creation_date = [earliest_creation_date, submission_earliest_creation_date].min
      last_update_date = [last_update_date, submission_last_update_date].max
      return earliest_creation_date, last_update_date
    else # no MolDB spectra exist
      return submission_earliest_creation_date, submission_last_update_date
    end
  end
end
