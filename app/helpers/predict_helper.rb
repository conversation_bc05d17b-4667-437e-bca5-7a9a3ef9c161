require 'fileutils'
require 'open3'

module Predict<PERSON>elper
  include QueriesHelper

  # If there is already spectra computed, write those results to the output file
  def check_for_precomputed(output_file, query)
    # We get the first precomputed results, if there is one
    all_precomputed = query.has_computed
    # If we didn't filter by adduct type, preferentially use the [M+H]+ or [M-H]- or [M]+ computed ones
    # (If we did filter by adduct, the first clause will return nil)
    precomputed = all_precomputed.find { |s| s.adduct_type == "[M+H]+" or s.adduct_type == "[M-H]-" or s.adduct_type == "[M]+" } || all_precomputed.first

    if precomputed.present?
      begin
        peak_list_file = File.open(precomputed.get_peak_list_file, "rb")
        peak_list = peak_list_file.read
        File.write(output_file, peak_list)
        query.update_attributes( runtime: "Precomputed spectra found: " + precomputed.id.to_s)
      rescue StandardError
        query.update_attributes( error: "Error getting precomputed spectra." )
      end
    end
  end

  def create_predict_output(output_file, query)
    # Create input structure image
    generate_structure(query.compound, "query_compound", query)

    # Parse output
    spectra = false
    fragments = false
    start = true
    frag_count = 0
    begin
      File.readlines(output_file).each do |line|
        next if line[0] =="#"
        if !spectra
          # end of spectra
          if line.blank?
            spectra = true
          end
        elsif !fragments
          # end of fragments
          if line.blank?
            fragments = true
          # line is a fragment, so generate structure images
          elsif line =~ /^(\d+)\s+([-+]?[0-9]*\.?[0-9]+)\s+(\S+)$/
            frag_count = frag_count + 1
            id = $1
            mass = $2
            structure = $3
            generate_structure(structure, id, query)
          end
        end
      end
    rescue
      return "Failed"
    else
      return "Successful"
    end
  end

end
