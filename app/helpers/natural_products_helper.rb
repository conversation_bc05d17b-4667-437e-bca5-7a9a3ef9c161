module NaturalProductsHelper
  # Sort spectra into a hash, by SpecDB type
  def sorted_spectra(spectra)
    sorted_spectra = {}
    sorted_spectra[:gcms] = []
    sorted_spectra[:msms] = []
    sorted_spectra[:lcms] = []
    sorted_spectra[:nmr1d] = []
    sorted_spectra[:nmr2d] = []

    spectra.each do |spectrum|
      if spectrum.instance_of?(Specdb::CMs)
        sorted_spectra[:gcms] << spectrum
      elsif spectrum.instance_of?(Specdb::MsMs)
        if spectrum.spectrum_type =~ /^LC-MS/
          sorted_spectra[:lcms] << spectrum
        else
          sorted_spectra[:msms] << spectrum
        end
      elsif spectrum.instance_of?(Specdb::NmrOneD)
        sorted_spectra[:nmr1d] << spectrum
      elsif spectrum.instance_of?(Specdb::NmrTwoD)
        sorted_spectra[:nmr2d] << spectrum
      end
    end

    return sorted_spectra
  end



  def rdkit_generate_3D_structure(smiles)
    error = false
    mol = nil
    message = nil
    begin
      stdin,stdout,stderr = Open3.popen3("python #{Rails.root}/public/python/rdkit_smiles_to_mol.py '#{smiles}'")
      mol = stdout.get(nil).to_s
      puts mol
      error = false
    rescue Exception => e
      message = e.message
      error = true
    end
    return mol, error, message
  end



end

