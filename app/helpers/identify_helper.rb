require 'fileutils'
require 'open3'
require 'jchem'

module I<PERSON><PERSON>Helper
  include <PERSON><PERSON><PERSON><PERSON><PERSON>

  def link_to_database(database, database_id)
    case database
    when "HMDB"
      bio_link_out(:hmdb, database_id)
    when "DrugBank"
      bio_link_out(:drugbank, database_id)
    when "PhytoHub"
      bio_link_out(:phytohub, database_id)
    when "KEGG"
      bio_link_out(:kegg_compound, database_id)
    when "HMDB_Deriv"
      bio_link_out(:hmdb, DatabaseMolecule.find_by(source: database, source_id: database_id).try(:derivatization_of_source_id))
    when "MassBank"
      bio_link_out(:massbank, database_id)
    when "FAHFA"
      bio_link_out(:mona_spectrum, database_id)
    when "LipidBlast"
      bio_link_out(:mona_spectrum, database_id)
    when "MetaboBASE"
      bio_link_out(:mona_spectrum, database_id)
    when "FooDB"
      bio_link_out(:foodb, database_id)
    else
      return database_id
    end
  end

  def create_identify_output(output_file, query)

    begin
      first = true
      File.readlines(output_file).each do |line|
        if line =~ /^(\d+)\s+([-+]?[0-9]*\.?[0-9e\-]+)\s+(\S+)\s+(\S+)\s*$/
          rank = $1
          score = $2
          cfmid = $3
          structure = $4
          generate_structure(structure, rank, query)

          # If we are comparing with database candidates and we have the
          # fragment annotations, generate the structures for the top result here
          # to speed up initial load time
          if query.database.present? && first
            database, database_id = cfmid.split("-", 2)
            id = cfmid
            first = false
            candidatedir = DatabaseMolecule.spectra_location(query.spectra_type, query.ion_mode)
            annotated_file = File.join(candidatedir, database.downcase + "_annotated", id + ".txt")
            if File.exists?(annotated_file)
              read_data = open(annotated_file).read
              generate_candidate_images(id, read_data, query)
            end
          end

        end
      end
    rescue
      return "Failed"
    else
      return "Successful"
    end
  end

  def get_formula(structure)
    if structure =~ /^InChI=\dS*\/([A-Z\d\.@]+)\//i
      formula = $1
      return formula
    else
      begin
        inchi = Jchem.structure_to_inchi(structure)
      rescue
        inchi = nil
      end
      if inchi =~ /^InChI=\dS*\/([A-Z\d\.@]+)\//i
        formula = $1
        return formula
      else
        return "<span style=\"font-style:italic;\">Unknown</span>".html_safe
      end
    end
  end

  def find_in_database(id, attribute, database)
    molecule = DatabaseMolecule.find_by(source: database, source_id: id)
    if molecule.present?
      return molecule.send(attribute)
    else
      return ""
    end
  end

  def get_spectrum_type(id)
    compound = Specdb::MsMs.find(id)
    if compound.predicted
      result = compound.notes 
    else 
      result = "Generated from #{compound.instrument_type}"
    end
  end

  def display(value)
    if value.blank?
      return "<span style=\"font-style:italic;\">Unknown</span>".html_safe
    else
      return value
    end
  end

  def display_list(array)
    if array.blank?
      return "<span style=\"font-style:italic;\">Unknown</span>".html_safe
    else
      list = "<ul class='display-list'>"
      array.each_with_index do |item, i|
        if i > 5
          list << "<li class='hidden-item'>" + item + "</li>"
        else
         list << "<li>" + item + "</li>"
       end
      end
      if array.length > 5
        list << "<li><a class='show-hide' href='#'>show more</a></li>"
      end
      list << "</ul>"
      return list.html_safe
    end
  end

  def loadCandidate(query_id, cfmid, database)
    # This is not a database query, so get the spectra from the output
    # candidate_spectra.msp file
    query = Query.find_by_secret_id(query_id)
    if database.blank?
      read_data = open(query.candidate_spectra_file.path).read

      parsed_data = []
      # Find the spectra
      spectra_blocks = read_data.split(/\n\n/)
      spectra_blocks.each do |block|
        if block =~ /($|\n)ID: #{cfmid}\n/
          if block =~ /($|\n)Comment: Energy0\n/
            parsed_data[0] = "energy0"
            # Remove all lines except peak list
            peak_list = block.gsub(/[\S\s]+:\s*.+\n/, "")
            parsed_data[1] = peak_list
            next
          elsif block =~ /($|\n)Comment: Energy1\n/
            parsed_data[2] = "energy1"
            # Remove all lines except peak list
            peak_list = block.gsub(/[\S\s]+:\s*.+\n/, "")
            parsed_data[3] = peak_list
            next
          elsif block =~ /($|\n)Comment: Energy2\n/
            parsed_data[4] = "energy2"
            # Remove all lines except peak list
            peak_list = block.gsub(/[\S\s]+:\s*.+\n/, "")
            parsed_data[5] = peak_list
            next
          end
        end
      end

      if !parsed_data[0].present?
        parsed_data[0] = "energy0"
        parsed_data[1] = ""
      end

      if !parsed_data[2].present?
        parsed_data[2] = "energy1"
        parsed_data[3] = ""
      end

      if !parsed_data[4].present?
        parsed_data[4] = "energy2"
        parsed_data[5] = ""
      end

      hashed_data = parsed_data_to_hash(parsed_data)
      hashed_data[:status] = "Complete"
    # Get the spectra from the precomputed spectra file
    else
      # If we have an annotated spectra file then load that one and set the
      # structure directory, else load the un-annotated file and don't st
      # a structure directory (so we know not to look for fragments later)
      candidatedir = DatabaseMolecule.spectra_location(query.spectra_type, query.ion_mode)
      candidate_file = File.join(candidatedir, database.downcase + "_annotated", cfmid + ".txt")
      if !File.exists?(candidate_file)
        candidate_file = File.join(candidatedir, database.downcase, cfmid + ".txt")
      end
      read_data = open(candidate_file).read

      # Split the data on two new lines to separate the spectra and the structures
      results = read_data.split(/\n\n/)
      data_string = results[0]
      structures = results[1]
      structure_list = {}

      # Only precomputed spectra have fragments
      # (On-the-fly spectra are in .msp format)
      if structures.present? # We only have these if the spectra is annotated
        # If there are structures and we have already computed them all
        structures.split(/\n/).each do |line|
          # Make a hash of structure_id => inchi
          # Line might look like e.g.:
          # 137 129.0658539 CCNC(=O)C(=[NH2+])C=O
          # 138 223.1077188 CC(=C=C=NC(=O)C(=[NH2+])C=O)CCC(C)O Intermediate Fragment 
          if line =~ /^([\d\-]+)\s+([-+]?[0-9]*\.?[0-9]+)\s+(\S+?)(?:\s+Intermediate Fragment\s*)?$/
            structure_list[$1] = [$2, $3]
          end
        end
        # Check if all the structures have images generated
        if structures.split(/\n/).all? { |line|
          if line =~ /^([\d\-]+)\s+([-+]?[0-9]*\.?[0-9]+)\s+(\S+?)(?:\s+Intermediate Fragment\s*)?$/
            Structure.find_by_smiles($3).present?
          else
            true
          end
        }
          #hashed_data = {} #TEMPORARY FIX
          parsed_data = make_candidate_spectra(data_string)
          hashed_data = parsed_data_to_hash(parsed_data, structure_list)
          hashed_data[:status] = "Complete"
        else
          # Check to make sure we aren't already generating structures for this job
          # This is necessary otherwise if sidekiq is down or something
          # a whole bunch of jobs will get started
          if !query.structure_job_id.present?
            # If the fragment structures don't exists yet, generate them
            job_id = StructureWorker.perform_async(cfmid, read_data, query.id)
            query.update_column(:structure_job_id, job_id)
          end

          hashed_data = {}
          parsed_data = make_candidate_spectra(data_string) #TEMPORARY FIX
          hashed_data = parsed_data_to_hash(parsed_data, structure_list) #TEMPORARY FIX
          hashed_data[:status] = "Working"
        end
      else
        #hashed_data = {} #TEMPORARY FIX
        parsed_data = make_candidate_spectra(data_string)
        hashed_data = parsed_data_to_hash(parsed_data)
        hashed_data[:status] = "Complete"
      end
    end

    # Store query info in result to get pathway when rendering in js.
    hashed_data[:compound] = cfmid
    hashed_data[:database] = database

    hashed_data
  end

  def to_boolean(str)
    return true if str=="true"
    return false if str=="false"
    return str
  end

  def loadCandidateNew(id_low, id_med, id_high, name, nl_search)
    nl_search = to_boolean(nl_search)
    data = {}
    hashed_data = Hash.new
    peaks_low = Specdb::MsMs.find(id_low)
    hashed_data["energy0"] = parse_spectra(peaks_low.peaks, false, nl_search)
    peaks_med = Specdb::MsMs.find(id_med)
    hashed_data["energy1"] = parse_spectra(peaks_med.peaks, false, nl_search)
    peaks_high = Specdb::MsMs.find(id_high)
    hashed_data["energy2"] = parse_spectra(peaks_high.peaks, false, nl_search)
    hashed_data["status"] = 'Complete'
    hashed_data["compound"] = name

    hashed_data

  end

  def parse_spectra(peaks, filter, nl_search)
    data = []
    if nl_search.to_s == "true"
      nl_search = true
    else
      nl_search = false
    end
    peaks.each do |peak|

      if !nl_search
        if peak.assignments.first.present?
          molecular_formula = [peak.assignments.first.formula]
          # Store url of assigned fragment structure image, if it exists
          if peak.assignments.first.image_url.present?
            puts "SMILES: #{peak.assignments.first.smiles}"
            # structures = [peak.assignments.first.image_url]
            s = Structure.find_by_smiles(peak.assignments.first.smiles)
            if s.nil?
              structures = [peak.assignments.first.image_url]
            else 
              # structures = ["http://moldb.cfmid.wishartlab.com/structures/#{inchi[0].gsub("InChIKey=", "")}/image.png"]
              structures = [s.image.url]
            end
          else
            structures = ["http://moldb.cfmid.com" + peak.assignments.first.path]
          end
        else
          structures = []
          molecular_formula = ""
        end
      end
      if nl_search
        structures = []
        molecular_formula = NeutralLossCalculator.find_neutral_mass_formula(peak.neutral_loss_mass)
        data_point = {x: peak.neutral_loss_mass.to_f, y: peak.intensity.to_f, structure_ids: [12, 10], structure_urls: structures, formula: [molecular_formula]}
      else 
        data_point = {x: peak.mass_charge.to_f, y: peak.intensity.to_f, structure_ids: [12, 10], structure_urls: structures, formula: [molecular_formula]}
      end
      data << data_point
    end
    return data
  end

  def make_candidate_spectra(data_string)
    # Split the data on the title energy1, energy2, ... etc.
    lines_arr = data_string.split("\n").map(&:strip).select{|l|!(l.empty? or l.start_with?('#'))} # Remove empty lines and comment lines
    data_string_mod = lines_arr.join("\n") # Re-join into single string

    parsed_data = data_string_mod.split(/(\n*[A-Za-z]+[\d]*\s*\n*)/i).
      # Strip the empty space
      map(&:strip).
      # Filter out empty chunks
      select{|l|!l.empty?}

    # Assign consistent energy level labels
    parsed_data[0] = "energy0"
    # Insert a blank item if there is no spectra data for an energy level
    if parsed_data[1] !~ /[-+]?[0-9]*\.?[0-9]+\s+[-+]?[0-9]*\.?[0-9]+/
      parsed_data.insert(1, "")
    end
    parsed_data[2] = "energy1"
    if parsed_data[3] !~ /[-+]?[0-9]*\.?[0-9]+\s+[-+]?[0-9]*\.?[0-9]+/
      parsed_data.insert(3, "")
    end
    parsed_data[4] = "energy2"
    if parsed_data[5] !~ /[-+]?[0-9]*\.?[0-9]+\s+[-+]?[0-9]*\.?[0-9]+/
      parsed_data.insert(5, "")
    end

    parsed_data
  end

  def parsed_data_to_hash(parsed_data, structure_list = nil)
    # Turn the split data into a hash like: {"energy1" => data, "energy2" => data }
    hashed_data = Hash[ *parsed_data ].delete_if { |k, v| v.empty? }

    # Parse the data for each energy level
    hashed_data.keys.each do |key|
      spectrum_data = hashed_data[key]

      # Split the data on newlines
      hashed_data[key] = spectrum_data.split("\n").map do |peak|
        # Split scores from spectra and fragments
        divided = peak.strip.split("(")
        # Split spectra and fragments
        peak_data = divided[0].strip.split("\s")
        # If there are scores, put them into and array
        if divided[1].present?
          scores = divided[1].chomp(")").strip.split("\s")
        else
          scores = []
        end
        x = peak_data.shift.to_f
        y = peak_data.shift.to_f

        if structure_list.present?
          structure_urls = peak_data.map { |id|
            if (s = Structure.find_by_smiles(structure_list[id][1])).present?
              s.image.url
            else
              ""
            end
          }
        else
          structure_urls = []
        end

        # Turn the data into a hash
        {x: x, y: y, structure_ids: peak_data, structure_urls: structure_urls, scores: scores }
      end
    end

    hashed_data
  end

  def generate_candidate_images(id, read_data, query)
    fragment_string = read_data.split(/\n\n/)[1]
    if fragment_string.present? # Have to check in case there are no annotations
      fragment_string.split(/\n/).each do |fragment|
        if fragment =~ /^([\w\-]+)\s+([-+]?[0-9]*\.?[0-9]+)\s+(\S+)$/
          frag_id = $1
          structure = $3
          generate_structure(structure, frag_id, query)
        end
      end
    end
  end

  
end

