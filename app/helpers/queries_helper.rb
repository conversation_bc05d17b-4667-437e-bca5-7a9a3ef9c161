module QueriesHelper

  def get_job_status(job_id)
    return Sidekiq::Status::status(job_id)
  end

  def safe_file_exist(output_file)
    begin
      if File.file?(output_file)
        return true
      else
        return false
      end
    rescue Exception => e
      return false
    end
  end

  def pretty_formula(compound_formula)
    compound_formula.gsub(/(\d+)/,'<sub>\1</sub>').html_safe
  end

  def get_image(compound, classname, altname)
    if compound =~ /^InChI/
      compound = Jchem.structure_to_smiles(compound)
    end
    if structure = Structure.find_by_smiles(compound)
      return "<img src=\"" + structure.image.url + "\" class=\"#{classname}\" alt=\"#{altname}\" >"
    else
      return "No Image"
    end
  end

  def get_image_id(compound, classname, altname, inchi)
    if compound =~ /^InChI/
      query_string = "java "
      compound = Jchem.structure_to_smiles(compound)
    end
    if structure = Structure.find_by_smiles(compound)
      return "<img src=\"" + structure.image.url + "\" class=\"#{classname}\" alt=\"#{altname}\" >"
    else
      return "<img src=\"" + "http://moldb.cfmid.wishartlab.com/structures/#{inchi.gsub("InChIKey=", '')}/image.png" + "\" class=\"#{classname}\" alt=\"#{altname}\" >"
    end
  end


  # Generates query compound image, returns false if fails
  def generate_structure(compound, filename, query)
    if !query.error.blank?
      # Don't keep trying to generate structures if we have already hit an error
      return
    else
      filename = "public/" + filename + ".png"
      # Structure must be a SMILES; fragments automatically are but
      # candidate compounds may not be
      if compound =~ /^InChI/
        compound = Jchem.structure_to_smiles(compound)
      end
      begin
        structure = Structure.find_or_create_by(smiles: compound)
        if structure.image.blank? || !File.exists?(structure.image.path)
          Jchem.structure_to_png_file(compound, filename)
          structure.update_attributes(image: File.new(filename))
        end
      rescue StandardError => e
        query.update_attributes( error: "Error generating structure images, please try again or contact an administrator." )
      ensure
        if File.exists?(filename)
          File.delete(filename)
        end
      end
    end
  end
end
