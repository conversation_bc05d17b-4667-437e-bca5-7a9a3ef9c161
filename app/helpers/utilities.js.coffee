$ ->
  # Load the predict examples (any spectra type, ion_type, 
  # adduct_type may be used)
  $('.example-loader').click (e) ->
    to_update = $(this).data('update')
    example = $(this).data('example')
    $("##{to_update}").val(example)
    e.preventDefault()

  $('.assign-example-loader').click (e) ->
    to_update = $(this).data('update')
    example = $(this).data('example')
    $("##{to_update}").val(example)
    to_update_low = $(this).data('update-low')
    example_low = $(this).data('example-low')
    $("##{to_update_low}").val(example_low)
    to_update_medium = $(this).data('update-medium')
    example_medium = $(this).data('example-medium')
    $("##{to_update_medium}").val(example_medium)
    to_update_high = $(this).data('update-high')
    example_high = $(this).data('example-high')
    $("##{to_update_high}").val(example_high)

    $("#mass_tol").val(0.5)
    $("#mass_tol_units").val("Da")

    $("#assign_query_spectra_type").val("ESI")
    set_spectra_options($("#assign_query_spectra_type"))

    e.preventDefault()

  $('.assign-ei-example-loader').click (e) ->
    to_update = $(this).data('update')
    example = $(this).data('example')
    $("##{to_update}").val(example)
    to_update_low = $(this).data('update-low')
    example_low = $(this).data('example-low')
    $("##{to_update_low}").val(example_low)

    $("#mass_tol").val(0.5)
    $("#mass_tol_units").val("Da")

    $("#assign_query_spectra_type").val("EI")
    set_spectra_options($("#assign_query_spectra_type"))

    e.preventDefault()
  $('.example-loader-nl-esi-1').click (e) ->
    mass = $(this).data('mass')
    $('#identify_query_parent_ion_mass').val(mass)
    $('#candidate_mass_tol').val(10.0)
    $('#candidate_mass_tol_units').val('ppm')
    $('#identify_query_candidate_limit').val(100)
    to_update_low = $(this).data('update-low')
    example_low = $(this).data('example-low')
    $("##{to_update_low}").val(example_low)
    to_update_medium = $(this).data('update-medium')
    example_medium = $(this).data('example-medium')
    $("##{to_update_medium}").val(example_medium)
    to_update_high = $(this).data('update-high')
    example_high = $(this).data('example-high')
    $("##{to_update_high}").val(example_high)
    spectra_type_input = $("#adduct_search_spectra_type")
    spectra_type_input.val("ESI")
    $("#adduct_search_ion_mode").val("positive")
    adduct_select = $('#identify_query_adduct_type')
    adduct_list = adduct_select.data('positive-adducts')
    adduct_select.empty()
    for adduct in adduct_list
      option = $('<option></option>').attr("value", adduct).text(adduct)
      adduct_select.append(option)
    $("#identify_query_adduct_type").val("[M+H]+")
    set_spectra_options(spectra_type_input)
    set_spectra_databases(spectra_type_input)
    $("input[id^='identify_query_experimental_database']").prop('checked', false)
    $("input[id^='identify_query_predicted_database']").prop('checked', false)
    $("#identify_query_predicted_database_hmdb").prop('checked', true)
    $("#identify_query_predicted_database_drugbank").prop('checked', true)
    # $("#identify_query_database_kegg").prop('checked', true)
    set_database_options()

    e.preventDefault()

  $('.example-loader-nl-esi-2').click (e) ->
    mass = $(this).data('mass')
    $('#identify_query_parent_ion_mass').val(mass)

    $('#candidate_mass_tol').val(10.0)
    $('#candidate_mass_tol_units').val('ppm')
    $('#identify_query_candidate_limit').val(100)
    to_update_low = $(this).data('update-low')
    example_low = $(this).data('example-low')
    $("##{to_update_low}").val(example_low)
    to_update_medium = $(this).data('update-medium')
    example_medium = $(this).data('example-medium')
    $("##{to_update_medium}").val(example_medium)
    to_update_high = $(this).data('update-high')
    example_high = $(this).data('example-high')
    $("##{to_update_high}").val(example_high)
    spectra_type_input = $("#adduct_search_spectra_type")
    spectra_type_input.val("ESI")
    scoring_function = $("#identify_query_scoring_function")
    scoring_function.val("Dice")
    $("#adduct_search_ion_mode").val("positive")
    adduct_select = $('#identify_query_adduct_type')
    adduct_list = adduct_select.data('positive-adducts')
    adduct_select.empty()
    for adduct in adduct_list
      option = $('<option></option>').attr("value", adduct).text(adduct)
      adduct_select.append(option)
    $("#identify_query_adduct_type").val("[M+H]+")
    set_spectra_options(spectra_type_input)
    set_spectra_databases(spectra_type_input)
    $("input[id^='identify_query_experimental_database']").prop('checked', false)
    $("input[id^='identify_query_predicted_database']").prop('checked', false)
    $("#identify_query_predicted_database_hmdb").prop('checked', true)
    # $("#identify_query_database_kegg").prop('checked', true)
    set_database_options()

    e.preventDefault()
  # Loads the mass example on the identify page when you click
  # the load example link in the description
  $('.example-loader-esi-1').click (e) ->
    mass = $(this).data('mass')
    $('#identify_query_parent_ion_mass').val(mass)

    $('#candidate_mass_tol').val(10.0)
    $('#candidate_mass_tol_units').val('ppm')
    $('#identify_query_candidate_limit').val(100)
    to_update_low = $(this).data('update-low')
    example_low = $(this).data('example-low')
    $("##{to_update_low}").val(example_low)
    to_update_medium = $(this).data('update-medium')
    example_medium = $(this).data('example-medium')
    $("##{to_update_medium}").val(example_medium)
    to_update_high = $(this).data('update-high')
    example_high = $(this).data('example-high')
    $("##{to_update_high}").val(example_high)
    spectra_type_input = $("#adduct_search_spectra_type")
    spectra_type_input.val("ESI")
    $("#adduct_search_ion_mode").val("negative")
    adduct_select = $('#identify_query_adduct_type')
    adduct_list = adduct_select.data('negative-adducts')
    adduct_select.empty()
    for adduct in adduct_list
      option = $('<option></option>').attr("value", adduct).text(adduct)
      adduct_select.append(option)
    $("#identify_query_adduct_type").val("[M-H]-")
    set_spectra_options(spectra_type_input)
    set_spectra_databases(spectra_type_input)
    $("input[id^='identify_query_predicted_database']").prop('checked', false)
    $("input[id^='identify_query_experimental_database']").prop('checked', false)
    $("#identify_query_predicted_database_hmdb").prop('checked', true)
    $("#identify_query_predicted_database_kegg").prop('checked', true)
    $("#identify_query_predicted_database_np-mrd").prop('checked', true)
    set_database_options()

    e.preventDefault()

  $('.example-loader-esi-2').click (e) ->
    mass = $(this).data('mass')
    $('#identify_query_parent_ion_mass').val(mass)
    $("#adduct_search_ion_mode").val("positive")
    $('#candidate_mass_tol').val(50.0)
    $('#candidate_mass_tol_units').val('ppm')
    $('#identify_query_candidate_limit').val(100)
    to_update_low = $(this).data('update-low')
    example_low = $(this).data('example-low')
    $("##{to_update_low}").val(example_low)
    to_update_medium = $(this).data('update-medium')
    example_medium = $(this).data('example-medium')
    $("##{to_update_medium}").val(example_medium)
    to_update_high = $(this).data('update-high')
    example_high = $(this).data('example-high')
    $("##{to_update_high}").val(example_high)
    spectra_type_input = $("#adduct_search_spectra_type")
    spectra_type_input.val("ESI")
    $("#adduct_search_ion_mode").val("positive")
    adduct_select = $('#identify_query_adduct_type')
    adduct_list = adduct_select.data('positive-adducts')
    adduct_select.empty()
    for adduct in adduct_list
      option = $('<option></option>').attr("value", adduct).text(adduct)
      adduct_select.append(option)
    $("#identify_query_adduct_type").val("[M+H]+")
    set_spectra_options(spectra_type_input)
    set_spectra_databases(spectra_type_input)
    $("input[id^='identify_query_predicted_database']").prop('checked', false)
    $("input[id^='identify_query_experimental_database']").prop('checked', false)
    $("#identify_query_predicted_database_hmdb").prop('checked', true)
    $("#identify_query_predicted_database_foodb").prop('checked', true)
    $("#identify_query_predicted_database_kegg").prop('checked', true)
    $("#identify_query_predicted_database_np-mrd").prop('checked', true)
    set_database_options()

    e.preventDefault()

  $('.example-loader-ei').click (e) ->
    mass = $(this).data('mass')
    $('#identify_query_parent_ion_mass').val(mass)
    $('#candidate_mass_tol').val(50.0)
    $('#candidate_mass_tol_units').val('ppm')
    $('#identify_query_candidate_limit').val(100)
    to_update_low = $(this).data('update-low')
    example_low = $(this).data('example-low')
    $("##{to_update_low}").val(example_low)

    spectra_type_input = $("#adduct_search_spectra_type")
    spectra_type_input.val("EI")
    set_spectra_options(spectra_type_input)
    set_spectra_databases(spectra_type_input)
    $("input[id^='identify_query_database']").prop('checked', false)
    $("#identify_query_database_hmdb_deriv").prop('checked', true)
    $("#identify_query_parent_ion_mass_type").val("Derivative")
    set_database_options()

    e.preventDefault()

  # Loads the candidate list example on the identify page when you click
  # the load example link in the description
  $('.example-loader-identify').click (e) ->
    mass = $(this).data('mass')
    to_update = $(this).data('update')
    example = $(this).data('example')
    $("##{to_update}").val(example)
    to_update_low = $(this).data('update-low')
    example_low = $(this).data('example-low')
    $("##{to_update_low}").val(example_low)
    to_update_medium = $(this).data('update-medium')
    example_medium = $(this).data('example-medium')
    $("##{to_update_medium}").val(example_medium)
    to_update_high = $(this).data('update-high')
    example_high = $(this).data('example-high')
    $("##{to_update_high}").val(example_high)

    spectra_type_input = $("#identify_query_spectra_type")
    spectra_type_input.val("ESI")
    set_spectra_options(spectra_type_input)

    e.preventDefault()

  $('.multi-spectra-example-loader').click (e) ->
    to_update_low = $(this).data('update-low')
    example_low = $(this).data('example-low')
    $("##{to_update_low}").val(example_low)
    to_update_medium = $(this).data('update-medium')
    example_medium = $(this).data('example-medium')
    $("##{to_update_medium}").val(example_medium)
    to_update_high = $(this).data('update-high')
    example_high = $(this).data('example-high')
    $("##{to_update_high}").val(example_high)
    e.preventDefault()

# On the identify page, this sets the adducts selector to list
# different adducts depending on the ion mode.
# The possible adducts are stored in the data attributes on
# the select.
ion_mode_selector = (ion_mode_selector, adduct_type_selector) ->
  $(ion_mode_selector).change (e) ->
    selected_ion_mode = $("option:selected",this).text()
    adduct_select = $(adduct_type_selector)

    # Get the appropriate adduct list
    adduct_list =
      switch selected_ion_mode.toUpperCase()
        when 'POSITIVE' then adduct_select.data('positive-adducts')
        when 'NEGATIVE' then adduct_select.data('negative-adducts')

    # Set the new list
    adduct_select.empty()
    for adduct in adduct_list
      option = $('<option></option>').attr("value", adduct).text(adduct)
      adduct_select.append(option)

    e.preventDefault()

$ ->
  ion_mode_selector('#adduct_search_ion_mode','#identify_query_adduct_type')
  ion_mode_selector('#predict_query_ion_mode','#predict_query_adduct_type')

# On the identify page, this sets the scoring functions
# depending on the spectra_type
set_scoring_function_list = (form) ->
  selected_spectra_type = $("option:selected", form.find("[id$=spectra_type]")).text()
  score_select = $('#identify_query_scoring_function')

    # Get the appropriate adduct list
  score_list =
    if (selected_spectra_type == "ESI") && form.is("#find-candidates")
      score_select.data('esi-functions')
    else if (selected_spectra_type == "ESI") && form.is("#submit-candidates")
      score_select.data('esi-functions')
    else 
      score_select.data('esi-functions')

  # Set the new list
  score_select.empty()
  for score in score_list
    option = $('<option></option>').attr("value", score).text(score)
    score_select.append(option)

$ ->
  $("#adduct_search_spectra_type").change (e) ->
    set_scoring_function_list($(this).closest(".tab-pane"))
    e.preventDefault()

$ ->
  $('.btn-runtime').click (e) ->
    $this = $(this)
    $this.toggleClass 'hide-runtime'
    if $this.hasClass('hide-runtime')
      $('.runtime-collapse').collapse('hide')
      $this.html('Show <span class="glyphicon glyphicon-collapse-down"></span>')
    else
      $('.runtime-collapse').collapse('show')
      $this.html('Hide <span class="glyphicon glyphicon-collapse-up"></span>')
    e.preventDefault()

$ ->
  $('#query-submit').click (e) ->
    $("#form-buttons").hide()
    $("#form-submit").show()

# Sets the database options based on the currently selected database
set_database_options = () ->
  selected = $("input[id^='identify_query_database']:checked").toArray().map (c) -> c.value
  if selected.includes("HMDB_Deriv")
    $("#identify_query_parent_ion_mass_type").prop("disabled", false)
    $("#identify_query_parent_ion_mass_type").val("Derivative")
  else
    $("#identify_query_parent_ion_mass_type").prop("disabled", "disabled")
    $("#identify_query_parent_ion_mass_type").val("Original")

# Sets the spectra options based on the currently selected spectra
set_spectra_options = (input) ->
  form = $(input).closest("form")
  ion_mode = form.find("[id$=ion_mode]")
  adduct_type = form.find("[id$=adduct_type]")

  mass_tol = form.find("#mass_tol")
  mass_tol_units = form.find("#mass_tol_units")
  scoring_function = form.find("[id$=scoring_function]")
  # If EI is selected, disable ion mode and adduct type with correct values
  if $(input).val() == "EI"
    ion_mode.val("positive")
    ion_mode.trigger("change") # Trigger change to update adduct_types
    ion_mode.prop("disabled", "disabled")
    if adduct_type.length > 0
      adduct_type.val("M+")
      adduct_type.prop("disabled", "disabled")
    form.find('label[for=low_spectra]').html("70eV Energy")
    form.find('#medium_spectra').parent().hide()
    form.find('#high_spectra').parent().hide()

    if mass_tol.length > 0
      mass_tol.val("0.5")
      mass_tol_units.val("Da")
    if scoring_function.length > 0
      scoring_function.val("DotProduct") # Default

  else
    ion_mode.prop("disabled", null)
    if adduct_type.length > 0
      adduct_type.prop("disabled", null)
    form.find('label[for=low_spectra]').html("Low Energy")
    form.find('#medium_spectra').parent().show()
    form.find('#high_spectra').parent().show()

    if mass_tol.length > 0
      mass_tol.val("10.0")
      mass_tol_units.val("ppm")
    if scoring_function.length > 0
      scoring_function.val("Dice") # Default

set_spectra_databases = (input) ->
  form = $(input).closest("form")
  if $(input).val() == "EI"
    form.find(".database-checkbox.esi:not(.ei) input").prop('checked', false)
    form.find(".database-checkbox.esi").hide()
    form.find(".database-checkbox.ei").show()
    set_database_options()
  else
    form.find(".database-checkbox.ei:not(.esi) input").prop('checked', false)
    form.find(".database-checkbox.ei").hide()
    form.find(".database-checkbox.esi").show()
    set_database_options()

$ ->
  # Mainly to set the form correctly on re-render (i.e. if there are errors)
  for input in $("[id$=spectra_type]")
    set_spectra_options(input)
    set_spectra_databases(input)

  # Change options when EI vs ESI is selected
  $("[id$=spectra_type]").change (e) ->
    set_spectra_options(this)
    set_spectra_databases(this)

$ ->
  # Enabled disabled form input on submit, otherwise values are not submitted
  $('form').bind('submit', () ->
    $(this).find(':input').prop('disabled', null)
  )

$ ->
  # Set database options on load
  set_database_options()
  set_spectra_databases($("#adduct_search_spectra_type"))

  # Only show parent_ion_mass_type select if searching HMDB_Deriv database
  $("input[id^='identify_query_database']").change (e) ->
    set_database_options()

# Reset spectra type to ESI and adjust related inputs accordingly
spectra_type_reset = (form) ->
  if form.find("[id$=spectra_type]").val() == "ESI"
    form.find("[id$=ion_mode]").prop("disabled", null)
    form.find("[id$=adduct_type]").prop("disabled", null)

    $('label[for=low_spectra]').html("Low Energy")
    $('#medium_spectra').parent().show()
    $('#high_spectra').parent().show()
  else
    form.find("[id$=ion_mode]").prop("disabled", "disabled")
    form.find("[id$=adduct_type]").prop("disabled", "disabled")

    $('label[for=low_spectra]').html("70eV Energy")
    $('#medium_spectra').parent().hide()
    $('#high_spectra').parent().hide()

  set_database_options()
  set_scoring_function_list(form)

$ ->
  $("a[href='#submit-candidates'], a[href='#find-candidates']").click (e) ->
    form = $("#" + this.href.replace(/.+#/, ""))
    spectra_type_reset(form)

$ ->
  # If a new .msp input spectra file is selected, get the ids from the 
  # file and populate the spectra_id select input to reduce user error
  $("input[id$=_input_file]").on("change", () ->
    # When the file in[ut is changed, get the necessary elements
    form = $(this).closest("form")
    spectra_id_element = form.find("select[id$=_spectra_id]")
    spectra_id_col = form.find(".spectra-id-col")
    spectra_id_element.html("")
    file = $(this).prop("files")[0] # Get the file
    fname = file.name
    # If this is an .msp get the ids and add them to the spectra_id select input
    if fname.substr((~-fname.lastIndexOf(".") >>> 0) + 2) == "msp"
      reader = new FileReader()
      reader.readAsText(file, "UTF-8") # read the file contents
      reader.onload = (evt) -> # When file is loaded

        fileContent = evt.target.result # event.target.result is the file content
        # User a regex to get all the spectra ids in the file
        regex = /(?:$|\n)ID:\s*(\S+)\n/ig
        ids = []
        while (matches = regex.exec(fileContent))
          ids.push(matches[1])
        # Add the ids to the spectra_id select
        for id in jQuery.unique(ids)
          spectra_id_element.append($("<option />").val(id).text(id))
        # Show the spectra_id select
        spectra_id_col.show()
    else
      # If it's not a .msp file, clear and hide the spectra_id select
      spectra_id_col.hide()
      spectra_id_element.val("")
  )
