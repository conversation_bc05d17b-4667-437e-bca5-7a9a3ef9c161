#!/usr/bin/env python3
"""
Retention Index Predictor - A simple interface for batch prediction of retention indices.
"""

import sys
import os
import pandas as pd
from typing import List, Optional, Dict, Any

# Add the parent directory to the path to import non_der_ri_pred
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from non_der_ri_pred import predict_batch_smiles
except ImportError as e:
    print(f"Error importing prediction module: {e}")
    sys.exit(1)

class RetentionIndexPredictor:
    """
    A class for predicting retention indices of compounds from SMILES strings.
    """
    
    SUPPORTED_PHASES = ["standard_non_polar", "standard_polar"]
    
    def __init__(self):
        """Initialize the predictor."""
        pass
    
    def predict(self, 
                smiles_list: List[str], 
                stationary_phase: str = "standard_non_polar",
                compound_names: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        Predict retention indices for a list of SMILES strings.
        
        Args:
            smiles_list: List of SMILES strings to predict
            stationary_phase: Either "standard_non_polar" or "standard_polar"
            compound_names: Optional list of compound names
            
        Returns:
            List of dictionaries containing prediction results
            
        Raises:
            ValueError: If stationary_phase is not supported
            Exception: If prediction fails
        """
        if stationary_phase not in self.SUPPORTED_PHASES:
            raise ValueError(f"Stationary phase must be one of: {self.SUPPORTED_PHASES}")
        
        if not smiles_list:
            raise ValueError("smiles_list cannot be empty")
        
        if compound_names and len(compound_names) != len(smiles_list):
            raise ValueError("compound_names must have the same length as smiles_list")
        
        try:
            results = predict_batch_smiles(
                smiles_list=smiles_list,
                stationary_phase=stationary_phase,
                compound_names=compound_names
            )
            return results
        except Exception as e:
            raise Exception(f"Prediction failed: {e}")
    
    def predict_to_dataframe(self, 
                           smiles_list: List[str], 
                           stationary_phase: str = "standard_non_polar",
                           compound_names: Optional[List[str]] = None) -> pd.DataFrame:
        """
        Predict retention indices and return results as a pandas DataFrame.
        
        Args:
            smiles_list: List of SMILES strings to predict
            stationary_phase: Either "standard_non_polar" or "standard_polar"
            compound_names: Optional list of compound names
            
        Returns:
            pandas DataFrame with prediction results
        """
        results = self.predict(smiles_list, stationary_phase, compound_names)
        return pd.DataFrame(results)
    
    def predict_from_csv(self, 
                        csv_file: str, 
                        smiles_column: str = "smiles",
                        name_column: Optional[str] = None,
                        stationary_phase: str = "standard_non_polar") -> pd.DataFrame:
        """
        Predict retention indices from a CSV file.
        
        Args:
            csv_file: Path to CSV file containing SMILES strings
            smiles_column: Name of column containing SMILES strings
            name_column: Optional name of column containing compound names
            stationary_phase: Either "standard_non_polar" or "standard_polar"
            
        Returns:
            pandas DataFrame with prediction results
        """
        try:
            df = pd.read_csv(csv_file)
        except Exception as e:
            raise Exception(f"Failed to read CSV file: {e}")
        
        if smiles_column not in df.columns:
            raise ValueError(f"Column '{smiles_column}' not found in CSV file")
        
        smiles_list = df[smiles_column].tolist()
        compound_names = None
        
        if name_column and name_column in df.columns:
            compound_names = df[name_column].tolist()
        
        return self.predict_to_dataframe(smiles_list, stationary_phase, compound_names)
    
    def predict_to_csv(self, 
                      smiles_list: List[str], 
                      output_file: str,
                      stationary_phase: str = "standard_non_polar",
                      compound_names: Optional[List[str]] = None):
        """
        Predict retention indices and save results to a CSV file.
        
        Args:
            smiles_list: List of SMILES strings to predict
            output_file: Path to output CSV file
            stationary_phase: Either "standard_non_polar" or "standard_polar"
            compound_names: Optional list of compound names
        """
        df = self.predict_to_dataframe(smiles_list, stationary_phase, compound_names)
        df.to_csv(output_file, index=False)
        print(f"Results saved to: {output_file}")

def main():
    """Example usage of the RetentionIndexPredictor class."""
    
    # Example SMILES strings
    smiles_list = [
        "CCO",  # Ethanol
        "CC(C)O",  # Isopropanol
        "c1ccccc1",  # Benzene
    ]
    
    compound_names = ["Ethanol", "Isopropanol", "Benzene"]
    
    # Create predictor instance
    predictor = RetentionIndexPredictor()
    
    try:
        # Predict and get results as DataFrame
        df = predictor.predict_to_dataframe(
            smiles_list=smiles_list,
            stationary_phase="standard_non_polar",
            compound_names=compound_names
        )
        
        print("Prediction Results:")
        print(df)
        
        # Save to CSV
        predictor.predict_to_csv(
            smiles_list=smiles_list,
            output_file="ri_predictions.csv",
            stationary_phase="standard_non_polar",
            compound_names=compound_names
        )
        
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
