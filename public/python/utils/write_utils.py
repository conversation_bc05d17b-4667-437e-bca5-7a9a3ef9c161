#def write_props(write_file, smiles_list, labels_list, preds_list):
#    for idx in range(len(smiles_list)):
#        smiles = smiles_list[idx]
#        label = labels_list[idx]
#        pred = preds_list[idx]
#
#        write_file.write('%s,%s,%s\n' % (smiles, str(label), str(pred)))

def write_props(write_file, smiles_list, labels_list, name_list, stat_list, derivative_type_list, preds_list):
    write_file.write('smiles,label,Predicted_RI,Name,Stationary_Phase,Derivatization_Type\n')
    for idx in range(len(smiles_list)):
        smiles = smiles_list[idx]
        label = labels_list[idx]
        pred=round(float(preds_list[idx]),2)
        name = name_list[idx]
        stat_phase = stat_list[idx]
        der_type=derivative_type_list[idx]
        
        write_file.write('%s,%s,%s,%s,%s,%s\n' % (smiles, str(label), str(pred), str(name),str(stat_phase), str(der_type)))
