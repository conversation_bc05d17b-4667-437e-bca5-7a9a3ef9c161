import os,sys
import numpy as np

from rdkit import Chem

"""
usage: python renumber_atoms.py -i 304_NP0001015_20_temp_3D.mol -o out_test.mol -n '8, 7, 6, 5, 4, 3, 2, 1, 0'
convert the renumber list to string before giving input

a = [8, 7, 6, 5, 4, 3, 2, 1, 0]
a_str = ', '.join(str(r) for r in a)
-i   input mol file
-o   output mol file
-n   new atom order as string

"""
def rearange_order(target_order):
    out_order  = np.zeros_like(target_order)
    for i in range(len(target_order)):
        out_order[target_order[i]] = i
    out_order_i = [int(r) for r in out_order]
    return out_order_i


def renumber_mol(mol_file,new_atom_order,include_H=True,out_file="out"):
    from rdkit import Chem
    new_atom_order_i = rearange_order(new_atom_order)
    if new_atom_order!= None:

        if include_H:
            m  = Chem.MolFromMolFile(mol_file,removeHs=False)
        else:
            m  = Chem.MolFromMolFile(mol_file)
        out = Chem.RenumberAtoms(m,new_atom_order_i)
        out_mol = Chem.MolToMolBlock(out)
        with open(out_file,"w") as new_file:
            new_file.write(out_mol)

    else:
        out_mol = Chem.MolToMolBlock(m)
        with open(out_file,"w") as new_file:
            new_file.write(out_mol)

mol_in = ""
mol_out = 'renumbered.mol'
new_atom_order = None

if len(sys.argv) >1:
    for i in range(len(sys.argv)):
        arg_len = len(sys.argv)
        if sys.argv[i] in ['-i']:
            mol_in = os.path.abspath(sys.argv[i+1])
        if sys.argv[i] in ['-o']:
            mol_out = os.path.abspath(sys.argv[i+1])
        if sys.argv[i] in ['-n']:
            new_atom_order = sys.argv[i+1]
            print('new_atom_order',new_atom_order)


if mol_in == "":
    print("no .mol files found")

if new_atom_order==None:
    print("new_atom_order not provided. will return the same file as out_same_as_input.mol")

    mol_out = "out_same_as_input.mol"

else:
    new_atom_order = [int(s) for s in new_atom_order.split(',')]
if mol_out == 'renumbered.mol':
    print("output file name is not provided. the output name will be renumbered.mol")




renumber_mol(mol_in,new_atom_order,include_H=True,out_file=mol_out)
