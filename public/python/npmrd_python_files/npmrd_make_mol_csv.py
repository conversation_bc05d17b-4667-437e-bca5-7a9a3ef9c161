from rdkit.Chem import AllChem as Chem
import sys, os, csv
import shutil

def parse_nmrpred_assignment(nmrpred_assignment):
    """
    Parse NMR pred assignment
    """
    verbose=1
    if verbose==1:
        print  ('Running function parse_nmrpred_assignment() for %s ' % nmrpred_assignment)


    cs_dictionary={}

    with open(nmrpred_assignment) as csv_file:
        reader = csv.reader(csv_file)
        for line in reader:

            if len(line) < 5:
                print ("ERROR 2 !!! NMRpred assignment  is misformatted")
                print ("Exiting...")
                sys.exit()
            else:
                atom_type = line[0]
                if "C" in atom_type or "H" in atom_type:

                    atom_number=int(line[1])
                    chemical_shift=line[2]
                    multiplet_structure=line[3]
                    j_couplings_string=line[4]
                    if j_couplings_string=="":
                        j_couplings_string=" "

                    cs_dictionary[atom_number]={}
                    cs_dictionary[atom_number]["atom_type"]=atom_type
                    cs_dictionary[atom_number]["chemical_shift"]=chemical_shift
                    cs_dictionary[atom_number]["multiplet_structure"]=multiplet_structure
                    cs_dictionary[atom_number]["j_couplings_string"]=j_couplings_string

    debug=1
    if debug==1:
        print ("cs_dictionary = ", cs_dictionary)

    return  cs_dictionary


def parse_and_write_csv(mol, cs_dict, csv_path):

    verbose=1
    debug=1

    header = ["AtomIdx", "AtomSymbol","ChemicalShift","MultipletStructure","J_Couplings"]

    with open(csv_path, 'w') as csv_file:
      writer = csv.writer(csv_file)
      writer.writerow(header)
      for a in mol.GetAtoms():

        atom_index=int(a.GetIdx())+1
        atom_symbol=a.GetSymbol()
        if "H" in atom_symbol or  "C" in atom_symbol:

            atom_type="NA"
            chemical_shift="NA"
            multiplet_structure="NA"
            j_couplings_string="NA"
            proceed=0
            if atom_index in cs_dict:
                atom_type=cs_dict[atom_index]["atom_type"]
                if cs_dict[atom_index]["chemical_shift"]: chemical_shift=cs_dict[atom_index]["chemical_shift"]
                if cs_dict[atom_index]["multiplet_structure"]: multiplet_structure=cs_dict[atom_index]["multiplet_structure"]
                if cs_dict[atom_index]["j_couplings_string"]: j_couplings_string=cs_dict[atom_index]["j_couplings_string"]
                print ("atom_type, chemical_shift, multiplet_structure, j_couplings_string = ", atom_type,chemical_shift,multiplet_structure, j_couplings_string)

                proceed=1

                if  atom_symbol!= atom_type:
                    print ("ERROR!!! atom_symbol %s != atom_type %s "  % (atom_symbol,atom_type))
                    print ("Exiting...")
                    sys.exit()

            else:
                print("ATOM IN MOLECULE NOT IN PREDICTION, SETTING AS NA")
                atom_type=atom_symbol
                proceed=1

            if ("H" in atom_symbol and proceed==1) or "C" in atom_symbol:


                data_row = []
                data_row.append(atom_index)
                data_row.append(atom_symbol)
                data_row.append(chemical_shift)
                data_row.append(multiplet_structure)
                if j_couplings_string.strip():
                    data_row.append(j_couplings_string)
                else:
                    data_row.append("NA")
                if verbose>=0:
                    print ("data_row = ", data_row)
                writer.writerow(data_row)

    if debug==1:
        pass

    return


if __name__ == "__main__":

    #> "#{session_name}_nmrpred_initial_predictions.log"     #SUBMISSIONID_NPID_USERID_nmrpred_initial_predictions.log

    #PATHS
    sid = None       #SUBMISSIONID_NPID_USERID (as a file prefix)
    odir_path = None #public/downloads/SESSIONID #output directory
    
    #INPUT
    input_mol = None        #path/to/SUBMISSIONID_NPID_USERID_output.mol            from draw_mol.py
    input_img = None        #path/to/SUBMISSIONID_NPID_USERID_2d.png                from draw_mol.py
    inputtable_path = None  #path/to/SUBMISSIONID_NPID_USERID_assignmenttable.csv   from nmrpred.py

    #OUTPUT
    mol_path = None     #path/to/SUBMISSIONID_NPID_USERID_temp_3D.mol (copy of -input_mol)
    mol_img_path = None #path/to/SUBMISSIONID_NPID_USERID_temp_3D.png (copy of -imput_img)
    cs_path = None      #path/to/SUBMISSIONID_NPID_USERID_mol.csv     (from parsing inputtable_path)

    for i in range(len(sys.argv)):

        if sys.argv[i] == "-sid": sid = sys.argv[i+1]
        if sys.argv[i] == "-odir_path": odir_path = sys.argv[i+1]

        if sys.argv[i] == "-input_mol": input_mol = sys.argv[i+1]
        if sys.argv[i] == "-inputtable_path": inputtable_path = sys.argv[i+1]

        if sys.argv[i] == "-cs_path": cs_path = sys.argv[i+1]



    try:
        m = Chem.MolFromMolFile(input_mol,removeHs=False)
    except Exception as e:
        print('error reading molecule:\n%s'%input_mol)
        sys.exit()

    if not m.GetConformer().Is3D():
        print('warning: molecule is not 3d')


    hflag = False
    for a in m.GetAtoms():
        if a.GetAtomicNum() == 1:
            hflag = True
    if not hflag:
        print('warning: molecule has no hydrogens')


    cs_dict = parse_nmrpred_assignment(inputtable_path)
    parse_and_write_csv(m, cs_dict, cs_path)


    pass
