import sys
from rdkit.Chem import AllChem as Chem
from rdkit.Chem.Draw import rdMolDraw2D
from rdkit.Chem import AllChem

"""
convert smiles string to various formats using rdkit
atoms are reordered using rdkit canonical smiles ordering

eg
python convert_from_smiles.py "C[C@@H](C(=O)O)N" "HMDB123456"
"""

#User submissions to the NP-MRD may need to be more carefully looked at later

inputstring = sys.argv[1]

mol = Chem.MolFromSmiles(inputstring)
m2=Chem.AddHs(mol)
AllChem.EmbedMolecule(m2)
AllChem.MMFFOptimizeMolecule(m2)

print(Chem.MolToMolBlock(m2))


