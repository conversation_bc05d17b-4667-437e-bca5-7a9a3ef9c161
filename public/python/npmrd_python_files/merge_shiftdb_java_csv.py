import csv
import sys
import os
import shutil

shiftdb_csv = sys.argv[1]
ml_csv = sys.argv[2]

orig_file = shiftdb_csv+".orig"

"""
shiftdb_csv = ..._mol.csv made by website ruby code:

AtomIdx,AtomSymbol,ChemicalShift,MultipletStructure,J_Couplings
1,C,10.9,s,NA
2,C,26.35,s,NA
3,C,64.25,s,NA
5,H,0.94,t,7.0
6,H,0.94,t,7.0
7,H,0.94,t,7.0
8,H,1.49,qt,"7.0,6.2"
9,H,1.49,qt,"7.0,6.2"
10,H,3.5,t,6.2
11,H,3.5,t,6.2
12,H,NA,NA,NA

move to ...mol.csv.orig
"""

"""
java predictor output (505_mol_H2O_prediction.csv_after_swap):

Position,Pred,Test
1,0.89,NA
2,0.89,NA
3,0.89,NA
4,0.89,NA
"""


#read file
header = []
shiftdb_data = []
with open(shiftdb_csv) as f:
    reader = csv.reader(f)
    header = next(reader)
    for line in reader:
        shiftdb_data.append(line)

#read file
ml_data = []
with open(ml_csv) as f:
    reader = csv.reader(f)
    #skip header
    next(reader)
    for line in reader:
        ml_data.append(line)




#copy file, unless it already exists
if not os.path.exists(orig_file):
    shutil.copyfile(shiftdb_csv, orig_file)



shiftdb_atoms = [i[0] for i in shiftdb_data]
ml_atoms = [i[0] for i in ml_data]

#check atoms
for atom in ml_atoms:
    if atom not in shiftdb_atoms:
        raise RuntimeError("ERROR: %s includes atoms not present in %s"%(ml_csv,shiftdb_csv))

#replace data
for line in shiftdb_data:

    atomidx = line[0]

    if atomidx in ml_atoms:

        oldppm = line[2]
        newppm = ml_data[ml_atoms.index(atomidx)][1]

        if newppm == "NA": continue

        if oldppm == "NA" and newppm != "NA":
            try:    float(newppm)
            except: continue

        if oldppm != "NA" and newppm != "NA":
            try:    diff = abs(float(oldppm) - float(newppm))
            except: continue

            if diff > 10:
                raise RuntimeError("ERROR: chemical shifts in %s and %s are inconsistent"%(shiftdb_csv,ml_csv))

        line[2] = newppm



#overwrite old csv
with open(shiftdb_csv, 'w', newline='') as f:
    writer = csv.writer(f)
    writer.writerow(header)
    writer.writerows(shiftdb_data)






