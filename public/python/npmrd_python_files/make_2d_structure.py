# https://iwatobipen.wordpress.com/2017/11/03/draw-high-quality-molecular-image-in-rdkit-rdkit/

import sys
from rdkit import Chem
from rdkit.Chem import Draw
from rdkit.Chem.Draw import DrawingOptions


DrawingOptions.atomLabelFontSize = 55
DrawingOptions.dotsPerAngstrom = 100
DrawingOptions.bondLineWidth = 3.0




def mol_with_atom_index(mol):
    for atom in mol.GetAtoms():
        atom.SetAtomMapNum(atom.GetIdx())
    return mol


def create_2d_str():
    sm = sys.argv[1]
    print("sm ",sm)
    mol   = Chem.MolFromSmiles(sm)
    # mol_H = Chem.AddHs(mol)
    # mol_i = mol_with_atom_index(mol_H)

    file_name_png = "public/structures/"+"temp_image.png"
    # file_name_svg = "public/"+"temp_str.svg"

    Draw.MolToFile(mol,file_name_png)
    # Draw.MolToFile(mol_i,file_name_svg)
    print(file_name_png)


if __name__=='__main__':
    print(sys.argv)
    create_2d_str()