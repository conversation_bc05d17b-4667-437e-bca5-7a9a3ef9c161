from rdkit.Chem import AllChem as Chem
import sys, os, csv
import shutil



if __name__ == "__main__":

    #> "#{session_name}_nmrpred_initial_predictions.log"     #SUBMISSIONID_NPID_USERID_nmrpred_initial_predictions.log

    #PATHS
    sid = None       #SUBMISSIONID_NPID_USERID (as a file prefix)
    odir_path = None #public/downloads/SESSIONID #output directory
    
    #INPUT
    input_mol = None        #path/to/SUBMISSIONID_NPID_USERID_output.mol            from draw_mol.py
    input_img = None        #path/to/SUBMISSIONID_NPID_USERID_2d.png                from draw_mol.py

    #OUTPUT
    mol_path = None     #path/to/SUBMISSIONID_NPID_USERID_temp_3D.mol (copy of -input_mol)
    mol_img_path = None #path/to/SUBMISSIONID_NPID_USERID_temp_3D.png (copy of -imput_img)

    for i in range(len(sys.argv)):

        if sys.argv[i] == "-sid": sid = sys.argv[i+1]
        if sys.argv[i] == "-odir_path": odir_path = sys.argv[i+1]

        if sys.argv[i] == "-input_mol": input_mol = sys.argv[i+1]
        if sys.argv[i] == "-input_img": input_img = sys.argv[i+1]

        if sys.argv[i] == "-mol_path": mol_path = sys.argv[i+1]
        if sys.argv[i] == "-mol_img_path": mol_img_path = sys.argv[i+1]



    try:
        m = Chem.MolFromMolFile(input_mol,removeHs=False)
    except Exception as e:
        print('error reading molecule:\n%s'%input_mol)
        sys.exit()

    if not m.GetConformer().Is3D():
        print('warning: molecule is not 3d')


    hflag = False
    for a in m.GetAtoms():
        if a.GetAtomicNum() == 1:
            hflag = True
    if not hflag:
        print('warning: molecule has no hydrogens')
    

    #make a copy as output
    Chem.rdmolfiles.MolToMolFile(m, mol_path)

    #make a copy as output
    shutil.copy2(input_img, mol_img_path)


    pass
