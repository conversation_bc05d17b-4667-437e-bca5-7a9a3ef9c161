import csv
import sys, os

peaklist = sys.argv[1]
mapfile = sys.argv[2]


"""
mapping file should have two columns

first column: original index
second column: new index


peaklist:

the first column in the csv file will be changed based on mapping
"""

with open(mapfile) as f:
    mapping = {}
    reader = csv.reader(f)
    for line in reader:
        mapping[line[0]] = line[1]



with open(peaklist) as f:
    peakdata = []
    reader = csv.reader(f)
    for line in reader:
        peakdata.append(line)


with open(peaklist + '.remapped', 'w', newline='') as f:
    writer = csv.writer(f)
    for line in peakdata:
        line[0] = mapping[line[0]]
        writer.writerow(line)
