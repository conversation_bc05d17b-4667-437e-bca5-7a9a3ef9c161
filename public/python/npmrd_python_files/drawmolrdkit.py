# coding: utf-8
###############################
# Importing modules
###############################

import sys, csv, os
from rdkit import Chem
from rdkit.Chem import AllChem
from rdkit.Chem.Draw import rdMolDraw2D
from standard_functions_py3 import *

import shutil


###############################
# Importing modules
###############################
verbose_level_1=1
###############################


###############################
# Output options
###############################
mol_file_name_default = "temp_3D.mol"
chemical_shift_output_file_default="mol.csv"
molecule_image_file_default="temp_3D.png"
output_directory_default="public/python"
nmrpred_location_default="backend/nmr-pred"
nmrpred_script="nmrpred.py"
nmrpred_python=None
nmrpred_location=None
mol_file_name=None
chemical_shift_output_file=None
molecule_image_file=None
output_directory=None
mol_file_abs_path=None
chemical_shift_path=None
output_directory_path=None
molecule_image_path = None
session_id=None
session_id_default="project"
solvent="d2o"
input_mol = None
input_img = None


Flags_for_arguments="""
"-s" - smiles string , required
"-mol" - basename of the output mol file, optional
"-mol_path" - absolute path to output mol file   , optional
"-cs" - basename of the output chemical shift file, optional
"-cs_path" - absolute path to output chemical shift file, optional
"-mol_img" - basename of the output image with molecule, optional
"-mol_img_path" - absolute path to the output image with molecule, optional
"-odir"  - relative path to the output direcrtory, optional
"-odir_path" - absolute path to the output direcrtory, optional

"-input_mol" - use an input mol file instead of smiles string
"""


def main(smiles_string,
    mol_file_name,
    chemical_shift_output_file,
    molecule_image_file,
    output_directory,
    mol_file_name_default,
    chemical_shift_output_file_default,
    molecule_image_file_default,
    output_directory_default,
    mol_file_abs_path,
    chemical_shift_path,
    output_directory_path,
    molecule_image_path,
    nmrpred_script,
    nmrpred_location,
    nmrpred_location_default,
    nmrpred_python,
    session_id,
    solvent=None,
    input_mol=None,
    input_img=None,
    ):
    """
    Main program
    """
    verbose=1
    if verbose==1:
        print  ('Running function main()')

    smiles_string=smiles_string.replace("([O-])","(O)")

    print  ('Starting output_directory = ', output_directory)


    if output_directory==None:

        if output_directory_path != None:

            output_directory = output_directory_path

        else:
            output_directory = output_directory_default

    if verbose==1:

        print  ('output_directory_default = ', output_directory_default)

        print  ('output_directory_path = ', output_directory_path)

        print  ('Final output_directory = ', output_directory)


    if mol_file_name != None:

        mol_file= "%s/%s" % (output_directory, mol_file_name)

    else:

        if mol_file_abs_path!=None:

            mol_file = mol_file_abs_path

        else:

            mol_file = "%s/%s" % (output_directory,mol_file_name_default)


    if verbose==1:
        print  ('mol_file = ', mol_file)


    if chemical_shift_output_file  != None:

        csv_path= "%s/%s" % (output_directory, chemical_shift_output_file)

    else:

        if chemical_shift_path!=None:

            csv_path = chemical_shift_path
        else:
            csv_path = "%s/%s" % (output_directory,chemical_shift_output_file_default)


    if verbose==1:
        print  ('csv_path = ', csv_path)


    if molecule_image_file != None:

        molecule_image_file= "%s/%s" % (output_directory, molecule_image_file)

    else:

        if molecule_image_path!=None:

            molecule_image_file= molecule_image_path

        else:
            molecule_image_file  = "%s/%s" % (output_directory, molecule_image_file_default)

    if verbose==1:
        print  ('molecule_image_file = ', molecule_image_file)


    if nmrpred_location == None:
            nmrpred_location = nmrpred_location_default


    if verbose==1:
        print  ('nmrpred_location = ', nmrpred_location)


    if verbose==1:

        print ("mol_file_name = ", mol_file_name)

        print ("molecule_image_file = ", molecule_image_file)

        print ("chemical_shift_output_file  = ", chemical_shift_output_file )

        print ("output_directory  = ", output_directory )

        print ("nmrpred_location  = ", nmrpred_location )


    if not input_mol:
        m = Chem.MolFromSmiles(smiles_string)
        m2=Chem.AddHs(m)
        AllChem.EmbedMolecule(m2)
        AllChem.MMFFOptimizeMolecule(m2)
        if verbose==1: print(m2)
        # AllChem.MolToMolFile(m2,'/public/sructures/temp_3D.sdf')
        # Chem.rdmolfiles.MolToMolFile(m2,"/Users/<USER>/Downloads/drawmolecule/temp_3D.mol")
    else:
        print("input mol file found, using instead of smiles string")
        m2 = Chem.MolFromMolFile(input_mol,removeHs=False)


    Chem.rdmolfiles.MolToMolFile(m2,mol_file)

    # mol_file = "/Users/<USER>/Downloads/drawmolecule/temp_3D.mol"
    #mol_file = "public/python/temp_3D.mol"

    cs_dictionary,\
    nmrpred_mol_file,\
    nmrpred_assignment=get_cs_j_for_mol_file(mol_file,
                        nmrpred_script,
                        nmrpred_location,
                        output_directory,
                        nmrpred_python,
                        session_id,
                        solvent,
                        )



    try:
        mol = Chem.MolFromMolFile(mol_file, removeHs=False)

    except Exception as e:

        print('error reading molecule:\n%s'%e)

        sys.exit()



    if not mol.GetConformer().Is3D():
        print('warning: molecule is not 3d')


    hflag = False
    for a in mol.GetAtoms():
        if a.GetAtomicNum() == 1:
            hflag = True

    if not hflag:
        print('warning: molecule has no hydrogens')


    if not input_img:

        #hack for wedge bonds/stereochemistry on hydrogens

        for a in mol.GetAtoms():
            if a.GetAtomicNum() == 1:
                a.SetAtomicNum(2)

        Chem.AssignAtomChiralTagsFromStructure(mol)
        Chem.AssignStereochemistry(mol, False, True)

        for a in mol.GetAtoms():
            if a.GetAtomicNum() == 2:
                a.SetAtomicNum(1)


        #hack to clean up drawing of ambiguous double bonds

        for b in mol.GetBonds():
            if b.GetBondDir() == Chem.BondDir.EITHERDOUBLE:
                b.SetBondDir(Chem.BondDir.NONE)
            if b.GetStereo() == Chem.BondStereo.STEREOANY:
                b.SetStereo(Chem.BondStereo.STEREONONE)





        #addChiralHs does nothing if H already added
        #comment this line out for 3d view
        mol = rdMolDraw2D.PrepareMolForDrawing(mol, kekulize=True, addChiralHs=True, wedgeBonds=True, forceCoords=True)

        drawer = rdMolDraw2D.MolDraw2DCairo(1200, 800)
        drawer.SetFontSize(0.4)
        opts = drawer.drawOptions()
        opts.updateAtomPalette({1:(0.5,0.5,0.5)}) #grey hydrogens
        #opts.atomLabelDeuteriumTritium = True
        opts.flagCloseContactsDist = False
        opts.additionalAtomLabelPadding = 0.05
        opts.maxFontSize = -1


        #set custom atom labels, including isotopes

        for i in range(mol.GetNumAtoms()):

            a = mol.GetAtomWithIdx(i)
            n = a.GetAtomicNum()
            iso = a.GetIsotope()
            s = a.GetSymbol()


            if n == 1 and iso == 2:
                s = "D"

            if n == 1 and iso == 3:
                s = "T"

            #"15N" etc...

            if n != 1 and iso != 0:
                s = str(a.GetIsotope()) + s

            opts.atomLabels[i] = s + str(i+1)

        drawer.DrawMolecule(mol)
        drawer.FinishDrawing()


        #drawer.WriteDrawingText("public/python/temp_3D.png")
        # drawer.WriteDrawingText("/Users/<USER>/Downloads/drawmolecule/temp_3D.png")
        drawer.WriteDrawingText(molecule_image_file)

    else:
        shutil.copy2(input_img,molecule_image_file)

    header = ["AtomIdx", "AtomSymbol","ChemicalShift","MultipletStructure","J_Couplings"]
    mol_atom = Chem.MolFromMolFile(mol_file, removeHs=False)


    # csv_path = "/Users/<USER>/Downloads/drawmolecule/mol.csv"
    #csv_path = "public/python/mol.csv"



    with open(csv_path, 'w') as csv_file:
      writer = csv.writer(csv_file)
      writer.writerow(header)
      for a in mol_atom.GetAtoms():
        #print ("a.GetAtomMapNum() = ", a.GetAtomMapNum())
        #print ("a.GetAtomicNum() = ", a.GetAtomicNum())
        #print ("a.GetIdx() = ", a.GetIdx()+1)
        #print ("a.GetSymbol() = ", a.GetSymbol())
        #print (linesep)
        atom_index=int(a.GetIdx())+1
        atom_symbol=a.GetSymbol()
        if "H" in atom_symbol or  "C" in atom_symbol:

            atom_type="NA"
            chemical_shift="NA"
            multiplet_structure="NA"
            j_couplings_string="NA"
            proceed=0
            if atom_index in cs_dictionary:
                atom_type=cs_dictionary[atom_index]["atom_type"]
                if cs_dictionary[atom_index]["chemical_shift"]: chemical_shift=cs_dictionary[atom_index]["chemical_shift"]
                if cs_dictionary[atom_index]["multiplet_structure"]:multiplet_structure=cs_dictionary[atom_index]["multiplet_structure"]
                if cs_dictionary[atom_index]["j_couplings_string"]: j_couplings_string=cs_dictionary[atom_index]["j_couplings_string"]
                print ("atom_type, chemical_shift, multiplet_structure, j_couplings_string = ", atom_type,chemical_shift,multiplet_structure, j_couplings_string)

                #j_couplings_string=j_couplings_string.replace(" ","")
                #j_couplings_string=j_couplings_string.replace("Hz,","Hz;")
                #j_couplings_string=j_couplings_string.replace(",","-")
                #j_couplings_string=j_couplings_string.replace(",","-")
                #j_couplings_string=j_couplings_string.replace(" = ","=")
                #j_couplings_string=j_couplings_string.replace(" Hz","Hz")
                #j_couplings_string=j_couplings_string.replace("Hz","")
                #j_couplings_string=j_couplings_string.replace(" Hz","Hz")
                #j_couplings_string=j_couplings_string.replace(";","")

                #for i in

                #if multiplet_structure=="s":
                #   j_couplings_string=""
                proceed=1

                if  atom_symbol!= atom_type:
                    print ("ERROR!!! atom_symbol %s != atom_type %s "  % (atom_symbol,atom_type))
                    print ("Exiting...")
                    sys.exit()

            else:
                print("ATOM IN MOLECULE NOT IN PREDICTION, SETTING AS NA")
                atom_type=atom_symbol
                proceed=1

        if ("H" in atom_symbol and proceed==1) or "C" in atom_symbol:
            if proceed:

                data_row = []
                data_row.append(atom_index)
                data_row.append(atom_symbol)
                data_row.append(chemical_shift)
                data_row.append(multiplet_structure)
                data_row.append(j_couplings_string)
                if verbose>=0:
                    print ("data_row = ", data_row)
                writer.writerow(data_row)

    debug=1
    if debug==1:
        #print ("Exiting after function main()")
        #sys.exit()
        pass

    return



def get_cs_j_for_mol_file(mol_file,
                        nmrpred_script,
                        nmrpred_location,
                        output_directory,
                        nmrpred_python,
                        session_id,
                        solvent,
                        ):
    """
    Get chemical shifts and J couplingds for a mol file
    python3 nmrpred_textoutput_20200727.py --mol dev/aGlucose.sdf  --writeassignmenttable
--outputprefix  prefix      prefix for output files (default: time in seconds)
--outputpath    path

-rw-r--r--@ 1 <USER>  <GROUP>    723 29 Jul 02:06 project_output.mol
-rw-r--r--@ 1 <USER>  <GROUP>    200 29 Jul 02:06 project_assignmenttable.txt
    """
    verbose=1
    if verbose==1:
        print  ('Running function get_cs_j_for_mol_file()')
        print  ('mol_file = ', mol_file)
        print  ('nmrpred_python = ', nmrpred_python)
        print  ('nmrpred_script = ', nmrpred_script)

    current_directory=os.getcwd()

    nmrpred_command=""
    nmrpred_command+="%s " % nmrpred_python
    nmrpred_command+="%s " % nmrpred_script
    nmrpred_command+="--mol %s " % mol_file
    nmrpred_command+="--outputpath %s " % output_directory
    nmrpred_command+="--writeassignmenttable "
    nmrpred_command+="--outputprefix %s " % session_id
    nmrpred_command+="--write1h  "
    nmrpred_command+="--write1hcoup "
    nmrpred_command+="--write13c "
    if solvent:
        nmrpred_command+="--solvent %s " % solvent




    print  ('nmrpred_command = ', nmrpred_command)
    #os.system('echo nmrpred_command = %s ' % nmrpred_command)

    os.chdir(nmrpred_location)
    os.system(nmrpred_command)
    os.chdir(current_directory)

    nmrpred_mol_file="%s/%s_output.mol" % (output_directory,session_id)
    nmrpred_assignment="%s/%s_assignmenttable.txt" % (output_directory,session_id)
    print  ('nmrpred_mol_file = ', nmrpred_mol_file)
    print  ('nmrpred_assignment = ', nmrpred_assignment)
    cs_dictionary=parse_nmrpred_assignment(nmrpred_assignment)
    debug=0
    if debug==1:
        print ("Exiting after function get_cs_j_for_mol_file()")
        sys.exit()
    return cs_dictionary, nmrpred_mol_file, nmrpred_assignment

def parse_nmrpred_assignment(nmrpred_assignment):
    """
    Parse NMR pred assignment

    C,1,20.59,s,
    C,2,176.55,s,
    H,5,1.96,s,"J(H5,H6) = -15.40 Hz, J(H5,H7) = -15.40 Hz"
    H,6,1.96,s,"J(H6,H5) = -15.40 Hz, J(H6,H7) = -15.40 Hz"
    H,7,1.96,s,"J(H7,H5) = -15.40 Hz, J(H7,H6) = -15.40 Hz"

    """
    verbose=1
    if verbose==1:
        print  ('Running function parse_nmrpred_assignment() for %s ' % nmrpred_assignment)


    cs_dictionary={}

#    file_lines=read_file(nmrpred_assignment)
#    for i in file_lines:
#        cs_atom_string=i=i.rstrip()
#
#        y="""
#        if verbose==1:print ("i = ", i)
#        i_split=i.split('"')
#        if verbose==1:print ("i_split = ", i_split)
#        j_couplings_string="NA"
#        cs_atom_string=""
#        if len(i_split)>1:
#            cs_atom_string=i_split[0]
#            j_couplings_string=i_split[1]
#        elif len(i_split)==1:
#             cs_atom_string=i_split[0]
#        else:
#             print ("ERROR 1 !!! NMRpred assignment  is misformatted")
#             print ("Exiting...")
#             sys.exit()
#        """
    with open(nmrpred_assignment) as csv_file:
        reader = csv.reader(csv_file)
        for line in reader:

        #if cs_atom_string!="":
        #    cs_atom_string_split= cs_atom_string.split(",")
        #    if len(cs_atom_string_split)<5:
            if len(line) < 5:
                print ("ERROR 2 !!! NMRpred assignment  is misformatted")
                print ("Exiting...")
                sys.exit()
            else:
                #atom_type=cs_atom_string_split[0]
                atom_type = line[0]
                if "C" in atom_type or "H" in atom_type:
                    #atom_number=int(cs_atom_string_split[1])
                    #chemical_shift=cs_atom_string_split[2]
                    #multiplet_structure=cs_atom_string_split[3]
                    #j_couplings_string=cs_atom_string_split[4]
                    atom_number=int(line[1])
                    chemical_shift=line[2]
                    multiplet_structure=line[3]
                    j_couplings_string=line[4]
                    if j_couplings_string=="":
                        j_couplings_string=" "

                    cs_dictionary[atom_number]={}
                    cs_dictionary[atom_number]["atom_type"]=atom_type
                    cs_dictionary[atom_number]["chemical_shift"]=chemical_shift
                    cs_dictionary[atom_number]["multiplet_structure"]=multiplet_structure
                    cs_dictionary[atom_number]["j_couplings_string"]=j_couplings_string

    debug=1
    if debug==1:
        print ("cs_dictionary = ", cs_dictionary)
        print ("Exiting after function parse_nmrpred_assignment()")
        #sys.exit()
    return  cs_dictionary


####################################
# Arguments
####################################
#f = sys.argv[1]
#print("f = {}".format(f))
# outp = sys.argv[2]
print ("Parsing  arguments:", sys.argv)
command_text="python "
i=0
if len(sys.argv)==2:
    smiles_string= sys.argv[1]
elif len(sys.argv)>2:
    for item in sys.argv:
        #print ("Arguments:", i,item )
        ##############################
        # Project/file management
        ##############################
        if item in ["-l","--l"]:
            location  = os.path.abspath(sys.argv[i + 1])
        if item in ["-p","--p"]:
            project  = sys.argv[i + 1]
        if item in ["-v1","--v1"]:
            verbose_level_1  = int(sys.argv[i + 1])

        if item in ["-nmrpred_loc","--nmrpred_loc"]:
            nmrpred_location_init  = sys.argv[i + 1]
            if (len(nmrpred_location_init)>0 and (nmrpred_location_init[0]=="/" or nmrpred_location_init[0]=="\\")) or  nmrpred_location_init==os.path.abspath(sys.argv[i + 1]):
                nmrpred_location  = os.path.abspath(sys.argv[i + 1])
            else:
               nmrpred_location=  nmrpred_location_init


        if item in ["-nmrpred_python","--nmrpred_python"]:
           nmrpred_python = os.path.abspath(sys.argv[i + 1])


        ##############################
        # RDKIT script options
        ##############################
        if item in ["-s","--s"]:
            smiles_string  = sys.argv[i + 1]
        if item in ["-mol","--mol"]:
            mol_file_name = sys.argv[i + 1]
        if item in ["-mol_path","--mol_path"]:
            mol_file_abs_path = os.path.abspath(sys.argv[i + 1])

        if item in ["-cs","--cs"]:
            chemical_shift_output_file = sys.argv[i + 1]
        if item in ["-cs_path","--cs_path"]:
            chemical_shift_path = os.path.abspath(sys.argv[i + 1])

        if item in ["-mol_img","--mol_img"]:
            molecule_image_file = sys.argv[i + 1]
        if item in ["-mol_img_path","--mol_img_path"]:
            molecule_image_path = os.path.abspath(sys.argv[i + 1])

        if item in ["-odir","--odir"]:
            output_directory = sys.argv[i + 1]

        if item in ["-odir_path","--odir_path"]:
            output_directory_path = os.path.abspath(sys.argv[i + 1])
            if verbose_level_1 >=0: print ("Found output_directory_path argument: ",output_directory_path )


        if item in ["-sid","--sid"]:
            session_id = sys.argv[i + 1]

        if item in ["-input_mol","--input_mol"]:
            input_mol = sys.argv[i + 1]

        if item in ["-input_img","--input_img"]:
            input_img = sys.argv[i + 1]


        # NMRPred arguments:

        if sys.argv[i] == '--sfrq':     sfrq = float(sys.argv[i+1])
        if sys.argv[i] == '--sw':       sw  = float(sys.argv[i+1])
        if sys.argv[i] == '--center':   center = float(sys.argv[i+1])
        if sys.argv[i] == '--npts':     npts = int(sys.argv[i+1])
        if sys.argv[i] == '--zf':       zf = int(sys.argv[i+1])
        if sys.argv[i] == '--lw':       lw = float(sys.argv[i+1])

        if sys.argv[i] == '--13csfrq':  c13sw = float(sys.argv[i+1])
        if sys.argv[i] == '--13csw':     c13sw = float(sys.argv[i+1])
        if sys.argv[i] == '--13ccenter': c13center = float(sys.argv[i+1])
        if sys.argv[i] == '--13cnpts':   c13npts = int(sys.argv[i+1])
        if sys.argv[i] == '--13czf':     c13zf = int(sys.argv[i+1])
        if sys.argv[i] == '--13clw':     c13lw = float(sys.argv[i+1])

        if sys.argv[i] == '--solvent':  solvent = sys.argv[i+1].lower()

        if sys.argv[i] == '--optmol':   optmol = True
        #if sys.argv[i] == '--nomoloutput': moloutput = False

        if sys.argv[i] == '--input1h':    shiftfilepath = sys.argv[i+1]
        if sys.argv[i] == '--input13c':   c13shiftfilepath = sys.argv[i+1]
        if sys.argv[i] == '--input1hcoup': coupfilepath = sys.argv[i+1]

        if sys.argv[i] == '--outputpath':   outputpath = sys.argv[i+1]
        if sys.argv[i] == '--outputprefix': outputprefix = sys.argv[i+1]


        if sys.argv[i] == '--nopred1h':    pred1h = False
        if sys.argv[i] == '--nopred13c':   pred13c = False
        if sys.argv[i] == '--nopred1hcoup': pred1hcoup = False
        if sys.argv[i] == '--noprediction':    pred1h = pred13c = pred1hcoup = False


        #if sys.argv[i] == '--ovwr1h':    ovwr1h = not ovwr1h
        #if sys.argv[i] == '--ovwr13h':   ovwr13c = not ovwr13c
        #if sys.argv[i] == '--ovwr1hcoup': ovwr1hcoup = not ovwr1hcoup


        if sys.argv[i] == '--write1h':    write1h = True
        if sys.argv[i] == '--write13':    write13c = True
        if sys.argv[i] == '--write1hcoup': write1hcoup = True

        if sys.argv[i] == '--writeassignmenttable': writeassigntable = True

        if sys.argv[i] == '--plot1h':     plot1h = True
        if sys.argv[i] == '--plot13c':    plot13c = True





        i+=1
    command_text+=" %s " % item

#######################################################
#  Processing arguments
#######################################################
if nmrpred_python == None:
   nmrpred_python = sys.executable
if session_id==None:
    session_id=session_id_default

#######################################################
#  Main function
#######################################################
main(smiles_string,
    mol_file_name,
    chemical_shift_output_file,
    molecule_image_file,
    output_directory,
    mol_file_name_default,
    chemical_shift_output_file_default,
    molecule_image_file_default,
    output_directory_default,
    mol_file_abs_path,
    chemical_shift_path,
    output_directory_path,
    molecule_image_path,
    nmrpred_script,
    nmrpred_location,
    nmrpred_location_default,
    nmrpred_python,
    session_id,
    solvent,
    input_mol,
    input_img,
    )

