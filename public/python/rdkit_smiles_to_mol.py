import sys
import rdkit
from rdkit.Chem import AllChem as Chem
from rdkit.Chem.Draw import rdMolDraw2D

# """
# convert smiles string to various formats using rdkit
# atoms are reordered using rdkit canonical smiles ordering

# eg
# python convert_from_smiles.py "C[C@@H](C(=O)O)N" "HMDB123456"
# """

#User submissions to the NP-MRD may need to be more carefully looked at later
inputstring = sys.argv[1]
try:
    mol = Chem.MolFromSmiles(inputstring)
    print(mol)
except:
    raise ValueError('conversion failed')
mol = Chem.MolFromSmiles(Chem.MolToSmiles(mol, canonical=True))
mol = Chem.AddHs(mol)
#TRY 3D
try:
    Chem.EmbedMolecule(mol)
    mol.GetConformer()
    Chem.AssignStereochemistryFrom3D(mol)
    print(Chem.MolToMolBlock(mol))
except:
    raise ValueError('3d generation failed')