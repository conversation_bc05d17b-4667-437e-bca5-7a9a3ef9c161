import sys
from rdkit import Chem
from rdkit.Chem import Descriptors
import argparse

# """
# convert smiles string to MW using rdkit
# atoms are reordered using rdkit canonical smiles ordering

# eg
# python convert_from_smiles.py "C[C@@H](C(=O)O)N"
# """

#User submissions to the RI-pred may need to be more carefully looked at later
inputstring = sys.argv[1]
try:
    mol = Chem.MolFromSmiles(inputstring)
    #print(mol)
except:
    raise ValueError('MW calculation failed')
MW=Descriptors.ExactMolWt(mol)
print(MW)