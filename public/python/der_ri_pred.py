#import rdkit and other necessary stuff

#retrieve all values from the args string coming from model ruby files

#smiles to shortest path calculation.

#load the calculated shortest path

#load best model out of the three types of non derivatized model: as per parameter's demand

#predict non derivatized values using that model

#take the smiles to create all possible TMS and TBDMS derivatives

#use ChemBL to discard invalid, infeasible and complex derivatives

#load best model out of the three types of derivatized model: as per parameter's demand

#one by one, predict the derivatized values using that model for all generated derivatives

#either return back all these values or for now: print and see it here.


import torch
import torch.nn as nn
import numpy as np
import tqdm

import os.path
import sys
import torch
import argparse
import utils.data_utils as utils
from models.prop_predictor import PropPredictor
from datasets.mol_dataset import get_loader
from graph.mol_graph import MolGraph
from utils import data_utils, train_utils, write_utils
from preprocess import shortest_paths
import pdb

import pickle
import rdkit.Chem as Chem

import csv
import os.path
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import numba
from numba import jit, cuda
import pandas as pd
import os, sys, re
import time
from optparse import OptionParser
from rdkit import Chem
from rdkit.Chem import AllChem
import csv
import rdkit.Chem.Descriptors
from chembl_structure_pipeline import checker

def transform(smarts_transformation, base_molecule):
    try:
        rxn = AllChem.ReactionFromSmarts(smarts_transformation)
        return rxn.RunReactants((base_molecule,))
    except:
        return -1

def remove_duplicates(products):
    products_hash = {}

    for i in range(0, len(products)):
        smiles = Chem.MolToSmiles(products[i][0], isomericSmiles=True, kekuleSmiles=False)
        if smiles not in products_hash:
            rd_mol = Chem.MolFromSmiles(smiles)
            mol_wt = Chem.Descriptors.ExactMolWt(rd_mol)
            # print(mol_wt)
            if mol_wt <= 900:
                products_hash[smiles] = products[i][0]
    return products_hash


def remove_dup_list(list1):
    list2 = []

    for i in range(len(list1)):
        if list1[i] not in list2:
            list2.append(list1[i])

    return list2


def check_issue(list1):
    new_list = []
    for i in range(len(list1)):
        m = Chem.MolFromSmiles(list1[i])
        if m is not None:
            m2 = Chem.MolToMolBlock(m)
            issues = checker.check_molblock(m2)
            # print(issues)
            if len(issues) > 0:
                if not issues[0][0] == 5 or not issues[0][0] == 6:
                    new_list.append(list1[i])
            else:
                new_list.append(list1[i])

    return new_list


def create_array_dicts(smiles, tms_coh, tms_soh, tms_poh, tms_thiol, tms_ketone1, tms_ketone2, tms_ketone3,
                       tms_aldehyde1, tms_aldehyde2, tms_aldehyde3, tms_amine1, tms_amine2):
    n = 12  # n is a big number
    num_tms = n
    hashlist = [dict() for x in range(num_tms + 1)]

    # hashlist[0][smiles] = Chem.Kekulize(Chem.MolFromSmiles(smiles)) # set consisting of initial molecule
    hashlist[0][smiles] = smiles

    # Attach phenyl group to someplace on the alkyl chain
    # print "adding TMS " + str(i)
    flag = 0
    for i in range(1, num_tms + 1):
        # print(hashlist)
        # print("Printing i:")
        # print(i)
        # print("add TMS " + str(i))
        for key in hashlist[i - 1]:
            # alcohol attached to carbon
            # print("printing key:")
            # print(key)
            ps = transform(tms_coh, Chem.MolFromSmiles(key))
            # print("printing ps:")
            # print(ps)
            if ps == -1:
                flag = 1
                break

            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # OH group attached to S (e.g. sulphate group)_
            ps = transform(tms_soh, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # OH group attached to P (e.g. phosphate group)_
            ps = transform(tms_poh, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # thiol
            ps = transform(tms_thiol, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # enolizable ketone
            ps = transform(tms_ketone1, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)
            ps = transform(tms_ketone2, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)
            ps = transform(tms_ketone3, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # enolizable aldehyde
            ps = transform(tms_aldehyde1, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)
            ps = transform(tms_aldehyde2, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)
            ps = transform(tms_aldehyde3, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # amine 1
            ps = transform(tms_amine1, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # amine 2
            ps = transform(tms_amine2, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

        if flag == 1:
            break
        if len(hashlist[i]) == 0:
            break

    # if flag==1:
    #    return "Cannot process"
    # Output structures
    # output = open(outfile, 'w')
    count = 0
    smiles_kekulized_all = []

    for i in range(1, num_tms + 1):
        for key in hashlist[i]:
            # Format SMILES to show explicit bonds in aromatic rings (Kekule form)
            mol = Chem.MolFromSmiles(key)
            Chem.Kekulize(mol)
            smiles_kekulized = Chem.MolToSmiles(mol, isomericSmiles=True, kekuleSmiles=True)
            smiles_kekulized_all.append(smiles_kekulized)
            count += 1

    return smiles_kekulized_all


def create_array_dicts2(smiles, tms_coh, tms_soh, tms_poh, tms_thiol, tms_ketone1, tms_ketone2, tms_ketone3,
                        tms_aldehyde1, tms_aldehyde2, tms_aldehyde3, tms_amine1, tms_amine2, methoxy_OH, ethoxy_OH,
                        methylation_amine1, methylation_amine2, ethylation_amine1, ethylation_amine2, methylation_soh,
                        ethylation_soh, methylation_poh, ethylation_poh, methylation_thiol, ethylation_thiol):
    n = 12  # n is a big number
    num_tms = n
    hashlist = [dict() for x in range(num_tms + 1)]

    # hashlist[0][smiles] = Chem.Kekulize(Chem.MolFromSmiles(smiles)) # set consisting of initial molecule
    hashlist[0][smiles] = smiles

    # Attach phenyl group to someplace on the alkyl chain
    # print "adding TMS " + str(i)
    flag = 0
    for i in range(1, num_tms + 1):
        print("add TMS " + str(i))
        for key in hashlist[i - 1]:
            # alcohol attached to carbon
            ps = transform(tms_coh, Chem.MolFromSmiles(key))

            if ps == -1:
                flag = 1
                break

            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # OH group attached to S (e.g. sulphate group)_
            ps = transform(tms_soh, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # OH group attached to P (e.g. phosphate group)_
            ps = transform(tms_poh, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # thiol
            ps = transform(tms_thiol, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # enolizable ketone
            ps = transform(tms_ketone1, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)
            ps = transform(tms_ketone2, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)
            ps = transform(tms_ketone3, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # enolizable aldehyde
            ps = transform(tms_aldehyde1, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)
            ps = transform(tms_aldehyde2, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)
            ps = transform(tms_aldehyde3, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # amine 1
            ps = transform(tms_amine1, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # amine 2
            ps = transform(tms_amine2, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # methoxy
            ps = transform(methoxy_OH, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # ethoxy
            ps = transform(ethoxy_OH, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # methylation_amine1
            ps = transform(methylation_amine1, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # methylation_amine2
            ps = transform(methylation_amine2, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # ethylation_amine1
            ps = transform(ethylation_amine1, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # ethylation_amine2
            ps = transform(ethylation_amine2, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # methylation_soh
            ps = transform(methylation_soh, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # ethylation_soh
            ps = transform(ethylation_soh, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # methylation_poh
            ps = transform(methylation_poh, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # ethylation_poh
            ps = transform(ethylation_poh, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # methylation_thiol
            ps = transform(methylation_thiol, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

            # ethylation_thiol
            ps = transform(ethylation_thiol, Chem.MolFromSmiles(key))
            ps_hash_tmp = remove_duplicates(ps)
            hashlist[i].update(ps_hash_tmp)

        if flag == 1:
            break
        if len(hashlist[i]) == 0:
            break

    # if flag==1:
    #    return "Cannot process"
    # Output structures
    # output = open(outfile, 'w')
    count = 0
    smiles_kekulized_all = []

    for i in range(1, num_tms + 1):
        for key in hashlist[i]:
            # Format SMILES to show explicit bonds in aromatic rings (Kekule form)
            mol = Chem.MolFromSmiles(key)
            Chem.Kekulize(mol)
            smiles_kekulized = Chem.MolToSmiles(mol, isomericSmiles=True, kekuleSmiles=True)
            smiles_kekulized_all.append(smiles_kekulized)
            count += 1

    return smiles_kekulized_all

def make_tms(smiles):
    tms_coh = "[O;X2;H1:1][#6:2]>>[O;X2;H0:1]([#6:2])[Si](C)(C)C"  # will also work on COOH
    tms_soh = "[O;X2;H1:1][S:2]>>[O;X2;H0:1]([S:2])[Si](C)(C)C"
    tms_poh = "[O;X2;H1:1][P:2]>>[O;X2;H0:1]([P:2])[Si](C)(C)C"
    # For OH, only allow for OH group attached to C, S, or P. (H2O, for example, should not match).
    tms_thiol = "[S;X2;H1:1]>>[S;X2;H0:1][Si](C)(C)C"
    # tms_ketone = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H1,H2,H3:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3:4])[Si](C)(C)C"
    tms_ketone1 = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H1:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3;H0:4])[Si](C)(C)C"
    tms_ketone2 = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H2:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3;H1:4])[Si](C)(C)C"
    tms_ketone3 = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H3:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3;H2:4])[Si](C)(C)C"
    # enolizable ketone
    # requires that the alpha carbon be aliphatic, and not involved in any double or triple bonds before the reaction
    # http://www.ochempal.org/index.php/alphabetical/e-f/enolizable-ketone/
    # tms_aldehyde = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H1,H2,H3:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3:3])[Si](C)(C)C"
    tms_aldehyde1 = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H1:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3;H0:3])[Si](C)(C)C"
    tms_aldehyde2 = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H2:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3;H1:3])[Si](C)(C)C"
    tms_aldehyde3 = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H3:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3;H2:3])[Si](C)(C)C"
    # enolizable aldehyde
    # requires that the alpha carbon be aliphatic, and not involved in any double or triple bonds before the reaction
    # http://www.ochempal.org/index.php/alphabetical/e-f/enolizable-aldehyde/
    tms_amine1 = "[#7;H2:1]>>[#7;H1:1][Si](C)(C)C"
    tms_amine2 = "[#7;H1:1]>>[#7;H0:1][Si](C)(C)C"

    dict_array1 = create_array_dicts(smiles, tms_coh, tms_soh, tms_poh, tms_thiol, tms_ketone1, tms_ketone2,
                                     tms_ketone3, tms_aldehyde1, tms_aldehyde2, tms_aldehyde3, tms_amine1, tms_amine2)

    return dict_array1


def make_tbdms(smiles):
    tms_coh = "[O;X2;H1:1][#6:2]>>[O;X2;H0:1]([#6:2])[Si](C)(C)C(C)(C)C"  # will also work on COOH
    tms_soh = "[O;X2;H1:1][S:2]>>[O;X2;H0:1]([S:2])[Si](C)(C)C(C)(C)C"
    tms_poh = "[O;X2;H1:1][P:2]>>[O;X2;H0:1]([P:2])[Si](C)(C)C(C)(C)C"
    # For OH, only allow for OH group attached to C, S, or P. (H2O, for example, should not match).
    tms_thiol = "[S;X2;H1:1]>>[S;X2;H0:1][Si](C)(C)C(C)(C)C"
    # tms_ketone = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H1,H2,H3:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3:4])[Si](C)(C)C"
    tms_ketone1 = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H1:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3;H0:4])[Si](C)(C)C(C)(C)C"
    tms_ketone2 = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H2:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3;H1:4])[Si](C)(C)C(C)(C)C"
    tms_ketone3 = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H3:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3;H2:4])[Si](C)(C)C(C)(C)C"
    # enolizable ketone
    # requires that the alpha carbon be aliphatic, and not involved in any double or triple bonds before the reaction
    # http://www.ochempal.org/index.php/alphabetical/e-f/enolizable-ketone/
    # tms_aldehyde = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H1,H2,H3:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3:3])[Si](C)(C)C"
    tms_aldehyde1 = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H1:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3;H0:3])[Si](C)(C)C(C)(C)C"
    tms_aldehyde2 = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H2:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3;H1:3])[Si](C)(C)C(C)(C)C"
    tms_aldehyde3 = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H3:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3;H2:3])[Si](C)(C)C(C)(C)C"
    # enolizable aldehyde
    # requires that the alpha carbon be aliphatic, and not involved in any double or triple bonds before the reaction
    # http://www.ochempal.org/index.php/alphabetical/e-f/enolizable-aldehyde/

    tms_amine1 = "[#7;H2:1]>>[#7;H1:1][Si](C)(C)C(C)(C)C"
    tms_amine2 = "[#7;H1:1]>>[#7;H0:1][Si](C)(C)C(C)(C)C"

    dict_array1 = create_array_dicts(smiles, tms_coh, tms_soh, tms_poh, tms_thiol, tms_ketone1, tms_ketone2,
                                     tms_ketone3, tms_aldehyde1, tms_aldehyde2, tms_aldehyde3, tms_amine1, tms_amine2)

    return dict_array1


def make_tbdps(smiles):
    tms_coh = "[O;X2;H1:1][#6:2]>>[O;X2;H0:1]([#6:2])CC(C)(C)[Si](C1=CC=CC=C1)C1=CC=CC=C1"  # will also work on COOH
    tms_soh = "[O;X2;H1:1][S:2]>>[O;X2;H0:1]([S:2])CC(C)(C)[Si](C1=CC=CC=C1)C1=CC=CC=C1"
    tms_poh = "[O;X2;H1:1][P:2]>>[O;X2;H0:1]([P:2])CC(C)(C)[Si](C1=CC=CC=C1)C1=CC=CC=C1"
    # For OH, only allow for OH group attached to C, S, or P. (H2O, for example, should not match).
    tms_thiol = "[S;X2;H1:1]>>[S;X2;H0:1]CC(C)(C)[Si](C1=CC=CC=C1)C1=CC=CC=C1"
    # tms_ketone = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H1,H2,H3:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3:4])[Si](C)(C)C"
    tms_ketone1 = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H1:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3;H0:4])CC(C)(C)[Si](C1=CC=CC=C1)C1=CC=CC=C1"
    tms_ketone2 = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H2:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3;H1:4])CC(C)(C)[Si](C1=CC=CC=C1)C1=CC=CC=C1"
    tms_ketone3 = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H3:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3;H2:4])CC(C)(C)[Si](C1=CC=CC=C1)C1=CC=CC=C1"
    # enolizable ketone
    # requires that the alpha carbon be aliphatic, and not involved in any double or triple bonds before the reaction
    # http://www.ochempal.org/index.php/alphabetical/e-f/enolizable-ketone/
    # tms_aldehyde = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H1,H2,H3:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3:3])[Si](C)(C)C"
    tms_aldehyde1 = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H1:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3;H0:3])CC(C)(C)[Si](C1=CC=CC=C1)C1=CC=CC=C1"
    tms_aldehyde2 = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H2:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3;H1:3])CC(C)(C)[Si](C1=CC=CC=C1)C1=CC=CC=C1"
    tms_aldehyde3 = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H3:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3;H2:3])CC(C)(C)[Si](C1=CC=CC=C1)C1=CC=CC=C1"

    # enolizable aldehyde
    # requires that the alpha carbon be aliphatic, and not involved in any double or triple bonds before the reaction
    # http://www.ochempal.org/index.php/alphabetical/e-f/enolizable-aldehyde/
    # tms_amine1 = "[#7;H2:1]>>[#7;H1:1][Si](C1=CC=CC=C1)C1=CC=CC=C1"
    # tms_amine2 = "[#7;H1:1]>>[#7;H0:1][Si](C1=CC=CC=C1)C1=CC=CC=C1"

    tms_amine1 = "[#7;H2:1]>>CC(C)(C)[Si]([#7;H1:1])(C1=CC=CC=C1)C1=CC=CC=C1"
    tms_amine2 = "[#7;H1:1]>>CC(C)(C)[Si]([#7;H0:1])(C1=CC=CC=C1)C1=CC=CC=C1"

    dict_array1 = create_array_dicts(smiles, tms_coh, tms_soh, tms_poh, tms_thiol, tms_ketone1, tms_ketone2,
                                     tms_ketone3, tms_aldehyde1, tms_aldehyde2, tms_aldehyde3, tms_amine1, tms_amine2)

    return dict_array1


def make_tips(smiles):
    tms_coh = "[O;X2;H1:1][#6:2]>>[O;X2;H0:1]([#6:2])[Si]([#6;A](C)(C)C)([#6;A](C)(C)C)[#6;A](C)(C)C"
    tms_soh = "[O;X2;H1:1][S:2]>>[O;X2;H0:1]([S:2])[Si]([#6;A](C)(C)C)([#6;A](C)(C)C)[#6;A](C)(C)C"
    tms_poh = "[O;X2;H1:1][P:2]>>[O;X2;H0:1]([P:2])[Si]([#6;A](C)(C)C)([#6;A](C)(C)C)[#6;A](C)(C)C"
    # For OH, only allow for OH group attached to C, S, or P. (H2O, for example, should not match).
    tms_thiol = "[S;X2;H1:1]>>[S;X2;H0:1][Si]([#6;A](C)(C)C)([#6;A](C)(C)C)[#6;A](C)(C)C"
    # tms_ketone = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H1,H2,H3:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3:4])[Si](C)(C)C"
    tms_ketone1 = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H1:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3;H0:4])[Si]([#6;A](C)(C)C)([#6;A](C)(C)C)[#6;A](C)(C)C"
    tms_ketone2 = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H2:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3;H1:4])[Si]([#6;A](C)(C)C)([#6;A](C)(C)C)[#6;A](C)(C)C"
    tms_ketone3 = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H3:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3;H2:4])[Si]([#6;A](C)(C)C)([#6;A](C)(C)C)[#6;A](C)(C)C"
    # enolizable ketone
    # requires that the alpha carbon be aliphatic, and not involved in any double or triple bonds before the reaction
    # http://www.ochempal.org/index.php/alphabetical/e-f/enolizable-ketone/
    # tms_aldehyde = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H1,H2,H3:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3:3])[Si](C)(C)C"
    tms_aldehyde1 = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H1:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3;H0:3])[Si]([#6;A](C)(C)C)([#6;A](C)(C)C)[#6;A](C)(C)C"
    tms_aldehyde2 = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H2:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3;H1:3])[Si]([#6;A](C)(C)C)([#6;A](C)(C)C)[#6;A](C)(C)C"
    tms_aldehyde3 = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H3:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3;H2:3])[Si]([#6;A](C)(C)C)([#6;A](C)(C)C)[#6;A](C)(C)C"
    # enolizable aldehyde
    # requires that the alpha carbon be aliphatic, and not involved in any double or triple bonds before the reaction
    # http://www.ochempal.org/index.php/alphabetical/e-f/enolizable-aldehyde/
    tms_amine1 = "[#7;H2:1]>>[#7;H1:1][Si]([#6;A](C)(C)C)([#6;A](C)(C)C)[#6;A](C)(C)C"
    tms_amine2 = "[#7;H1:1]>>[#7;H0:1][Si]([#6;A](C)(C)C)([#6;A](C)(C)C)[#6;A](C)(C)C"

    dict_array1 = create_array_dicts(smiles, tms_coh, tms_soh, tms_poh, tms_thiol, tms_ketone1, tms_ketone2,
                                     tms_ketone3, tms_aldehyde1, tms_aldehyde2, tms_aldehyde3, tms_amine1, tms_amine2)

    return dict_array1


def make_tes(smiles):
    tms_coh = "[O;X2;H1:1][#6:2]>>[O;X2;H0:1]([#6:2])[Si](CC)(CC)CC"  # will also work on COOH
    tms_soh = "[O;X2;H1:1][S:2]>>[O;X2;H0:1]([S:2])[Si](CC)(CC)CC"
    tms_poh = "[O;X2;H1:1][P:2]>>[O;X2;H0:1]([P:2])[Si](CC)(CC)CC"
    # For OH, only allow for OH group attached to C, S, or P. (H2O, for example, should not match).
    tms_thiol = "[S;X2;H1:1]>>[S;X2;H0:1][Si](CC)(CC)CC"
    # tms_ketone = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H1,H2,H3:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3:4])[Si](CC)(CC)CC"
    tms_ketone1 = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H1:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3;H0:4])[Si](CC)(CC)CC"
    tms_ketone2 = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H2:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3;H1:4])[Si](CC)(CC)CC"
    tms_ketone3 = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H3:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3;H2:4])[Si](CC)(CC)CC"
    # enolizable ketone
    # requires that the alpha carbon be aliphatic, and not involved in any double or triple bonds before the reaction
    # http://www.ochempal.org/index.php/alphabetical/e-f/enolizable-ketone/
    # tms_aldehyde = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H1,H2,H3:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3:3])[Si](CC)(CC)CC"
    tms_aldehyde1 = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H1:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3;H0:3])[Si](CC)(CC)CC"
    tms_aldehyde2 = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H2:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3;H1:3])[Si](CC)(CC)CC"
    tms_aldehyde3 = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H3:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3;H2:3])[Si](CC)(CC)CC"
    # enolizable aldehyde
    # requires that the alpha carbon be aliphatic, and not involved in any double or triple bonds before the reaction
    # http://www.ochempal.org/index.php/alphabetical/e-f/enolizable-aldehyde/
    tms_amine1 = "[#7;H2:1]>>[#7;H1:1][Si](CC)(CC)CC"
    tms_amine2 = "[#7;H1:1]>>[#7;H0:1][Si](CC)(CC)CC"

    dict_array1 = create_array_dicts(smiles, tms_coh, tms_soh, tms_poh, tms_thiol, tms_ketone1, tms_ketone2,
                                     tms_ketone3, tms_aldehyde1, tms_aldehyde2, tms_aldehyde3, tms_amine1, tms_amine2)

    return dict_array1


def make_tfa(smiles):
    tms_coh = "[O;X2;H1:1][#6:2]>>C-[#8]-C(=O)C(F)(F)F"
    tms_soh = "[#8;H1X2:1]-[#16]>>FC(F)(F)[#6](=O)-[#8]-[#16]"
    tms_poh = "[#8;H1X2:1]-[#15]>>FC(F)(F)[#6](=O)-[#8]-[#15]"
    # For OH, only allow for OH group attached to C, S, or P. (H2O, for example, should not match).
    tms_thiol = "[#16;A;H1X2:1]>>FC(F)(F)[#6](-[#16;H0X2:1])=O"
    # tms_ketone = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H1,H2,H3:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3:4])[Si](C)(C)C"

    # tms_ketone1 = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H1:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3;H0:4])[Si](C)(C)C"
    tms_ketone1 = "[#6;A;H1X4:4][#6:2]([#6;A:3])=[O;H0X1D1:1]>>[#6;A:3][#6:2](=[#6;A;H0X3:4])-[#8;H0X2D1:1]-[#6](=O)C(F)(F)F"
    tms_ketone2 = "[#6;A;H2X4:4][#6:2]([#6;A:3])=[O;H0X1D1:1]>>[#6;A:3][#6:2](=[#6;A;H1X3:4])-[#8;H0X2D1:1]-[#6](=O)C(F)(F)F"
    tms_ketone3 = "[#6;A:3][#6:2]([#6;A;H3X4:4])=[O;H0X1D1:1]>>[#6;A:3][#6:2](=[#6;A;H2X3:4])-[#8;H0X2D1:1]-[#6](=O)C(F)(F)F"
    # enolizable ketone
    # requires that the alpha carbon be aliphatic, and not involved in any double or triple bonds before the reaction
    # http://www.ochempal.org/index.php/alphabetical/e-f/enolizable-ketone/
    # tms_aldehyde = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H1,H2,H3:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3:3])[Si](C)(C)C"
    tms_aldehyde1 = "[#6;A;H1X4:3][#6H1:2]=[O;H0X1D1:1]>>FC(F)(F)[#6](=O)-[#8;H0X2D1:1]-[#6H1:2]=[#6;A;H0X3:3]"
    tms_aldehyde2 = "[#6;A;H2X4:3][#6H1:2]=[O;H0X1D1:1]>>FC(F)(F)[#6](=O)-[#8;H0X2D1:1]-[#6H1:2]=[#6;A;H1X3:3]"
    tms_aldehyde3 = "[#6;A;H3X4:3][#6H1:2]=[O;H0X1D1:1]>>FC(F)(F)[#6](=O)-[#8;H0X2D1:1]-[#6H1:2]=[#6;A;H2X3:3]"
    # enolizable aldehyde
    # requires that the alpha carbon be aliphatic, and not involved in any double or triple bonds before the reaction
    # http://www.ochempal.org/index.php/alphabetical/e-f/enolizable-aldehyde/
    tms_amine1 = "[#7H2:1]>>[#7H1:1]-[#6](=O)C(F)(F)F"
    tms_amine2 = "[#7H1:1]>>[#7H0:1]-[#6](=O)C(F)(F)F"

    dict_array1 = create_array_dicts(smiles, tms_coh, tms_soh, tms_poh, tms_thiol, tms_ketone1, tms_ketone2,
                                     tms_ketone3, tms_aldehyde1, tms_aldehyde2, tms_aldehyde3, tms_amine1, tms_amine2)

    return dict_array1


def make_oximation(smiles):
    tms_coh = "[O;X2;H1:1][#6:2]>>[O;X2;H0:1]([#6:2])[Si](C)(C)C"  # will also work on COOH
    tms_soh = "[O;X2;H1:1][S:2]>>[O;X2;H0:1]([S:2])[Si](C)(C)C"
    tms_poh = "[O;X2;H1:1][P:2]>>[O;X2;H0:1]([P:2])[Si](C)(C)C"
    # For OH, only allow for OH group attached to C, S, or P. (H2O, for example, should not match).
    tms_thiol = "[S;X2;H1:1]>>[S;X2;H0:1][Si](C)(C)C"
    # tms_ketone = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H1,H2,H3:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3:4])[Si](C)(C)C"
    tms_ketone1 = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H1:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3;H0:4])[Si](C)(C)C"
    tms_ketone2 = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H2:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3;H1:4])[Si](C)(C)C"
    tms_ketone3 = "[O;X1;D1;H0:1]=[C:2]([C:3])[C;X4;H3:4]>>[O;X2;D1;H0:1]([C:2]([C:3])=[C;X3;H2:4])[Si](C)(C)C"
    # enolizable ketone
    # requires that the alpha carbon be aliphatic, and not involved in any double or triple bonds before the reaction
    # http://www.ochempal.org/index.php/alphabetical/e-f/enolizable-ketone/
    # tms_aldehyde = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H1,H2,H3:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3:3])[Si](C)(C)C"
    tms_aldehyde1 = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H1:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3;H0:3])[Si](C)(C)C"
    tms_aldehyde2 = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H2:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3;H1:3])[Si](C)(C)C"
    tms_aldehyde3 = "[O;X1;D1;H0:1]=[C;H1:2][C;X4;H3:3]>>[O;X2;D1;H0:1]([C;H1:2]=[C;X3;H2:3])[Si](C)(C)C"
    # enolizable aldehyde
    # requires that the alpha carbon be aliphatic, and not involved in any double or triple bonds before the reaction
    # http://www.ochempal.org/index.php/alphabetical/e-f/enolizable-aldehyde/
    tms_amine1 = "[#7;H2:1]>>[#7;H1:1][Si](C)(C)C"
    tms_amine2 = "[#7;H1:1]>>[#7;H0:1][Si](C)(C)C"

    methoxy_OH = "[#6:2]-[#8;H1X2:1]>>[#6:2]-[#8;H0X2:1][#6;A]"
    ethoxy_OH = "[#6:2]-[#8;H1X2:1]>>[#6:2]-[#8;H0X2:1][#6;A][#6;A]"

    methylation_amine1 = "[#7;H2:1]>>[#7;H1:1][#6;A]"
    ethylation_amine1 = "[#7;H2:1]>>[#7;H1:1][#6;A][#6;A]"

    methylation_amine2 = "[#7;H1:1]>>[#7;H0:1][#6;A]"
    ethylation_amine2 = "[#7;H1:1]>>[#7;H0:1][#6;A][#6;A]"

    methylation_soh = "[O;X2;H1:1][S:2]>>[O;X2;H0:1]([S:2])[#6;A]"
    ethylation_soh = "[O;X2;H1:1][S:2]>>[O;X2;H0:1]([S:2])[#6;A][#6;A]"

    methylation_poh = "[O;X2;H1:1][P:2]>>[O;X2;H0:1]([P:2])[#6;A]"
    ethylation_poh = "[O;X2;H1:1][P:2]>>[O;X2;H0:1]([P:2])[#6;A][#6;A]"

    methylation_thiol = "[S;X2;H1:1]>>[S;X2;H0:1][#6;A]"
    ethylation_thiol = "[S;X2;H1:1]>>[S;X2;H0:1][#6;A][#6;A]"

    dict_array1 = create_array_dicts2(smiles, tms_coh, tms_soh, tms_poh, tms_thiol, tms_ketone1, tms_ketone2,
                                      tms_ketone3, tms_aldehyde1, tms_aldehyde2, tms_aldehyde3, tms_amine1, tms_amine2,
                                      methoxy_OH, ethoxy_OH, methylation_amine1, methylation_amine2, ethylation_amine1,
                                      ethylation_amine2, methylation_soh, ethylation_soh, methylation_poh,
                                      ethylation_poh, methylation_thiol, ethylation_thiol)

    return dict_array1


start_time = time.time()


def create_dirs(args, output_dir):
    utils.create_dir_if_not_exists(output_dir)
    args.model_dir = '%s/models' % output_dir
    args.result_dir = '%s/results' % output_dir

    for dir in [args.model_dir, args.result_dir]:
        utils.create_dir_if_not_exists(dir)

def write_args(args):
    path=os.getcwd()
    
    params_file = open(path+"/public/python/"+'%s/params.txt' % args.output_dir, 'w+')
    for attr, value in sorted(args.__dict__.items()):
        params_file.write("%s=%s\n" % (attr, value))
    params_file.close()

def get_args():
    parser = argparse.ArgumentParser()

    parser.add_argument('-cuda', action='store_true', default=False,
                        help='Whether or not to use GPU.')
    parser.add_argument('-data', type=str, default='data/dersemistdnp',
                        help='Input data directory.')
    parser.add_argument('-output_dir', type=str, default='output_test/dersemistdnp_transformer',
                        help='The output directory.')

    parser.add_argument('-test_mode', action='store_true', default=False,
                        help='Activates test mode for debugging purposes.')
    parser.add_argument('-test_model', type=str, default='',
                        help='The model path used for testing')
    parser.add_argument('-data_split', type=int, default=0,
                        help='Test split to run model on')
    parser.add_argument('-pretrain_model', type=str, default='',
                        help='The model path used for pretrained model')
    parser.add_argument('-multi', action='store_true', default=False,
                        help='Indicator for being multiclass training.')
    parser.add_argument('-n_classes', type=int, default=1,
                        help='Helper variable for number of classes')

    parser.add_argument('-model_type', type=str, default='transformer',
                        help='The graph model type to use')
    parser.add_argument('-loss_type', type=str, default='mae',
                        help='The loss type for the dataset')
    parser.add_argument('-agg_func', type=str, default='sum',
                        help='Agg function, sum or mean')
    parser.add_argument('-self_attn', action='store_true', default=False,
                        help='Whether or not to include self in attn')

    parser.add_argument('-n_rounds', type=int, default=1,
                        help='Number of times to run a model')
    parser.add_argument('-num_epochs', type=int, default=3000,
                        help='Number of epochs to train model.')
    parser.add_argument('-batch_size', type=int, default=200,
                        help='Number of examples per batch.')
    parser.add_argument('-batch_splits', type=int, default=1,
                        help='Used to aggregate batches')
    parser.add_argument('-lr', type=float, default=5e-4,
                        help='The default learning rate for the optimizer.')
    parser.add_argument('-dropout', type=float, default=0.2,
                        help='The dropout probability for the model.')
    parser.add_argument('-max_grad_norm', type=float, default=10.0,
                        help='The maximum gradient norm allowed')
    parser.add_argument('-hidden_size', type=int, default=160,
                        help='The number of hidden units for the model.')
    parser.add_argument('-depth', type=int, default=5,
                        help='The depth of the net.')

    # Transformer arguments
    parser.add_argument('-n_heads', type=int, default=2,
                        help='Number of heads in multihead attention.')
    parser.add_argument('-d_k', type=int, default=80,
                        help='The size of each indvidiual attention head')

    # Path Arguments
    parser.add_argument('-max_path_length', type=int, default=3,
                        help='The max path length to consider')
    parser.add_argument('-p_embed', action='store_true', default=True,
                        help='use distance position or not')
    parser.add_argument('-ring_embed', action='store_true', default=True,
                        help='use ring in path info')
    parser.add_argument('-no_truncate', action='store_true', default=False,
                        help='Whether or not to truncate atom paths.')

    # Graph Attn Network Arguments
    parser.add_argument('-no_share', action='store_true', default=True,
                        help='Whether to deactivate weight sharing for gcn')
    parser.add_argument('-mask_neigh', action='store_true', default=False,
                        help='Whether or not to mask outside neighborhood')
    
    #Webserver Single Arguments
    parser.add_argument('-input_smiles', type=str, default='',
                        help='The inputted smiles string used for testing')
    parser.add_argument('-input_stat', type=str, default='',
                        help='The inputted stationary phase used for testing')
    parser.add_argument('-input_dtype', type=str, default='',
                        help='The inputted derivatized type used for testing')           
    parser.add_argument('-input_name', type=str, default='',
                        help='The inputted name used for testing')  

    args = parser.parse_args()
    args.device = torch.device('cuda:0' if args.cuda else 'cpu')
    print('Using device %s' % (args.device))

    if not args.test_mode:
        create_dirs(args, args.output_dir)
        write_args(args)

    args.use_paths = False
    return args


def init_model(args, n_classes):
    prop_predictor = PropPredictor(args, n_classes=n_classes)
    prop_predictor.to(args.device)

    optimizer = torch.optim.Adam(prop_predictor.parameters(), lr=args.lr)
    return prop_predictor, optimizer

def load_datasets(raw_data, split_idx, args):
    path=os.getcwd()
    data_splits = data_utils.read_splits(path+"/public/python/"+'%s/split_%d.txt' % (args.data, split_idx))
    dataset_loaders = {}
    if not args.test_mode:
        #change by Afia
        dataset_loaders['train'] = get_loader(
            raw_data, data_splits['train'], args, shuffle=False)
        #dataset_loaders['valid'] = get_loader(
        #    raw_data, data_splits['valid'], args, shuffle=False)
    #dataset_loaders['test'] = get_loader(
    #    raw_data, data_splits['test'], args, shuffle=False)
    return dataset_loaders


def run_epoch(name_list, stat_list, derivative_type_list,data_loader, model, optimizer, stat_names, args, mode, write_path=None):
    #,write_path=None)
    training = mode == 'train'
    prop_predictor = model
    prop_predictor.train() if training else prop_predictor.eval()

    if write_path is not None:
        write_file = open(write_path, 'w+')
    stats_tracker = data_utils.stats_tracker(stat_names)

    batch_split_idx = 0
    all_pred_logits, all_labels = [], []  # Used to compute Acc, AUC
    for batch_idx, batch_data in enumerate(tqdm.tqdm(data_loader, dynamic_ncols=True)):
        if training and batch_split_idx % args.batch_splits == 0:
            optimizer.zero_grad()
        batch_split_idx += 1

        smiles_list, labels_list, path_tuple = batch_data
        path_input, path_mask = path_tuple
        if args.use_paths:
            path_input = path_input.to(args.device)
            path_mask = path_mask.to(args.device)

        n_data = len(smiles_list)
        mol_graph = MolGraph(smiles_list, args, path_input, path_mask)

        pred_logits = prop_predictor(mol_graph, stats_tracker).squeeze(1)
        labels = torch.tensor(labels_list, device=args.device)

        if args.loss_type == 'ce':  # memory issues
            all_pred_logits.append(pred_logits)
            all_labels.append(labels)

        if args.loss_type == 'mse' or args.loss_type == 'mae':
            loss = nn.MSELoss()(input=pred_logits, target=labels)
        elif args.loss_type == 'ce':
            pred_probs = nn.Sigmoid()(pred_logits)
            loss = nn.BCELoss()(pred_probs, labels)
        else:
            assert(False)
        stats_tracker.add_stat('loss', loss.item() * n_data, n_data)
        loss = loss / args.batch_splits

        if args.loss_type == 'mae':
            mae = torch.mean(torch.abs(pred_logits - labels))
            stats_tracker.add_stat('mae', mae.item() * n_data, n_data)

        if training:
            loss.backward()

            if batch_split_idx % args.batch_splits == 0:
                train_utils.backprop_grads(
                    prop_predictor, optimizer, stats_tracker, args)
                batch_split_idx = 0

        if write_path is not None:
            #write_utils.write_props(write_file, smiles_list, labels_list,
            #                        pred_logits.cpu().numpy())
            #write_utils.write_props(write_file, smiles_list, labels_list, name_list, stat_list, 
            #                        pred_logits.cpu().numpy())
            write_utils.write_props(write_file, smiles_list, labels_list, name_list, stat_list, derivative_type_list,
                                    pred_logits.cpu().numpy())

    if training and batch_split_idx != 0:
        train_utils.backprop_grads(model, optimizer, stats_tracker, args)  # Any remaining

    if args.loss_type == 'ce':
        all_pred_logits = torch.cat(all_pred_logits, dim=0)
        all_labels = torch.cat(all_labels, dim=0)
        pred_probs = nn.Sigmoid()(all_pred_logits).detach().cpu().numpy()
        all_labels = all_labels.detach().cpu().numpy()
        acc = train_utils.compute_acc(pred_probs, all_labels)
        auc = train_utils.compute_auc(pred_probs, all_labels)
        stats_tracker.add_stat('acc', acc, 1)
        stats_tracker.add_stat('auc', auc, 1)

    if write_path is not None:
        write_file.close()
    return stats_tracker.get_stats()
    
def train_model(dataset_loaders, model, optimizer, stat_names, selection_stat,
                train_func, name_list, stat_list, der_type, args):
                #train_func, args):

    path=os.getcwd()
    best_model_path = path+"/public/python/"+'%s/model_best' % (args.model_dir)
    print("prnting best model path:")
    print(best_model_path)

    train_pred_output = open(path+"/public/python/"+'%s/train_pred_stats.csv' % args.output_dir, 'w+', buffering=1)

    for file in [train_pred_output]:
        file.write(','.join(stat_names) + '\n')

    #best_model_path = ''
    if best_model_path != '':
        model.load_state_dict(torch.load(best_model_path,map_location=torch.device('cpu')))
        #change by Afia: print the model here and see what happens
        print('Loading model from %s' % best_model_path)
        
        #sept 1, 2021: recent change by Afia, redundant line
        #torch.save(model.state_dict(), '%s/model_best' % args.model_dir)
        
        #change by Afia
        #load state dict -> usually it means that we are saving/loading the weights of the model, not the model itself
        
    
    with torch.no_grad():
        #train_pred_stats = train_func(data_loader=dataset_loaders['train'], model=model, optimizer=None,stat_names=stat_names,mode='test',args=args, write_path=path+"/public/python/"+'%s/train_results' % args.result_dir)
        train_pred_stats = train_func(name_list=name_list, stat_list=stat_list,derivative_type_list=der_type, data_loader=dataset_loaders['train'], model=model, optimizer=None,stat_names=stat_names,mode='test',args=args, write_path=path+"/public/python/"+'%s/train_results.csv' % args.result_dir)
        #if draw_func is not None:
        #    draw_func(data_loader=dataset_loaders['test'],
        #        model=model,
        #        output_dir='%s/vis_output' % args.output_dir,
        #        args=args,)
    print(data_utils.dict_to_pstr(train_pred_stats, header_str='Test:'))
    train_pred_output.write(data_utils.dict_to_dstr(train_pred_stats, stat_names) + '\n')
    train_pred_output.close()

    return train_pred_output, best_model_path



def main():

    path=os.getcwd()
    args = get_args()

    m = Chem.MolFromSmiles(args.input_smiles, sanitize=False)
    if m:
        try:
            Chem.SanitizeMol(m)
        except:
            raise ValueError('bad input')
            sys.exit()
    else:
        raise ValueError('bad input')
        sys.exit()


    # try:
    #     mol = Chem.MolFromSmiles(args.input_smiles)
    # except:
    #     raise ValueError('bad input')
    #     sys.exit()


    if args.input_stat=="standard_non_polar":
        args.data='data/derstdnp'
        args.output_dir='output_test/derstdnp_transformer'
        args.num_epochs=1000
        args.batch_size=100
        args.batch_splits=2
    elif args.input_stat=="standard_polar":
        args.data='data/derstdpolar'
        args.output_dir='output_test/derstdpolar_transformer'
        args.num_epochs=3000
        args.batch_size=100
        args.batch_splits=2


    path2=path+"/public/python/"+'%s/raw.csv' % args.data    

    path3=path+"/public/python/"+'%s/split_0.txt' % args.data   

    finallist = []
    list3=[]
    list3.append(args.input_smiles)
    print(list3)

    #retrieve this list3 info for non-der parsing and prediction


    #end_time = time.time()
    #diff_time = end_time - start_time
    #print("  done in %f seconds" % diff_time)

    for i in range(0, len(list3)):
        if i % 5000 == 0:
            print(str(i) + "processings are done")

        #start_time = time.time()

        dict_array = []

        if args.input_dtype=="TMS":
            dict_array = make_tms(list3[i])
        if args.input_dtype=="TBDMS":
            dict_array = make_tbdms(list3[i])
        if args.input_dtype=="TMS_AND_TBDMS":
            dict_array1 = make_tms(list3[i])
            dict_array2 = make_tbdms(list3[i])
            dict_array = dict_array + dict_array1
            dict_array = dict_array + dict_array2


        # dict_array3=make_tbdps(list3[i])
        # dict_array4=make_tes(list3[i])
        # dict_array5=make_tips(list3[i])
        # dict_array7=make_oximation(list3[i])

        l = remove_dup_list(dict_array)

        l = check_issue(l)
        finallist.append(l)
        #end_time = time.time()
        #diff_time = end_time - start_time
        # print("  done in %f seconds" % diff_time)

    print("length of finallist:")
    print(len(finallist))
    print("length of finallist[0]:")
    print(len(finallist[0]))


    print(args.input_smiles)

    if len(finallist[0])==0:
        exit()

    string=""
    new_name_id=[]
    new_names_list=[]
    stat_phase_list=[]
    derivative_type_list=[]
    cnt=0
    if len(finallist[0])>0:
        with open(path2,'w') as f1:
            writer=csv.writer(f1)
            string2=1
            for i in range(len(finallist[0])):
                #if args.input_smiles:

                #raise error if the smiles in invalid, show it as the webserver message
                
                #try:
                #    mol = Chem.MolFromSmiles(input_smiles)
                #except:
                #    raise ValueError('Invalid SMILES string.')

                #overwrite the raw.csv file, put a comma and 0 with the smiles string, save and close it.
                writer.writerow([finallist[0][i], '0'])
                if '[Si](C)(C)C(C)(C)C' in finallist[0][i] or 'CC(C)(C)[Si](C)(C)' in finallist[0][i]:
                    string="TBDMS"
                elif '[Si](C)(C)C' in finallist[0][i] or 'C[Si](C)(C)' in finallist[0][i]:
                    string="TMS"
                count_Si=finallist[0][i].count("Si")

                if len(new_name_id)>0:
                    splitting=new_name_id[cnt-1].split("_")
                    if string==splitting[1]:
                        if str(count_Si)==splitting[2]:
                            string2=int(splitting[3])+1
                        else:
                            string2=1
                    else:
                        string2=1
                new_name_id.append(args.input_name+"_"+string+"_"+str(count_Si)+"_"+str(string2))
                new_names_list.append('"'+args.input_name+","+str(count_Si)+string+",isomer#"+str(string2)+'"')
                stat_phase_list.append(args.input_stat)
                derivative_type_list.append(string)
                cnt=cnt+1

        f1.close()

        string_in_split_txt="train,"
        for j in range(len(finallist[0])):
            if j < (len(finallist[0])-1):
                string_in_split_txt+=str(j)+","
            else:
                string_in_split_txt+=str(j)
        
        print(string_in_split_txt)

        f = open(path3, "w")
        f.write(string_in_split_txt)
        f.close()




    #exit()
    
    shortest_paths.parse_mol(args.data, args.max_path_length)
    #print(args)
    
    #model_types = ['conv_net', 'conv_net_attn', 'transformer']
    #assert args.model_type in model_types
    
    print("Program started!")
    
    if args.multi:
        raw_data = data_utils.read_smiles_multiclass('%s/raw.csv' % args.data)
        n_classes = len(raw_data[0][1])
    else:
        raw_data = data_utils.read_smiles_from_file('%s/raw.csv' % args.data)
        n_classes = 1

    prop_predictor, optimizer = init_model(args, n_classes)
    

    data_utils.load_shortest_paths(args)  # Shortest paths includes all splits

    agg_stats = ['loss', 'nei_score']
    if args.loss_type == 'ce':
        agg_stats += ['acc', 'auc']
    elif args.loss_type == 'mae':
        agg_stats += ['mae']

    stat_names = agg_stats + ['gnorm', 'gnorm_clip']

    selection_stat = 'loss'
    select_higher = False
    if args.loss_type == 'ce':
        selection_stat = 'auc'
        select_higher = True
    if args.loss_type == 'mae':
        selection_stat = 'mae'

    if args.test_mode:
        dataset_loaders = load_datasets(raw_data, 0, args)

        test_model(
            dataset_loaders=dataset_loaders,
            model=prop_predictor,
            stat_names=stat_names,
            train_func=run_epoch,
            args=args,)

        exit()

    all_stats = {}
    for name in agg_stats:
        all_stats[name] = []
    output_dir = args.output_dir
    all_model_paths = []
    for round_idx in range(args.n_rounds):
        dataset_loaders = load_datasets(raw_data, round_idx, args)
        prop_predictor, optimizer = init_model(args, n_classes)

        cur_output_dir = '%s/run_%d' % (output_dir, round_idx)
        args.output_dir = cur_output_dir
        create_dirs(args, cur_output_dir)

        test_stats, best_model_path = train_model(
            dataset_loaders=dataset_loaders,
            model=prop_predictor,
            optimizer=optimizer,
            #model=None,
            #optimizer=None,
            stat_names=stat_names,
            selection_stat=selection_stat,
            train_func=run_epoch,
            name_list=new_names_list,
            stat_list=stat_phase_list,
            der_type=derivative_type_list,
            args=args)
            #args=args)

    
if __name__ == '__main__':
    print("MAIN !")
    main()
