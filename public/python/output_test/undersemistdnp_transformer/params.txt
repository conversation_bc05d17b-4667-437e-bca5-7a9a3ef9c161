agg_func=sum
batch_size=100
batch_splits=2
cuda=False
d_k=80
data=data/undersemistdnp
data_split=0
depth=5
device=cpu
dropout=0.2
hidden_size=160
input_dtype=
input_name=
input_smiles=
input_stat=
loss_type=mae
lr=0.0005
mask_neigh=False
max_grad_norm=10.0
max_path_length=3
model_dir=output_test/undersemistdnp_transformer/models
model_type=transformer
multi=False
n_classes=1
n_heads=2
n_rounds=1
no_share=True
no_truncate=False
num_epochs=3000
output_dir=output_test/undersemistdnp_transformer
p_embed=True
pretrain_model=
result_dir=output_test/undersemistdnp_transformer/results
ring_embed=True
self_attn=False
test_mode=False
test_model=
