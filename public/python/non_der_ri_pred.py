import torch
import torch.nn as nn
import numpy as np
import tqdm

import os.path
import sys
import torch
import argparse
import utils.data_utils as utils
from models.prop_predictor import PropPredictor
from datasets.mol_dataset import get_loader
from graph.mol_graph import MolGraph
from utils import data_utils, train_utils, write_utils
from preprocess import shortest_paths
import pdb

import pickle
import rdkit.Chem as Chem

import csv
import os.path
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))


def create_dirs(args, output_dir):
    utils.create_dir_if_not_exists(output_dir)
    args.model_dir = '%s/models' % output_dir
    args.result_dir = '%s/results' % output_dir

    for dir in [args.model_dir, args.result_dir]:
        utils.create_dir_if_not_exists(dir)

def write_args(args):
    path=os.getcwd()
    
    params_file = open(path+"/public/python/"+'%s/params.txt' % args.output_dir, 'w+')
    for attr, value in sorted(args.__dict__.items()):
        params_file.write("%s=%s\n" % (attr, value))
    params_file.close()

def get_args():
    parser = argparse.ArgumentParser()

    parser.add_argument('-cuda', action='store_true', default=False,
                        help='Whether or not to use GPU.')
    parser.add_argument('-data', type=str, default='data/undersemistdnp',
                        help='Input data directory.')
    parser.add_argument('-output_dir', type=str, default='output_test/undersemistdnp_transformer',
                        help='The output directory.')

    parser.add_argument('-test_mode', action='store_true', default=False,
                        help='Activates test mode for debugging purposes.')
    parser.add_argument('-test_model', type=str, default='',
                        help='The model path used for testing')
    parser.add_argument('-data_split', type=int, default=0,
                        help='Test split to run model on')
    parser.add_argument('-pretrain_model', type=str, default='',
                        help='The model path used for pretrained model')
    parser.add_argument('-multi', action='store_true', default=False,
                        help='Indicator for being multiclass training.')
    parser.add_argument('-n_classes', type=int, default=1,
                        help='Helper variable for number of classes')

    parser.add_argument('-model_type', type=str, default='transformer',
                        help='The graph model type to use')
    parser.add_argument('-loss_type', type=str, default='mae',
                        help='The loss type for the dataset')
    parser.add_argument('-agg_func', type=str, default='sum',
                        help='Agg function, sum or mean')
    parser.add_argument('-self_attn', action='store_true', default=False,
                        help='Whether or not to include self in attn')

    parser.add_argument('-n_rounds', type=int, default=1,
                        help='Number of times to run a model')
    parser.add_argument('-num_epochs', type=int, default=3000,
                        help='Number of epochs to train model.')
    parser.add_argument('-batch_size', type=int, default=100,
                        help='Number of examples per batch.')
    parser.add_argument('-batch_splits', type=int, default=2,
                        help='Used to aggregate batches')
    parser.add_argument('-lr', type=float, default=5e-4,
                        help='The default learning rate for the optimizer.')
    parser.add_argument('-dropout', type=float, default=0.2,
                        help='The dropout probability for the model.')
    parser.add_argument('-max_grad_norm', type=float, default=10.0,
                        help='The maximum gradient norm allowed')
    parser.add_argument('-hidden_size', type=int, default=160,
                        help='The number of hidden units for the model.')
    parser.add_argument('-depth', type=int, default=5,
                        help='The depth of the net.')

    # Transformer arguments
    parser.add_argument('-n_heads', type=int, default=2,
                        help='Number of heads in multihead attention.')
    parser.add_argument('-d_k', type=int, default=80,
                        help='The size of each indvidiual attention head')

    # Path Arguments
    parser.add_argument('-max_path_length', type=int, default=3,
                        help='The max path length to consider')
    parser.add_argument('-p_embed', action='store_true', default=True,
                        help='use distance position or not')
    parser.add_argument('-ring_embed', action='store_true', default=True,
                        help='use ring in path info')
    parser.add_argument('-no_truncate', action='store_true', default=False,
                        help='Whether or not to truncate atom paths.')

    # Graph Attn Network Arguments
    parser.add_argument('-no_share', action='store_true', default=True,
                        help='Whether to deactivate weight sharing for gcn')
    parser.add_argument('-mask_neigh', action='store_true', default=False,
                        help='Whether or not to mask outside neighborhood')
    
    #Webserver Single Arguments
    parser.add_argument('-input_smiles', type=str, default='',
                        help='The inputted smiles string used for testing')
    parser.add_argument('-input_stat', type=str, default='',
                        help='The inputted stationary phase used for testing')
    parser.add_argument('-input_dtype', type=str, default='',
                        help='The inputted derivatized type used for testing')           
    parser.add_argument('-input_name', type=str, default='',
                        help='The inputted name used for testing')    


    args = parser.parse_args()
    args.device = torch.device('cuda:0' if args.cuda else 'cpu')
    print('Using device %s' % (args.device))

    if not args.test_mode:
        create_dirs(args, args.output_dir)
        write_args(args)

    args.use_paths = False
    return args


def init_model(args, n_classes):
    prop_predictor = PropPredictor(args, n_classes=n_classes)
    prop_predictor.to(args.device)

    optimizer = torch.optim.Adam(prop_predictor.parameters(), lr=args.lr)
    return prop_predictor, optimizer

def load_datasets(raw_data, split_idx, args):
    path=os.getcwd()
    data_splits = data_utils.read_splits(path+"/public/python/"+'%s/split_%d.txt' % (args.data, split_idx))
    dataset_loaders = {}
    if not args.test_mode:
        #change by Afia
        dataset_loaders['train'] = get_loader(
            raw_data, data_splits['train'], args, shuffle=False)
        #dataset_loaders['valid'] = get_loader(
        #    raw_data, data_splits['valid'], args, shuffle=False)
    #dataset_loaders['test'] = get_loader(
    #    raw_data, data_splits['test'], args, shuffle=False)
    return dataset_loaders
    
def run_epoch(name_list, stat_list, derivative_type_list, data_loader, model, optimizer, stat_names, args, mode, write_path=None):
    #write_path=None, name_list, stat_list):
    training = mode == 'train'
    prop_predictor = model
    prop_predictor.train() if training else prop_predictor.eval()

    if write_path is not None:
        write_file = open(write_path, 'w+')
    stats_tracker = data_utils.stats_tracker(stat_names)

    batch_split_idx = 0
    all_pred_logits, all_labels = [], []  # Used to compute Acc, AUC
    for batch_idx, batch_data in enumerate(tqdm.tqdm(data_loader, dynamic_ncols=True)):
        if training and batch_split_idx % args.batch_splits == 0:
            optimizer.zero_grad()
        batch_split_idx += 1

        smiles_list, labels_list, path_tuple = batch_data
        path_input, path_mask = path_tuple
        if args.use_paths:
            path_input = path_input.to(args.device)
            path_mask = path_mask.to(args.device)

        n_data = len(smiles_list)
        mol_graph = MolGraph(smiles_list, args, path_input, path_mask)

        pred_logits = prop_predictor(mol_graph, stats_tracker).squeeze(1)
        labels = torch.tensor(labels_list, device=args.device)

        if args.loss_type == 'ce':  # memory issues
            all_pred_logits.append(pred_logits)
            all_labels.append(labels)

        if args.loss_type == 'mse' or args.loss_type == 'mae':
            loss = nn.MSELoss()(input=pred_logits, target=labels)
        elif args.loss_type == 'ce':
            pred_probs = nn.Sigmoid()(pred_logits)
            loss = nn.BCELoss()(pred_probs, labels)
        else:
            assert(False)
        stats_tracker.add_stat('loss', loss.item() * n_data, n_data)
        loss = loss / args.batch_splits

        if args.loss_type == 'mae':
            mae = torch.mean(torch.abs(pred_logits - labels))
            stats_tracker.add_stat('mae', mae.item() * n_data, n_data)

        if training:
            loss.backward()

            if batch_split_idx % args.batch_splits == 0:
                train_utils.backprop_grads(
                    prop_predictor, optimizer, stats_tracker, args)
                batch_split_idx = 0

        if write_path is not None:
            #write_utils.write_props(write_file, smiles_list, labels_list,
            #                        pred_logits.cpu().numpy())
            write_utils.write_props(write_file, smiles_list, labels_list, name_list, stat_list,derivative_type_list,
                                    pred_logits.cpu().numpy())

    if training and batch_split_idx != 0:
        train_utils.backprop_grads(model, optimizer, stats_tracker, args)  # Any remaining

    if args.loss_type == 'ce':
        all_pred_logits = torch.cat(all_pred_logits, dim=0)
        all_labels = torch.cat(all_labels, dim=0)
        pred_probs = nn.Sigmoid()(all_pred_logits).detach().cpu().numpy()
        all_labels = all_labels.detach().cpu().numpy()
        acc = train_utils.compute_acc(pred_probs, all_labels)
        auc = train_utils.compute_auc(pred_probs, all_labels)
        stats_tracker.add_stat('acc', acc, 1)
        stats_tracker.add_stat('auc', auc, 1)

    if write_path is not None:
        write_file.close()
    return stats_tracker.get_stats()
    
def train_model(dataset_loaders, model, optimizer, stat_names, selection_stat, train_func, name_list, stat_list, derivative_type_list,args):
    #train_func, args):
    
    path=os.getcwd()
    best_model_path = path+"/public/python/"+'%s/model_best' % (args.model_dir)
    print(best_model_path)

    train_pred_output = open(path+"/public/python/"+'%s/train_pred_stats.csv' % args.output_dir, 'w+', buffering=1)

    for file in [train_pred_output]:
        file.write(','.join(stat_names) + '\n')

    #best_model_path = ''
    if best_model_path != '':
        model.load_state_dict(torch.load(best_model_path,map_location=torch.device('cpu')))
        #change by Afia: print the model here and see what happens
        print('Loading model from %s' % best_model_path)
        
        #sept 1, 2021: recent change by Afia, redundant line
        #torch.save(model.state_dict(), '%s/model_best' % args.model_dir)
        
        #change by Afia
        #load state dict -> usually it means that we are saving/loading the weights of the model, not the model itself
        
    
    with torch.no_grad():
        #train_pred_stats = train_func(data_loader=dataset_loaders['train'], model=model, optimizer=None,stat_names=stat_names,mode='test',args=args, write_path=path+"/public/python/"+'%s/train_results' % args.result_dir)
        train_pred_stats = train_func(name_list=name_list, stat_list=stat_list, derivative_type_list=derivative_type_list, data_loader=dataset_loaders['train'], model=model, optimizer=None,stat_names=stat_names,mode='test',args=args, write_path=path+"/public/python/"+'%s/train_results.csv' % args.result_dir)

        #if draw_func is not None:
        #    draw_func(data_loader=dataset_loaders['test'],
        #        model=model,
        #        output_dir='%s/vis_output' % args.output_dir,
        #        args=args,)
    print(data_utils.dict_to_pstr(train_pred_stats, header_str='Test:'))
    train_pred_output.write(data_utils.dict_to_dstr(train_pred_stats, stat_names) + '\n')
    train_pred_output.close()

    return train_pred_output, best_model_path


def predict_batch_smiles(smiles_list, stationary_phase="standard_non_polar", compound_names=None):
    """
    Predict retention index values for a list of SMILES strings.

    Args:
        smiles_list: List of SMILES strings
        stationary_phase: Either "standard_non_polar" or "standard_polar"
        compound_names: Optional list of compound names (same length as smiles_list)

    Returns:
        List of dictionaries containing SMILES, predicted RI, and compound name
    """
    path = os.getcwd()

    # Create mock args object for batch processing
    class MockArgs:
        def __init__(self):
            self.cuda = False
            self.device = torch.device('cpu')
            self.test_mode = False
            self.multi = False
            self.n_classes = 1
            self.model_type = 'transformer'
            self.loss_type = 'mae'
            self.agg_func = 'sum'
            self.self_attn = False
            self.n_rounds = 1
            self.num_epochs = 3000
            self.batch_size = 100
            self.batch_splits = 2
            self.lr = 5e-4
            self.dropout = 0.2
            self.max_grad_norm = 10.0
            self.hidden_size = 160
            self.depth = 5
            self.n_heads = 2
            self.d_k = 80
            self.max_path_length = 3
            self.p_embed = True
            self.ring_embed = True
            self.no_truncate = False
            self.no_share = True
            self.mask_neigh = False
            self.use_paths = False

    args = MockArgs()

    # Set stationary phase specific parameters
    if stationary_phase == "standard_non_polar":
        args.data = 'data/understdnp'
        args.output_dir = 'output_test/understdnp_transformer'
        args.num_epochs = 1000
        args.batch_size = 100
        args.batch_splits = 2
    elif stationary_phase == "standard_polar":
        args.data = 'data/understdpolar'
        args.output_dir = 'output_test/understdpolar_transformer'
        args.num_epochs = 3000
        args.batch_size = 100
        args.batch_splits = 1
    else:
        raise ValueError("stationary_phase must be 'standard_non_polar' or 'standard_polar'")

    # Validate all SMILES strings
    valid_smiles = []
    valid_names = []
    for i, smiles in enumerate(smiles_list):
        m = Chem.MolFromSmiles(smiles, sanitize=False)
        if m:
            try:
                Chem.SanitizeMol(m)
                valid_smiles.append(smiles)
                if compound_names and i < len(compound_names):
                    valid_names.append(compound_names[i])
                else:
                    valid_names.append(f"Compound_{i+1}")
            except:
                print(f"Warning: Invalid SMILES string skipped: {smiles}")
        else:
            print(f"Warning: Invalid SMILES string skipped: {smiles}")

    if not valid_smiles:
        raise ValueError("No valid SMILES strings provided")

    # Create CSV file with all valid SMILES
    path2 = path + "/public/python/" + '%s/raw.csv' % args.data
    with open(path2, 'w', newline='') as f:
        writer = csv.writer(f)
        for smiles in valid_smiles:
            writer.writerow([smiles, '0'])

    # Prepare lists for processing
    new_names_list = valid_names
    stat_list = [stationary_phase] * len(valid_smiles)
    derivative_type_list = ["No-Derivatization"] * len(valid_smiles)

    return process_predictions(args, new_names_list, stat_list, derivative_type_list, valid_smiles, stationary_phase)

def process_predictions(args, new_names_list, stat_list, derivative_type_list, smiles_list, stationary_phase):
    """Process the predictions and return results"""
    path = os.getcwd()

    # Continue with the existing processing logic
    shortest_paths.parse_mol(args.data, args.max_path_length)

    print("Program started!")

    if args.multi:
        raw_data = data_utils.read_smiles_multiclass('%s/raw.csv' % args.data)
        n_classes = len(raw_data[0][1])
    else:
        raw_data = data_utils.read_smiles_from_file('%s/raw.csv' % args.data)
        n_classes = 1

    prop_predictor, optimizer = init_model(args, n_classes)

    data_utils.load_shortest_paths(args)

    agg_stats = ['loss', 'nei_score']
    if args.loss_type == 'ce':
        agg_stats += ['acc', 'auc']
    elif args.loss_type == 'mae':
        agg_stats += ['mae']

    stat_names = agg_stats + ['gnorm', 'gnorm_clip']
    selection_stat = 'loss'

    if args.loss_type == 'ce':
        selection_stat = 'auc'
    if args.loss_type == 'mae':
        selection_stat = 'mae'

    # Process predictions
    dataset_loaders = load_datasets(raw_data, 0, args)

    # Create output directory if it doesn't exist
    create_dirs(args, args.output_dir)

    test_stats, best_model_path = train_model(
        dataset_loaders=dataset_loaders,
        model=prop_predictor,
        optimizer=optimizer,
        stat_names=stat_names,
        selection_stat=selection_stat,
        train_func=run_epoch,
        name_list=new_names_list,
        stat_list=stat_list,
        derivative_type_list=derivative_type_list,
        args=args)

    # Read results and return
    results = []
    results_file = path + "/public/python/" + f'{args.output_dir}/run_0/results/train_results.csv'

    try:
        with open(results_file, 'r') as f:
            reader = csv.DictReader(f)
            for i, row in enumerate(reader):
                if i < len(smiles_list):
                    results.append({
                        'smiles': smiles_list[i],
                        'compound_name': new_names_list[i] if i < len(new_names_list) else f"Compound_{i+1}",
                        'predicted_ri': float(row['Predicted_RI']) if 'Predicted_RI' in row else None,
                        'stationary_phase': stat_list[i] if i < len(stat_list) else stationary_phase
                    })
    except FileNotFoundError:
        print(f"Results file not found: {results_file}")
        return []

    return results

def main():

    path=os.getcwd()
    args = get_args()

    m = Chem.MolFromSmiles(args.input_smiles, sanitize=False)
    if m:
        try:
            Chem.SanitizeMol(m)
        except:
            raise ValueError('bad input')
            sys.exit()
    else:
        raise ValueError('bad input')
        sys.exit()

    if args.input_stat=="standard_non_polar":
        args.data='data/understdnp'
        args.output_dir='output_test/understdnp_transformer'
        args.num_epochs=1000
        args.batch_size=100
        args.batch_splits=2
    elif args.input_stat=="standard_polar":
        args.data='data/understdpolar'
        args.output_dir='output_test/understdpolar_transformer'
        args.num_epochs=3000
        args.batch_size=100
        args.batch_splits=1


    path2=path+"/public/python/"+'%s/raw.csv' % args.data
    print(args.input_smiles)
    if args.input_smiles:

        #raise error if the smiles in invalid, show it as the webserver message

        #try:
        #    mol = Chem.MolFromSmiles(input_smiles)
        #except:
        #    raise ValueError('Invalid SMILES string.')

        #overwrite the raw.csv file, put a comma and 0 with the smiles string, save and close it.
        f = open(path2, 'w')
        writer = csv.writer(f)
        writer.writerow([args.input_smiles, '0'])
        f.close()

    new_names_list=[]
    stat_list=[]
    derivative_type_list=[]

    new_names_list.append(args.input_name)
    stat_list.append(args.input_stat)
    derivative_type_list.append("No-Derivatization")


    #exit()
    
    shortest_paths.parse_mol(args.data, args.max_path_length)
    #print(args)
    
    #model_types = ['conv_net', 'conv_net_attn', 'transformer']
    #assert args.model_type in model_types
    
    print("Program started!")
    
    if args.multi:
        raw_data = data_utils.read_smiles_multiclass('%s/raw.csv' % args.data)
        n_classes = len(raw_data[0][1])
    else:
        raw_data = data_utils.read_smiles_from_file('%s/raw.csv' % args.data)
        n_classes = 1

    prop_predictor, optimizer = init_model(args, n_classes)
    

    data_utils.load_shortest_paths(args)  # Shortest paths includes all splits

    agg_stats = ['loss', 'nei_score']
    if args.loss_type == 'ce':
        agg_stats += ['acc', 'auc']
    elif args.loss_type == 'mae':
        agg_stats += ['mae']

    stat_names = agg_stats + ['gnorm', 'gnorm_clip']

    selection_stat = 'loss'
    select_higher = False
    if args.loss_type == 'ce':
        selection_stat = 'auc'
        select_higher = True
    if args.loss_type == 'mae':
        selection_stat = 'mae'

    if args.test_mode:
        dataset_loaders = load_datasets(raw_data, 0, args)

        test_model(
            dataset_loaders=dataset_loaders,
            model=prop_predictor,
            stat_names=stat_names,
            train_func=run_epoch,
            args=args,)

        exit()

    all_stats = {}
    for name in agg_stats:
        all_stats[name] = []
    output_dir = args.output_dir
    all_model_paths = []
    for round_idx in range(args.n_rounds):
        dataset_loaders = load_datasets(raw_data, round_idx, args)
        prop_predictor, optimizer = init_model(args, n_classes)

        cur_output_dir = '%s/run_%d' % (output_dir, round_idx)
        args.output_dir = cur_output_dir
        create_dirs(args, cur_output_dir)

        test_stats, best_model_path = train_model(
            dataset_loaders=dataset_loaders,
            model=prop_predictor,
            optimizer=optimizer,
            #model=None,
            #optimizer=None,
            stat_names=stat_names,
            selection_stat=selection_stat,
            train_func=run_epoch,
            name_list=new_names_list,
            stat_list=stat_list,
            derivative_type_list=derivative_type_list,
            args=args)
            #args=args)
    
if __name__ == '__main__':
    print("MAIN !")
    main()

#retrieve all values from the args string coming from model ruby files

#smiles to shortest path calculation.

#load the calculated shortest path

#load best model out of the three types of non derivatized model: as per parameter's demand

#predict using that model

#either return back the values or for now: print and see it here.