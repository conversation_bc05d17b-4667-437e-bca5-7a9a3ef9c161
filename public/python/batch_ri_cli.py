#!/usr/bin/env python3
"""
Command-line interface for batch retention index prediction.
"""

import argparse
import sys
import os
import json

# Add the parent directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from ri_predictor import RetentionIndexPredictor
except ImportError as e:
    print(f"Error importing predictor: {e}")
    sys.exit(1)

def main():
    parser = argparse.ArgumentParser(
        description="Predict retention indices for multiple SMILES strings",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Predict from command line SMILES
  python batch_ri_cli.py --smiles "CCO" "CC(C)O" "c1ccccc1"
  
  # Predict from CSV file
  python batch_ri_cli.py --csv input.csv --smiles-column "smiles" --output results.csv
  
  # Use polar stationary phase
  python batch_ri_cli.py --smiles "CCO" --phase standard_polar
        """
    )
    
    # Input options
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument(
        '--smiles', 
        nargs='+', 
        help='SMILES strings to predict (space-separated)'
    )
    input_group.add_argument(
        '--csv', 
        help='CSV file containing SMILES strings'
    )
    
    # CSV-specific options
    parser.add_argument(
        '--smiles-column', 
        default='smiles',
        help='Name of column containing SMILES strings (default: smiles)'
    )
    parser.add_argument(
        '--name-column',
        help='Name of column containing compound names (optional)'
    )
    
    # Prediction options
    parser.add_argument(
        '--phase', 
        choices=['standard_non_polar', 'standard_polar'],
        default='standard_non_polar',
        help='Stationary phase for prediction (default: standard_non_polar)'
    )
    parser.add_argument(
        '--names',
        nargs='+',
        help='Compound names (same order as SMILES, optional)'
    )
    
    # Output options
    parser.add_argument(
        '--output', '-o',
        help='Output CSV file (default: print to stdout)'
    )
    parser.add_argument(
        '--format',
        choices=['csv', 'json', 'table'],
        default='table',
        help='Output format (default: table)'
    )
    
    args = parser.parse_args()
    
    # Create predictor
    predictor = RetentionIndexPredictor()
    
    try:
        if args.csv:
            # Predict from CSV file
            df = predictor.predict_from_csv(
                csv_file=args.csv,
                smiles_column=args.smiles_column,
                name_column=args.name_column,
                stationary_phase=args.phase
            )
        else:
            # Predict from command line SMILES
            df = predictor.predict_to_dataframe(
                smiles_list=args.smiles,
                stationary_phase=args.phase,
                compound_names=args.names
            )
        
        # Output results
        if args.output:
            if args.format == 'csv' or args.output.endswith('.csv'):
                df.to_csv(args.output, index=False)
                print(f"Results saved to: {args.output}")
            elif args.format == 'json' or args.output.endswith('.json'):
                df.to_json(args.output, orient='records', indent=2)
                print(f"Results saved to: {args.output}")
            else:
                # Default to CSV for file output
                df.to_csv(args.output, index=False)
                print(f"Results saved to: {args.output}")
        else:
            # Print to stdout
            if args.format == 'csv':
                print(df.to_csv(index=False))
            elif args.format == 'json':
                print(df.to_json(orient='records', indent=2))
            else:  # table format
                print(df.to_string(index=False))
                
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
