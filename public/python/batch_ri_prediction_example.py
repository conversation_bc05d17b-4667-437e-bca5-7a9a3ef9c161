#!/usr/bin/env python3
"""
Example script showing how to use the retention index prediction model
with a list of SMILES strings for batch processing.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from non_der_ri_pred import predict_batch_smiles

def main():
    # Example list of SMILES strings
    smiles_list = [
        "CCO",  # Ethanol
        "CC(C)O",  # Isopropanol
        "CCCCCCCCCCCCCCCCCC(=O)O",  # Stearic acid
        "CC(C)(C)O",  # tert-Butanol
        "c1ccccc1",  # Benzene
    ]
    
    # Optional compound names (same length as smiles_list)
    compound_names = [
        "Ethanol",
        "Isopropanol", 
        "Stearic acid",
        "tert-Butanol",
        "Benzene"
    ]
    
    print("Starting batch retention index prediction...")
    print(f"Processing {len(smiles_list)} compounds")
    
    try:
        # Predict for standard non-polar stationary phase
        results_nonpolar = predict_batch_smiles(
            smiles_list=smiles_list,
            stationary_phase="standard_non_polar",
            compound_names=compound_names
        )
        
        print("\n=== Results for Standard Non-Polar Phase ===")
        for result in results_nonpolar:
            print(f"Compound: {result['compound_name']}")
            print(f"SMILES: {result['smiles']}")
            print(f"Predicted RI: {result['predicted_ri']:.2f}")
            print(f"Stationary Phase: {result['stationary_phase']}")
            print("-" * 50)
        
        # Predict for standard polar stationary phase
        results_polar = predict_batch_smiles(
            smiles_list=smiles_list,
            stationary_phase="standard_polar",
            compound_names=compound_names
        )
        
        print("\n=== Results for Standard Polar Phase ===")
        for result in results_polar:
            print(f"Compound: {result['compound_name']}")
            print(f"SMILES: {result['smiles']}")
            print(f"Predicted RI: {result['predicted_ri']:.2f}")
            print(f"Stationary Phase: {result['stationary_phase']}")
            print("-" * 50)
            
    except Exception as e:
        print(f"Error during prediction: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
