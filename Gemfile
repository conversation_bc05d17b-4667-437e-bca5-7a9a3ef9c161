source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby '2.5.1'

#mimemagic
gem 'mimemagic', github: 'mimemagicrb/mimemagic', ref: '01f92d86d15d85cfd0f20dabd025dcbd36a8a60f'
gem 'rails', '~> 4.2.0'
# Use mysql as the database for Active Record
gem 'mysql2', '~> 0.4.9'
# Use Puma as the app server
gem 'puma', '~> 4.3.1'
# Use SCSS for stylesheets
gem 'sass-rails', '~> 5.0'
# Use Uglifier as compressor for JavaScript assets
gem 'uglifier', '>= 1.3.0'
gem 'authlogic' #user,usersession,password handler
# Use CoffeeScript for .coffee assets and views
gem 'coffee-rails', '~> 4.2'
# Turbolinks makes navigating your web application faster. Read more: https://github.com/turbolinks/turbolinks
gem 'turbolinks', '~> 5'
# Build JSON APIs with ease. Read more: https://github.com/rails/jbuilder
gem 'jbuilder', '~> 2.5'
# Handle nested forms
gem 'cocoon'
# Handle uploading files with :remote => true
gem 'remotipart'
gem 'rubyzip'

gem 'jquery-rails'
gem 'jquery-turbolinks'
gem 'bootstrap-sass'
gem 'bootstrap-kaminari-views'

gem 'paperclip'
# gem 'nokogiri', '~> 1.6.1'

gem 'whenever', require: false
gem 'responders', '~> 2.0'
gem 'sprockets-rails'
gem 'binding_of_caller'
gem 'bio'
gem 'readmorejs-rails'
gem 'inherited_resources'
gem 'oink'
gem 'her', '1.1.0'
# Use this for better support for legacy database schemas for ActiveRecord,
# such as columns named class, or any other name that conflicts with an 
# instance method of ActiveRecord.
gem 'safe_attributes'
gem 'redis-rails'
gem 'redis-session-store'

# Use Redis adapter to run Action Cable in production
# gem 'redis', '~> 4.0'

# Use ActiveModel has_secure_password
# gem 'bcrypt', '~> 3.1.7'

# Use ActiveStorage variant
# gem 'mini_magick', '~> 4.8'

# Reduces boot times through caching; required in config/boot.rb
gem 'bootsnap', '>= 1.1.0', require: false

# Remove unnecessary whitespaces in user input
gem "auto_strip_attributes", "~> 2.6"

# Wishartlab Gems
gem 'admin_mailer', git: '*****************:wishartlab/admin-mailer'
# gem 'admin_mailer', path: '/mnt/d/ALBERTA/Rails_tutorial/admin-mailer'
gem 'tmic_banner', git: '*****************:wishartlab/tmic_banner'
gem 'wishart', git: '*****************:wishartlab/wishart'
# gem 'wishart', path: '/Volumes/Samsung_T5/wishart'
#gem 'wishart', path: '~/WorkSpace/wishart'
gem 'moldbi', git: '*****************:wishartlab/moldbi'
#gem 'moldbi', path: '~/WorkSpace/moldbi'
gem 'specdb', git: '*****************:wishartlab/specdbi', branch: 'np-mrd'
#gem 'specdb', path: '~/Workspace/specdbi'
# gem 'specdb', path: '/Volumes/Samsung_T5/specdbi_nmr/specdbi'
gem 'unearth', git: '*****************:wishartlab/unearth', branch: 'v6-ES6'
#gem 'unearth', path: '~/Workspace/unearth'
gem 'seq_search', git: '*****************:wishartlab/seqsearch'
gem 'cite_this', git: '*****************:wishartlab/cite_this'
gem 'elasticsearch', "< 6.9"
group :development, :test do
  # Call 'byebug' anywhere in the code to stop execution and get a debugger console
  gem 'byebug', platforms: [:mri, :mingw, :x64_mingw]
end
gem 'sidekiq'
gem 'sidekiq-status'

group :development do
  # Access an interactive console on exception pages or by calling 'console' anywhere in the code.
  gem 'web-console', '>= 3.3.0'
  gem 'listen', '>= 3.0.5', '< 3.2'
  # Spring speeds up development by keeping your application running in the background. Read more: https://github.com/rails/spring
  gem 'spring'
  gem 'spring-watcher-listen', '~> 2.0.0'
  gem 'quiet_assets'
  gem 'thin'
  gem 'better_errors'
  # gem 'rb-readline'

  # Use Capsitrano for deployment
  gem "capistrano"
  gem 'capistrano-rails'
  gem 'capistrano-bundler'
  # gem 'capistrano-chruby'
  gem 'capistrano-rbenv'
  gem 'capistrano3-puma'
  gem 'syncfast', git: '*****************:wishartlab/syncfast'
end

group :test do
  # Adds support for Capybara system testing and selenium driver
  gem 'capybara', '>= 2.15'
  gem 'selenium-webdriver'
  # Easy installation and use of chromedriver to run system tests with Chrome
  gem 'chromedriver-helper'
end

group :production do
  gem 'newrelic_rpm'
  gem 'appsignal'
  gem 'execjs'
  #gem 'libv8', '~> 7.3.492.27.1'
  gem 'mini_racer'#, '0.2.14'
  #gem 'therubyracer', require: 'v8'
  gem 'autoprefixer-rails', "8.6.5"
  gem 'puma_worker_killer'
  gem 'rack-mini-profiler', require: false
  gem 'flamegraph'
  gem 'stackprof'
  gem 'memory_profiler'
end

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem 'tzinfo-data', platforms: [:mingw, :mswin, :x64_mingw, :jruby]
