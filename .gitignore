# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
!/log/.keep
!/tmp/.keep

# Ignore uploaded files in development
/storage/*
!/storage/.keep

/node_modules
/yarn-error.log

/public/downloads
/public/system

.byebug_history

# Ignore master key for decrypting credentials and more.
/config/master.key

/config/database.yml
/config/moldb.yml
/config/specdb.yml
# /config/secrets.yml
/config/unearth.yml
#/config/python_paths.yml

.DS_Store
**/.DS_Store

/dump.rdb

# Byte-compiled / optimized / DLL files
__pycache__/
