import numpy as np
import itertools
from . import pascal
#import pascal

"""
spin system object for calculating nmr spectra
contains atom objects, with unique names and a chemical shift value
and j couplings objects, with a j coupling value associated with two atom names,

also functions for splitting up spin system into smaller spin systems
"""

class atom():

    def __init__(self,id,ppm,mult=1,spin=0.5):
        self.id = id #unique name
        self.ppm = float(ppm) #chemial shift, ppm
        self.mult = int(mult) #multiplicity (eg methyls)
        self.spin = float(spin) #spin quantum number

        #per atom spectra
        self.intens = []
        self.trans = []
        #cosy spectra
        self.cosy_cross_intens = {}
        self.cosy_cross_trans = {}
        self.cosy_diag_intens = {}
        self.cosy_diag_trans = {}
        #tocsy
        self.tocsy_intens = []
        self.tocsy_trans = []

    def groupspin(self):
        #for composite particle method
        return np.arange(self.spin*self.mult, -0.5, -1)
        
    def spinwt(self):
        #for composite particle method
        tmp = pascal.pascal(self.mult, int(2*self.spin+1))
        tmp = tmp[:(len(tmp)-1)//2+1]
        weight = np.ediff1d(tmp, to_begin=1)
        return weight
        
    def hz(self,sfrq,center=0):
        #ppm to hz
        return (self.ppm - center)* -sfrq
        
    def rad(self,sfrq,center=0):
        #ppm to rad
        return (self.ppm - center)* -sfrq * 2 * np.pi

    def reset_intensity(self):
        self.intens = []
        self.trans = []
        self.cosy_cross_intens = {}
        self.cosy_cross_trans = {}
        self.cosy_diag_intens = {}
        self.cosy_diag_trans = {}
        self.tocsy_intens = []
        self.tocsy_trans = []
    
    
class jcoup():

    def __init__(self,id1,id2,j):
        self.atmids = [id1,id2]
        self.val = float(j)

class spin_system():

    def __init__(self):
        self.atoms = []
        self.jcoups = []
        self.scale = 1.0 #relative concentration
        
        #array representation of jcoups
        #jmatrix[i,j] is coupling between atoms[i] and atoms[j]
        self.jmatrix = None
        
        #emtpy or indices of items in self.atoms
        #only cacluate spectra for atoms in self.detect
        #xapprox: not observed, but contribute to weak coupling in system
        #chemical shifts should be set far apart??
        #all atoms used for xapprox must be at the END of the atoms list
        self.detect = []
        self.xapprox = []

        self._tmpidx = 1000
        
    def add_atom(self,id,*args,overwrite=True):

        #if id == 'ND':
        #    id = self._tmpidx
        #    self._tmpidx += 1

        if self.get_atom(id):
            if overwrite:
                print("WARNING: overwriting atom",*args)
                pass
            else:
                print("WARNING: duplicate atom, skipping",*args) #don't add duplicate entries
                return
        self.atoms.append(atom(id,*args))
        print("Added atom",id,*args)

    def add_coupling(self,*args,overwrite=True):

        #if args[0] == 'ND':
        #    args[0] = self._tmpidx
        #    self._tmpidx += 1
        #if args[1] == 'ND':
        #    args[1]  = self._tmpidx
        #    self._tmpidx += 1

        tmp = jcoup(*args)
        if self.get_jcoups(*tmp.atmids):
            if overwrite:
                print("WARNING: overwriting coupling",*args)
            else:
                print("WARNING: duplicate coupling, skipping",*args) #don't add duplicate entries
                return
        for id in tmp.atmids:
            if not self.get_atom(id):
                #add anyways, probably has no effect unless appropriate atom added
                print("WARNING: atom in jcoupling does not exist",*args)
        self.jcoups.append(tmp)
        print("Added coupling",*args)
        
    def set_scale(self,val):
        self.scale = val

    def setup(self,shifttable,jtable,detect=[],xapprox=[],addunknownj=False):
        #generally use this to setup spinsystem
        #shifttable and jtable is a list of lists
        #detect and xapprox are indces of shifttable
        #atoms for xapprox indces must be at end of shifttable
        for shift in shifttable:
            self.add_atom(*shift)
        for coup in jtable:
            self.add_coupling(*coup)
        self.detect = list(detect)
        self.xapprox = list(xapprox)

        if addunknownj:
            for a,b,j in jtable:
                if not self.get_atom(a):
                    print("ADDING unknown atom",a)
                    self.add_atom(a,-999)
                    self.xapprox.append(self.get_atomind(a))
                if not self.get_atom(b):
                    print("ADDING unknown atom",b)
                    self.add_atom(b,-999)
                    self.xapprox.append(self.get_atomind(b))

        self.gen_jmatrix()
        return self

    def get_atom(self,id):
        for a in self.atoms:
            if a.id == id:
                return a

    def get_atomind(self,id):
        for i,a in enumerate(self.atoms):
            if a.id == id:
                return i

    def get_jcoups(self,id1,id2=None):
        jlist = []
        for j in self.jcoups:
            if id2 is None:
                if id1 in j.atmids:
                    jlist.append(j)
            else:
                if id1 in j.atmids and id2 in j.atmids:
                    jlist.append(j)
        return jlist

    def get_ids(self):
        return [a.id for a in self.atoms]

    def get_ppms(self):
        return np.array([a.ppm for a in self.atoms])

    def gen_jmatrix(self):
        self.jmatrix = np.zeros((len(self.atoms),)*2)
        ids = self.get_ids()
        for j in self.jcoups:
            try:
                i1 = ids.index(j.atmids[0])
                i2 = ids.index(j.atmids[1])
            except ValueError:
                continue
            self.jmatrix[i1,i2] = j.val
            self.jmatrix[i2,i1] = j.val
        return self.jmatrix

    def get_ppmtable(self):
        table = []
        for a in self.atoms:
            table.append([a.id,a.ppm,a.mult,a.spin])
        return np.array(table,dtype=object)

    def get_jtable(self):
        table = []
        for j in self.jcoups:
            table.append([j.atmids[0],j.atmids[1],j.val])
        return np.array(table,dtype=object)

    def jmatrixtotable(self):
        table = []
        ids = self.get_ids()
        for i,j in np.ndenumerate(self.jmatrix):
            if j == 0: continue
            table.append([ids[i[0]],ids[i[1]],j])
        return np.array(table,dtype=object)

    def reset_intensity(self):
        for a in self.atoms:
            a.reset_intensity()

def get_neighbors(matrix,indlist,
                  includeself=False,exclude=None,sortbyval=False):
    #returns indices of columns wih nonzero entries given row indices in indlist
    #matrix is j coupling matrix or coupling/(difference in chemical shift)
    
    if len(indlist) == 0:
        return []
    
    rows = matrix[indlist]
    nz = np.nonzero(rows)
    
    neigh = np.array(nz[1].flat)
    vals = rows[nz].flat


    if includeself:
        neigh = np.concatenate((neigh,indlist))
        vals =  np.concatenate((vals,[np.inf for i in indlist]))
    else:
        selfind = np.array([i not in indlist for i in neigh])
        if selfind.size > 0:
            neigh = neigh[selfind]
            vals = vals[selfind]

    
    if exclude:
        mask = np.array([i not in exclude for i in neigh])
        if mask.size > 0:
            neigh = neigh[mask]
            vals = vals[mask]

    
    if sortbyval:
        #sorts by values in matrix
        sortind = np.argsort(vals)[::-1]
        neigh = neigh[sortind]
        vals = vals[sortind]
        _,ind = np.unique(neigh,return_index=True)
        return list(neigh[np.sort(ind)])
    
    return list(np.unique(neigh))

def get_groups(matrix):
    #list of list of indices of connected matrix row/columns
    visited = {}
    queue = []
    finaloutput = []
    for i in range(len(matrix)):
        output = []
        queue.append(i)
        while queue:
            current = queue.pop()
            if current not in visited:
                output.append(current)
                visited[current] = True
                neighbors = np.flatnonzero(matrix[current])
                if len(neighbors) > 0:
                    queue.extend( neighbors )
        if output:
            finaloutput.append( sorted(output) )
    return finaloutput

def extract_spinsystem(spinsys,atmids):
    #make new spin system from spinsys given list of atom.id in atmids
    #doenst keep detect and xapprox
    atomlist = [spinsys.get_atom(i) for i in atmids]
    jcouplist = []
    for i in range(len(atomlist)):
        for j in range(len(atomlist)):
            if i <= j: continue
            jcouplist.extend(spinsys.get_jcoups(atomlist[i].id,atomlist[j].id))
    newsys = spin_system()
    newsys.atoms = atomlist
    newsys.jcoups = jcouplist
    newsys.gen_jmatrix()
    return newsys

def split_spinsystem(spinsys,matrix):
    #split spinsys into non-connected spin systems using matrix
    spinsyslist = []

    #print(spinsys.get_ppmtable())
    #print(spinsys.get_jtable())
    #print(spinsys.xapprox)

    #ingore coupling between xapprox atoms
    matrix = np.copy(matrix)
    for i in spinsys.xapprox:
        for j in spinsys.xapprox:
            matrix[i,j] = 0
            matrix[j,i] = 0
            
    for s in get_groups(matrix):

        if len(s) == 1 and s[0] in spinsys.xapprox:
            continue

        if spinsys.detect:
            test = [i in spinsys.detect for i in s]
            if sum(test) == 0:
                continue
        
        atmids = [spinsys.atoms[i].id for i in s]
        newsys = extract_spinsystem(spinsys,atmids)
        
        newsys.detect = [newsys.atoms.index(spinsys.atoms[i])
                         for i in spinsys.detect
                         if spinsys.atoms[i] in newsys.atoms]
        newsys.xapprox = [newsys.atoms.index(spinsys.atoms[i])
                         for i in spinsys.xapprox
                         if spinsys.atoms[i] in newsys.atoms]
        newsys.scale = spinsys.scale
        
        spinsyslist.append( newsys )
        
    return spinsyslist


def split_ind_byatom_old(spinsys,level=1,maxcoup=6,maxxapp=4):
    #spin spinsystem into multple spin systems, one per atom
    #level 0: include directly coupled nieghbors
    #level 1,2..: include additional levels of indirectly coupled atoms
    #maxcoup: max atoms to include for direct calcuation
    #maxxapp: max of remaining atoms to include for xapproximation
    #xapproximation atoms should come last
    #this returns the atom indices in spinsys.atoms that form a new system
    
    #sortbyval sorts atoms by coupling/shift so strong coupling should get
    #priority for direct calcuation, weak couplings should go in xapprox or 
    #not used at all if too many neighbors
    
    jmatrix = np.abs(spinsys.jmatrix)
    ppm = spinsys.get_ppms()
    ppmdiff = ppm[:,None] - ppm[None,:]
    ppmdiff[ppmdiff == 0] = 1
    ppmdiff = np.abs(ppmdiff)
    couplim = jmatrix / ppmdiff

    spins = []
    detect = []
    xapprox = []

    for i in range(len(spinsys.atoms)):

        iself = [i]
        idirect = get_neighbors(couplim,
                                [i],
                                includeself=False,
                                sortbyval=True) #level 0
        iindir = []
        for l in range(level):
            if l == 0:
                iindir = get_neighbors(couplim,
                                       iself+idirect,
                                       includeself=False,
                                       sortbyval=True)
            #else:
            #    iindir = get_neighbors(couplim,iself+idirect+iindir,
            #                           includeself=True,
            #                           exclude=iself+idirect,
            #                           sortbyval=True)
            else:
                iindir.extend(get_neighbors(couplim,
                                            iself+idirect+iindir,
                                            includeself=False,
                                            sortbyval=True))

        if maxcoup is None:
            allspins = iself+idirect+iindir
        else:
            allspins = (iself+idirect+iindir)[:maxcoup + maxxapp]
        
        spins.append(allspins)
        detect.append(iself)

        if maxcoup is None:
            keep = np.inf
        else:
            keep = maxcoup - len(allspins)
            
        if keep < 0:
            xapprox.append(allspins[keep:])
        else:
            xapprox.append([])


    return spins,detect,xapprox #indicies of orig spinsys

def split_ind_byatom(spinsys,level=1,maxcoup=6,maxxapp=4):
    #spin spinsystem into multple spin systems, one per atom
    #level 0: include directly coupled nieghbors
    #level 1,2..: include additional levels of indirectly coupled atoms
    #maxcoup: max atoms to include for direct calcuation
    #maxxapp: max of remaining atoms to include for xapproximation
    #xapproximation atoms should come last
    #this returns the atom indices in spinsys.atoms that form a new system
    
    #sortbyval sorts atoms by coupling/shift so strong coupling should get
    #priority for direct calcuation, weak couplings should go in xapprox or 
    #not used at all if too many neighbors
    
    jmatrix = spinsys.jmatrix
    ppm = spinsys.get_ppms()
    ppmdiff = ppm[:,None] - ppm[None,:]
    ppmdiff[ppmdiff == 0] = 1
    couplim = np.abs(jmatrix / ppmdiff)

    spins = []
    detect = []
    xapprox = []

    s_xappr = spinsys.xapprox

    #xapprox should not be coupled to each other
    for i in s_xappr:
        for j in s_xappr:
            jmatrix[i,j] = 0
            jmatrix[j,i] = 0

    for i in range(len(spinsys.atoms)):

        if i in s_xappr: continue
        if spinsys.detect and i not in spinsys.detect: continue

        iself = [i]
        idirect = get_neighbors(couplim,
                                [i],
                                includeself=False,
                                sortbyval=True) #level 0
        iindir = []
        for l in range(level):
            if l == 0:
                iindir = get_neighbors(couplim,
                                       iself+idirect,
                                       includeself=False,
                                       sortbyval=True)
            #else:
            #    iindir = get_neighbors(couplim,iself+idirect+iindir,
            #                           includeself=True,
            #                           exclude=iself+idirect,
            #                           sortbyval=True)
            else:
                iindir.extend(get_neighbors(couplim,
                                            iself+idirect+iindir,
                                            includeself=False,
                                            sortbyval=True))

        if maxcoup is None:
            allspins = iself+idirect+iindir
        else:
            allspins = (iself+idirect+iindir)[:maxcoup + maxxapp]

        xapp = [idx for idx in allspins if idx in s_xappr]
        allspins_new = [idx for idx in allspins if idx not in s_xappr]
        
        spins.append(allspins_new + xapp)
        detect.append(iself)

        if maxcoup is None:
            keep = np.inf
        else:
            keep = maxcoup - len(allspins) - len(xapp)
            
        if keep < 0:
            xapprox.append(allspins[keep:])
        else:
            xapprox.append([])

        for x in xapp:
            if x not in xapprox[-1]:
                xapprox[-1].append(x)

    return spins,detect,xapprox #indicies of orig spinsys

def split_sys_byatom(spinsys,level=1,maxcoup=6,maxxapp=4):
    #returns list of new spin systems, by atom
    #see split_ind_byatom()
    
    syslist = []
    for spins, detect, xapprox in zip(*split_ind_byatom(spinsys,level=level,maxcoup=maxcoup,maxxapp=maxxapp)):
        #print(spins, detect, xapprox)
        ids = [spinsys.atoms[i].id for i in spins]
        newsys = extract_spinsystem(spinsys, ids)
        #new indices for detect and xpparox
        newsys.detect = [spins.index(i) for i in detect]
        newsys.xapprox = [spins.index(i) for i in xapprox]
        newsys.scale = spinsys.scale
        syslist.append(newsys)
    return syslist
    
    
    
def split_ind_byatom_simple_old(spinsys,maxcoup=6,**kwargs):
    #spin spinsystem into multple spin systems, one per atom
    #add successively more couplings until up to but not over limit
    
    jmatrix = np.abs(spinsys.jmatrix)

    spins = []
    detect = []
    xapprox = []

    
    for i in range(len(spinsys.atoms)):

        iself = [i]

        idirect = get_neighbors(jmatrix,
                                [i],
                                includeself=False,
                                sortbyval=False) #level 0

        iindir = []

        while True:
        
            tmp = get_neighbors(jmatrix,
                                iself+idirect+iindir,
                                exclude=iself+idirect,
                                includeself=True,
                                sortbyval=False)
                                
            if len(iself+idirect+tmp) <= maxcoup:
                iindir = tmp
            else:
                break

        allspins = iself+idirect+iindir
        spins.append(allspins)
        detect.append(iself)
        xapprox.append([])

    return spins,detect,xapprox #indicies of orig spinsys
    
def split_ind_byatom_simple(spinsys,maxcoup=6,level=1,**kwargs):
    #spin spinsystem into multple spin systems, one per atom
    #add successively more couplings until up to but not over limit
    
    jmatrix = np.abs(spinsys.jmatrix)
    maxcoup = min(maxcoup,len(spinsys.atoms))

    s_xappr = spinsys.xapprox

    #xapprox should not be coupled to each other
    for i in s_xappr:
        for j in s_xappr:
            jmatrix[i,j] = 0
            jmatrix[j,i] = 0

    spins = []
    detect = []
    xapprox = []
    
    for i in range(len(spinsys.atoms)):

        if i in s_xappr: continue
        if spinsys.detect and i not in spinsys.detect: continue
        
        iself = [i]

        idirect = get_neighbors(jmatrix,
                                [i],
                                includeself=False,
                                sortbyval=False) #level 0

        iindir = []

        for _ in range(level):
        
            tmp = get_neighbors(jmatrix,
                                iself+idirect+iindir,
                                exclude=iself+idirect,
                                includeself=True,
                                sortbyval=False)
                                
            if len(iself+idirect+tmp) <= maxcoup:
                iindir = tmp
            else:
                break

        allspins = iself+idirect+iindir
        
        xapp = [idx for idx in allspins if idx in s_xappr]
        allspins_new = [idx for idx in allspins if idx not in s_xappr]
        
        spins.append(allspins_new + xapp)
        detect.append(iself)
        xapprox.append(xapp)

    return spins,detect,xapprox #indicies of orig spinsys

def split_sys_byatom_simple(spinsys,maxcoup=6,**kwargs):
    #returns list of new spin systems, by atom
    #see split_ind_byatom_simple()
    
    syslist = []
    for spins, detect, xapprox in zip(*split_ind_byatom_simple(spinsys,maxcoup=maxcoup)):
        #print(spins, detect, xapprox)
        ids = [spinsys.atoms[i].id for i in spins]
        newsys = extract_spinsystem(spinsys, ids)
        #new indices for detect and xpparox
        newsys.detect = [spins.index(i) for i in detect]
        newsys.xapprox = [spins.index(i) for i in xapprox]
        newsys.scale = spinsys.scale
        syslist.append(newsys)
    return syslist
    
#split_sys_byatom = split_sys_byatom_simple

if __name__ == "__main__":

    shifts = [["A",1],
              ["B",2],
              ["X",5],
              ["Y",10]]

    coup = [["A","B",0],
            ["B","X",20],
            ["X","M",20],
            ]

    s = spin_system().setup(shifts,coup)
    s.xapprox = [2,3]

    #slist  = split_spinsystem(s,s.jmatrix)
    #for s in slist:
    #    print([a.id for a in s.atoms],s.detect,s.xapprox)

    print(s.gen_jmatrix())

    s.add_atom("M",99)
    print(s.gen_jmatrix())
