from . import spinsystem
from . import subsystem
import numpy as np
import matplotlib.pyplot as plt
from scipy.signal import argrelmin, argrelmax, savgol_filter
from scipy.optimize import minimize

"""
main fuctions for calcuating spectra from a spin system
can calcuate 1d spectra
and 2d dqfcosy spectra
and 2d tocsy (intensities not calculated for tocsy)

need to make spin system first
"""

#TODO?
#maybe output should be list of lists, or dict{id:list} ?
#allow for separate x,y resolution?
#smarter automatic 2d levels


def calc_ft_axis(sfrq,center,sw,npts):
    #number of points may be zero-filled value
    return np.fft.fftshift(np.fft.fftfreq(npts,1/sw/sfrq)) / -sfrq + center

def calc_fid_axis(sfrq,center,sw,npts):
    return np.arange(npts) * 1/sw/sfrq

def calc_contour_levels(low,numlvl,mult):
    poslvl = [low*mult**i for i in range(numlvl)  ]
    neglvl = [-low*mult**i for i in range(numlvl)  ]
    return neglvl[::-1] + poslvl

def fft_fid(fid,npts):
    #fid: 1d comlex array, imaginary kept
    #size: size with zerofill/truncation
    return np.fft.fftshift(np.fft.fft(fid,npts))

def fft_2dfid(fid,size_f2,size_f1):
    #fid: complex fids in f2; interleaved real, imag in f1
    #imaginary discarded
    #size: size with zerofill/truncation
    #assume processing done already

    #f2 processing here if not done yet...
    spec = np.fft.fftshift(np.fft.fft(fid,size_f2,axis=-1), axes=-1)
    spec = spec[0::2].real + 1j * spec[1::2].real
    spec = spec.T
    #f1 processing here if not done yet...
    spec = np.fft.fftshift(np.fft.fft(spec,size_f1,axis=-1), axes=-1)
    spec = spec.real
    spec = spec.T
    
    return spec


def calc_1d_spectrum(spinsys,sfrq,center,sw,npts,level=1,maxcoup=6,maxxapp=4,byatom=True,weakcoupling=False):
    #trys to calcuate spectrum in one pass if number of spins is small
    #otherwise splits spin system by atoms and  calcuations each atom separately
    #returns spectrum in ppm,intensity lists
    
    x = []
    y = []

    #try spliting into separate spin systems by jcoupling
    syslist = spinsystem.split_spinsystem(spinsys,spinsys.jmatrix)
    for s in syslist:

        if maxcoup is not None and len(s.atoms) <= maxcoup:
            #print("CALCULATE DIRECTLY")
            print("SPINSYS:",s.get_ids())
            print("SPINS:",s.get_ids(),'\n',
                  "DETECT:",[s.atoms[i].id for i in s.detect],
                  "XAPPROX:",[s.atoms[i].id for i in s.xapprox])
            print("SCALE:",s.scale)

            for sub in subsystem.subsystem.iterate_subsystems(s):
                trans, intens = calc_subspectrum(sub,sfrq,center,byatom=byatom,weakcoupling=weakcoupling)
                x.extend(trans)
                y.extend(intens)

        else:
            #print("CALCULATING BY ATOM")
            for s2 in spinsystem.split_sys_byatom(s,level=level,maxcoup=maxcoup,maxxapp=maxxapp):
                print("SPINS:",s2.get_ids(),'\n',
                      "DETECT:",[s2.atoms[i].id for i in s2.detect],
                      "XAPPROX:",[s2.atoms[i].id for i in s2.xapprox])
                print("SCALE:",s2.scale)

                for sub in subsystem.subsystem.iterate_subsystems(s2):
                    trans, intens = calc_subspectrum(sub,sfrq,center,byatom=byatom,weakcoupling=weakcoupling)
                    x.extend(trans)
                    y.extend(intens)

    return x,y
                    
def calc_subspectrum(subsys,sfrq,center,byatom=False,weakcoupling=False):
    #retruns spectrum calcuated from a single subsys
    #'factorization' by x approxmation is done here as well
    
    subsys.makemzlist()
    
    if subsys.xmzlist is None or len(subsys.xmzlist) == 0:
        ham = subsys.blockhamiltonian(sfrq,center,weakcoupling=weakcoupling)
        val, vec = subsys.diagonalizehamiltonian(ham)
        trans, intens = subsys.calctrans(val,vec,sfrq,center,byatom=byatom)
        
    else:
        trans = []
        intens = []
        for xlist in subsys.xmzlist:
            print(".",end="")
            ham = subsys.blockhamiltonian(sfrq,center,xlist=xlist,weakcoupling=weakcoupling)
            val, vec = subsys.diagonalizehamiltonian(ham)
            tmptrans, tmpintens = subsys.calctrans(val,vec,sfrq,center,byatom=byatom)

            trans.extend(tmptrans)
            intens.extend(tmpintens)
        print()
        
    return trans, intens

def procspec(freq,intens,sfrq,center,sw,npts,lw=1,c=0.5,zf=None,sinoff=None,absval=False,returnfid=False):
    #input and output in ppm, intensity
    #requires spectral parameters
    #processing: either or both exponential/line broadening and shifted sine
    #c: first point multiplication
    #zf: final number of points after zero filling / truncation
    
    #xaxis:
    #the first (leftmost, low freq) point is exactly at center+sw/2 ppm
    #the last (rightmost, high freq) point is at center-sw/2-sw/npt ppm 
    #center is at point npt/2 + 1
    
    if zf is None:
        zf = npts

    freq = np.array(freq)
    intens = np.array(intens)
        
    #makes histogram of values
    #mult = 16
    #npts_hist = npts*mult
    #off = sw/npts_hist/2
    npts_hist = int(max(zf*10,npts*10))
    #npts_hist = npts*1
    off = sw/npts_hist/2
    hrange = (center-sw/2+off,center+sw/2+off)
    
    hist,edges = np.histogram(freq,weights=intens,
                              bins=npts_hist, #warns and very slow if not int
                              range=hrange)
                              
    #stick spectrum low ppm to high ppm, high frq to low frq
    #reverse so low frq to high frq
    histxaxis = (edges[0:-1] + edges[1:]) / 2
    histxaxis = histxaxis[::-1]
    hist = hist[::-1]
    #return histxaxis[::-1], hist[::-1]
    



    #remove negative peaks
    #negative peaks occur because of strong coupling
    #they should normally subtract intensity from other existing peaks
    #but are missing couplings so are not split correctly
    #due to approximation
    if absval:
        hist[hist<0] = 0
        #hist = np.abs(hist)
    
    #turn into fid, truncate to actual npts
    fid = np.fft.ifft(np.fft.ifftshift(hist))[:npts]
    
    #return np.arange(0,npts/sw/sfrq,1/sw/sfrq), fid
    
    #processing
    if c is not None:
        fid[0] *= c
    if lw is not None:
        fid *= np.exp(-np.pi*lw/sw/sfrq*np.arange(npts))
    if sinoff is not None:
        fid *= np.sin(np.pi*sinoff + np.pi*(1.0-sinoff)*np.arange(npts)/(npts-1) )
    if zf is not None and not returnfid: 
        npts = zf
    
    if not returnfid:
        #final spectrum, filled to zf
        xaxis = np.fft.fftshift(np.fft.fftfreq(npts,1/sw/sfrq)) / -sfrq + center
        #print(xaxis)
        #print(xaxis[npts/2-1],xaxis[npts/2],xaxis[npts/2+1])
        spec = np.fft.fftshift(np.fft.fft(fid,npts)) 
    else:
        xaxis = np.arange(npts) * 1/sw/sfrq
        spec = fid

    return xaxis, spec
    




def calc_cosy(spinsys,sfrq,center,sw,npts,level=1,maxcoup=6,maxxapp=4,byatom=False,weakcoupling=False):
    #calcuate 1d spectra for generating 2d cosy
    #byatom does nothing
    
    if npts > 1024*4:
        raise ValueError("Number of points too large")
    
    print('CALCULATING COSY PEAKS')
    for s in spinsystem.split_sys_byatom(spinsys,level=level,maxcoup=maxcoup,maxxapp=maxxapp):
        print("SPINS",s.get_ids(),'\n',
              "DETECT",[s.atoms[i].id for i in s.detect],
              "XAPPROX",[s.atoms[i].id for i in s.xapprox])
        print("SCALE",s.scale)
        
        for sub in subsystem.subsystem.iterate_subsystems(s):
            calc_cosy_subspectrum(sub,sfrq,center,byatom=byatom,weakcoupling=weakcoupling)
            
    return

def calc_cosy_subspectrum(subsys,sfrq,center,byatom=False,weakcoupling=False):
    subsys.makemzlist()
    if subsys.xmzlist is None or len(subsys.xmzlist) == 0:
        ham = subsys.blockhamiltonian(sfrq,center,weakcoupling=weakcoupling)
        val, vec = subsys.diagonalizehamiltonian(ham)
        subsys.calctranscosy(val,vec,sfrq,center,byatom=byatom)
    else:
        for xlist in subsys.xmzlist:
            print(".",end="")
            ham = subsys.blockhamiltonian(sfrq,center,xlist=xlist,weakcoupling=weakcoupling)
            val, vec = subsys.diagonalizehamiltonian(ham)
            subsys.calctranscosy(val,vec,sfrq,center,byatom=byatom)
        print()
    return

def plot_2d_cosy(spinsys,sfrq,center,sw,npts,lw=None,c=0.5,zf=None,sinoff=0.5,scale=20,nlv=20,mult=1.2,zoom=False):
    #todo: different npts in each dimension?
    
    spec2d = 0

    if zf is not None and zf > 1024*4:
        raise ValueError("Zero filling too large")
        
    print("PLOTTING...")
    
    x,y = None,None
        
    for a in spinsys.atoms:
    
        print("ATOM",a.id)

        for key in a.cosy_diag_trans:
            x,y = procspec(a.cosy_diag_trans[key],a.cosy_diag_intens[key],
                            sfrq,center,sw,npts,lw=lw,c=c,zf=zf,sinoff=sinoff)
            #print(x[len(x)/2])

            if len(key)%2 == 1:
                #dispersive diagional peaks
                #double, quadurple, ... antiphase terms
                spec2d += np.outer(y.imag,y.imag)
            if len(key)%2 == 0:
                #absorbtive diagonl peaks
                #triple, quint, ... antiphase terms
                spec2d -= np.outer(y.real,y.real)

    for j in spinsys.jcoups:
        #crosspeaks
        
        a1 = spinsys.get_atom(j.atmids[0])
        a2 = spinsys.get_atom(j.atmids[1])
        #jval = spinsys.get_jcoups(a1.id,a2.id)

        key1 = (a1.id,a2.id)
        key2 = (a2.id,a1.id)
        
        print("CROSSPEAK",key1, key2)

        #combine lists (shouldn't be np.array)
        try:
            trans = a1.cosy_cross_trans[key1] + a2.cosy_cross_trans[key2]
            intens = a1.cosy_cross_intens[key1] + a2.cosy_cross_intens[key2]
        except KeyError:
            continue
        
        x,y = procspec(trans,intens,sfrq,center,sw,npts,lw=lw,c=c,zf=zf,sinoff=sinoff)
        #print(x[len(x)/2])
        spec2d -= np.outer(y.real,y.real)


    if spec2d is 0: #emtpy plot
        return None, None, None
        
    #contour levels
    lo = np.max(spec2d)/scale
    plvl = [lo*mult**i for i in range(nlv)  ]
    nlvl = [-lo*mult**i for i in range(nlv)  ]

    xax,yax = np.meshgrid(x,x) #use last x (should all be the same)
    
    fig = plt.figure()
    
    plt.contour(xax,yax,spec2d,
                levels=nlvl[::-1]+plvl,
                colors=['r']*len(nlvl)+['b']*len(plvl),
                )

    if zoom:
        ppmmax = -999
        ppmmin = 999
        for a in spinsys.atoms:
            #print(a.ppm)
            if a.ppm + 1 > ppmmax: ppmmax = a.ppm + 1
            if a.ppm - 1 < ppmmin: ppmmin = a.ppm - 1

        #print(ppmmax,ppmmin)
        plt.xlim(ppmmax,ppmmin)
        plt.ylim(ppmmax,ppmmin)
                
    #plt.gca().invert_xaxis()
    #plt.gca().invert_yaxis()
    
    return xax, yax, spec2d
    
    
    
    
def plot_1d(spinsys,trans,intens,sfrq,center,sw,npts,lw=1,c=0.5,zf=None,newfig=True,yoff=0,offset=0,absval=True,byatom=False,returnfid=False):

    #for plotting
    #trans intens can be plotted
    #even if not by atom

    #byatom = False, ytot -> array, byatom = True, ytot -> DICTIONARY of arrays
    #returnfid = returns fid instead of processed spectrum
    
    if newfig:
        plt.figure()

    #ytot = 0
    x = np.fft.fftshift(np.fft.fftfreq(zf if zf else npts,1/sw/sfrq)) / -sfrq + center
    ytot = {}
    
    for a in spinsys.atoms:
        if spinsys.detect and spinsys.atoms.index(a) not in spinsys.detect:
            ytot[a.id] = np.zeros(zf if zf else npts)
            continue

        x,y = procspec(a.trans,a.intens,sfrq,center,sw,npts,lw=lw,c=c,zf=zf,absval=absval,returnfid=returnfid)
        if byatom: plt.plot(x,y.real+yoff)
        yoff += offset
        #ytot += y.real
        ytot[a.id] = y
            
    #combination lines or manual input
    if len(trans) > 0:
        x,y = procspec(trans,intens,sfrq,center,sw,npts,lw=lw,c=c,zf=zf,absval=absval,returnfid=returnfid)
        if byatom: plt.plot(x,y.real+yoff)
        #ytot += y.real
        ytot["other"] = y
    
    if not byatom:
        ytot = np.sum([ytot[i] for i in ytot],axis=0)
        if ytot.size <= 1:
            ytot = np.zeros(x.size)
        plt.plot(x,ytot.real+yoff)
    
    if newfig: 
        plt.gca().invert_xaxis()
    
    if returnfid or byatom:
        return x,ytot
    else:
        return x,ytot.real

def calc_tocsy(spinsys,sfrq,center,sw,npts,level=1,maxcoup=8,maxxapp=2,byatom=False,weakcoupling=False):
    #calcuate 1d spectra for generating 2d tocsy
    #byatom does nothing
            
    print("CALCULATING TOCSY PEAKS")
    for s in spinsystem.split_sys_byatom(spinsys,level=level,maxcoup=maxcoup,maxxapp=maxxapp):
        print("SPINS",s.get_ids(),'\n',
              "DETECT",[s.atoms[i].id for i in s.detect],
              "XAPPROX",[s.atoms[i].id for i in s.xapprox])
        print("SCALE",s.scale)

        for sub in subsystem.subsystem.iterate_subsystems(s):
            x,y = calc_subspectrum(sub,sfrq,center,byatom=False,weakcoupling=False)
            #only one detected at a time
            s.atoms[s.detect[0]].tocsy_trans.extend(x)
            s.atoms[s.detect[0]].tocsy_intens.extend(y)
   
    return
    
def plot_tocsy(spinsys,sfrq,center,sw,npts,dist=4,lw=None,c=0.5,zf=None,sinoff=0.5,scale=20,nlv=20,mult=1.2):
    
    if npts > 1024*4:
        raise ValueError("Number of points too large")

        
    print("GENERATING INTENSITIES...")
    #take powers of matrix as "mixing"
    tocsymatrix = np.linalg.matrix_power(spinsys.jmatrix+np.eye(len(spinsys.atoms)),dist)    
    tocsymatrix = tocsymatrix != 0
    
    spec2d = 0
    
    for i in range(tocsymatrix.shape[0]):
    
        print("ATOM",spinsys.atoms[i].id)

        y_trans = spinsys.atoms[i].tocsy_trans
        y_intens = spinsys.atoms[i].tocsy_intens
        
        x_trans = []
        x_intens = []
        
        for j in range(tocsymatrix.shape[1]):
            if tocsymatrix[i,j]:
                x_trans.extend(spinsys.atoms[j].tocsy_trans)
                x_intens.extend(spinsys.atoms[j].tocsy_intens)

        y_ax,y_spec = procspec(y_trans,y_intens,sfrq,center,sw,npts,lw=lw,c=c,zf=zf,sinoff=sinoff)
        x_ax,x_spec = procspec(x_trans,x_intens,sfrq,center,sw,npts,lw=lw,c=c,zf=zf,sinoff=sinoff)
        x_spec = x_spec / np.count_nonzero(tocsymatrix[i,:])    
        #plt.plot(x_ax,x_spec)
        
        spec2d += np.outer(x_spec.real,y_spec.real)
        
    #contour levels
    lo = np.max(spec2d)/scale
    plvl = [lo*mult**i for i in range(nlv)  ]
    nlvl = [-lo*mult**i for i in range(nlv)  ]

    xax,yax = np.meshgrid(x_ax,y_ax) #use last x (should all be the same)
    
    plt.figure()
    
    plt.contour(xax,yax,spec2d,
                levels=nlvl[::-1]+plvl,
                colors=['r']*len(nlvl)+['b']*len(plvl),
                )
                
    plt.gca().invert_xaxis()
    plt.gca().invert_yaxis()
    
    return x_ax,y_ax,spec2d
    
    
def plot_1h_13c_hsqc(hspinsys,cspinsys,pairs,
                     h_sfrq,h_center,h_sw,h_npts,
                     c_sfrq,c_center,c_sw,c_npts,
                     lw=None,c13lw=None,c=0.5,c13c=0.5,zf=None,c13zf=None,sinoff=0.5,scale=20,nlv=20,mult=1.2,
                     returnfid=False):
    #must have calculated peaks for hspinsys (1H) and cspinsys (13C) first
    #pairs are indices for each crosspeak

    if max(h_npts,zf if zf else 0) > 1024*4:
        print( "Number of 1h points too large, setting npts to 512, zf to 1024")
        h_npts  = 512
        zf      = 1024

    if max(c_npts,c13zf if c13zf else 0) > 1024*4:
        print("Number of 13c points too large, setting npts to 128, zf to 256")
        c_npts  = 128
        c13zf   = 256

    spec2d = 0

    spec2dR = 0
    spec2dI = 0

    Hatmid = hspinsys.get_ids()
    Catmid = cspinsys.get_ids()
    #print(Hatmid)
    #print(Catmid)
    #print(pairs)

    if not pairs:
        print('warning: no pairs of peaks for hsqc output')
        return [], [], []

    for hidx,cidx in pairs:
        #skip if eg methyls are merged
        #print("DEBUG",hidx,cidx)
        hatom = hspinsys.get_atom(hidx)
        catom = cspinsys.get_atom(cidx)
        if not hatom or not catom: 
            #print("DEBUG PAIR NOT FOUND")
            continue

        print("PEAK",hatom.id,catom.id,hatom.ppm,catom.ppm)
        x_trans = hatom.trans
        x_intens = np.array(hatom.intens) * hatom.mult
        y_trans = catom.trans
        y_intens = np.array(catom.intens) * catom.mult
        x_ax,x_spec = procspec(x_trans,x_intens,h_sfrq,h_center,h_sw,h_npts,lw=lw,c=c,zf=zf,sinoff=sinoff,returnfid=returnfid)
        y_ax,y_spec = procspec(y_trans,y_intens,c_sfrq,c_center,c_sw,c_npts,lw=c13lw,c=c13c,zf=c13zf,sinoff=sinoff,returnfid=returnfid)
        
        if not returnfid:
            spec2d += np.outer(y_spec.real,x_spec.real)
        if returnfid:
            spec2dR += np.outer(y_spec.real,x_spec)
            spec2dI += np.outer(y_spec.imag,x_spec)
    
    #print(spec2d.shape)    
    #contour levels
    if not returnfid:
        lo = np.max(spec2d)/scale
        plvl = [lo*mult**i for i in range(nlv)  ]
        nlvl = [-lo*mult**i for i in range(nlv)  ]

        xax,yax = np.meshgrid(x_ax,y_ax) #use last x (should all be the same)
        #print(xax.shape,yax.shape)
        
        plt.figure()
        
        plt.contour(xax,yax,spec2d,
                    levels=nlvl[::-1]+plvl,
                    colors=['r']*len(nlvl)+['b']*len(plvl),
                    )
                   
        plt.gca().invert_xaxis()
        plt.gca().invert_yaxis()
    
        return x_ax,y_ax,spec2d

    if returnfid:
        fid = np.empty((spec2dR.shape[0]*2,spec2dR.shape[1],), dtype=np.complex128)
        fid[0::2] = spec2dR
        fid[1::2] = spec2dI

        return fid

def get_all_transitions(spinsys):
    atomlist = []
    peratomxy = []
    for atom in spinsys.atoms:
        atomlist.append(atom.id)
        atomxy = []
        for x,y in zip(atom.trans,atom.intens):
            atomxy.append((x,y))
        peratomxy.append(atomxy)
    return atomlist, peratomxy
    
def L(x,x0,lw,A):
    #a lorentian function (x: xaxis points, x0, lw same units as x)
    #height at maximum is A
    y = A * (lw/2)**2 / ((x-x0)**2 + (lw/2)**2) 
    return y

def minfunc(intensities,ydata,xaxis,pos,lw):
    synthdata = np.sum( L(xaxis[None,:],pos[:,None],lw,intensities[:,None]) ,axis=0)
    return np.sum((ydata - synthdata)**2)

def minimizeintens(xaxis,ydata,guess,peaklist,lw,bounds=None):
    result = minimize(minfunc,guess,args=(ydata,xaxis,peaklist,lw),bounds=bounds)
    return result.x

def pickpeaks_byatom(spinsys,sfrq,center,sw,npts,lw=1.0,c=0.5,zf=None,cutoff=-1e-10,minimize=False):
    #this requires that the spectrum is simulated with spinsys.calc_1d_spectrum()
    #for each atom in the spinsys, this function uses 2nd derivatives to pick peaks
    #npts should be large (eg 64k for 1hz lw) otherwise truncation wiggles will also be picked

    blank = spinsystem.spin_system()
    result = []

    for atm in spinsys.atoms:

        #print('atom', atm.id)

        #calc x/y spectrum
        if len(atm.trans) == 0: 
            #print('atom', atm.id, 'no peaks')
            continue

        tmpzf = zf if zf else npts
        best_x = None
        best_y = None
        best_d2y = np.array([])
        best_zf = tmpzf
    
        for mult in [1,2]:

            #print('atom', atm.id, 'run mult', mult)

            newzf = tmpzf * mult
            x,y = plot_1d(blank,atm.trans,atm.intens,sfrq,center,sw,npts,lw=lw,
                                       c=c,zf=newzf,byatom=False)
            #plt.plot(x,y)
            #plt.show()
            plt.close() #kill the figure window

            #second derivative (local minima are peak maxima)
            d2y = np.gradient(np.gradient(y))
            d2y[d2y > cutoff] = 0 #filter out tiny peaks
            d2y_min = argrelmin(d2y)[0]

            #plt.plot(x,y)
            #plt.plot(x,d2y)
            #plt.plot(x[d2y_min],y[d2y_min],'o')
            #plt.show()

            #if d2y_min.size != 0: break #argrelmin cannot see plateaus
            #                            #so try increasing interploation (zf)

            if d2y_min.size > best_d2y.size and d2y_min.size < 1000:
                #print('found better', best_d2y.size, d2y_min.size, d2y_min)
                best_x, best_y = x, y
                best_d2y = d2y_min
                best_zf = newzf
            #else:
            #    d2y_min = best_d2y

            #print('peak',atm.id,'try increasing zero filling')

        if best_d2y.size == 0:    
            #print('peak',atm.id,'no peaks detected')        
            continue

        if minimize:
            print('fitting peak %s'%(atm.id))
            #print(best_d2y.size)

            buff = int(best_zf/sw/sfrq*2*lw) #npts per hz * 2*lw in hz
            minind = max(0,best_d2y[0] - buff)
            maxind = min(npts,best_d2y[-1] + buff)
            
            peaklist = np.array([best_x[i] for i in best_d2y])
            guess = np.array([best_y[i] for i in best_d2y])

            #print(peaklist, guess)
            #plt.plot(x,y)
            #plt.plot(x,d2y)
            #plt.show()

            fit = minimizeintens(best_x[minind:maxind],best_y[minind:maxind],guess,peaklist,lw/sfrq)
            result.append([atm.id, atm.ppm, [(i,j) for i,j in zip(peaklist,fit)] ])
        else:
            result.append([atm.id, atm.ppm, [(best_x[i],best_y[i]) for i in best_d2y] ])

    return result

def mergespectra(y,mergelist):
    #y,result is a dictionary of arrays
    #print(y)
    #print(mergelist)
    result = {}
    for m in mergelist:
        key = tuple(m)
        #print(key)
        for i in m:
            if i in y:
                #print(i,y[i])
                if key not in result:
                    result[key] = [y[i]]
                else:
                    result[key].append(y[i])
        #print(key,result.keys())
        if key in result and len(m) != len(result[key]):
            raise KeyError("spectrum for atom in mergelist does not exist")
        elif key in result:
            result[key] = np.sum(result[key],axis=0)
    return result

def pickpeaks_byatom_merged(spinsys,mergelist,sfrq,center,sw,npts,lw=1.0,c=0.5,zf=None,cutoff=-1e-10,minimize=False):
    #this requires that the spectrum is simulated with spinsys.calc_1d_spectrum()
    #for each atom in the spinsys, this function uses 2nd derivatives to pick peaks
    #npts should be large (eg 64k for 1hz lw) otherwise truncation wiggles will also be picked

    #TODO UPDATE/FIX as like pickpeaks_byatom()

    blank = spinsystem.spin_system()
    result = []

    for atomids in mergelist:

        tmpzf = zf if zf else npts
        best_x = None
        best_y = None
        best_d2y = np.array([])
        best_zf = tmpzf

        for mult in [1,2]:

            newzf = tmpzf*mult
            subresult = [[],[],0,0]

            for a in atomids:

                atm = spinsys.get_atom(a)
                if atm is None: continue
                if len(atm.trans) == 0: continue

                #calc x/y spectrum
                x,y = plot_1d(blank,atm.trans,atm.intens,sfrq,center,sw,npts,lw=lw,
                                           c=c,zf=newzf,byatom=False)
                plt.close() #kill the figure window

                subresult[0].append(atm.id)
                subresult[1].append(atm.ppm)
                subresult[2] = x
                subresult[3] += y

            if len(subresult[0]) == 0: continue

            x = subresult[2]
            y = subresult[3]

            d2y = np.gradient(np.gradient(y))
            d2y[d2y > cutoff] = 0 #filter out tiny peaks
            d2y_min = argrelmin(d2y)[0]

            if d2y_min.size > best_d2y.size and d2y_min.size < 1000:
                best_x, best_y = x, y
                best_d2y = d2y_min
                best_zf = newzf
            #else:
            #    d2y_min = best_d2y

            #if d2y_min.size == 0:
            #    continue

        if best_d2y.size == 0:    
            #print('peak',atm.id,'no peaks detected')        
            continue
            
        if minimize:
            print('fitting peak %s'%str(atomids))

            buff = int(best_zf/sw/sfrq*2*lw) #npts per hz * 2*lw in hz
            minind = max(0,best_d2y[0] - buff)
            maxind = min(npts,best_d2y[-1] + buff)
            
            peaklist = np.array([best_x[i] for i in best_d2y])
            guess = np.array([best_y[i] for i in best_d2y])
            
            fit = minimizeintens(best_x[minind:maxind],best_y[minind:maxind],guess,peaklist,lw/sfrq)
            result.append([subresult[0],subresult[1], [(i,j) for i,j in zip(peaklist,fit)] ])
        else: 
            result.append([subresult[0],subresult[1],[(x[i],y[i]) for i in best_d2y]])

    return result


class spectrum():
    #nested lists of peak positions and intensites
    #with corresponding (possibly grouped) atom ids
    #possibly replace putting data in atoms
    def __init__():
        self.spinsys = None #link to original spinsys data
        self.idlist = {} #dictionary of spectrum index -> atm id (or groups of ids)

        #per atom spectra as list of lists
        self.intens = []
        self.trans = []
        #cosy spectra
        self.cosy_cross_intens = {}
        self.cosy_cross_trans = {}
        self.cosy_diag_intens = {}
        self.cosy_diag_trans = {}
        #tocsy
        self.tocsy_intens = []
        self.tocsy_trans = []


    def clear_data(self):
        self.intens = []
        self.trans = []
        self.cosy_cross_intens = {}
        self.cosy_cross_trans = {}
        self.cosy_diag_intens = {}
        self.cosy_diag_trans = {}
        self.tocsy_intens = []
        self.tocsy_trans = []


