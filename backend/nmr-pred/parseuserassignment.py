import sys, os, re, time
import csv
import numpy as np

"""

script to take prediction csv files and formated user input csv files for
writing new csv files for simulation

the atom indices from the input/output files from this script should 
match the mol file used as input for nmrpred, although this is not checked
indices should be one-indexed

for --userinput:
atom symbol and atom index must be present
other fields can be empty strings or "NA"
ambiguous assignments can be in square brackets eg "1.5[H1,H5]" or a range "2.3[2.0-2.5]"
but anything in brackets are ignored for nmrpred simulation and will need to be parsed somewhere else
unassigned couplings are comma separated values eg "7.5,3.5"
assigned couplings have the paired atom in brackets eg "7.5[H1,H2,H3],3.5[H5,H6]"

for --labels:
optional labels used in user input, mainly in tags after values (ambiguous assingments, couplings)
labels must be unique to each atom and refer to a particular atom index


(may be fixed later)
NO attempt is made at making sure all couplings are consistent
eg mixing assigned and unassigned couplings will result in extra splittings
on the atoms with unassigned couplings 
eg for two different couplings for the same atom pair, only one is used

output will have dummy atoms (idx > 1000) if
couplings are not assigned or paired atom is unknown


options and examples:

--pred1hcsv         'results/example_1h_shifts.txt' (not used)
--pred1hcoupcsv     'results/example_1h_couplings.txt'
--pred13ccsv        'results/example_13c_shifts.txt' (not used)
--pred13ccoupcsv    None

--userinput         'results/example_assignmenttable_manual.txt' (required)
--labels  <file> [<col# to use>]  csv with columns (first col is #1): atom index, label, label, ...
                                  comments, header etc should start with "#"

--write1hshift      'results/example_parsed1hshifts.txt'
--write1hcoup       'results/example_parsed1hcoup.txt'
--write13cshift
--write13ccoup

for simulation, after running script:
nmrpred.py --smiles CCO --noprediction --input1h results/example_parsed1hshifts.txt --input1hcoup results/example_parsed1hcoup.txt --plot1h

"""

#defaults
pred1hcsv = None
pred1hcoupcsv = None
pred13ccsv = None
pred13ccoupcsv = None
userinput = None
labels = None
labels_col = 2
write1hshift = None
write1hcoup = None
write13cshift = None
write13ccoup = None
opbracket,clbracket = "[","]"

for i in range(len(sys.argv)):

    #all atom ids must be numeric
    #csv with id, shift
    if sys.argv[i] == '--pred1hcsv': pred1hcsv = sys.argv[i+1]
    #csv with id1, id2, coupling
    if sys.argv[i] == '--pred1hcoupcsv': pred1hcoupcsv = sys.argv[i+1]

    #csv with id, shift
    if sys.argv[i] == '--pred13ccsv': pred13ccsv = sys.argv[i+1]
    #csv with id1, id2, coupling
    if sys.argv[i] == '--pred13ccoupcsv': pred13ccoupcsv = sys.argv[i+1]

    #csv with symbol, id, shift, multiplicity ("t", "d", etc), coupling string
    if sys.argv[i] == '--userinput': userinput = sys.argv[i+1]
    if sys.argv[i] == '--labels': 
        labels = sys.argv[i+1]
        try:
            labels_col = int(sys.argv[i+2])
        except:
            pass

    #paths to file to write
    if sys.argv[i] == '--write1hshift': write1hshift = sys.argv[i+1]
    if sys.argv[i] == '--write1hcoup': write1hcoup = sys.argv[i+1]

    if sys.argv[i] == '--write13cshift': write13cshift = sys.argv[i+1]
    if sys.argv[i] == '--write13ccoup': write13ccoup = sys.argv[i+1]

##examples
##id, shift
#predshift = [[1,0],
#             [2,2],
#             [3,3],
#             [4,6],
#             [5,8],
#             ]

##id1, id2, coup
#predcoup = [[1,2,5],
#            [1,3,7],
#            [2,3,-11],
#            [4,5,10],
#            ] 

##id, shift, mult, coup
#usertable = [[1,1,"td","11.5(H2),7.5(H3,H5)"],
#             [2,2,"td","11.5,7.5"],
#             [3,2.5,"td",""],
#             [4,6,"","2.2(H4)"],
#             [5,7,"","2.2"],] 


predshift = []

if pred1hcsv:
    with open(pred1hcsv, 'r') as f:
        r = csv.reader(f)
        for line in r:
            if len(line) == 2:
                line.extend([1,0.5])
            predshift.append(line)

if pred13ccsv:
    with open(pred13ccsv, 'r') as f:
        r = csv.reader(f)
        for line in r:
            if len(line) == 2:
                line.extend([1,0.5])
            predshift.append(line)

predshift = np.array(predshift, dtype=float)
print("input shift prediction")
print(predshift)


predcoup = []

if pred1hcoupcsv:
    with open(pred1hcoupcsv, 'r') as f:
        r = csv.reader(f)
        for line in r:
            predcoup.append(line)

if pred13ccoupcsv:
    with open(pred13ccoupcsv, 'r') as f:
        r = csv.reader(f)
        for line in r:
            predcoup.append(line)

print("input shift coupling")
predcoup = np.array(predcoup, dtype=float)
print(predcoup)

usertable = []
if userinput:
    with open(userinput, 'r') as f:
        r = csv.reader(f)
        for line in r:

            atomsym = line[0].strip()
            if atomsym not in ('H','C'): continue

            atmid = int(line[1].strip())
            atmppm = line[2].strip() if line[2].strip() not in ("","NA") else ""
            atmppm = atmppm.replace(" ", "")
            atmmult = line[3].strip() if line[3].strip() not in ("","NA") else ""
            atmmult = atmmult.replace(" ", "")
            atmcoup = line[4].strip() if line[4].strip() not in ("","NA") else ""
            atmcoup = atmcoup.replace(" ", "")
            usertable.append([atomsym, atmid, atmppm, atmmult, atmcoup])

print("input user assignments")
for row in usertable:
    print(row)
    
    
labelmap = {}
if labels:
    with open(labels, 'r') as f:
        r = csv.reader(f)
        col = labels_col - 1
        for line in r:
            if not line: continue
            if line[0].startswith("#"): continue
            idx = int(line[0].strip())
            lab = line[col].strip().replace(" ", "")
            if lab in labelmap:
                print('warning: label %s, on atom %d, duplicated'%(lab, idx))
            if lab:
                labelmap[lab] = idx

    print("input user labels")
    for lab in labelmap:
        print(lab, labelmap[lab])



multdict = {"m":0, #Multiplet -> display as 's'
            "s":0,
            "d":1,
            "t":2,
            "q":3,

            #the following are nonstandard
            #"p":4, #Pentet, or QUINTet
            #"x":5, #seXtet 
            #"h":6, #Heptet, or septet
            #"o":7, #Octet
            #"n":8, #Nonet
            #"????":9, #decet?
            }

#choose some non-overlapping values
tmpcoup = [11,7,5,3,2,1,0,0,0]

#parsedata is a list of lists
#[sym, id, shift, multlist, atmlist]
#multlist and atomlist are the same size
#multlist: coupling value, atomlist: atom idx or ""
parsedata = []
assignedatoms = []
assignedatomsym = []

print('parsing user assignment table')

for line in usertable:
    
    sym = line[0]
    id = line[1]

    print('atom %s %d'%(sym, id))
    print(line)

    #shift is decimal with +/- at front, ambiguous assignments afterwards
    p = "([\+\-]?[\d\.]+)(.*)"
    #alternatively inputed as a range with a dash (-) => average kept
    #p2 = "([\+\-]?[\d\.]+)-([\+\-]?[\d\.]+)(.*)"
    shift = line[2]
    shift = shift.replace(" ","") #remove spaces
    match = re.match(p,shift)
    #match2 = re.match(p2,shift)
    #print(match, match2)
    #if match2:
    #    print('range found, using average')
    #    shift = (float(match2.group(1)) + float(match2.group(2))) / 2.0
    #    if match2.group(3): print('ambiguity "%s" ignored'%(match2.group(3)))
    if match:
        shift = float(match.group(1))
        if match.group(2): print('ambiguity "%s" ignored'%(match.group(2)))
    else:
        shift = None

    #mult to number of repeats of each coupling
    mult = line[3]
    mult.replace('quint','m')
    mult.replace('broad','')
    mult.replace('br','')
    for m in mult: #any unknowns => multiplet
        if m not in multdict:
            mult = "m"
            break
    mult = [multdict[m] if m in multdict else 0 for m in mult]

    #parse couplign string
    coup = line[4]
    coup = coup.replace(" ","") #remove spaces
    #+1.2(H1), -3.4(H2,H3), ...
    #value followed by brackets with one or more labels, separated by commas
    p = "([\+\-]?[\d\.]+)(?:\%s(.+?)\%s)?"%(opbracket,clbracket)
    #p2 = "[\d]+"
    splitcoup = re.findall(p,coup)
    splitcoup_val = [float(j[0]) for j in splitcoup]
    #splitcoup_id = [[int(s) for s in re.findall(p2, j[1])] for j in splitcoup]
    splitcoup_id = [[s for s in j[1].split(',')] for j in splitcoup]

    print(coup,splitcoup_val,splitcoup_id)
    #print(sym, id, shift, mult, splitcoup_val, splitcoup_id)

    
    if shift is None:
        print('not assigned, skipping')
        continue

    assignedatoms.append(id)
    assignedatomsym.append(sym)

    if not mult and not coup:
        print('no couplings, using predicted')
        parsedata.append([sym, id, shift, [j[2] for j in predcoup if j[0] == id or j[1] == id],
                          [j[0] if j[0] != id else j[1] for j in predcoup if j[0] == id or j[1] == id]])

    if mult and not coup:
        print('multiplicity only, use fixed values')
        multlist = []
        for i,m in enumerate(mult):
            try: multlist.extend([tmpcoup[i]] * m)
            except IndexError: break
        parsedata.append([sym, id, shift, multlist, []])

    if coup and opbracket not in coup:
        print('unassigned values, pair with multiplicity or "d"')
        couplen = len(splitcoup)
        while len(mult) < couplen:
            mult.append(1)
        multlist = []
        for i,m in enumerate(mult):
            multlist.extend([splitcoup_val[i]] * m)
        parsedata.append([sym, id, shift, multlist, []])

    if coup and  opbracket in coup:
        print('has assignments, multiplicity ignored')
        multlist = []
        atmlist = []
        #print(splitcoup_val)
        #print(splitcoup_id)

        for i in range(len(splitcoup_val)):
            coup = splitcoup_val[i]
            atms = splitcoup_id[i]

            #print(i,coup,atms)
            #print(labelmap)

            if atms:
                for a in atms:
                    if a in labelmap: #try labels first
                        multlist.append(coup)
                        atmlist.append(labelmap[a])
                    else: #try as atom index
                        a = re.search("([\d]+)", a)
                        #print(a)
                        if a:
                            multlist.append(coup)
                            atmlist.append(int(a.group(1)))
                        else:
                            multlist.append(coup)
                            atmlist.append('')
            else:
                multlist.append(coup)
                atmlist.append('')

        parsedata.append([sym, id, shift, multlist, atmlist])

for row in parsedata:
    print(row)

print('writing new shift and coupling tables for nmrpred')

maxmolatoms = 1001 #assume natural products have <= 1000 atoms

newshifttable = []
newcouptable = []

for line in parsedata:

    print(line)
    
    sym = line[0]
    id = line[1]
    ppm = line[2]
    coup = line[3]
    atms = line[4]
    
    newshifttable.append([sym, id, ppm])

    for i in range(len(coup)):

        if not coup[i]: continue

        if len(atms) == len(coup):

            if atms[i]:
                if atms[i] not in assignedatoms:
                    newshifttable.append([sym, atms[i],-999])
                if atms[i] in assignedatoms  and  assignedatomsym[assignedatoms.index(atms[i])] != sym:
                    newshifttable.append([sym, atms[i],-999])

            elif atms[i] == '':
                atms[i] = maxmolatoms
                maxmolatoms += 1
                newshifttable.append([sym, atms[i],-999])

            newcouptable.append([sym, id, atms[i], coup[i]])

        else:

            newid = maxmolatoms
            maxmolatoms += 1

            newshifttable.append([sym, newid,-999])
            newcouptable.append([sym, id, newid, coup[i]])

print('output shifts')
for row in newshifttable:
    print(row)

print('output couplings')
for row in newcouptable:
    print(row)

if write1hshift:
    with open(write1hshift, 'w') as f:
        w = csv.writer(f)
        for row in newshifttable:
            if row[0] == "H":
                w.writerow(row[1:])

if write1hcoup:
    with open(write1hcoup, 'w') as f:
        w = csv.writer(f)
        for row in newcouptable:
            if row[0] == "H":
                w.writerow(row[1:])


if write13cshift:
    with open(write13cshift, 'w') as f:
        w = csv.writer(f)
        for row in newshifttable:
            if row[0] == "C":
                w.writerow(row[1:])

if write13ccoup:
    with open(write13ccoup, 'w') as f:
        w = csv.writer(f)
        for row in newcouptable:
            if row[0] == "C":
                w.writerow(row[1:])

