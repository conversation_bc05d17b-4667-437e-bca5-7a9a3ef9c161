import numpy as np
import matplotlib.pyplot as plt

import os
import sys
import time
import csv

from predictor import readinput
from predictor import nmrpred
from predictor import measurej
from spectrum import spinsystem
from spectrum import calcspectrum
from predictor import getequiv

"""

predicts 1H, 13C chemical shifts and 1H-1H coupling constants from molecules
and simulates their spectra


required arguments:

--mol|--smiles|--inchi|--molstring  <inputfile>   input type and file/string


optional arguments:

1h spectrum parameters, also 13c: --13csfrq, --13csw, etc.

--sfrq          <value>     spectrometer 1H frequency in MHz (500)
--sw            <value>     sweep width in ppm (14.0)
--center        <value>     center of spectrum in ppm (5.0)
--npts          <value>     points in fid (65536)
--zf            <value>     points in spectrum (131072)
--lw            <value>     linewidth of peaks (1.0)

2d parameters (max 4096)
--npts_f2       <value>     x axis (512)
--npts_f1       <value>     y axis (128)
--zf_f2         <value>     x axis (1024)
--zf_f1         <value>     y axis (256)

--solvent       <d2o|h2o....>   if d2o/h2o (default), exchangeable protons removed from prediction


optional csv file input (must be numerical, comma separated) 
if no prediction, input is used directly
if prediction and input both, input atoms replace predicted values, based
on atom index, but indexes not in prediction are ignored.

--input1h       filepath    csv file with atomid, shift (ppm)
--input13c      filepath
--input1hcoup   filepath    csv file with atomid1, atomid2, coupling (Hz)


output file options

--outputprefix  prefix      prefix for output files (default: time in seconds)
--outputpath    path        path for output files (scriptpath/result/)

--optmol                    add Hs and 3d coordinates generation for mol/sdf
                            this is always done for smiles input
--ffopt                     turn on MMFF minimization step for 3d coords (slow for large mol)
--nooptmol                  read mol file and use directly, no addh, no sanitization

--noprediction              skip prediction
--nopred1h                  
--nopred13c   
--nopred1hcoup 

--deletemol                 delete mol file generated for java programs (default: keep)
--write1h                   write csv files with atom indices and value
--write13c                   
--write1hcoup               csv with formated j couplings 

--jcoupformat   pairs       format: "J(H1,H2) = 3.5 Hz"
                values      format: "3.5" (default)
                idafter     format: "3.5(H2)" 

--writeassignmenttable      write csv with shift, multiplets, couplings (values only)

--plot1h                    write csv and png files for predicted spectra
--plot13c
--plotcshqc                 write binary and png file for predicted 2d chsqc

--mergepeaks                merge atoms and peaks with same chemical shift for spectrum peak lists

--labelmap      file        csv with... col 1: old idx (matching input mol) col 2: new idx or label
                            only affects the output peak lists (from --plot1h, etc)

TODO/not implemented
--maxatoms      1000        atom ids higher than this are dummy atoms used for weak coupling
--setxapprox    -999        shift value used for weak coupling to dummy atoms in input files
--plotdqfcosy

examples:

predict ethanol at 60 Mhz:
python nmrpred.py --smiles CCO --sfrq 60 --plot1h

predict ethanol but replace chemical shifts from myshifts.csv:
python nmrpred.py --smiles CCO --sfrq 60 --input1h myshifts.csv --plot1h

simulate from csv files:
python nmrpred.py --smiles CCO --sfrq 60 --input1h myshifts.csv --input1hcoup mycoups.csv --noprediction --plot1h

the result/example files:
python nmrpred.py --smiles CCO --sfrq 500 --plot1h --plot13c --write1h --write13c --write1hcoup --writeassignmenttable --outputprefix test

see *_1h_shifts.txt and *1h_couplings.txt output files for example inputs


examples for npmrd website:

write assignments:
python3 nmrpred.py --mol aGlucose.sdf  --writeassignmenttable

overwrite shift predictions and write spectra:
python3 nmrpred.py --mol aGlucose.sdf  --input1h myshifts.csv --input1hcoup mycoups.csv  --plot1h --plot13c


requires:

python (and numpy, scipy, matplotlib, rdkit; recommend using anaconda/miniconda to install)
java

"""





#defaults
molfiletype = None
molfilepath = None

sfrq = 500.0
sw = 14.0
center = 5.0
npts = 1024 * 64
zf = npts * 2
lw = 1.0
c = 0.5

c13sfrq = None
c13sw = 200 
c13center = 100
c13npts = 1024 * 128
c13zf = npts * 2
c13lw = 1.0
c13c = 0.5

npts_f2 = 512
npts_f1 = 128
zf_f2 = 1024
zf_f1 = 256

optmol = False
ffopt = False
nooptmol = False

shiftfilepath = None
c13shiftfilepath = None
coupfilepath = None
solvent = 'd2o'

outputprefix = str(time.time())
outputpath = None

pred1h = True
pred13c = True
pred1hcoup = True

#ovwr1h = True
#ovwr13c = True
#ovwr1hcoup = True

moloutput = True
write1h = False
write13c = False
write1hcoup = False
writeassigntable = False
mergepeaks = False
jcoupformat = "values"

plot1h = False
plot13c = False

plotwidth = 8 #inches
plotheight = 2 #inches
plotwidth_2d = 4 #inches
plotheight_2d = 3 #inches
dpi = 300

writechsqctable = False
plotchsqc = False

plotdqfcosy = False

labelmap = None


#check arguments
validarguments = (  '--mol', '--smiles', '--inchi', '--molstring',
                    '--sfrq', '--sw', '--center', '--npts', '--zf', '--lw',  
                    '--13csfrq', '--13csw', '--13ccenter', '--13cnpts', '--13czf', '--13clw', 
                    '--solvent',  
                    '--optmol',  '--ffopt', '--nooptmol',
                    '--nopred1h', '--nopred13c', '--nopred1hcoup', '--noprediction', 
                    '--input1h', '--input13c', '--input1hcoup',  
                    '--outputpath', '--outputprefix', 
                    '--deletemol',
                    '--write1h', '--write13c', '--write1hcoup',  '--writeassignmenttable', '--mergepeaks',
                    '--jcoupformat',  
                    '--plot1h', '--plot13c',
                    '--npts_f2', '--npts_f1', '--zf_f2', '--zf_f1',  
                    '--plotchsqc','--plotdqfcosy', 
                    '--labelmap',
                    )
for i in sys.argv:
    if i.startswith('--') and i not in validarguments:
        raise ValueError('invalid argument %s'%i)
                                            

for i in range(len(sys.argv)):

    if sys.argv[i] == '--mol':
        molfiletype = 'mol'
        molfilepath = sys.argv[i+1]
        print('input mol:',molfilepath)
    if sys.argv[i] == '--smiles':
        molfiletype = 'smiles'
        molfilepath = sys.argv[i+1]
        print('input smiles:',molfilepath)
    if sys.argv[i] == '--molstring':
        molfiletype = 'molstring'
        molfilepath = sys.argv[i+1]
        print('input mol:',molfilepath)
    if sys.argv[i] == '--inchi':
        molfiletype = 'inchi'
        molfilepath = sys.argv[i+1]
        print('input inchi:',molfilepath)

    if sys.argv[i] == '--sfrq':     sfrq = float(sys.argv[i+1])
    if sys.argv[i] == '--sw':       sw  = float(sys.argv[i+1])
    if sys.argv[i] == '--center':   center = float(sys.argv[i+1])
    if sys.argv[i] == '--npts':     npts = int(sys.argv[i+1])
    if sys.argv[i] == '--zf':       zf = int(sys.argv[i+1])
    if sys.argv[i] == '--lw':       lw = float(sys.argv[i+1])

    if sys.argv[i] == '--13csfrq':   c13sfrq = float(sys.argv[i+1])
    if sys.argv[i] == '--13csw':     c13sw = float(sys.argv[i+1])
    if sys.argv[i] == '--13ccenter': c13center = float(sys.argv[i+1])
    if sys.argv[i] == '--13cnpts':   c13npts = int(sys.argv[i+1])
    if sys.argv[i] == '--13czf':     c13zf = int(sys.argv[i+1])
    if sys.argv[i] == '--13clw':     c13lw = float(sys.argv[i+1])

    if sys.argv[i] == '--solvent':  solvent = sys.argv[i+1]

    if sys.argv[i] == '--optmol':   optmol = True
    if sys.argv[i] == '--ffopt':    ffopt = True
    if sys.argv[i] == '--nooptmol': nooptmol = True

    if sys.argv[i] == '--input1h':     shiftfilepath = sys.argv[i+1]
    if sys.argv[i] == '--input13c':    c13shiftfilepath = sys.argv[i+1]
    if sys.argv[i] == '--input1hcoup': coupfilepath = sys.argv[i+1]

    if sys.argv[i] == '--outputpath':   outputpath = sys.argv[i+1]   
    if sys.argv[i] == '--outputprefix': outputprefix = sys.argv[i+1]


    if sys.argv[i] == '--nopred1h':     pred1h = False
    if sys.argv[i] == '--nopred13c':    pred13c = False
    if sys.argv[i] == '--nopred1hcoup': pred1hcoup = False
    if sys.argv[i] == '--noprediction': pred1h = pred13c = pred1hcoup = False


    #if sys.argv[i] == '--ovwr1h':    ovwr1h = not ovwr1h
    #if sys.argv[i] == '--ovwr13h':   ovwr13c = not ovwr13c
    #if sys.argv[i] == '--ovwr1hcoup': ovwr1hcoup = not ovwr1hcoup 

    if sys.argv[i] == '--deletemol': moloutput = False

    if sys.argv[i] == '--write1h':     write1h = True
    if sys.argv[i] == '--write13c':    write13c = True
    if sys.argv[i] == '--write1hcoup': write1hcoup = True

    if sys.argv[i] == '--writeassignmenttable': writeassigntable = True
    if sys.argv[i] == '--mergepeaks': mergepeaks = True
    if sys.argv[i] == '--jcoupformat': jcoupformat = sys.argv[i+1]

    if sys.argv[i] == '--plot1h':  plot1h = True
    if sys.argv[i] == '--plot13c': plot13c = True

    if sys.argv[i] == '--npts_f2': npts_f2 = int(sys.argv[i+1])
    if sys.argv[i] == '--npts_f1': npts_f1 = int(sys.argv[i+1])
    if sys.argv[i] == '--zf_f2':   zf_f2 = int(sys.argv[i+1])
    if sys.argv[i] == '--zf_f1':   zf_f1 = int(sys.argv[i+1])
    if sys.argv[i] == '--plotchsqc':    plotchsqc = True
    if sys.argv[i] == '--plotdqfcosy':  plotdqfcosy = True

    if sys.argv[i] == '--labelmap':     labelmap = sys.argv[i+1]

if c13sfrq is None:
    c13sfrq = sfrq * 10.7084 / 42.577478518


try:
    if molfiletype not in ('mol','smiles','molstring','inchi'): raise ValueError('invalid molecule file type')
    if molfiletype == 'mol' and not os.path.isfile(molfilepath): raise ValueError('molecule file not found')
except:
    raise ValueError('mol file or smiles string required')


if (not pred1h and not shiftfilepath and
    not pred13c and not c13shiftfilepath and 
    not pred1hcoup and not coupfilepath):
        raise RuntimeError('no prediction and no input provided')

if jcoupformat not in ('values','pairs','atomafter'):
    raise ValueError('unknown option for assignment table jcoupling format')
else:
    jflag = {'values':0,'pairs':1,'atomafter':2}
    valuesonly = jflag[jcoupformat]


scriptdir = os.path.dirname(os.path.realpath(__file__)) + "/"

if outputpath:
    if not outputpath.endswith("/"):
        outputpath = outputpath + "/"
    datprefix = outputpath
else:
    datprefix = scriptdir  + "results/"

#output files
newmolfile = datprefix + outputprefix + '_output.mol'

shiftoutputfile = datprefix + outputprefix + '_1h_shifts.txt'
couplingoutputfile = datprefix +  outputprefix + '_1h_couplings.txt'
formatedcouplingfile = datprefix +  outputprefix + '_1h_formatedcouplings.txt'
c13shiftoutputfile = datprefix + outputprefix + '_13c_shifts.txt'


peaklistfile = datprefix +  outputprefix + '_1h_peaklist.txt'
c13peaklistfile = datprefix +  outputprefix + '_13c_peaklist.txt'

paramfile = datprefix +  outputprefix + '_1h_params.txt'
c13paramfile = datprefix +  outputprefix + '_13c_params.txt'

fidfile = datprefix +  outputprefix + '_1h_fid.txt'
c13fidfile = datprefix +  outputprefix + '_13c_fid.txt'

spectrumxyfile = datprefix +  outputprefix + '_1h_spectrum.txt'
c13spectrumxyfile = datprefix +  outputprefix + '_c13_spectrum.txt'

specfile = datprefix + outputprefix + '_1h_1d.png'
c13specfile = datprefix + outputprefix + '_13c_1d.png'

assignmenttablefile = datprefix + outputprefix + '_assignmenttable.txt'

hsqcassignmenttablefile = datprefix + outputprefix + '_hsqc_assignmenttable.txt'
chsqcparamfile = datprefix +  outputprefix + '_13c_hsqc_params.txt'
chsqcspecimg = datprefix + outputprefix + '_13c_hsqc.png'
chsqcspecbin = datprefix + outputprefix + '_13c_hsqc.dat'
chsqcsfidbin = datprefix + outputprefix + '_13c_hsqc_fid.dat'

dqfcosyimg = datprefix + outputprefix + '_1h_1h_dqfcosy.png'

#read input smiles or mol file (mol file must be 3d and have hydrogen)
if not nooptmol:

    mol = readinput.readinput(molfiletype, molfilepath)

    if molfiletype == 'mol' or molfiletype == 'molstring' and optmol:
        mol, confid = readinput.cleanupgeometry(mol, addh=True, embed=True, optimize=ffopt)

    if molfiletype == "smiles" or molfiletype == "inchi":
        mol, confid = readinput.cleanupgeometry(mol, addh=True, embed=True, optimize=ffopt)

    try:
        if not mol.GetConformer().Is3D():
            print('warning: molecule is not 3d')
    except Exception as e:
        print('error with 3d molecule conformation generation, keep going anyways')
        #sys.exit()

else:

    print('warning: reading molecule without processing')

    if molfiletype == 'mol':
        mol = readinput.readinput(molfiletype, molfilepath, removeHs=False, sanitize=False)
    

#write out rdkit-cleaned mol file and use this output for prediction
#file needed for the java programs
if molfiletype == "mol" and os.path.abspath(newmolfile) == os.path.abspath(molfilepath):
    pass
else:
    readinput.writemol(mol, 'mol', newmolfile)



def read_shifts(filename):
    # a file with at least two columns: atom_index, shift_ppm, ...
    #numbers only
    tbl = []
    with open(filename) as f:
        for line in f:
            if line.startswith("#"): continue
            line = line.split(',')
            tbl.append(line)
    return np.array(tbl,dtype=float)

def read_couplings(filename):
    # a file with three columns: atom1_index, atom2_index, coupling_hz
    #numbers only
    tbl = []
    with open(filename) as f:
        for line in f:
            if line.startswith("#"): continue
            line = line.split(',')
            tbl.append(line)
    return np.array(tbl, dtype=float)


def read_labelmap(filename):
    mapping = {}
    with open(filename) as f:
        reader = csv.reader(f)
        for line in reader:
            if not line: continue
            if line[0].startswith("#"): continue
            mapping[int(line[0])] = line[1]
    return mapping

if shiftfilepath:
    shifttable_user = read_shifts(shiftfilepath)
    print('input 1h shift table')
    print(shifttable_user)
else:
    shifttable_user = np.array([])

if c13shiftfilepath:
    c13shifttable_user = read_shifts(c13shiftfilepath)
    print('input 13c shift table')
    print(c13shifttable_user)
else:
    c13shifttable_user = np.array([])

if coupfilepath:
    jtable_user = read_couplings(coupfilepath)
    print('input coupling table')
    print(jtable_user)
else:
    jtable_user = np.array([])

if labelmap is not None:
    mapping = read_labelmap(labelmap)
    print('labels')
    for m in mapping:
        print(m, mapping[m])
else:
    mapping = {}


if pred1h:
    shifttable = nmrpred.predictshifts(newmolfile, scriptpath=scriptdir+'java/', scriptname='Hpredictor.sh')
else:
    shifttable = np.array([])

if pred13c:
    c13shifttable = nmrpred.predictshifts(newmolfile, scriptpath=scriptdir+'java/', scriptname='Cpredictor.sh')
else:
    c13shifttable = np.array([])

if pred1hcoup:
    jtable = nmrpred.predictjcoup(mol)
else:
    jtable = np.array([])


if pred1h or pred1hcoup:

    #clean up prediction:

    #remove exchangable protons from prediction
    print('checking for exhangable protons')
    if solvent.lower().startswith("h2o"):
        shifttable, jtable = nmrpred.filterexchangableH(mol, shifttable, jtable)
    elif solvent.lower() == 'd2o':
        shifttable, jtable = nmrpred.filterexchangableH(mol, shifttable, jtable, filteramide=True)

    #check for 2H and 14NX4+ in prediction, set spin 1
    #couplings are scaled in nmrpred.predictjcoup()
    print('checking for isotopes')
    shifttable, jtable = nmrpred.checkisotopes(mol, shifttable, jtable)

    #average CH3 couplings
    print('checking for methyls')
    jtable = nmrpred.averagemethylcoupling(mol, shifttable, jtable, keepzeros=True) 

if pred1h:
    print('predicted 1h shift table')
    print(shifttable)

if pred13c:
    print('predicted 13c shift table')
    print(c13shifttable)

if pred1hcoup:
    print('predicted coupling table')
    print(jtable)


#update predicted tables with user input
if pred1h and shiftfilepath:
    for userlines in shifttable_user:
        for predlines in shifttable:
            if userlines[0] == predlines[0]: #if idxs match
                predlines[1] = userlines[1] #set chemical shift

    print('updated 1h shift table for simulation')
    print(shifttable)

if pred13c and c13shiftfilepath:
    for userlines in c13shifttable_user:
        for predlines in c13shifttable:    #for c13 as well
            if userlines[0] == predlines[0]: #if idxs match
                predlines[1] = userlines[1] #set chemical shift

    print('updated 13c shift table for simulation')
    print(c13shifttable)

if pred1hcoup and coupfilepath:
    for userlines in jtable_user:
        for predlines in jtable:
            if sorted(userlines[0:2]) == sorted(predlines[0:2]): #if idxs match
                predlines[2] = userlines[2] #set coupling

    print('updated coupling table for simulation')
    print(jtable)


if shiftfilepath and not pred1h:
    shifttable = shifttable_user
    print('input 1h shift table')
    print(shifttable)

if c13shiftfilepath and not pred13c:
    c13shifttable = c13shifttable_user
    print('input 13c shift table')
    print(c13shifttable)

if coupfilepath and not pred1hcoup:
    jtable = jtable_user
    print('input coupling table')
    print(jtable)  


#setup spin system
#if calc1h:
spinsys = spinsystem.spin_system().setup(shifttable, jtable, addunknownj=True)

#check isotopes in spinsys (detect 1H, use 2H for couplings only)
nmrpred.checkisotopes_inspinsys(spinsys, mol)

#calculate 1d spectrum (peaks)
calcspectrum.calc_1d_spectrum(spinsys, sfrq, center, sw, npts)

#if calc13c:
c13spinsys = spinsystem.spin_system().setup(c13shifttable, [])

calcspectrum.calc_1d_spectrum(c13spinsys, c13sfrq, c13center, c13sw, c13npts)


#write output:

#mol file: done above

if write1h:
    #shifts as table:
    with open(shiftoutputfile,'w') as csvfile:
        writer = csv.writer(csvfile,)
        for line in shifttable:
            if line[0] in mapping:
                line = [i for i in line]
                line[0] = mapping[line[0]]
            writer.writerow(map(str,line))

if write13c:
    #shifts as table:
    with open(c13shiftoutputfile,'w') as csvfile:
        writer = csv.writer(csvfile,)
        for line in c13shifttable:
            if line[0] in mapping:
                line = [i for i in line]
                line[0] = mapping[line[0]]
            writer.writerow(map(str,line))

if write1hcoup:
    #couplings as table:
    with open(couplingoutputfile,'w') as csvfile:
        writer = csv.writer(csvfile,)
        for line in jtable:
            if line[0] in mapping:
                line = [i for i in line]
                line[0] = mapping[line[0]]
            if line[1] in mapping:
                line = [i for i in line]
                line[1] = mapping[line[1]]
            writer.writerow(map(str, line))

    #formatted couplings (1H)
    #with open(formatedcouplingfile,'w') as csvfile:
    #    writer = csv.writer(csvfile,)
    #    for atom in spinsys.atoms:
    #        id = atom.id
    #        s = measurej.formatjcoupstringforatomid(jtable, id, valuesonly=valuesonly)
    #        #if not s: continue
    #        writer.writerow([str(id), s])




if writeassigntable:

    #for website table
    table = {}
    for atom in mol.GetAtoms():
        atomtype = atom.GetSymbol()
        atomno = atom.GetIdx() + 1

        table[atomno] = {'symbol':atomtype,
                         'center':'',
                         'mult':'',
                         'coup':'',}


        if atomtype == "H" and pred1h and pred1hcoup:
            #result = calcspectrum.pickpeaks_byatom(spinsys, sfrq, center, sw, npts, lw=lw, zf=zf, c=c, minimize=True)
            #for atom, shift, peakxy in result:
            #    if atom not in table: continue
            for atom in spinsys.atoms:
                atomnotest = int(atom.id)
                if atomnotest != atomno: continue
                if atom.ppm == -1 or atom.ppm == -999: continue
                if atom.ppm > center+sw/2 or atom.ppm < center-sw/2: continue
                if atomno in table:
                    table[atomno]['center'] = atom.ppm
                    table[atomno]['mult'], table[atomno]['coup'] = measurej.countjcoup(atomno, shifttable, jtable)
                    #table[atom]['mult'] = measurej.peakmult(peakxy)
                    #table[atom]['coup'] = measurej.formatjcoupstringforatomid(jtable, atom, valuesonly=valuesonly)
                    #table[atom]['coup'] = measurej.formatjcoupstringforatomid(jtable, atom, valuesonly=jflag, peakxy=peakxy, peakxysfrq=sfrq)

                #print("="*80)
                #print(atom, shift)
                #print(peakxy)
                #print(table[atom]['mult'], table[atom]['coup'])

        elif atomtype == "C" and pred13c:
            #c13result = calcspectrum.pickpeaks_byatom(c13spinsys, c13sfrq, c13center, c13sw, c13npts, lw=c13lw, zf=c13zf, c=c13c, minimize=True)
            #for atom, shift, peakxy in c13result:
            #    if atom not in table: continue
            for atom in c13spinsys.atoms:
                atomnotest = int(atom.id)
                if atomnotest != atomno: continue
                if atom.ppm == -1 or atom.ppm == -999: continue
                if atom.ppm > c13center+c13sw/2 or atom.ppm < c13center-c13sw/2: continue
                if atomno in table:
                    table[atomno]['center'] = atom.ppm
                    table[atomno]['mult'] = 's'


        else:
            continue

    #print("DEBUG")
    with open(assignmenttablefile,'w') as csvfile:
        writer = csv.writer(csvfile,)
        for atom in sorted(table.keys()):
 
            #if table[atom]['center']:
            if atom in mapping:
                atomlabel = mapping[atom]
            else:
                atomlabel = int(atom)

            writer.writerow([   table[atom]['symbol'],
                                str(atomlabel),
                                str(table[atom]['center']),
                                table[atom]['mult'],
                                table[atom]['coup']])


#dont use this, may be better and more flexible to write 1h and 13c in separate rows
#if writechsqctable:
#    #hsqcassignmenttablefile
#
#    table = {}
#    for atom in mol.GetAtoms():
#        atomtype = atom.GetSymbol()
#        atomno = atom.GetIdx() + 1
#
#        table[atomno] = {'symbol':atomtype,
#                         'center':None,
#                        }
#
#    for atom in spinsys.atoms:
#        atomno = int(atom.id)
#        if atom.ppm == -1 or atom.ppm == -999: continue
#        if atom.ppm > center+sw/2 or atom.ppm < center-sw/2: continue
#        if atomno in table:
#            table[atomno]['center'] = atom.ppm
#
#    for atom in c13spinsys.atoms:
#        atomno = int(atom.id)
#        if atom.ppm == -1 or atom.ppm == -999: continue
#        if atom.ppm > c13center+c13sw/2 or atom.ppm < c13center-c13sw/2: continue
#        if atomno in table:
#            table[atomno]['center'] = atom.ppm
#
#    with open(hsqcassignmenttablefile,'w') as csvfile:
#        writer = csv.writer(csvfile,)
#        for atom in sorted(table.keys()):
#            if table[atom]['symbol'] != "H": continue
#            if not table[atom]['center']: continue
#            neighbor = mol.GetAtomWithIdx(atom-1).GetNeighbors()
#            if neighbor[0].GetSymbol() == "C":
#                cid = neighbor[0].GetIdx()+1
#                if not table[cid]['center']: continue
#                writer.writerow([   table[atom]['symbol'],
#                                    str(int(atom)),
#                                    table[cid]['symbol'],
#                                    str(int(cid)),
#                                    str(table[atom]['center']),
#                                    str(table[cid]['center']),
#                                   ])




def plotxy(x ,y, w=8, h=2, lw=0.5, dpi=300,):
    plt.rc('axes', labelsize=4.0)
    plt.rc('xtick', labelsize=4.0)
    fig = plt.figure(figsize=(w,h), dpi=dpi)
    plt.plot(x, y, lw=lw)
    plt.ylim(0,None)
    plt.gca().invert_xaxis()
    plt.gca().set_xlabel(r'${^1}$H ppm',labelpad=0.0)
    plt.gca().tick_params(bottom=True, top=False, left=False, right=False,
                          labelbottom=True, labeltop=False, labelleft=False, labelright=False)
    plt.gca().tick_params(axis='x', direction='out', length=2.0, width=0.5, pad=0.0)
    plt.gca().spines['top'].set_visible(False)
    plt.gca().spines['left'].set_visible(False)
    plt.gca().spines['right'].set_visible(False)
    plt.gca().spines['bottom'].set_linewidth(0.5)
    return fig

def plot2d(x, y, z, lvl_min, lvl_num, lvl_mult, w=4, h=3, lw=0.5, dpi=300):

    levels = calcspectrum.calc_contour_levels(lvl_min,lvl_num,lvl_mult)

    plt.rc('axes', labelsize=8.0)
    plt.rc('xtick', labelsize=8.0)
    plt.rc('ytick', labelsize=8.0)

    fig = plt.figure(figsize=(8,6), dpi=dpi)
    plt.contour(x,y,spec,levels=levels)

    plt.gca().tick_params(axis='x', direction='out', length=4.0, width=0.5, pad=0.0)
    plt.gca().tick_params(axis='y', direction='out', length=4.0, width=0.5, pad=0.0)
    plt.gca().spines['top'].set_linewidth(0.5)
    plt.gca().spines['left'].set_linewidth(0.5)
    plt.gca().spines['right'].set_linewidth(0.5)
    plt.gca().spines['bottom'].set_linewidth(0.5)
    plt.gca().invert_xaxis()
    plt.gca().invert_yaxis()
    plt.gca().set_xlabel(r'${^1}$H ppm',labelpad=0.0)
    plt.gca().set_ylabel(r'${^{13}}$C ppm',labelpad=0.0)

    return fig


#equivalence for peaklists
if (plot1h or plot13c) and mergepeaks:
    chiral, prochiral = getequiv.getchiral(mol, tmploc=datprefix)
    stereoequiv = getequiv.findequivalentatoms(mol,chiralmap=(chiral,prochiral),unique=True)


#1H OUTPUT

if plot1h:
    #parameter file
    with open(paramfile, 'w') as p:
        p.write("spectrometer_proton_frequency_MHz: %s\n"%sfrq)
        p.write("spectrometer_nucleus: 1H\n")
        p.write("spectrometer_frequency_MHz: %s\n"%sfrq)
        p.write("fid_complex_points: %s\n"%npts)
        p.write("spectrum_real_points: %s\n"%zf)
        p.write("linewidth_Hz: %s\n"%lw)
        p.write("fid_first_point_multiplier: %s\n"%c)
        p.write("center_frequency_ppm: %s\n"%center)
        p.write("sweep_width_ppm: %s\n"%sw)
        p.write("solvent: %s\n"%solvent)

    fullx, fully = calcspectrum.plot_1d(spinsys, [], [], sfrq, center, sw, npts, lw=lw, zf=zf, c=c)
    plt.close()

    #png file
    fig = plotxy(fullx, fully)
    fig.savefig(specfile, dpi=dpi, bbox_inches='tight')
    plt.close()

    #csv file
    with open(spectrumxyfile, 'w') as csvfile:
        writer = csv.writer(csvfile,)
        for i,j in zip(fullx, fully):
            writer.writerow([str(i), str(j)])

    #fid file
    with open(fidfile, 'w') as f:
        x, y = calcspectrum.plot_1d(spinsys, [], [], sfrq, center, sw, npts, lw=lw, zf=zf, c=c, returnfid=True)
        plt.close()
        R = y.real
        I = y.imag
        for a, b, c in zip(x, R, I):
            f.write('%s,%s,%s\n'%(float(a), float(b), float(c)))

    #peaklist:
    #atomid, clustercenter, multiplicity, ppm, intensity
    if mergepeaks:
        result = calcspectrum.pickpeaks_byatom_merged(spinsys, stereoequiv, sfrq, center, sw, npts, lw=lw, zf=zf, c=c, minimize=True)
        #for atom, shift, peakxy in result:
        #    print(atom, shift, peakxy)
        with open(peaklistfile,'w') as csvfile:
            writer = csv.writer(csvfile,)
            for atom, shift, peakxy in result:
                mult_list = []
                for a in atom:
                    mult, coup = measurej.countjcoup(a, shifttable, jtable)
                    mult_list.append(mult)
                for x, y in peakxy:
                    #print(atom,shift,mult_list,x,y)
                    if mapping: 
                        #print(atom, mapping)
                        atom = [mapping[a] if a in mapping else a for a in atom]
                        #print(atom, mapping)
                    writer.writerow([",".join([str(a) for a in atom]), str(np.mean(shift)), mult_list[0], str(x), str(y)])

    else:
        result = calcspectrum.pickpeaks_byatom(spinsys, sfrq, center, sw, npts, lw=lw, zf=zf, c=c, minimize=True)
        #for atom, shift, peakxy in result:
        #    print(atom, shift, peakxy)
        with open(peaklistfile,'w') as csvfile:
            writer = csv.writer(csvfile,)
            for atom, shift, peakxy in result:
                mult, coup = measurej.countjcoup(atom, shifttable, jtable)
                for x, y in peakxy:
                    #print(atom,shift,mult_list,x,y)
                    if mapping: atom = mapping[atom] if atom in mapping else atom
                    writer.writerow([str(atom), str(np.mean(shift)), mult, str(x), str(y)])



#C13 OUTPUT
if plot13c:
    #parameters
    with open(c13paramfile, 'w') as p:
        p.write("spectrometer_proton_frequency_MHz: %s\n"%sfrq)
        p.write("spectrometer_nucleus: 13C\n")
        p.write("spectrometer_frequency_MHz: %s\n"%c13sfrq)
        p.write("fid_complex_points: %s\n"%c13npts)
        p.write("spectrum_real_points: %s\n"%c13zf)
        p.write("linewidth_Hz: %s\n"%c13lw)
        p.write("fid_first_point_multiplier: %s\n"%c13c)
        p.write("center_frequency_ppm: %s\n"%c13center)
        p.write("sweep_width_ppm: %s\n"%c13sw)
        p.write("solvent: %s\n"%solvent)

    fullx, fully = calcspectrum.plot_1d(c13spinsys, [], [], c13sfrq, c13center, c13sw, c13npts, lw=c13lw, zf=c13zf, c=c13c)
    plt.close()

    #spectrum
    with open(c13spectrumxyfile, 'w') as csvfile:
        writer = csv.writer(csvfile,)
        for i,j in zip(fullx, fully):
            writer.writerow([str(i), str(j)])

    #png
    fig = plotxy(fullx,fully)
    fig.savefig(c13specfile, dpi=dpi, bbox_inches='tight')
    plt.close()

    #fid
    with open(c13fidfile, 'w') as f:
        x, y = calcspectrum.plot_1d(c13spinsys, [], [], c13sfrq, c13center, c13sw, c13npts, lw=c13lw, zf=c13zf, c=c13c, returnfid=True)
        plt.close()
        R = y.real
        I = y.imag
        for a, b, c in zip(x, R, I):
            f.write('%s,%s,%s\n'%(float(a), float(b), float(c)))


    if mergepeaks:
        result = calcspectrum.pickpeaks_byatom_merged(c13spinsys, stereoequiv, c13sfrq, c13center, c13sw, c13npts, lw=c13lw, zf=c13zf, c=c13c, minimize=True)
        print(c13spinsys.get_ids())
        print(c13spinsys.atoms)
        print(stereoequiv)
        print(result)
        with open(c13peaklistfile,'w') as csvfile:
            writer = csv.writer(csvfile,)
            for atom, shift, peakxy in result:
                print(atom, shift, peakxy)
                for a in atom:
                    mult, coup = "s",""
                    mult_list.append(mult)
                for x, y in peakxy:
                    #print(atom,shift,mult_list,x,y)
                    if mapping: atom = [mapping[a] if a in mapping else a for a in atom]
                    writer.writerow([",".join([str(a) for a in atom]), str(np.mean(shift)), mult_list[0], str(x), str(y)])

    else:
        result = calcspectrum.pickpeaks_byatom(c13spinsys, c13sfrq, c13center, c13sw, c13npts, lw=c13lw, zf=c13zf, c=c13c, minimize=True)
        print(c13spinsys.get_ids())
        print(c13spinsys.atoms)
        print(result)
        with open(c13peaklistfile,'w') as csvfile:
            writer = csv.writer(csvfile,)
            for atom, shift, peakxy in result:
                print(atom, shift, peakxy)
                mult = "s"
                for x, y in peakxy:
                    #print(atom,shift,mult_list,x,y)
                    if mapping: atom = mapping[atom] if atom in mapping else atom
                    writer.writerow([str(atom), str(np.mean(shift)), mult, str(x), str(y)])

if plotchsqc:

    #chsqcparamfile
    with open(chsqcparamfile, 'w') as p:

        #TODO set 2d npts/zf separately from 1d

        p.write("1h_spectrometer_frequency_MHz: %s\n"%sfrq)
        p.write("1h_fid_complex_points: %s\n"%npts_f2)
        p.write("1h_spectrum_real_points: %s\n"%zf_f2)
        p.write("1h_linewidth_Hz: %s\n"%lw)
        p.write("1h_fid_first_point_multiplier: %s\n"%c)
        p.write("1h_fid_sine_apodization_offset_x_pi: %s\n"%0.5)
        p.write("1h_center_frequency_ppm: %s\n"%center)
        p.write("1h_sweep_width_ppm: %s\n"%sw)

        p.write("13c_spectrometer_frequency_MHz: %s\n"%c13sfrq)
        p.write("13c_fid_complex_points: %s\n"%npts_f1)
        p.write("13c_spectrum_real_points: %s\n"%zf_f1)
        p.write("13c_linewidth_Hz: %s\n"%c13lw)
        p.write("13c_fid_first_point_multiplier: %s\n"%c13c)
        p.write("13c_fid_sine_apodization_offset_x_pi: %s\n"%0.5)
        p.write("13c_center_frequency_ppm: %s\n"%c13center)
        p.write("13c_sweep_width_ppm: %s\n"%c13sw)

        p.write("solvent: %s\n"%solvent)

    pairs = []
    hids = spinsys.get_ids()
    cids = c13spinsys.get_ids()

    for a in mol.GetAtoms():
        if a.GetSymbol() == "H":
            n = a.GetNeighbors()[0]
            if n.GetSymbol() == "C":
                hidx = a.GetIdx()+1
                cidx = n.GetIdx()+1
                if hidx in hids and cidx in cids:
                    pairs.append((hidx,cidx))
    #chsqcsfidbin
    #this fid already has processing applied
    fid = calcspectrum.plot_1h_13c_hsqc( spinsys, c13spinsys, pairs,
            sfrq,center,sw,npts_f2,
            c13sfrq,c13center,c13sw,npts_f1,
            lw=lw,c13lw=c13lw,zf=zf_f2,c13zf=zf_f1,returnfid=True)
    fid.astype('<c16').tofile(chsqcsfidbin)

    #chsqcspecbin
    spec = calcspectrum.fft_2dfid(fid,zf_f2,zf_f1)
    spec.astype('<f8').tofile(chsqcspecbin)

    #chsqcspecimg
    x = calcspectrum.calc_ft_axis(sfrq, center, sw, zf_f2)
    y = calcspectrum.calc_ft_axis(c13sfrq, c13center, c13sw, zf_f1)
    fig = plot2d(x, y, spec, np.max(spec)/20, 20, 1.5, w=plotwidth_2d, h=plotheight_2d, lw=0.5, dpi=dpi)
    fig.savefig(chsqcspecimg, dpi=dpi, bbox_inches='tight')
    plt.close()


if plotdqfcosy:
    calcspectrum.calc_cosy(spinsys,sfrq,center,sw,256)
    x, y, z = calcspectrum.plot_2d_cosy(spinsys,sfrq,center,sw,256,zf=1024,scale=20,nlv=20,mult=1.5,zoom=True)
    plt.savefig(dqfcosyimg, dpi=dpi, bbox_inches='tight')
    plt.close()


#clean up:
if not moloutput:
    #if input and output mol files are same, dont delete
    if molfiletype == "mol" and os.path.abspath(newmolfile) == os.path.abspath(molfilepath):
        pass
    else:
        os.remove(newmolfile)

sys.exit(0)
