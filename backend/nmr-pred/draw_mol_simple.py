import sys, os, time, csv
from rdkit import Chem
from rdkit.Chem.Draw import rdMolDraw2D


"""
takes a 3d mol file 
outputs a png file (for display) and 2d mol file (for jspectraviewer)

labels atoms as element + index by default
or use csv file with index <-> labels

options
--mol           3d mol file with hydrogens (required)

--size          default: --size 1200 800
--filetype      png or svg
--write2dmol    write the 2d mol file
--outputpath    default current directory
--outputprefix  prefix for output files (default: time)
                prefix_2d.png/svg, prefix_output_2d.mol

--labels        csv file with alternate labels
--idxcol        0 (column of molecule index, ONE-INDEXED)
--labcol        1
"""

#defaults for function, reset when running as script
scriptdir = os.path.dirname(os.path.realpath(__file__)) + "/"
outputpath = scriptdir  + "results/"
outputprefix = str(time.time())


def readlabels(labelfile, indexcolumn=0, labelcolumn=1):
    labeldic = {}
    with open(labelfile) as csvfile:
        reader = csv.reader(csvfile)
        for row in reader:
            labeldic[int(row[indexcolumn])-1] = str(row[labelcolumn]) #CONVERT TO ZERO INDEXED FOR RDKIT
    return labeldic


def draw_mol(mol, size=(1200,800),atomdic={}, 
             outputpath=outputpath, outputprefix=outputprefix, filetype='png', write2dmol=False):

    #copy molecule
    mol = Chem.Mol(mol)

    #hack for wedge bonds/stereochemistry on hydrogens

    for a in mol.GetAtoms():
        if a.GetAtomicNum() == 1:
            a.SetAtomicNum(2)

    Chem.AssignAtomChiralTagsFromStructure(mol)
    Chem.AssignStereochemistry(mol, False, True)

    for a in mol.GetAtoms():
        if a.GetAtomicNum() == 2:
            a.SetAtomicNum(1)


    #hack to clean up drawing of ambiguous double bonds

    for b in mol.GetBonds():
        if b.GetBondDir() == Chem.BondDir.EITHERDOUBLE:
            b.SetBondDir(Chem.BondDir.NONE)
        if b.GetStereo() == Chem.BondStereo.STEREOANY:
            b.SetStereo(Chem.BondStereo.STEREONONE)

    #addChiralHs does nothing if H already added
    #comment this line out for 3d view
    mol = rdMolDraw2D.PrepareMolForDrawing(mol, kekulize=True, addChiralHs=True, wedgeBonds=True, forceCoords=True)


    if filetype == 'png':
        drawer = rdMolDraw2D.MolDraw2DCairo(*size)
        filesuffix = '.png'
    if filetype == 'svg':
        drawer = rdMolDraw2D.MolDraw2DSVG(*size)
        filesuffix = '.svg'

    #drawer.SetFontSize(0.35)
    drawer.SetFontSize(0.33)
    opts = drawer.drawOptions()
    opts.updateAtomPalette({1:(0.5,0.5,0.5)}) #grey hydrogens
    opts.flagCloseContactsDist = False
    opts.additionalAtomLabelPadding = 0.05
    opts.maxFontSize = -1
    opts.annotationFontScale = 0.5
    #opts.clearBackground = True


    for i in atomdic:
        opts.atomLabels[i] = atomdic[i]

    print('writing image file')

    if filetype == 'png':
        drawer.DrawMolecule(mol)
        drawer.FinishDrawing()
        drawer.WriteDrawingText(outputpath + outputprefix + "_2d" + filesuffix)
    if filetype == 'svg':
        drawer.DrawMolecule(mol)
        #drawer.TagAtoms(mol)
        drawer.FinishDrawing()
        svgout = drawer.GetDrawingText()
        with open(outputpath + outputprefix + "_2d" + filesuffix, 'w') as f:
            f.write(svgout)

    if write2dmol:
        print('writing 2d mol file')
        Chem.rdmolfiles.MolToMolFile(mol, outputpath + outputprefix + "_2d.mol")

    return


if __name__ == "__main__":

    molfilepath = None

    outputpath = "./"
    outputprefix = str(time.time())
    size = (1200,800)
    filetype = 'png'
    write2dmol = False

    labels = None
    idxcol = 0
    labcol = 1 

    for i in range(len(sys.argv)):

        if sys.argv[i] == '--mol':
            molfilepath = sys.argv[i+1]
            print('input mol:',molfilepath)
            if not os.path.isfile(molfilepath): 
                raise ValueError('molecule file not found')

        if sys.argv[i] == "--outputpath": outputpath = sys.argv[i+1]
        if sys.argv[i] == "--outputprefix": outputprefix = sys.argv[i+1] #write to prefix_*.png
        if sys.argv[i] == "--size": size = (int(sys.argv[i+1]), int(sys.argv[i+2]))

        if sys.argv[i] == "--filetype": filetype = sys.argv[i+1]
        if sys.argv[i] == "--write2dmol": write2dmol = True #write to prefix_output_2d.mol

        if sys.argv[i] == "--labels": labels = sys.argv[i+1]
        if sys.argv[i] == "--idxcol": idxcol = int(sys.argv[i+1])
        if sys.argv[i] == "--labcol": labcol = int(sys.argv[i+1])

    #read mol file
    if molfilepath is None:
        raise ValueError('mol file is required')

    try:
        mol = Chem.MolFromMolFile(molfilepath,removeHs=False)
    except Exception as e:
        raise ValueError('error reading mol file')
    

    try:
        if not mol.GetConformer().Is3D():
            print('warning: molecule is not 3d')
    except Exception as e:
        print('warning: no 3d coordinates')


    hflag = False
    for a in mol.GetAtoms():
        if a.GetAtomicNum() == 1:
            hflag = True

    if not hflag:
        print('warning: molecule has no hydrogens') 



    if outputpath:
        if not outputpath.endswith("/"):
            outputpath = outputpath + "/"


    print('output path:', outputpath)
    print('output prefix:', outputprefix)


    atomlabeldic = {"sym_idx":{}}

    for a in mol.GetAtoms():

        idx = a.GetIdx()
        sym = a.GetSymbol()
        chg = a.GetFormalCharge()
        chgstr = ""
        if chg:
            if chg == 1:
                chgstr = "+"
            elif chg == -1:
                chgstr = "-"
            elif chg > 1:
                chgstr = "+"+str(chg)
            elif chg < 1:
                chgstr = str(chg)

        iso = a.GetIsotope()
        if iso and sym != "H": sym = str(iso)+sym
        if iso == 2: sym = "D"
        if iso == 3: sym = "T"

        atomlabeldic["sym_idx"][idx] = "%s%d"%(sym, idx+1)


    if labels:
        print('reading label file:', labels)
        labelstouse = readlabels(labels, indexcolumn=idxcol, labelcolumn=labcol)
    else:
        labelstouse = atomlabeldic["sym_idx"]


    draw_mol(mol, size, atomdic=labelstouse, outputpath=outputpath, outputprefix=outputprefix, filetype=filetype, write2dmol=write2dmol)


