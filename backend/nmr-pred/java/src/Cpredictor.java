/* compile */
/* Unix: javac -classpath predictorc.jar:cdk-interfaces.jar:cdk-io.jar:cdk-core.jar:cdk-data.jar:cdk-standard.jar:cdk-sdg.jar:cdk-valencycheck.jar:vecmath1.2-1.14.jar:jgrapht.jar Test.java */
/* Windows: "C:\Program Files (x86)\Java\jdk1.7.0_72\bin\javac.exe" -classpath predictorc.jar;cdk-interfaces.jar;cdk-io.jar;cdk-core.jar;cdk-data.jar;cdk-standard.jar;cdk-sdg.jar;cdk-valencycheck.jar;vecmath1.2-1.14.jar;jgrapht.jar Test.java */

/* use */
/* Unix: java -classpath predictorc.jar:cdk-interfaces.jar:cdk-io.jar:cdk-core.jar:cdk-data.jar:cdk-standard.jar:cdk-sdg.jar:cdk-valencycheck.jar:vecmath1.2-1.14.jar:jgrapht.jar:. PredictorTest myfile.mol [no3d] */
/* Windows: "C:\Program Files (x86)\Java\jre7\bin\java.exe" -classpath predictorc.jar;cdk-interfaces.jar;cdk-io.jar;cdk-core.jar;cdk-data.jar;cdk-standard.jar;cdk-sdg.jar;cdk-valencycheck.jar;vecmath1.2-1.14.jar;jgrapht.jar;. Test myfile.mol [no3d] */
import java.io.FileReader;

import java.util.Locale;

import org.openscience.cdk.DefaultChemObjectBuilder;
import org.openscience.cdk.AtomContainer;
import org.openscience.cdk.aromaticity.CDKHueckelAromaticityDetector;
import org.openscience.cdk.interfaces.IAtom;
import org.openscience.cdk.interfaces.IAtomContainer;
import org.openscience.cdk.io.MDLReader;
import org.openscience.cdk.io.MDLV2000Writer;
import org.openscience.nmrshiftdb.PredictionTool;
import org.openscience.nmrshiftdb.util.AtomUtils;
import org.openscience.cdk.geometry.GeometryUtil;

public class Cpredictor {

        public static void main(String[] args) throws Exception {
                if(args.length == 0){
                        /* file name as command line argument */
                        java.lang.System.exit(1);
                }
                /* if second argument is no3d, we do not use extended hose code generator */
                boolean use3d = true;
                if(args.length==2 && args[1].equals("no3d"))
                    use3d=false;
                /* get mol file reader (for 1 structure) from file name */
                MDLReader mdlreader = new MDLReader(new FileReader(args[0]));
                /* get molecule for reader */
                AtomContainer mol = (AtomContainer)mdlreader.read(DefaultChemObjectBuilder.getInstance().newInstance(IAtomContainer.class));
                GeometryUtil.translateAllPositive(mol);

                /* !!! No explicit H in mol !!! */
                /* add explicit H atoms */
                /* ASSUME H ADDED BY RDKIT ALREADY */
                /* AtomUtils.addAndPlaceHydrogens(mol); */
                /* detect aromaticity */
                CDKHueckelAromaticityDetector.detectAromaticity(mol);

                /* print mol file with added hydrogens*/
                /*                MDLV2000Writer writer = new MDLV2000Writer(System.out);
                                writer.write((IAtomContainer)mol);
                                System.out.println("");
                */

                /* current atom and current result */
                float[] result;
                IAtom curAtom;
                /* get predictor */
                PredictionTool predictor = new PredictionTool();
                /* write header 
                                System.out.println("  C    min.    mean    max.");
                                System.out.println(" ---------------------------");
                */
                /* write header 
                                System.out.println("NMRSHIFTDB_STANDALONE");*/
                /* loop over atoms in mol */
                for(int i = 0 ; i < mol.getAtomCount() ; i++){
                        curAtom = mol.getAtom(i);
                        if(curAtom.getAtomicNumber() == 6) {
                                /* only for C atoms */
                                result = predictor.predict(mol, curAtom, use3d);
                                /* show result */
                                System.out.format(Locale.US, "%3d%8.2f%8.2f%8.2f\n", i+1, result[0], result[1], result[2]);
                        }
                }
                java.lang.System.exit(0);
        }
}
