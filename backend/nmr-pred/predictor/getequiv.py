from rdkit.Chem import AllChem as Chem
import numpy as np
import time
import os
import subprocess
import copy

"""
note: rdkit canonical ranking gives unintuitively high ranking to isotopes

Chem.FindMolChiralCenters(m,useLegacyImplementation=False)
is a port of centers.jar to rdkit (>= 2020.09.01)
"""

scriptdir = os.path.dirname(os.path.realpath(__file__)) + "/"
#print('DEFAULT GETEQUIV PATHDIR',scriptdir)

def getequiv(mol):
    #find chemically equivalent groups with rdkit
    #try to account for resonance
    #does not see stereochemistry, stereotopicity,
    #returns zero-indexed as dict

    mol = Chem.Mol(mol)
    mol_noh = Chem.RemoveHs(mol)

    #any match is ok
    mol_match = mol.GetSubstructMatch(mol_noh,useChirality=True)
    molmap = {i.GetIdx():j for i,j in zip(mol_noh.GetAtoms(),mol_match)}
    
    #brute force match including resonance
    suppl = Chem.ResonanceMolSupplier(mol_noh,)
    allmatches = []
    for m in suppl:
        #need to fix bond types before matching
        for b in m.GetBonds():
            if b.GetIsAromatic():
                b.SetBondType(Chem.BondType.AROMATIC)
        matches = m.GetSubstructMatches(mol_noh,uniquify=False)
        allmatches.extend(matches)
    allmatches = np.array(allmatches).T
    
    atomidxs = [ None for i in range(mol.GetNumAtoms())]
    for i in range(mol_noh.GetNumAtoms()):
        
        #remap
        origidx = molmap[i]
        atomidxs[origidx] = np.array([molmap[idx] for idx in np.unique(allmatches[i])])

        #add attatched H
        alln = []
        for a in atomidxs[origidx]:
            neigh = mol.GetAtomWithIdx(int(a)).GetNeighbors()
            for n in neigh:
                if n.GetSymbol() == "H" and (not n.GetIsotope() or n.GetIsotope() == 1):
                    alln.append(n.GetIdx())
        for a in alln:
            if atomidxs[a] is None:
                atomidxs[a] = np.array(alln)
    
    return atomidxs

def getequiv_forordering(mol, rank):
    #simple equivalence for numbering
    #rank = Chem.CanonicalRankAtoms(mol,includeChirality=False,breakTies=False)
    rank = np.array(list(rank))
    equivmap = {}
    for a in mol.GetAtoms():
        idx = a.GetIdx()
        i = rank[idx]
        equivmap[idx] = np.where(rank == i)[0]
    return equivmap

def getchiralidx(s):
    #for getchiral
    r = []
    for c in s:
        if c.isdigit() or c == "-":
            r.append(c)
        else:
            break
    return int("".join(r))

def getchiralval(s):
    #for getchiral
    r = []
    flg = False
    for c in s:
        if not flg and c.isalpha():
            flg = True
        if flg:
            r.append(c)
    return "".join(r)

def replaceamidebond(mol):
    #make O=CN >> [O-]C=[N+] so non-rotatable, except urea
    amide = Chem.MolFromSmarts("[C,$(S=O)](=[OX1,SX1])-[NX3]")
    urea = Chem.MolFromSmarts("[C,$(S=O)](=[OX1,SX1])(-[NX3])-[NX3]")
    match = mol.GetSubstructMatches(amide)
    ureamatch = mol.GetSubstructMatches(urea)
    for m in match:
        umatch = False
        for u in ureamatch:
            if u[0] == m[0]: umatch = True
        if umatch: continue
        #print(match)
        cobond = mol.GetBondBetweenAtoms(m[0],m[1]).SetBondType(Chem.BondType.SINGLE)
        oatom = mol.GetAtomWithIdx(m[1]).SetFormalCharge(-1)
        cnbond = mol.GetBondBetweenAtoms(m[0],m[2]).SetBondType(Chem.BondType.DOUBLE)
        natom = mol.GetAtomWithIdx(m[2]).SetFormalCharge(+1)
    return
    

def getchiral(mol,scriptpath=scriptdir+"../java/",scriptname='centres.jar',tmploc=scriptdir+'../results/'):

    #runs java program for labeling stereochemistry
    #newer rdkit may have ported verion
    #tests all atoms for prochirality and labels result
    #returns zero-indexed

    #copy mol
    mol = Chem.Mol(mol)
    replaceamidebond(mol)
    
    #mol file header (moleucle name)
    mol.SetProp("_Name","-1")
    mollist = [mol]

    #ranks for filtering chiral
    rank = list(Chem.CanonicalRankAtoms(mol,breakTies=False))

    methyl_ids = []
    chiral_ids = []

    for i in range(mol.GetNumAtoms()):
        #do this for all atoms, even if not written later
        #so isotopes are all consistent
        curratm = mol.GetAtomWithIdx(i)

        if curratm.GetIsotope():
            curriso = curratm.GetIsotope()
        else:
            curriso = int(round(curratm.GetMass()))

        #do this so isotopes work XC[1H][2H][2H]
        curratm.SetIsotope(2*curriso)

    #add isotope label
    for i in range(mol.GetNumAtoms()):


        sym = mol.GetAtomWithIdx(i).GetSymbol()

        #neighbors of heavy atom
        if sym == "H":
            neigh = mol.GetAtomWithIdx(i).GetNeighbors()
            neigh = neigh[0].GetNeighbors()
        else:
            neigh = mol.GetAtomWithIdx(i).GetNeighbors()

        n_rank = [rank[n.GetIdx()] for n in neigh]
        u, c = np.unique(n_rank,return_counts=True)

        #skip hydrogen on chiral heavy atoms (add back later)
        if u.size >= 4 and sym == "H": 
            chiral_ids.append(i)
            continue

        #skip XH3 hydrogen (add back later)
        if sym == "H" and 3 in c:
            r = u[list(c).index(3)]
            n = neigh[n_rank.index(r)]
            sym_n = n.GetSymbol()
            if sym_n == "H":
                methyl_ids.append(i)
                continue

        newmol = Chem.Mol(mol) #makes a copy
        curratm = newmol.GetAtomWithIdx(i)
        curriso = curratm.GetIsotope()
        curratm.SetIsotope(curriso+1) #substitution test
        newmol.SetProp("_Name",str(i))

        mollist.append(newmol)

    #temp file
    RAND = str(time.time())
    tmpfile = tmploc + RAND + "_mol_isotopes.sdf"

    w = Chem.SDWriter(tmpfile)
    for m in mollist:
        w.write(m)
    w.close()

    #python3    
    import os
    #print("CURRENT DIR",os.getcwd())
    #print(scriptpath+scriptname)
    output = subprocess.run(["java", "-jar", scriptpath+scriptname, tmpfile],
                            stdout=subprocess.PIPE, universal_newlines=True,)
    table = output.stdout
    os.remove(tmpfile)

    #parse output
    table = table.split('\n')
    for i in range(len(table)):
        #print(table[i])
        table[i] = table[i].replace("\r","")
        table[i] = table[i].split()


    inputchirality = table[0][:-1]
    inputchirality = {getchiralidx(item) - 1: getchiralval(item) for item in inputchirality}

    #list all chiral centers for each substituted atom
    prochirality = table[1:-1]
    chiralmap = {}
    for line in prochirality:
        idx = int(line[-1])
        chiralmap[idx] = {}
        for item in line[:-1]:
            k = getchiralidx(item) - 1
            v = getchiralval(item)
            chiralmap[idx][k] = v


    for i in chiral_ids:
        chiralmap[i] = copy.deepcopy(inputchirality)
    for i in methyl_ids:
        n = mol.GetAtomWithIdx(i).GetNeighbors()[0]
        chiralmap[i] = chiralmap[n.GetIdx()]
            
    return inputchirality, chiralmap


def sortchiralbydist(k,d,mol,fororder=False):
    #distance and priority values for sorting
    idx = k
    dic = d
    priority = {"R":1,      "M":1,
                "S":2,      "P":2,
                "r":3,      "m":3,
                "s":4,      "p":4,
                "Z":5,      "E":6,
                "seqCis":7, "seqTrans":8}
    r = []
    for d in dic:
        if d == idx:
            dist = 0
        else:
            dist = len(Chem.GetShortestPath(mol,idx,d))
        if dic[d] in priority:
            pri = priority[dic[d]]
            if fororder: r.append((dist,pri,idx))
            else: r.append((dist,pri))
        else:
            if fororder: r.append((dist,999,idx))
            else: r.append((dist,999))
    if not r:
        if fororder: return [(999,999,idx)]
        else: return [(999,999)]
    return sorted(r)

def bydistinv(v,fororder=False):
    #swap ouput of sortchiralbydist eg R<>S, M<>P
    r = []
    swap = {1:2,2:1,
            3:3,4:4,
            5:5,6:6,7:7,8:8,0:0,999:999}
    for i in v:
        if fororder:r.append((i[0],swap[i[1]],i[2]))
        else:r.append((i[0],swap[i[1]],))
        
    return r


def findequivalentatoms(mol,unique=True,scriptpath="java/",scriptname='centres.jar',tmploc='results/',chiralmap=None):
    #inclucds stereochemistry/diastereotopicity
    #probably does not see 3d chirality
    #molecule must be 3d, with hydrogens
    #return ONE-indexed
    
    equiv = getequiv(mol)

    #allow for passing in if run already
    if not chiralmap:
        inp, chiral = getchiral(mol,scriptpath=scriptpath,scriptname=scriptname,tmploc=tmploc)
    else:
        inp, chiral = chiralmap


    for i,e in enumerate(equiv):

        if e.size == 1: 
            equiv[i] = equiv[i] + 1  #CONVERT BACK TO 1INDEXED
            continue

        #only continue with prochiral 
        if {c:chiral[i][c] for c in chiral[i] if c not in inp or inp[c] != chiral[i][c]}:
            pass
        else:
            equiv[i] = equiv[i] + 1  #CONVERT BACK TO 1INDEXED
            continue

        notequiv = []
        for j in e:
            if j != i:
                j = int(j)

                chiral_i = sortchiralbydist(i,chiral[i],mol,)
                chiral_j = sortchiralbydist(j,chiral[j],mol,)
                chiral_jrev = bydistinv(chiral_j,)
                if chiral_i != chiral_j and chiral_i != chiral_jrev:
                    notequiv.append(j)

        equiv[i] = np.setdiff1d(equiv[i],notequiv) + 1 #CONVERT BACK TO 1INDEXED
        

    if unique:
        equiv = sorted(set([tuple(e) for e in equiv]))
        
    return equiv

def getcanonical(mol, stereo, chiral):

    #stereo(prochiral), chiral: pass in so only run once

    #try to get a canonical numbering
    #using rdkit canonical rank + resonance (getchiral())
    #and sorting using chirality/prochirality
    #atoms still equivalent after chirality (probably) stay in same order as input

    #does NOT account for resonance
    
    #copy mol
    mol = Chem.Mol(mol)
    
    #order is affected by stero flags
    Chem.AssignAtomChiralTagsFromStructure(mol)
    Chem.AssignStereochemistry(mol)

    
    #ignoring resonance, chirality
    rank = list(Chem.CanonicalRankAtoms(mol, includeChirality=False, breakTies=False))
    equiv = getequiv_forordering(mol, rank)

    #print("START",rank)

    #sort by chirality first
    finalrank = []
    for i in range(len(rank)):
        #print("\nATOM",i)
        r = rank[i]
        e = equiv[i]
        #print("RANK",r, "EQUIVIDX", e)
        s = [sortchiralbydist(int(e), {c:chiral[c] for c in chiral},mol,fororder=True) for e in equiv[i]]
        #print(s)
        s_tr = [tuple(z[y] for y in range(2) for z in x) for x in s]
        #print(s_tr)
        s_srt = sorted(s_tr)
        #print(s_srt)
        si = [s_tr.index(a) for a in s_srt]
        ei = list(e).index(i)
        #print(si, ei)
        finalrank.append(r + max(si)-si[ei])

    #print("CHIRL",finalrank)

    rank = finalrank
    equiv = getequiv_forordering(mol, rank)

    finalrank = []
    #sort by pro-chirality
    for i in range(len(rank)):
        #print("\nATOM",i)
        r = rank[i]
        e = equiv[i]
        #print("RANK",r, "EQUIVIDX", e)
        #for sorting: the closer prochiral center considered first 
        s = [sortchiralbydist(int(e), {c:stereo[e][c] for c in stereo[e] if c not in chiral or chiral[c] != stereo[e][c]},mol,fororder=True) for e in equiv[i]]
        #print(s)
        #'transpose' for sorting
        s_tr = [tuple(z[y] for y in range(2) for z in x) for x in s]
        #print(s_tr)
        #sort by 1. distance 2. priorty of stereoceners
        s_srt = sorted(s_tr)
        #print(s_srt)
        #rearrange ranks based on sorted order
        si = [s_tr.index(a) for a in s_srt]
        ei = list(e).index(i)
        #print(si, ei)
        #higher index after sort: lower priority
        finalrank.append(r + max(si)-si[ei])

    #print("PROCH",finalrank)


    #break remaining ranks
    rank = finalrank
    equiv = getequiv_forordering(mol, rank)
    finalrank = []
    for i in range(len(rank)):
        r = rank[i]
        e = equiv[i]
        finalrank.append(r + len(e)-list(e).index(i)-1)

    #print("FINAL",finalrank)

    return finalrank



def getcanonical2(mol, stereo, chiral):
    #for using round trip smiles...
    #non-1H atoms remain in rdkit canonical smiles order
    #attatched atoms are sorted if prochiral

    #copy mol
    mol = Chem.Mol(mol)
    
    #order is affected by stero flags
    Chem.AssignAtomChiralTagsFromStructure(mol)
    Chem.AssignStereochemistry(mol)
    
    #only hydrogens will be reordered
    equiv = []
    for a in mol.GetAtoms():
        if a.GetSymbol() == "H" and a.GetIsotope() <= 1:
            neigh = a.GetNeighbors()[0] #heavy atom
            neigh = neigh.GetNeighbors() #neighbors of heavy atom
            ids = [n.GetIdx() for n in neigh if n.GetSymbol() == "H" and n.GetIsotope() <= 1]
            equiv.append(ids)
        else:
            equiv.append([a.GetIdx()])

    rank = [equiv[::-1].index(i) for i in equiv]
    #print("START",rank)
    

    finalrank = []
    #sort by pro-chirality
    for i in range(len(rank)):
        #print("\nATOM",i)
        r = rank[i]
        e = equiv[i]
        #print("RANK",r, "EQUIVIDX", e)
        #for sorting: the closer prochiral center considered first 
        s = [sortchiralbydist(int(e), {c:stereo[e][c] for c in stereo[e] if c not in chiral or chiral[c] != stereo[e][c]},mol,fororder=True) for e in equiv[i]]
        #print(s)
        #'transpose' for sorting
        s_tr = [tuple(z[y] for y in range(2) for z in x) for x in s]
        #print(s_tr)
        #sort by 1. distance 2. priorty of stereoceners 3. atom index
        s_srt = sorted(s_tr)
        #print(s_srt)
        #rearrange ranks based on sorted order
        si = [s_tr.index(a) for a in s_srt]
        ei = list(e).index(i)
        #print(si, ei)
        #higher index after sort: lower priority
        finalrank.append(r + max(si)-si[ei])
    #print("PROCH",finalrank)

    #break remaining ranks
    rank = finalrank
    equiv = getequiv_forordering(mol, rank)
    finalrank = []
    for i in range(len(rank)):
        r = rank[i]
        e = equiv[i]
        finalrank.append(r + len(e)-list(e).index(i)-1)
    #print("FINAL",finalrank)
    return finalrank

def getchiralflags(mol):
    #similar output to old function for prochiral flags for ML
    inp, chiral = getchiral(mol)
    frompriority = {1:1,2:2,3:1,4:2,5:7,6:8,7:7,8:8,0:0,999:999}
    flags = []

    for c in chiral:
        for i in inp:
            if i in chiral[c]:
                del chiral[c][i]

    for k in chiral:
        if not chiral[k]: 
            flags.append(0)
            continue

        closest = sortchiralbydist(k,chiral[k],mol)
        flag = frompriority[closest[0][1]]
        flags.append(flag)

    return flags

if __name__ == "__main__":

    import sys

    scriptdir = os.path.dirname(os.path.realpath(__file__)) + "/"
    JAVADIR = scriptdir+'../java/'
    OUTPUTDIR = scriptdir+'../results/'

    if len(sys.argv) > 1:
        INPUT = sys.argv[1]
        #print('='*80)
        #print(INPUT)

        if INPUT.endswith('.mol') or INPUT.endswith('.sdf'):
            #input mol must be 3d, with hydrogens
            mol = Chem.MolFromMolFile(INPUT,removeHs=False)
            if mol is None: 
                print('error parsing mol file')
                sys.exit()            
        else:
            mol = Chem.MolFromSmiles(INPUT,sanitize=False)
            if mol is None: 
                print('error parsing smiles')
                sys.exit()
            Chem.SanitizeMol(mol)

            #smiles = Chem.MolToSmiles(mol)
            #mol = Chem.MolFromSmiles(smiles)

            mol = Chem.AddHs(mol)
            Chem.EmbedMolecule(mol)
    else:
        #mol = Chem.MolFromSmiles('NC(CCC(=O)N)C(=O)C') #non equiv CH2 Hs, non-equiv NH2
        #mol = Chem.MolFromSmiles('FC1CCCCC1') #non equiv CH2 Hs, mirror sym
        #mol = Chem.MolFromSmiles('C(O)(C(C(C)(C))(C(C)(C)))(C(C(C)(C))(C(C)(C)))') #4 non equiv CH3 pairs, mirror sym
        #mol = Chem.MolFromSmiles('C(O)(C(C)(C))(C(C)(C))') #2 non equiv CH3 pairs, mirror sym
        mol = Chem.MolFromSmiles('c1ccccc1') #6-fold symm
        #mol = Chem.MolFromSmiles('[NH3+][C@H](C(C)(C))C(=O)[O-]') #valine, CH3 non equiv
        #mol = Chem.MolFromSmiles('[NH3+][C@H]([C@H](CC)(CC))C(=O)[O-]') #each CH2 H and CH3 non equiv
        #mol = Chem.MolFromSmiles('C(=O)([O-])[C@@H](C(CC)(CC))[NH3+]') #each CH2 H and CH3 non equiv
        #mol = Chem.MolFromSmiles('[NH3+][C@H](C12)C(=O)[O-].C1C.C2C') #each CH2 H and CH3 non equiv
        #mol = Chem.MolFromSmiles('C2C.C1C.[NH3+][C@H](C12)C(=O)[O-]') #each CH2 H and CH3 non equiv
        #mol = Chem.MolFromSmiles('[N+]C(Cc1ccccc1)C(=O)[O-]') #tyrosine, side chain symmetric
        #mol = Chem.MolFromSmiles('CCO') #ethanol, non chiral
        #mol = Chem.MolFromSmiles('CC([Cl])F') #chiral
        #mol = Chem.MolFromSmiles('C1[C@H](O)C[C@H](O)C[C@H](O)1') #3 fold symm
        #mol = Chem.MolFromSmiles('O=C([O-])([O-])') #carbonate, 3 fold
        #mol = Chem.MolFromSmiles('c1c[nH+]c[nH]1') #imimdazole, 2 fold symmetric
        #mol = Chem.MolFromSmiles('NC(=O)N(C)(C)') #N,N-dimethylurea, should NH2, CH3 be equivalent???
        #mol = Chem.MolFromSmiles('C1([Br])([Br])CC1(C[Cl])(C[Cl])') #CH2Cl are diast., ring CH2 are not
        #mol = Chem.MolFromSmiles('F[C@](O)C[C@@](O)F') #rotationally symmetric
        #mol = Chem.MolFromSmiles('C1CCCC(=O)C1C(Sc1ccccc1)(Sc1ccccc1)') #diastereotopic phenlys
        #mol = Chem.MolFromSmiles('C(C)(F)=C') #cis trans CH2
        #mol = Chem.MolFromSmiles('[H]-C#C-[H]',sanitize=False); Chem.SanitizeMol(mol) #forced atom order
        #mol = Chem.MolFromSmiles('C12C3C4C1C5C2C3C45') #cubane, slow with embed molecule
        #mol = Chem.MolFromSmiles('[NH2+]=C/C=C/C=C/[NH2]') #resonance, will not show NMR equiv (which does not accout for resonance) correctly
        #mol = Chem.MolFromSmiles('[NH2]/C=C/C=C/C=[NH2+]') #resonance
        #mol = Chem.MolFromSmiles("[C@H](CF)(C)CC(O)C[C@H](CF)(C)") #chiral and prochiral
        #mol = Chem.MolFromSmiles("[C@H](CF)(C)CC(O)C[C@@H](CF)(C)") #chiral and prochiral
        #mol = Chem.MolFromSmiles("[C@@H](CF)(C)CC(O)C[C@H](CF)(C)") #chiral and prochiral
        #mol = Chem.MolFromSmiles("[C@@H](CF)(C)CC(O)C[C@@H](CF)(C)") #chiral and prochiral
        #mol = Chem.MolFromSmiles("O[C@H](CC)(C([2H])C)") #isotopic
        mol = Chem.AddHs(mol)
        Chem.EmbedMolecule(mol)

        #print(mol)


    #Chem.MolToMolFile(mol, OUTPUTDIR + 'original.mol')

    equiv = getequiv(mol)
    #print('symmetry classes')
    #for i,e in enumerate(equiv):
    #    print(i, mol.GetAtomWithIdx(i).GetSymbol(), e)

    chiral, prochiral = getchiral(mol, scriptpath=JAVADIR,tmploc=OUTPUTDIR)
    #print('chirality')
    #print(chiral)
    #for i in prochiral:
    #    print(i, mol.GetAtomWithIdx(i).GetSymbol(), prochiral[i])
    
    stereoequiv = findequivalentatoms(mol,chiralmap=(chiral,prochiral),unique=False)
    #print('chemically equivalent classes')    
    #for i,e in enumerate(stereoequiv):
    #    print("%s %s %s"%(i,mol.GetAtomWithIdx(int(e[0]-1)).GetSymbol(),e))

    #print equiv each line
    equivlist = [",".join([str(int(i)) for i in e]) for e in stereoequiv]
    for e in sorted(set(equivlist)):
        print(e)

    #newrank = getcanonical(mol, prochiral, chiral)
    #newrank = getcanonical(mol, prochiral, chiral)
    #neworder = [newrank.index(i) for i in range(mol.GetNumAtoms()-1,-1,-1)]
    #for a in mol.GetAtoms():
    #    a.SetProp('oldidx',str(a.GetIdx()))
    #newmol = Chem.RenumberAtoms(mol, neworder)

    #print('chirality',chiral)
    #print('mapping:','newidx', 'sym', 'oldidx', 'nmrequiv(oldidx)', 'chiral(oldidx)')
    #for a in newmol.GetAtoms():
    #    old = int(a.GetProp('oldidx'))
    #    print(a.GetIdx(), a.GetSymbol(), old, stereoequiv[old]-1, {c:prochiral[old][c] for c in prochiral[old] if c not in chiral or chiral[c] != prochiral[old][c]})

    #Chem.MolToMolFile(newmol, OUTPUTDIR + 'canonicalorder.mol')

