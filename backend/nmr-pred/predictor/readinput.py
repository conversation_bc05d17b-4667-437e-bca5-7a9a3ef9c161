from rdkit.Chem import AllChem as Chem
from rdkit.Chem import rdMolTransforms

"""
reads *.mol file or smiles string to rdkit
add hydgrogens, check  geometry
and output to mol file
"""


def readinput(inputtype,inputstring,**kwargs):
    #returns rdk molecule from filename or string
    readerlist = {'mol':Chem.MolFromMolFile,
                  'pdb':Chem.MolFromPDBFile,
                  'molstring':Chem.MolFromMolBlock,
                  'pdbstring':Chem.MolFromPDBBlock,
                  'smiles':Chem.MolFromSmiles,
                  'inchi':Chem.inchi.MolFromInchi,
                  #'smarts':Chem.MolFromSmarts,
                  }
    
    #default args
    args = {'mol':{"removeHs":False},
            'pdb':{"removeHs":False},
            'molstring':{"removeHs":False},
            'pdbstring':{"removeHs":False},
            'smiles':{},
            'inchi':{},
            #'smarts':{},
            }
                  
    reader = readerlist[inputtype]
    args[inputtype].update(kwargs)
    
    return reader(inputstring,**args[inputtype])


def writemol(rdkmol,outputtype,outputfile=None,**kwargs):
    writerlist = {'mol':Chem.MolToMolFile,
                  'pdb':Chem.MolToPDBFile,
                  'molstring':Chem.MolToMolBlock,
                  'pdbstring':Chem.MolToPDBBlock,
                  'smiles':Chem.MolToSmiles,
                  }
                  
    writer = writerlist[outputtype]
    
    if outputfile:
        return writer(rdkmol,outputfile,**kwargs)
    else:
        return writer(rdkmol,**kwargs)

def cleanupgeometry(rdkmol,addh=False,embed=False,optimize=False,optimize_attempts=1,optimize_restr=5.0,optimize_scale=1e5):
    
    mol = rdkmol
    
    if addh: #returns a new molecule
        mol = Chem.AddHs(mol)

    #if input is 3d use the coordinates
    Chem.AssignStereochemistryFrom3D(mol)
    #just in case...
    Chem.AssignAtomChiralTagsFromStructure(mol)
    Chem.AssignStereochemistry(mol)

    if embed: #make 3d
        try:
            confid = Chem.EmbedMolecule(mol, clearConfs=False, maxAttempts=100)
            mol.GetConformer()
        except:
            print('inital 3d embedding failed, trying again...')

            try:
                confid = Chem.EmbedMolecule(mol, clearConfs=False, maxAttempts=100, useRandomCoords=True)
                mol.GetConformer()
            except Exception as e2:
                print("error with 3d embedding:", e2)
                return mol, -1

    if optimize:

        #with restraints so chiral centers don't invert
        stereorestraints = getstereoconf(mol,mol.GetConformer())

        if mol.GetNumConformers() < optimize_attempts:
            Chem.EmbedMultipleConfs(mol, clearConfs=False, maxAttempts=1, numConfs= optimize_attempts - mol.GetNumConformers())
        
        energies = []
        cids = [c.GetId() for c in mol.GetConformers()]
        for c in cids:
            ff = Chem.MMFFGetMoleculeForceField(mol, Chem.MMFFGetMoleculeProperties(mol), confId=c)
            if not ff:
                print('something went wrong with forcefield generation, stop optimization')
                return mol, -1

            for line in stereorestraints:
                ff.MMFFAddTorsionConstraint(line[0],line[1],line[2],line[3],
                                            False,line[4]-optimize_restr,line[4]+optimize_restr,optimize_scale)
            ff.Initialize()
            result = ff.Minimize(maxIts=1000)
            e = ff.CalcEnergy()

            if matchstereoconf(mol,mol.GetConformer(c),stereorestraints,tol=optimize_restr):
                energies.append((result,e))
            else:
                energies.append((result,float('inf')))

        minidx = energies.index(min(energies))
        for i,c in enumerate(cids):
            if i != minidx:
                mol.RemoveConformer(c)
            else:
                mol.GetConformer(c).SetId(0)

    #do again incase something changed (eg 2d->3d)
    Chem.AssignStereochemistryFrom3D(mol)
    Chem.AssignAtomChiralTagsFromStructure(mol)
    Chem.AssignStereochemistry(mol)

    return mol, -1

def getstereoconf(mol,conf):
    match_tetr = Chem.MolFromSmarts("[X4]")
    match_pyra = Chem.MolFromSmarts("[X3;^3]")
    match_trig = Chem.MolFromSmarts("[X3;^2;!a]=[X3;^2;!a]")
    
    dihed = []
    
    for match in mol.GetSubstructMatches(match_tetr):
        a = mol.GetAtomWithIdx(match[0])
        #if a.GetChiralTag(): continue
        n = a.GetNeighbors()
        ijkl = [a.GetIdx(), n[0].GetIdx(), n[1].GetIdx(), n[2].GetIdx()]
        d = rdMolTransforms.GetDihedralDeg(conf, *ijkl)
        dihed.append(ijkl + [d,"TET"])

    for match in mol.GetSubstructMatches(match_pyra):
        a = mol.GetAtomWithIdx(match[0])
        n = a.GetNeighbors()
        ijkl = [a.GetIdx(), n[0].GetIdx(), n[1].GetIdx(), n[2].GetIdx()]
        d = rdMolTransforms.GetDihedralDeg(conf, *ijkl)
        dihed.append(ijkl + [d,"PYR"])
        
    for match in mol.GetSubstructMatches(match_trig):
        a = mol.GetAtomWithIdx(match[0])
        a2 = mol.GetAtomWithIdx(match[1])
        n = [atm for atm in a.GetNeighbors() if atm.GetIdx() != a2.GetIdx()]
        n2 = [atm for atm in a2.GetNeighbors() if atm.GetIdx() != a.GetIdx()]
        if len(n) < 2 or len(n2) < 2: continue # skip C=C=C
        ijkl = [n[0].GetIdx(),match[0],match[1],n2[0].GetIdx()]
        ijkl2 = [n[1].GetIdx(),match[0],match[1],n2[1].GetIdx()]
        d = rdMolTransforms.GetDihedralDeg(conf, *ijkl)
        d2 = rdMolTransforms.GetDihedralDeg(conf, *ijkl2)
        dihed.append(ijkl + [d,"TRI"])
        #dihed.append(ijkl2 + [d])
        
    return dihed

def matchstereoconf(mol,conf,ref,tol=5.0):
    flg = True #if ref blank return True
    for line in ref:
        d_ref = line[4]
        d = rdMolTransforms.GetDihedralDeg(conf, *line[:4])
        if abs((d-d_ref+180)%360-180) > tol:
            #print("FAIL",conf.GetId(),line,d)
            flg = False
        else: 
            #print("PASS",conf.GetId(),line,d)
            pass
    return flg



