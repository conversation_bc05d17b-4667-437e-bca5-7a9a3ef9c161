#from rdkit import Chem
import numpy as np
from collections import OrderedDict
from rdkit.Chem import AllChem as Chem
from rdkit.Chem import rdMolTransforms
from rdkit.Chem import rdDistGeom

"""
functions for calculating jhh
coupling is zero for H coupled to other nuclei
"""


PRINTFN = False
DEBUG = False #prints table
DEBUGSKIP = False
DEBUGFUN = False
DEBUGDIC = False
DEBUGZERO = False
DEBUGFILE = "" #for a specific file

#https://www.chem.wisc.edu/areas/reich/nmr

###adjustemnts relative to CH4 (-12.4)
table_2jhh_sp3_lin = OrderedDict([
                                  ("[C;!R]C#N",-4),
                                  ("[C;!R][Si]",-2),
                                  ("[C;!R][OX2]",+1),
                                  ("[C;!R][F,Cl,Br,I]",+2),
                                  ("[C;!R][NH2,NH3+]",-4),
                                  ("[C;!R][$(C(=O)[OH]),$(C(=O)[O-])]",-3),
                                  ("[C;!R][C]=O",-2),
                                  ("[C;!R][^2;!$(C(=O))]",-1),
                                  ("[C;!R][CX4][NH2,NH3+]",-2),
                                  ])

###relative to -CH2- (-12.4 Hz)
table_2jhh_sp3_ring = OrderedDict([
                                   ("[C;r3][O]",+17),
                                   ("[C;r3][N]",+14),
                                   ("[C;r3][S]",+12),
                                   ("[C;r3][$([C;r3][Cl])]",+3),
                                   ("[C;r3][C;r3][$([C;r3][Cl])]",+3),
                                   ("[C;r3][C;r3;^2]=[C;r3;^2]",+11/2),
                                   ("[C;r3][C;r3][C;r3]",+10/2),

                                   ("[C;r4][O]",+6),
                                   ("[C;r4][S]",+4),
                                   ("[C;r4][N;R2]",+9),
                                   ("[C;r4][#6;^2]",-4),
                                   ("[C;r4;R2][C]",+10/2),

                                   ("[C;r5][O]C=O",0),
                                   ("[C;r5][O]",+6),
                                   ("[C;r5][N;^3]",+3),
                                   ("[C;r5][CH2;r5][$(O);r5]",+4),
                                   ("[C;r5][C]=O",-6),
                                   ("[C;r5]([^2])[^2]",-5),

                                   ("[C;r7][^2]",-1),

                                   ("[C;!r3;!r4;!r5;!r7][O]",+3),
                                   ("[C;!r3;!r4;!r5;!r7][#6;^2]",-3),
                                  ])


###relative to C2H4 (2.5 hz)
table_2jhh_sp2 = OrderedDict([    ("[C]=O",+37.5), #formaldehyde

                                  ("[C]=N-O",+6),
                                  ("[C]=N=N",+2),
                                  ("[C]=N-[CH0]",+14), #N-tert-butylmethanimine

                                  ("[C]=C=O",-17.8), #ketene
                                  ("[C]=C-[OH]",+0),
                                  ("[C]=C-[OH0]",-4),

                                  ("[C]=C-[Si]",+1),
                                  ("[C]=C-C#N",-1.5),

                                  ("[C]=C-S",-2),
                                  ("[C]=C-N",-4),

                                  ("[C]=C-[F]",-5),
                                  ("[C]=C-[Cl,I,Br]",-3),

                                  ("[C]=C=[CH2]",-10), #propyl diene
                                  ("[C]=C-c",-2),
                                  ("[C]=C-C",0),
                                  ])


##
##table_4jhh_aro = {"[c][c;!$(c[F,O]);!cH0][c][c][F,O]":-0.4, #ortho/para
##                  "[c][c;!$(c[F,O]);!cH0][c][c][c][F,O]":+0.4/2, #both meta, matches twice
##                  "[c][$(c[F,O])][c]":+1.3, #both ortho
##                  "[c][$(c[N])][c]":+1.0, #both ortho
##                  }
##           

table_4jhh_arom = OrderedDict([
                                ("[c][H]",0),
                                ("[c][C,O]",0.5),
                                ("[c][N]",1.0),
                                ("[c][F]",1.5),
                                ])

table_3jhh_arom = OrderedDict([
                              ("[c][n,o]",-3),
                              ("[c][s]",-1),
                              ])

#relative to CC=CC
table_3jhh_sp2_trans_2 = OrderedDict([
                                      ("[$(C=C)]([H])[H]",+1),
                                      ("[$(C=C)]F",-4),
                                      ("[$(C=C)]O",-2),
                                      ("[$(C=C)][Cl]",-2),
                                      ("[$(C=C)][Br]",-1),
                                      ("[$(C=C)][I]",-1),
                                      ("[$(C=C)][C;^3]",0),
                                      ("[$(C=C)][C;^2]",+1),
                                      ("[$(C=C)][N]",-1),
                                      ("[$(C=C)][Si]",+4),
                         ])

table_3jhh_sp2_cis_2 =   OrderedDict([
                                      ("[$(C=C)][$([N+](=O)([O-]))]",-4),
                                      ("[$(C=C)]C=[CH2]",-3),

                                      ("[$(C=C)]([H])[H]",+1),
                                      ("[$(C=C)]F",-5),
                                      ("[$(C=CO)]O",-3/2),
                                      ("[$(C=C)]O",-3),
                                      ("[$(C=C)][Cl]",-2),
                                      ("[$(C=C)][Br]",-2),
                                      ("[$(C=C)][I]",-2),
                                      ("[$(C=C)][C;^3]",0),
                                      ("[$(C=C)][C;^2]",+1),
                                      ("[$(C=C)][Si]",+4),
                         ])


lambda_d2o = OrderedDict([  ("C[#1]",0.00),
                            
                            ("CC#N",0.33),
                            ("C[CX2]",0.33),
                            
                            ("CC(=O)[OH,O-]",0.41),
                            ("CC(=O)[OX2H0,N]",0.42),
                            ("CC(=O)",0.50),
                            ("Cc",0.45),
                            ("C[CX3]",0.35),

                            ("CC(C)F",0.62),
                            ("CC(C)[Cl]",0.85),
                            ("CC(C)[Br]",0.92),
                            ("CC(C)I",1.02),

                            ("CCF",0.65),
                            ("CC[Cl]",0.82),
                            ("CC[Br]",0.92),
                            ("CCI",0.99),
                            
                            ("C[CH0]",0.48),
                            ("C[CH1]",0.62),
                            ("C[CH2]",0.72),
                            ("C[CH3]",0.80),
                            ("C[CX4]",0.48),

                            ("CS",0.75),
                            ("CI",0.63),
                            ("C[Br]",0.80),
                            ("C[Cl]",0.94),
                            ("CF",1.37),
                            
                            ("Cn1cnc2cncnc12",0.56),
                            ("Cn1cccnc1",0.56),
                            
                            ("C[NH0]C(=O)",0.52),
                            ("C[NH1]C(=O)",0.81),
                            ("C[$([N](=O)=O),$([N+](=O)[O-])]",0.77),
                            ("C[NH0]",1.00),
                            ("C[NH1]",1.02),
                            ("C[NH3+,NH2]",0.82),

                            ("COC=O",1.16),
                            ("COS(O)(O)(O)",1.19),
                            ("COP(O)(O)(O)",1.25),
                            ("C[OH]",1.25),
                            ("C[OH0]",1.26),
                            ("CO[c,n]",1.34),

                            ("C*",0.00),
                            ])




def calc2jhh(mol,conf,path):
    atoms = [mol.GetAtomWithIdx(i) for i in path]
    types = [str(a.GetHybridization()) for a in atoms]
    rings = [a.IsInRing() for a in atoms]
    
    middle = mol.GetAtomWithIdx(path[1])
    middleid = middle.GetIdx()
    middletype = str(middle.GetHybridization())
    middlering = middle.IsInRing()

    neigh = get_neigh_ids(mol,middleid,path)
    
    angle = rdMolTransforms.GetAngleDeg(conf,*path)

    jval = 0
    if middletype == "SP3":
        jval = -12.4

        #substituent effects
        if middlering:
            pass
            for n in neigh:
                jval += get_adj_from_dic(mol,middleid,n,table_2jhh_sp3_ring)

            #2jhh changes with bond angle
            #print("ANGLE",angle)
            #jval += (angle - 107)/(114.35 - 107) * (-4.3 - -12.4)
            #jval += (angle - 107)/(115 - 107) * (-4.3 - -12.4)
            #if angle > 112: jval += 10

        if not middlering:
            for n in neigh:
                jval += get_adj_from_dic(mol,middleid,n,table_2jhh_sp3_lin)

        if DEBUGZERO: jval = 0

    if middletype == "SP2":
        jval = +2.5 
        for n in neigh:
            jval += get_adj_from_dic(mol,middleid,n,table_2jhh_sp2)

        if DEBUGZERO: jval = 0

    return jval


def ringsize(atomobj, maxsize):
    #return if atom is in a ring of size up to maxsize
    for i in range(3,maxsize+1):
        if atomobj.IsInRingSize(i):
            return True
    return False


def calc4jhh(mol,conf,path): #+/- signs may not be accurate

    types = [str(mol.GetAtomWithIdx(p).GetHybridization()) for p in path]
    bondords = [mol.GetBondBetweenAtoms(*i).GetBondTypeAsDouble() for i in zip(path[0:],path[1:])]
    aromatic = np.array([mol.GetAtomWithIdx(i).GetIsAromatic() for i in path[1:-1]])
    aromaticids = path[1:-1]
    middleneigh = get_neigh_ids(mol,path[2],path)[0]

    dihed_d = rdMolTransforms.GetDihedralDeg(conf,path[0],path[1],path[3],path[4])

    methyls = mol.GetSubstructMatches(Chem.MolFromSmarts("[CH3]([H])([H])([H])")) #may want to avg over methyls
    inring = [mol.GetAtomWithIdx(p).IsInRing() for p in path]
    inringsize = [ringsize(mol.GetAtomWithIdx(p), 6) for p in path]

    jval = None

    if DEBUGZERO: return jval

    if np.all(aromatic): #HcccH
        jval = 1.4 # probably positive (angle = 0 in garbisch eqn)
        jval += get_adj_from_dic(mol,path[2],middleneigh,table_4jhh_arom)
        return jval
        #jval += adjust4jfromdict(mol,aromaticids,table_4jhh_aro)

    if np.sum(aromatic) == 2: #HCccH
        #jval = -1.0
        jval = 0
        return jval
    
    for match in mol.GetSubstructMatches(Chem.MolFromSmarts("cc(c)c"),uniquify=False): #Hc-c(c)-cH aromatic fused rings
        if path[1:3] == match[0:2]:
            #jval = -0.5
            jval = 0
            return jval
##
##    if len([t for t in types if t == "SP"]) == 1: #HC=C=CH
##        jval = -6
##        return jval
##
##    if len([t for t in types if t == "SP"]) == 2: #HC#C#CH
##        jval = -2
##        return jval
##
##
    if not np.any(aromatic) and len([t for t in types if t == "SP2"]) == 2 and "SP" not in types and 2.0 in bondords: #HCC=CH, not HC=C=CH
        jval = garbisch_allyl_4j(dihed_d)
        return jval

    if 0 <= np.abs(dihed_d) < 15 and len([t for t in types if t == "SP3"]) == 3 and path[1] not in [a[0] for a in methyls] and path[-2] not in [a[0] for a in methyls]: #SP3 "w' path
        #jval = -0.5
        jval = 0
        return jval

    # H-CH3-C-CH in a ring or  HC-C-CH in a ring (ring < 6)
    if (inringsize[1] and inringsize[2] and inringsize[3]) or (path[1] in [a[0] for a in methyls] and inringsize[2] and inringsize[3]) or (path[3] in [a[0] for a in methyls] and inringsize[2] and inringsize[1]): 
        jval = 0
        return jval

    #if True:
    #    return 0

    return jval


def calc5jhh(mol,conf,path): #+/- signs may not be accurate

    types = [str(mol.GetAtomWithIdx(p).GetHybridization()) for p in path]
    aromatic = np.array([mol.GetAtomWithIdx(i).GetIsAromatic() for i in path[1:-1]])
    aromaticids = path[1:-1]
    types = [str(mol.GetAtomWithIdx(p).GetHybridization()) for p in path]

    dihed_d = rdMolTransforms.GetDihedralDeg(conf,path[0],path[1],path[4],path[5])

    jval = None

    if DEBUGZERO: return jval

    if np.all(aromatic): #HcccH
        jval = 0.5 #seems positive for eg tyrosine
        return jval
    
    #HC-C=C-CH
    if types[2] == "SP2" and types[3] == "SP2" and not aromatic[2] and not aromatic[3]:
        jval = 0
        return jval

    #if True:
    #    return 0

    return jval

##def adjust4jfromdict(mol,aromaticids,dic):
##    heavy1,heavy2,heavy3 = aromaticids
##    jadj = 0
##    for string in dic:
##        patt = Chem.MolFromSmarts(string)
##        for match in mol.GetSubstructMatches(patt):
##            if (
##                (heavy1 == match[0] and heavy2 == match[1] and heavy3 == match[2])
##                or
##                (heavy1 == match[2] and heavy2 == match[1] and heavy3 == match[0])
##                ):
##                jadj += dic[string]
##    return jadj
    
    

def calc3jhh(mol,conf,path,force_noaverage=False):
    if force_noaverage:
        averaging = False
    else:
        averaging = True
        
    centerid1 = path[1]
    centerid2 = path[2]
    center1 = mol.GetAtomWithIdx(path[1])
    center2 = mol.GetAtomWithIdx(path[2])
    
    bond12 = mol.GetBondBetweenAtoms(path[1],path[2])
    bondord = bond12.GetBondTypeAsDouble()
    bondring = bond12.IsInRing()
    
    hyb1 = str(center1.GetHybridization())
    hyb2 = str(center2.GetHybridization())

    dihed_d = rdMolTransforms.GetDihedralDeg(conf,*path)
    dihed_r = dihed_d/360.0*2.0*np.pi
    angle1 = rdMolTransforms.GetAngleDeg(conf,*path[:3])
    angle2 = rdMolTransforms.GetAngleDeg(conf,*path[1:])
    angle = angle1 + angle2 - 180

    
    jval = 0

    if bondord == 2.0:
        neigh1 = get_neigh_ids(mol,centerid1,path)
        neigh2 = get_neigh_ids(mol,centerid2,path)

        if 175 < abs(dihed_d) < 185: #trans
            jval = 15
            for n in neigh1:
                jval += get_adj_from_dic(mol,centerid1,n,table_3jhh_sp2_trans_2)
            for n in neigh2:
                jval += get_adj_from_dic(mol,centerid2,n,table_3jhh_sp2_trans_2)

        if -5 < dihed_d < 5: #cis
            jval = 11
            for n in neigh1:
                jval += get_adj_from_dic(mol,centerid1,n,table_3jhh_sp2_cis_2)
            for n in neigh2:
                jval += get_adj_from_dic(mol,centerid2,n,table_3jhh_sp2_cis_2)

        if bondring:
            jval *= 1 / 2**(max(0,angle-60)/15) #ring adjustment
            #jval += -0.206*((angle1+angle2)/2 - 110) #imai 1990 angle adjustment term

        if DEBUGZERO: jval = 0

##  for aromatic
    if bondord == 1.5:
        neigh1 = get_neigh_ids(mol,centerid1,path)
        neigh2 = get_neigh_ids(mol,centerid2,path)
        jval = 7.5
        for n in neigh1:
            jval += get_adj_from_dic(mol,centerid1,n,table_3jhh_arom)
        for n in neigh2:
            jval += get_adj_from_dic(mol,centerid2,n,table_3jhh_arom)
        jval *= 1 / 2**((angle-60)/15) #ring adjustment
        #jval *= 1 / 2**(max(0,angle-60)/15)

        if DEBUGZERO: jval = 0

    #rings
    if bondord == 1.0 and bondring:
        
        if hyb1 == "SP3" and hyb2 == "SP3":
            #altona 2007
            neigh, signs = getneighborsigns(mol,conf,path)
            if neigh is None: return 0

            parents = [path[1],path[1],path[2],path[2]]
            coeff = [get_atona_coeff(mol,p,n,lambda_d2o) for p,n in zip(parents,neigh)]
            jval = diez_altona_3j(dihed_r,*coeff)
            #jval *= 1 / 2**(max(0,angle-60)/15) #ring adjustment
            jval += -0.206*((angle1+angle2)/2 - 110) #imai 1990 angle adjustment term
            
        if (hyb1 == "SP2") ^ (hyb2 == "SP2"): #vinyl
            #if 0 <= abs(dihed_d) < 90:
            #    jval = 6.6*np.cos(dihed_r)**2 + 2.6*np.sin(dihed_r)**2
            #if 90 <= abs(dihed_d) < 180:
            #    jval = 11.6*np.cos(dihed_r)**2 + 2.6*np.sin(dihed_r)**2
            
            jval = garbisch_vinyl_3j(dihed_d) #not much different than above
            jval *= 1 / 2**(max(0,angle-60)/15) #ring adjustment

        if hyb1 == "SP2" and hyb2 == "SP2": #diene
            if 0 <= abs(dihed_d) < 90:
                jval = 6.6*np.cos(dihed_r)**2 + 2.6*np.sin(dihed_r)**2
            if 90 <= abs(dihed_d) < 180:
                jval = 11.6*np.cos(dihed_r)**2 + 2.6*np.sin(dihed_r)**2
            
            jval -= 1 # for the extra double bond nearby
            jval *= 1 / 2**((angle-60)/15) #ring adjustment

        if DEBUGZERO: jval = 0

    #not rings
    if bondord == 1.0 and not bondring:
        
        if hyb1 == "SP3" and hyb2 == "SP3":
            #altona 2007, first term
            neigh, signs = getneighborsigns(mol,conf,path)
            parents = [path[1],path[1],path[2],path[2]]
            coeff = [get_atona_coeff(mol,p,n,lambda_d2o) for p,n in zip(parents,neigh)]
            jval = diez_altona_3j(dihed_r,*coeff,averaged=averaging)
                    
        if (hyb1 == "SP2") ^ (hyb2 == "SP2"): #vinyl
            jval = 7
            for match in mol.GetSubstructMatches(Chem.MolFromSmarts("O=[CH1]-C-C")):
                if centerid1 in match[1:3] and centerid2 in match[1:3]:
                    jval = 1.3

        if hyb1 == "SP2" and hyb2 == "SP2": #diene
            jval = 11
            for match in mol.GetSubstructMatches(Chem.MolFromSmarts("O=[CH1]-[CH1]=C")):
                if centerid1 in match[1:3] and centerid2 in match[1:3]:
                    jval = 7

        if DEBUGZERO: jval = 0

    return jval


def allpairs(mol,cutoff=(1,5),filter=("H","H")):
    #higher atm number comes first in path: HH, PH, FH

    atomlist = [a.GetSymbol() for a in mol.GetAtoms()]
    atomlist = np.array(atomlist,dtype=str)
    filt1 = np.in1d(atomlist,filter[0])
    filt2 = np.in1d(atomlist,filter[1])
    mask = np.outer(filt1, filt2).astype(bool)

    mat = Chem.GetDistanceMatrix(mol).astype(np.int)
    mat[mat < cutoff[0]] = 0
    mat[mat >= cutoff[1]] = 0
    mat[~mask] = 0

    if filter[0] == filter[1]:
        mat = np.maximum(mat,mat.T)
        mat = np.triu(mat)

    idx = np.argwhere(mat)

    for i in idx:

        dist = mat[i[0],i[1]]
        path = Chem.GetShortestPath(mol,int(i[0]),int(i[1]))
        anum = [mol.GetAtomWithIdx(a).GetAtomicNum() for a in path]
        elem = [mol.GetAtomWithIdx(a).GetSymbol() for a in path]
        #deg = [mol.GetAtomWithIdx(a).GetDegree() for a in path]
        #if dist >= 3:
        #    ang = [Chem.GetAngleDeg(mol.GetConformer(),*path[i:i+3]) for i in range(len(path)-2)]
        #else:
        #    ang = []

        if anum[0] < anum[-1]:
            path = path[::-1]
            elem = elem[::-1]
            #deg = deg[::-1]
            #ang = ang[::-1]

        yield dist, path, elem,

def getneighborsigns(mol,conf,ids):

    a2ind = [a.GetIdx() for a in mol.GetAtomWithIdx(ids[1]).GetNeighbors() if a.GetIdx() not in ids]
    a3ind = [a.GetIdx() for a in mol.GetAtomWithIdx(ids[2]).GetNeighbors() if a.GetIdx() not in ids]

    if len(a2ind) != 2 or len(a3ind) != 2:
        #raise ValueError("only for saturated HCCH")
        print("WARNING inconsistent structure, skipping")
        return None, None

    c1 = np.array(conf.GetAtomPosition(ids[0]))
    c2 = np.array(conf.GetAtomPosition(ids[1]))
    c3 = np.array(conf.GetAtomPosition(ids[2]))
    c4 = np.array(conf.GetAtomPosition(ids[3]))

    n2 = np.cross(c2-c1,c3-c2)
    v2 = [np.array(conf.GetAtomPosition(a))-c2 for a in a2ind]
    s2 = [+1 if np.dot(v,n2) > 0 else -1 for v in v2]
    
    n3 = np.cross(c4-c3,c3-c2)
    v3 = [np.array(conf.GetAtomPosition(a))-c3 for a in a3ind]
    s3 = [+1 if np.dot(v,n3) > 0 else -1 for v in v3]

    if s2[0] < 0:
        s2 = s2[::-1]
        a2ind = a2ind[::-1]

    if s3[0] < 0:
        s3 = s3[::-1]
        a3ind = a3ind[::-1]

    return a2ind+a3ind, s2+s3


def get_atona_coeff(mol,parent,neigh,dic):
    for smarts in dic:
        patt = Chem.MolFromSmarts(smarts)
        matches = mol.GetSubstructMatches(patt,uniquify=False)
        for match in matches:
            if (parent,neigh) == match[0:2]:
                if DEBUGDIC: print("MATHCED",smarts)
                return dic[smarts] #only first match

def get_neigh_ids(mol,parent,path):
    n = [a.GetIdx() for a in mol.GetAtomWithIdx(parent).GetNeighbors()]
    n_uniq = set(n) - set(path)
    return list(n_uniq)

def get_adj_from_dic(mol,parent,neigh,dic,matchall=False):
    #DEBUGDIC = True
    #if DEBUGDIC: print(parent,neigh,path,dic)
    if DEBUGDIC: print(parent,neigh,dic)
    val = 0
    for smarts in dic:
        patt = Chem.MolFromSmarts(smarts)
        matches = mol.GetSubstructMatches(patt,uniquify=False)
        if DEBUGDIC: print("PATTERN",smarts,len(matches))
        for match in matches:
            #if DEBUGDIC:  print("FOUND", fname, match, path, parent, neigh, smarts)
            if DEBUGDIC:  print("FOUND",  match,  parent, neigh, smarts)
            if (parent,neigh) == match[0:2]:
                #if DEBUGDIC:  print("MATCHED", fname,  path, parent, neigh, smarts)
                if DEBUGDIC:  print("MATCHED",  match,  parent, neigh, smarts)
                val += dic[smarts]
                if matchall is False: return val #only first match
    return val


def predictjcoup(mol,conf):

    jarray = []

    for d,path,names in allpairs(mol,cutoff=(2,6),filter=("H","H")):
       
        j = None

        if d == 2:
            if names == ["H","C","H"]:
                j = calc2jhh(mol,conf,path)
        if d == 3:
            if names == ["H","C","C","H"]:
                j = calc3jhh(mol,conf,path)
        if d == 4:
            if names == ["H","C","C","C","H"]:
               j = calc4jhh(mol,conf,path)
        if d == 5:
            if names == ["H","C","C","C","C","H"]:
               j = calc5jhh(mol,conf,path)

        if j is None: 
            continue

        if mol.GetAtomWithIdx(path[0]).GetIsotope() == 2: j *=  6.536 / 42.577478518
        if mol.GetAtomWithIdx(path[-1]).GetIsotope() == 2: j *=  6.536 / 42.577478518
    
        jarray.append([path[0]+1,path[-1]+1,j])

    for d,path,names in allpairs(mol,cutoff=(3,5),filter=("P","H")):
        
        j = None
        
        deg = mol.GetAtomWithIdx(path[0]).GetDegree()
        
        if deg == 4 and (d == 3 or d ==4) and names[1:-1] == ["O","C"]:
            j = 7

        if j == None: continue
        jarray.append([path[0]+1,path[-1]+1,j])

    for d,path,names in allpairs(mol,cutoff=(1,5),filter=("F","H")):

        j = None

        if d == 2:
            j = 50
        if d == 3:
            j = 10
        if d == 4:
            j = 5
            
        if j == None: continue
        jarray.append([path[0]+1,path[-1]+1,j])
        
    return np.array(jarray)

karplus_cos2x_coeff = {"HCCH":[7.80,-1,5.6]} #generic (ethane)

karplus_sqcos_coeff = {"HNCH":[1.5,-1.3,6.7], #peptides?
                       "HNCH":[0.4,-1.1,9.4], #nac-sugars?
                       "POCH":[1.6,-6.1,15.3], #phosphate not phosphine
                       }

def karplus_cos2x(x,a=7.8,b=-1,c=5.6,p=0):
    #x = x * 2*np.pi/360.0
    return a + b*np.cos(x+p) + c*np.cos(2*x+p)

def karplus_sqcos(x,a,b,c,p=0):
    #x = x * 2*np.pi/360.0
    return a + b*np.cos(x+p) + c*np.cos(x+p)**2 


def diez_altona_3j(phi,l1,l2,l3,l4,averaged=False):

    #phi = phi * 2*np.pi/360.0 #input degrees -> radians
    
    L = np.array([l1,l2,l3,l4])
    S = np.array([+1,-1,+1,-1])
    
    c00 = 7.41
    c01 = -0.59 * np.sum(L)
    c012 = -0.27 * (l1*l2+l3*l4)
    c0 = c00 + c01 + c012

    if averaged: return c0

    c10 = -0.85 
    c1 = c10 * np.cos(phi)

    c20 = 6.84
    c21 = -0.87 * np.sum(L)
    c214 = 0.29 * (l1*l4+l2*l3) 
    c2 = (c20 + c21 + c214) * np.cos(2*phi)

    c30 = -0.10 
    c3 = c30 * np.cos(3*phi)
    
    s211 = 0.71 * np.sum(S*L**2) 
    s2 = s211 * np.sin(2*phi)

    return c0 + c1 + c2 + s2

def thibaudeau_HCCF_3j(phi,l1,l2,l3,l4,a1,a2):
    #phi = phi * 2*np.pi/360.0 #input degrees -> radians
    a1 = a1 * 2*np.pi/360.0
    a2 = a2 * 2*np.pi/360.0
    
    L = np.array([l1,l2,l3,l4])
    S = np.array([+1,-1,+1,-1])
    
    j = 40.61*np.cos(phi)**2 - 4.22*np.cos(phi) + 5.88
    j2 = np.sum(L * (-1.27 -6.20*np.cos(S*phi + 0.20*L)**2 ))
    j3 = -3.72*((a1+a2)/2-110)*np.cos(phi)**2
    
    return j + j2 + j3

def provasi_FCCF_3j(phi):
    #phi = phi * 2*np.pi/360.0 #input degrees -> radians
    c = np.array([-7.761,22.945,3.887,9.245,3.241,0.588,-0.155,-0.242])
    i = np.arange(8)
    return np.sum( c * np.cos(i*phi) )
    

def garbisch_vinyl_3j(phi):
    phi_deg = phi
    phi = phi * 2*np.pi/360.0 #input degrees -> radians
    #HC-CH=C
    if -90 <= phi_deg <= 90:
        j = 6.6*np.cos(phi)**2 + 2.6*np.sin(phi)**2
    else:
        j = 11.6*np.cos(phi)**2 + 2.6*np.sin(phi)**2
    return j

def garbisch_allyl_4j(phi):
    phi_deg = phi
    phi = phi * 2*np.pi/360.0 #input degrees -> radians
    #HCC=CH
    if -90 <= phi <= 90:
        j = 1.3*np.cos(phi)**2 - 2.6*np.sin(phi)**2
    else:
        j = -2.6*np.sin(phi)**2
    return j


if __name__ == "__main__":
    import sys
    import glob
    import os
    import matplotlib.pyplot as plt
    #import readinput
    import csv

    
    fpatt = "jcoupdata/data/train/*/*.txt"
    flist = glob.glob(fpatt)

    table = []
    data = []

    fig = plt.figure()

    for fname in flist:
        head,tail = os.path.split(fname)
        molfile = tail[:-4] + ".mol"
        
        if PRINTFN: print(molfile)
        if DEBUGFILE and molfile != DEBUGFILE: continue

        molpath = os.path.join(head, molfile)

        jtable = np.atleast_2d(np.loadtxt(fname))
        
        try:
            #mol = readinput.readinput('mol',molpath)
            mol = Chem.MolFromMolFile(molpath,removeHs=False)
        except OSError:
            continue

        confid = -1

        for line in jtable:
            atm1 = int(line[0])
            atm2 = int(line[1])
            jexp = line[2]

            try:
                path = Chem.GetShortestPath(mol,atm1-1,atm2-1)
            except:
                raise ValueError(" ".join(["error in mol file:",molfile,atm1,atm2,jexp]))

            elem = [mol.GetAtomWithIdx(a).GetSymbol() for a in path]
            jcalc = 0

            #if len(path) == 5 and elem[0] == "H" and elem[-1] == "H":
            #    jcalc = calc4jhh(mol,mol.GetConformer(confid),path)
            #    if jcalc == 0: 
            #        if DEBUGSKIP: print( fname, molfile, atm1, atm2, jexp, jcalc, path, "SKIPPED")
            #        continue
            if len(path) == 4 and elem == ["H","C","C","H"]:
                jcalc = calc3jhh(mol,mol.GetConformer(confid),path)
                if jcalc == 0: 
                    if DEBUGSKIP: print( fname, molfile, atm1, atm2, jexp, jcalc, path, "SKIPPED")
                    continue
            if len(path) == 3 and elem == ["H","C","H"]:
                jcalc = calc2jhh(mol,mol.GetConformer(confid),path)
                if jcalc == 0: 
                    if DEBUGSKIP: print( fname, molfile, atm1, atm2, jexp, jcalc, path, "SKIPPED")
                    continue

            if jcalc == 0: 
                if DEBUGSKIP: print( fname, molfile, atm1, atm2, jexp, jcalc, path, "SKIPPED (NOT HC...CH)")
                continue

            if DEBUG: print( fname, molfile, atm1, atm2, jexp, jcalc, path)

            data.append([jexp,jcalc])

    data = np.array(data)

    plt.scatter(data[:,0],data[:,1],marker='.')

    plt.plot([-20,20],[-20,20])

    plt.show()

    
        
    
