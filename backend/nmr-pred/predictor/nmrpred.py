from rdkit.Chem import AllChem as Chem
import numpy as np
import subprocess
from . import predictj


def predictshifts(filename,scriptpath="java/",scriptname='Hpredictor.sh'):
    print('running',scriptpath+scriptname)
    #takes *.mol file as input
    #atoms are 1-indexed
    #output of -1 usually indicates no prediction
    predtbl = subprocess.run(["sh", scriptpath+scriptname, scriptpath, filename, "no3d"],
                            stdout=subprocess.PIPE, universal_newlines=True,
                            stderr=subprocess.DEVNULL, #hide cdk warnings
                            )

    table = predtbl.stdout.split('\n')
    table = [line.split() for line in table if line]
    table = np.array(table,dtype=float)
    
    print("   index\t     min\t    mean\t     max")
    for a,b,c,d in table:
        print("%8d\t%8.2f\t%8.2f\t%8.2f"%(a,b,c,d))
    
    if table.size:
        return table[:,[0,2]] #only keep index and mean shift
    else:
        return table


def predictjcoup(rdkmol,confid=-1):
    #returns atom1, atom2, jcoupling
    #output is 1-indexed
    try:
        table = predictj.predictjcoup(rdkmol,rdkmol.GetConformer(confid))
    except Exception as e:
        print('error with jcoupling prediction, skipping:', e)
        return []
    print(    "    atm1\t    atm2\t   jcoup")
    if table.size == 0:
        print('    NO  J  COUPLINGS  FOUND')
        return table
    for a,b,c in table[:,:3]:
        print("%8d\t%8d\t%8.2f"%(a,b,c))
    return table


def filterexchangableH(rdkmol,shifttable,jtable,filteramide=False):
    matchOH = "[$([H][O])]"
    hydroxyl = rdkmol.GetSubstructMatches(Chem.MolFromSmarts(matchOH))
    hydroxyl_ind = [i+1 for i in np.array(hydroxyl).flat]
    print("hydroxyl OH found:",*hydroxyl_ind)
    print("removing")
    
    #matchNH = "[$([H][N;^3;!$(NC=O)]),$([H][N;^2;!$(NC=O)])]"
    if filteramide: matchNH = "[$([H][N])]" #all amine/amide removed
    else: matchNH = "[$([H][N;!$(NC=O)])]" #amides kept

    amino = rdkmol.GetSubstructMatches(Chem.MolFromSmarts(matchNH))
    amino_ind = [i+1 for i in np.array(amino).flat]
    print("amino/aromatic NH found:",*amino_ind)
    print("removing")

    if len(shifttable):
        mask = ~ np.array([i[0] in hydroxyl_ind + amino_ind for i in shifttable])
        newshifttable = shifttable[mask]
    else:
        newshifttable = np.array([])
    
    if len(jtable):
        mask1 = np.array([i[0] in hydroxyl_ind + amino_ind for i in jtable])
        mask2 = np.array([i[1] in hydroxyl_ind + amino_ind for i in jtable])
        mask = ~ np.logical_or(mask1,mask2)
        newjtable = jtable[mask]
    else:
        newjtable = np.array([])
    
    return newshifttable,newjtable

def averagemethylcoupling(rdkmol,shifttable,jtable,keepzeros=True):

    if len(jtable) == 0:
        return jtable

    matchCH3 = "[CH3]([H])([H])[H]"
    methyl = rdkmol.GetSubstructMatches(Chem.MolFromSmarts(matchCH3))
    methyl = [i[1:] for i in methyl]
    #check for deuteration
    methyl_iso = np.array([[rdkmol.GetAtomWithIdx(j).GetIsotope() for j in i] for i in methyl])

    methyl = np.array(methyl) + 1
    print('methyls found',methyl)
    for i in range(len(methyl)):
        if np.all(methyl_iso[i] < 2):
            jtable = averagecouplings(methyl[i],shifttable,jtable,keepzeros=keepzeros)

    return jtable
    
def cleanupjcoup(rdkmol,shifttable,jtable,keepch2coup=True):

    if len(jtable) == 0 or len(shifttable) == 0:
        return jtable

    #remove couplings from protons with same chemical shift and same carbon
    matchCH3 = "[CH3]([H])([H])[H]"
    #aug 12: made ch2 match more specific (non chiral neighbors)
    matchCH2 = "[$([CH2;!R;!^2]([*;!D3,!D4])([*;!D3,!D4]))]([H])[H]" #ch2 not on a ring

    methyl = rdkmol.GetSubstructMatches(Chem.MolFromSmarts(matchCH3))
    methylene = rdkmol.GetSubstructMatches(Chem.MolFromSmarts(matchCH2))

    methyl = [i[1:] for i in methyl]
    methylene = [i[1:] for i in methylene]

    #check for deuteration
    methyl_iso = np.array([[rdkmol.GetAtomWithIdx(j).GetIsotope() for j in i] for i in methyl])
    methylene_iso = np.array([[rdkmol.GetAtomWithIdx(j).GetIsotope() for j in i] for i in methylene])
    #print(methyl_iso)

    methyl = np.array(methyl) + 1
    methylene = np.array(methylene) + 1


    print('methyls found',methyl)
    print('methylene found',methylene)
    for i in range(len(methyl)):
        if np.all(methyl_iso[i] < 2):
            jtable = averagecouplings(methyl[i],shifttable,jtable)
    if not keepch2coup:
        for group in methylene:
            if np.all(methylene_iso[i] < 2):
                jtable = averagecouplings(group,shifttable,jtable)
    
    return jtable
    
    
def averagecouplings(atomids,shifttable,jtable,keepzeros=False):
    #print("averaging and removing couplings for",atomids)
    print("averaging couplings for",atomids)

    if len(atomids) == 0: return jtable

    #assume all shifts are zero if emtpy
    if len(shifttable) == 0:
        print('no shifts to average couplings')
        print('assume shifts = 0')
        jatoms = set([i[0] for i in jtable] + [i[1] for i in jtable])
        shifttable = np.array([[a,0] for a in jatoms])

    atomshifts = [shift for i,shift in shifttable[:,:2] if i in atomids]

    if not np.all(np.equal(atomshifts[0],atomshifts)): 
        print('shifts not the same, skipping',atomids)
        return jtable
    
    jids = list(shifttable[:,0])

    #for extra couplings
    extra = []
    for line in jtable:
        if line[0] not in jids and line[0] not in extra:
            extra.append(line[0])
        if line[1] not in jids and line[1] not in extra:
            extra.append(line[1])
    jids = jids + extra

    jmatrix = jcoupasmatrix(jtable,jids,'triu',nandiag=True,keepzeros=keepzeros)

    #print(shifttable)
    #print(jtable)
    #print(jmatrix)

    jmask = np.array([i in atomids for i in jids])

    #print('debug',jids)
    #print('debug',jmatrix)
    #print('debug',jmask)

    #rowav = np.mean(jmatrix[jmask],axis=0)
    rowav = np.nanmean(jmatrix[jmask],axis=0)
    jmatrix[jmask] = rowav
    
    #colav = np.mean(jmatrix[:,jmask],axis=1)
    colav = np.nanmean(jmatrix[:,jmask],axis=1)
    jmatrix[:,jmask] = colav[:,np.newaxis]
    
    #maskind = np.flatnonzero(jmask)
    #maskind2d = np.meshgrid(maskind,maskind)
    #jmatrix[maskind2d] = 0 #skip this for strong coupling
    
    return jcoupastable(np.triu(jmatrix,1),jids,keepzeros=keepzeros)


def jcoupasmatrix(jtable,idlist,shape='sym',nandiag=False,keepzeros=False):
    idlist = list(idlist)
    mat = np.zeros((len(idlist),len(idlist)))
    if len(jtable) == 0:
        return mat
    #jtable = np.array(jtable)
    for a,b,j in jtable[:,:3]:
        #if a in idlist and b in idlist:
        if keepzeros and j == 0: j = 999
        ida, idb = idlist.index(a),idlist.index(b)

        if shape == 'sym':
            mat[ida, idb] = j
        if shape == 'triu':
            if ida > idb: ida,idb = idb,ida
            #print(shape, idlist, a,b,j, idlist.index(a),idlist.index(b))
            mat[ida, idb] = j
        if shape == 'tril':
            if ida < idb: ida,idb = idb,ida
            mat[ida, idb] = j
    #print(mat)

    if shape == 'triu':
        mat = np.triu(mat) + np.triu(mat).T
    if shape == 'tril':
        mat = np.tril(mat) + np.tril(mat).T
    if nandiag == True:
        mat[np.diag_indices_from(mat)] = np.nan
    return mat


def jcoupastable(jtable,ids,keepzeros=False):
    #dont keep the last column
    #print("JCOUPAST", jtable, ids)
    table = []
    for i in range(len(ids)):
        for j in range(len(ids)):
            if jtable[i,j]:
                if keepzeros and (jtable[i,j] == 999 or jtable[i,j] == 333): jtable[i,j] = 0
                table.append([ids[i],ids[j],jtable[i,j]])
    return np.array(table)


def removeids(ids,shifttable,jtable):
    shiftmask = ~ np.array([s in ids for s in shifttable[:,0]])
    if len(jtable) == 0:
        return shifttable[shiftmask], jtable
    jmask = ~ np.array([j[0] in ids  or j[1] in ids for j in jtable[:,:2]])
    return shifttable[shiftmask], jtable[jmask]
    
    
def findequiv(shifttable,jtable):
    ids = shifttable[:,0]
    shifts = shifttable[:,1]

    #fix for extra couplings
    extra = []
    for line in jtable:
        if line[0] not in ids and line[0] not in extra:
            extra.append(line[0])
        if line[1] not in ids and line[1] not in extra:
            extra.append(line[1])
    newids = np.array(list(ids) + extra)
    newshifts = np.array(list(shifts) + [-999 for i in extra])
    ids = newids
    shifts = newshifts

    jmatrix = jcoupasmatrix(jtable,ids,'triu')
    #print("FINDEQUIV",jmatrix)

    uniqueshifts,counts = np.unique(shifts,return_counts=True)
    repeatshifts = uniqueshifts[counts > 1]
    
    boolarray = np.zeros(jmatrix.shape)
    
    for ppm in repeatshifts:
        shiftids = np.flatnonzero(shifts == ppm)
        for a in shiftids:
            for b in shiftids:
                if np.all(jmatrix[a] == swapind(jmatrix[b],a,b)):
                    boolarray[a,b] = 1
    equivids = {}
    for row in boolarray:
        if np.count_nonzero(row) > 1:
            equivids[tuple(ids[np.flatnonzero(row)])] = 1

    return list(equivids.keys())

def swapind(arr1d,a,b):
    newarr = np.copy(arr1d)
    newarr[[a,b]] = newarr[[b,a]]
    return newarr
    
def mergeequiv(grouplist,shifttable,jtable,keepequivpairs=True):
    if shifttable.shape[1] < 3:
        #add column for multiplicity
        shifttable = np.hstack((shifttable,np.ones((shifttable.shape[0],1))))
    for group in grouplist:
        if keepequivpairs and len(group) == 2: 
            print('skipping',group)
            continue
        print('merging',group)
        gids = np.flatnonzero(np.array([shift in group for shift in shifttable[:,0]]))
        shifttable[gids,2] = len(gids)
        shifttable, jtable = removeids(group[1:],shifttable,jtable)
    return shifttable,jtable
    
def getCHpairs(rdkmol):
    patt = Chem.MolFromSmarts("[#1][#6]")
    if rdkmol.HasSubstructMatch(patt):
        return rdkmol.GetSubstructMatches(patt)
    else:
        return ()

def checkisotopes(mol,shifts,couplings):

    newshifts = np.zeros((shifts.shape[0],4))
    #newcouplings = np.copy(couplings)

    if len(shifts):
        if shifts.shape[1] == 2:
            newshifts[:,:2] = shifts
            newshifts[:,2] = 1
            newshifts[:,3] = 0.5
        if shifts.shape[1] == 3:
            newshifts[:,:3] = shifts
            newshifts[:,3] = 0.5
        if shifts.shape[1] == 4:
            newshifts[:,:] = shifts

    h2coupatms = []

    for i in range(len(newshifts)):

        a = mol.GetAtomWithIdx(int(newshifts[i][0])-1)
        num = a.GetAtomicNum()
        iso = a.GetIsotope()
        chg = a.GetFormalCharge()
        #deuterium
        if num == 1 and iso == 2:
            print('2H found %s'%(newshifts[i]))
            newshifts[i][3] = 1
            h2coupatms.append(newshifts[i][0])
        #tert-nitrogen
        if num == 7 and iso != 15 and chg == 1 and a.GetDegree() == 4:
            print('14NX4+ found %s'%(newshifts[i]))
            newshifts[i][3] = 1

    return newshifts, couplings

def checkisotopes_inspinsys(spinsys,mol,detect="1H"):

    #explicitly specified isotopes
    detectdic = {"1H":(1,"H"),"2H":(2,"H"),
                 "13C":(13,"C"),"15N":(15,"N"),"31P":(31,"P"),
                 "14N":(14,"N"),"19F":(19,"F"),}
    #default isotopes (by default isotope not set in rdkit)
    defaultdic = {"1H":(0,"H"),"13C":(0,"C"),"31P":(0,"P"),"19F":(0,"F"),}

    detectval = detectdic[detect]
    try:
        defaultval = defaultdic[detect]
    except:
        defaultval = (0,"")
    
    newatomlist = []
    newdetect = []
    newxapprox = []

    for i in range(len(spinsys.atoms)):
        atmid = spinsys.atoms[i].id

        if (atmid - 1) >= mol.GetNumAtoms():
            #ASSUME SAME AS DETECT
            num = detectdic[detect][1]
            iso = detectdic[detect][0]
        else:
            a = mol.GetAtomWithIdx(int(atmid)-1)
            num = a.GetSymbol()
            iso = a.GetIsotope()

        testval = (iso,num)

        if testval == detectval or testval == defaultval:
            newatomlist.append(spinsys.atoms[i])
            if i in spinsys.xapprox: 
                tmp = newatomlist.pop()
                newxapprox.append(tmp)
                continue
            if i in spinsys.detect: 
                newdetect.append(spinsys.atoms[i])
        else:
            newxapprox.append(spinsys.atoms[i])

    newatomlist = newatomlist + newxapprox
    newdetect = [newatomlist.index(i) for i in newdetect]
    newxapprox = [newatomlist.index(i) for i in newxapprox]

    spinsys.atoms = newatomlist
    spinsys.detect = newdetect
    spinsys.xapprox = newxapprox
    spinsys.gen_jmatrix()
    return









