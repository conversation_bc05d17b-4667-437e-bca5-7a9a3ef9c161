import sys
import numpy as np
import matplotlib.pyplot as plt
import itertools
from . import nmrpred


def peaksclose(y,comp,atol=0.3):
    return np.all(np.isclose(y,comp,atol=atol))

def peakmult(peakxy, cutoff=0.1, xtol=1e-3, ytol=0.3):

    peakx = np.array([x for x,y in peakxy])
    peaky = np.array([y for x,y in peakxy])

    #print(peakx, peaky)

    peakx = peakx[peakx > np.max(peakx) * cutoff]
    peaky = peaky[peaky > np.max(peaky) * cutoff]

    #print(peakx)
    #print(peaky)

    if len(peaky) == 1: return 's'
    if len(peaky) == 2: return 'd'
    if len(peaky) == 3: return 't'

    #symetritize and normalize to first peak
    spacing = np.diff(peakx)
    equal_spacing = np.all(np.isclose(spacing, spacing[0], atol=xtol))
    peaky = peaky + peaky[::-1]
    peaky = peaky / peaky[0]

    #print('\n', peaky)

    if equal_spacing:
        if len(peaky) == 4 and peaksclose(peaky, [1,3,3,1], atol=ytol): return 'q'
        if len(peaky) == 4 and peaksclose(peaky, [1,2,2,1], atol=ytol): return 'td' #spin 1
        if len(peaky) == 5 and peaksclose(peaky, [1,4,6,4,1], atol=ytol): return 'p'
        if len(peaky) == 5 and peaksclose(peaky, [1,2,2,2,1], atol=ytol): return 'td'

    if len(peaky) == 4: return 'dd'
    if len(peaky) == 6:
        if np.sum(np.isclose(peaky,2,atol=ytol)) == 2 and np.sum(np.isclose(peaky,1,atol=ytol)) == 4: return 'td'
    if len(peaky) == 8 and peaksclose(peaky, 1, atol=ytol): return 'ddd'

    return 'm'


def getnumatomsforatomid(equivatoms, atomid):

    for e in equivatoms:
        if atomid in e:
            return len(e)


def formatjcoupstringforatomid(jtable, atomid, elem="H",valuesonly=False,peakxy=None, peakxytol=0.5, peakxysfrq=500):

    coup_val = [j[2] for j in jtable if j[0] == atomid or j[1] == atomid]

    #print(valuesonly, atomid, coup_val)

    if peakxy:
        #not really working
        xpos = np.array([x for x,y in peakxy]) * peakxysfrq
        xdiff = np.abs(xpos[:,None] - xpos[None,:])
        xuniq = np.unique(xdiff[xdiff != 0])
        coup_val = [v for v in coup_val for x in xuniq if np.isclose(np.abs(v),x,atol=peakxytol)]


    if valuesonly == 0:
        if not coup_val: return ""
        else: return ",".join([ "%0.2f"%i for i in np.unique(coup_val)])


    if valuesonly == 1:
        coup_atm = [(atomid,j[0]) if j[0] != atomid else (atomid,j[1])  for j in jtable if j[0] == atomid or j[1] == atomid]
        coups = ["J(%s%d,%s%d) = %0.2f Hz"%(elem, int(a[0]), elem, int(a[1]), float(v)) for a, v in zip(coup_atm, coup_val)]

        if not coups: return ""
        else: return ", ".join(coups)


    if valuesonly == 2:
        coup_atm = [j[0] if j[0] != atomid else j[1]  for j in jtable if j[0] == atomid or j[1] == atomid]
        coups = ["%0.2f(%s%d)"%(float(v), elem, int(a)) for a, v in zip(coup_atm, coup_val)]

        if not coup_val: return ""
        else: return ",".join(coups)


def parsejcoupstring(s):
    import re

    #should match:
    #               3J    (                 H10      ,            H11        )   =        +2.55       Hz       
    #                J    (                 H10      ,            H11        )   =        -2.55       Hz   
    #                J                                                           =         2.55       Hz      
    #idx:         1                    2       3             4        5                      6           
    matchstr = "(\d*)?J\s*(?:\(\s*([A-Za-z]*)(\d*)\s*,\s*([A-Za-z]*)(\d*)\s*\)\s*)?=\s*([0-9\.\-\+]+)\s*Hz"

    result = re.findall(s, matchstr)

    for r in result:
        for i in range(len(r)):
            if not r[i]:
                r[i] = "NA"

    return [[int(r[3]),int(r[5]),float(r[6])] for r in result]


def getshiftforpeakid(shifttable, atomid):

    return shifttable[shifttable[:,0] == atomid][1]


def matchint(y, maxatm=10):
    #take symmetretized and normalized intensities
    tmp = np.sum(y)
    for n in range(maxatm):
        test = 2**n
        #print("TMP",tmp,test)
        if np.isclose(tmp, test, rtol=0.2):
            return n
    return np.nan

def getcomb(jlist):
    for i in range(2,np.count_nonzero(jlist)+1):
        c = itertools.combinations(jlist[jlist != 0], i)
        for jc in c: 
            if jc:
                yield jc

def nextnonnan(a):
    for i in a:
        if np.isnan(i): continue
        return i

def nannext(a):
    for i in range(a.size):
        if np.isnan(a[i]): continue
        a[i] = np.nan
        return 


def measurejcoup(pkxy, sfrq=500, roundint=1, rounddist=1, disttol=0.2, yfilt=0.05):
    """
    takes a list of tuples of (ppm, intensity)
    assumes the peaks are symmetrically arranged

    try to measure couplings from peaklists
    """

    pkx = np.array([x for x,y in pkxy]) * sfrq
    pky = np.array([y for x,y in pkxy])


    first = np.round(pky/pky[0], roundint)
    last  = np.round(pky/pky[-1], roundint)
    avg   = np.round( (pky + pky[::-1])/(pky[0] + pky[-1]) , roundint)  
    norm = pky/np.sum(pky)
   
    #print(pky)
    #print(first)
    #print(last)
    #print(avg)
    #print(pky/np.sum(pky))

    filt = pky > np.max(pky) * yfilt
    pkx = pkx[filt]
    pky = pky[filt]
    pky = np.round( (pky + pky[::-1])/(pky[0] + pky[-1]) , roundint) #round to nearest 0.1

    #print(pky)

    natoms = matchint(pky)

    if np.isnan(natoms):
        return 'm', '' #unknown multiplet or strong coupling

    diffhz = abs(pkx - pkx[0])

    #repeat vs intensity
    distances = np.repeat(diffhz, np.round(pky).astype(int))
    distances = distances[1:]
    distances = np.round(distances, rounddist)

    jlist = np.zeros(natoms)
    jidx = 0

    jlist_nz = jlist[jlist != 0]

    while jidx < jlist.size:

        jlist[jidx] = nextnonnan(distances)
        nannext(distances)

        for c in getcomb(jlist):
            distances[np.isclose(distances,sum(c),atol=disttol)] = np.nan

        jidx += 1


    dic = { 1:'d',
            2:'t',
            3:'q',
            #non standard:
            4:'p', #Pentet
            5:'x', #seXtet
            6:'h', #Heptet
            7:'o', #Octet
            8:'n'} #Nonet

    if len(jlist) == 0:
        return 's', ''

    u,c = np.unique(jlist,return_counts=True)

    coup = ",".join(["%0.1f"%i for i in u[::-1]])
    mult = "".join([dic[int(i)] if int(i) in dic else "m" for i in c[::-1] ])

    return mult, coup

def countjcoup(id, shifttable, jtable):
    """
    use predicted shifts, couplings
    filters couplings based on equivalency
    """

    dic = { 1:'d',
            2:'t',
            3:'q',
            #non standard:
            #4:'p', #Pentet
            #5:'x', #seXtet
            #6:'h', #Heptet
            #7:'o', #Octet
            #8:'n', #Nonet
            }

    shifttable = np.array(shifttable)
    jtable = np.array(jtable)

    #print("MEASUREJ",id)
    #print(shifttable)
    #print(jtable)

    if not shifttable.size:
        print('measurej: no shifts',id)
        return "", ""

    if not np.all(np.isin([id],shifttable[:,0])):
        print('measurej: id not in shifts',id)
        return "", ""

    if not jtable.size:
        print('measurej: no couplings',id)
        return "s", ""

    if not np.all(np.isin([id],jtable[:,0:2])):
        print('measurej: id not in couplings',id)
        return "s", ""

    #filter equivalent (chemical shift and coup same)
    equiv = nmrpred.findequiv(shifttable, jtable)

    #filt = [np.all(np.isin(j[:2], np.array(list(e)))) for j in jtable for e in equiv]
    filt = []
    for j in jtable:
        filt.append(True)
        for e in equiv:
            if np.all(np.isin(j[:2], np.array(list(e)))):
                filt[-1] = False
                break
    newjtable = np.copy(jtable)[filt]

    #print(newjtable)

    atmids = shifttable[:,0]
    if shifttable.shape[1] >= 3:
        mult = shifttable[:,2]
    else:
        mult = np.ones(atmids.size)

    j = []
    for line in newjtable:
        if np.isclose(line[2],0,atol=0.2): continue
        
        if id == line[0] or id == line[1]:
            i = np.argwhere(atmids == id)[0]
            m = mult[i]
            for i in range(int(m)):
                j.append(line[2])

    if not j:
        return "s", ""

    j = np.abs(j)
    j = np.round(j,1)
    #print(j)
    
    j_u, j_c = np.unique(j, return_counts=True)

    coup = ",".join(["%0.1f"%j for j in j_u[::-1]])
    mult = "".join([dic[c] if c in dic else 'm' for c in j_c[::-1]])

    #print(coup, mult)

    if "m" in mult: mult = "m"

    if len(mult) > 3:
        mult = "m"
        coup = ""

    return mult, coup


if __name__ == "__main__":
    from spectrum import spinsystem
    from spectrum import calcspectrum


    sh = [  [1, 4.0 ],
            [2, 4.05 ],
            [3, -999],
            [4, -999],
            [5, -999],
            [6, -999],
            [7, -999],
         ]

    jc = [
            [1, 2, 4.0],
            [1, 3, 7.0],
            [1, 4, 7.0],
            [1, 5, 7.0],
            [1, 6, 9.5],
            [1, 7, 9.5],
         ]




    s = spinsystem.spin_system()
    s.setup(sh,jc)
    s.detect = [s.get_atomind(1)]

    f, h = calcspectrum.calc_1d_spectrum(s, 500,  4, 1, 1024*4,)
    x, y = calcspectrum.plot_1d(s, [], [], 500,  4, 1, 1024*4, )

    result = calcspectrum.pickpeaks_byatom(s, 500,  4, 1, 1024*4, minimize=True)
    
    #print(result)

    pkxy = result[0][2]

    print(measurejcoup(pkxy, sfrq=500))

