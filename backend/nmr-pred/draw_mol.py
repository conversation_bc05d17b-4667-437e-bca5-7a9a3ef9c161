import sys, os, time, csv
from rdkit import Chem
from rdkit.Chem.Draw import rdMolDraw2D
from predictor import readinput
from predictor import getequiv
import numpy as np

#defaults for function, reset when running as script
scriptdir = os.path.dirname(os.path.realpath(__file__)) + "/"
outputpath = scriptdir  + "results/"
outputprefix = str(time.time())


def draw_mol(mol, size=(1200,800), atomnotes={},bondnotes={},atomdic={},stereodic={},equivdic={}, 
             outputpath=outputpath, outputprefix=outputprefix, filetype='png', write2dmol=False):

    #print(atomnotes)
    #print(atomdic)
    #print(stereodic)
    #print(equivdic)

    #copy molecule
    mol = Chem.Mol(mol)

    #hack for wedge bonds/stereochemistry on hydrogens

    for a in mol.GetAtoms():
        if a.GetAtomicNum() == 1:
            a.Set<PERSON>tom<PERSON>um(2)

    Chem.AssignAtomChiralTagsFromStructure(mol)
    Chem.AssignStereochemistry(mol, False, True)

    for a in mol.GetAtoms():
        if a.GetAtomicNum() == 2:
            a.SetAtomicNum(1)


    #hack to clean up drawing of ambiguous double bonds

    for b in mol.GetBonds():
        if b.GetBondDir() == Chem.BondDir.EITHERDOUBLE:
            b.SetBondDir(Chem.BondDir.NONE)
        if b.GetStereo() == Chem.BondStereo.STEREOANY:
            b.SetStereo(Chem.BondStereo.STEREONONE)

    #addChiralHs does nothing if H already added
    #comment this line out for 3d view
    mol = rdMolDraw2D.PrepareMolForDrawing(mol, kekulize=True, addChiralHs=True, wedgeBonds=True, forceCoords=True)


    if filetype == 'png':
        drawer = rdMolDraw2D.MolDraw2DCairo(*size)
        filesuffix = '.png'
    if filetype == 'svg':
        drawer = rdMolDraw2D.MolDraw2DSVG(*size)
        filesuffix = '.svg'

    #drawer.SetFontSize(0.35)
    drawer.SetFontSize(0.33)
    opts = drawer.drawOptions()
    opts.updateAtomPalette({1:(0.5,0.5,0.5)}) #grey hydrogens
    opts.flagCloseContactsDist = False
    opts.additionalAtomLabelPadding = 0.05
    opts.maxFontSize = -1
    opts.annotationFontScale = 0.5
    #opts.clearBackground = True


    for i in atomdic:
        opts.atomLabels[i] = atomdic[i]
    for i in atomnotes:
        mol.GetAtomWithIdx(i).SetProp('atomNote', atomnotes[i])


    if filetype == 'png':
        drawer.DrawMolecule(mol)
        drawer.FinishDrawing()
        drawer.WriteDrawingText(outputpath + outputprefix + "_2d" + filesuffix)
    if filetype == 'svg':
        drawer.DrawMolecule(mol)
        #drawer.TagAtoms(mol)
        drawer.FinishDrawing()
        svgout = drawer.GetDrawingText()
        with open(outputpath + outputprefix + "_2d" + filesuffix, 'w') as f:
            f.write(svgout)

    if stereodic:

        if filetype == 'png':
            drawer2 = rdMolDraw2D.MolDraw2DCairo(*size)
            filesuffix = '.png'
        if filetype == 'svg':
            drawer2 = rdMolDraw2D.MolDraw2DSVG(*size)
            filesuffix = '.svg'

        #drawer2.SetFontSize(0.35)
        drawer2.SetFontSize(0.33)
        opts = drawer2.drawOptions()
        opts.updateAtomPalette({1:(0.5,0.5,0.5)}) #grey hydrogens
        opts.flagCloseContactsDist = False
        opts.additionalAtomLabelPadding = 0.05
        opts.maxFontSize = -1
        opts.annotationFontScale = 0.5
        #opts.clearBackground = True

        for i in atomdic:
            opts.atomLabels[i] = atomdic[i]
        for i in stereodic:
            opts.atomLabels[i] = stereodic[i]

        if filetype == 'png':
            drawer2.DrawMolecule(mol)
            drawer2.FinishDrawing()
            drawer2.WriteDrawingText(outputpath + outputprefix + "_stereo"  + filesuffix)
        if filetype == 'svg':
            drawer2.DrawMolecule(mol)
            #drawer2.TagAtoms(mol)
            drawer2.FinishDrawing()
            svgout = drawer2.GetDrawingText()
            with open(outputpath + outputprefix + "_stereo" + filesuffix, 'w') as f:
                f.write(svgout)

    if equivdic:

        if filetype == 'png':
            drawer3 = rdMolDraw2D.MolDraw2DCairo(*size)
            filesuffix = '.png'
        if filetype == 'svg':
            drawer3 = rdMolDraw2D.MolDraw2DSVG(*size)
            filesuffix = '.svg'

        #drawer3.SetFontSize(0.35)
        drawer3.SetFontSize(0.33)
        opts = drawer3.drawOptions()
        opts.updateAtomPalette({1:(0.5,0.5,0.5)}) #grey hydrogens
        opts.flagCloseContactsDist = False
        opts.additionalAtomLabelPadding = 0.05
        opts.maxFontSize = -1
        opts.annotationFontScale = 0.5
        #opts.clearBackground = True

        for i in atomdic:
                opts.atomLabels[i] = atomdic[i]
        for i in equivdic:
            opts.atomLabels[i] = equivdic[i]

        if filetype == 'png':
            drawer3.DrawMolecule(mol)
            drawer3.FinishDrawing()
            drawer3.WriteDrawingText(outputpath + outputprefix + "_equiv"  + filesuffix)
        if filetype == 'svg':
            drawer3.DrawMolecule(mol)
            #drawer3.TagAtoms(mol)
            drawer3.FinishDrawing()
            svgout = drawer3.GetDrawingText()
            with open(outputpath + outputprefix + "_equiv" + filesuffix, 'w') as f:
                f.write(svgout)

    if write2dmol:
        Chem.rdmolfiles.MolToMolFile(mol, outputpath + outputprefix + "_output_2d.mol")

    return


if __name__ == "__main__":

    molfiletype = None
    molfilepath = None

    optmol = False
    ffopt = False
    outputpath = None
    writemol = False
    outputprefix = str(time.time())
    size = (1200,800)
    showstereo = False
    showequiv = False
    canonicalorder = False
    filetype = 'png'
    write2dmol = False
    smilesorder = False
    writelabels = False
    defaultlabel = "sym_idx"

    for i in range(len(sys.argv)):

        if sys.argv[i] == '--mol':
            molfiletype = 'mol'
            molfilepath = sys.argv[i+1]
            print('input mol:',molfilepath)
        if sys.argv[i] == '--smiles':
            molfiletype = 'smiles'
            molfilepath = sys.argv[i+1]
            print('input smiles:',molfilepath)
        if sys.argv[i] == '--molstring':
            molfiletype = 'molstring'
            molfilepath = sys.argv[i+1]
            print('input mol:',molfilepath)
        if sys.argv[i] == '--inchi':
            molfiletype = 'inchi'
            molfilepath = sys.argv[i+1]
            print('input inchi:',molfilepath)

        if sys.argv[i] == "--optmol": optmol = True
        if sys.argv[i] == "--ffopt": ffopt = True
        if sys.argv[i] == "--writemol": writemol = True #write to prefix_output.mol
        if sys.argv[i] == "--outputpath": outputpath = sys.argv[i+1]
        if sys.argv[i] == "--outputprefix": outputprefix = sys.argv[i+1] #write to prefix_*.png
        if sys.argv[i] == "--size": size = (int(sys.argv[i+1]), int(sys.argv[i+2]))
        if sys.argv[i] == "--showstereo": showstereo = True
        if sys.argv[i] == "--showequiv": showequiv = True
        if sys.argv[i] == "--smilesorder": smilesorder = True #order atoms by round trip through smiles (also works with canonicalorder)
        if sys.argv[i] == "--canonicalorder": canonicalorder = True #order atoms with chirality
        if sys.argv[i] == "--filetype": filetype = sys.argv[i+1]
        if sys.argv[i] == "--write2dmol": write2dmol = True #write to prefix_output_2d.mol
        if sys.argv[i] == "--writelabels": writelabels = True #write csv file with various labels
        if sys.argv[i] == "--defaultlabel": defaultlabel = sys.argv[i+1]

    try:
        if molfiletype not in ('mol','smiles','molstring','inchi'): raise ValueError('invalid molecule file type')
        if molfiletype == 'mol' and not os.path.isfile(molfilepath): raise ValueError('molecule file not found')
    except:
        raise ValueError('mol file or smiles string required')


    #read input smiles or mol file -> mol
    try:
        print('reading:',molfilepath)
        mol = readinput.readinput(molfiletype, molfilepath)
    except Exception as e:
        print('error reading molecule:\n%s'%e)
        sys.exit()


    if (molfiletype == 'mol' or molfiletype == 'molstring') and optmol:
        mol, confid = readinput.cleanupgeometry(mol, addh=True, embed=True, optimize=ffopt)

    if molfiletype == "smiles" or molfiletype == "inchi":
        mol, confid = readinput.cleanupgeometry(mol, addh=True, embed=True, optimize=ffopt)


    #track indices
    for a in mol.GetAtoms():
        a.SetProp('oldidx',str(a.GetIdx()))


    if smilesorder:
        try:
            smiles = Chem.MolToSmiles(Chem.RemoveHs(mol),canonical=True)
            print("smiles ordering:", smiles)
            newmol = Chem.MolFromSmiles(smiles)
            newmol, confid = readinput.cleanupgeometry(newmol, addh=True, embed=True, optimize=ffopt)

            #add back "oldidx"
            idx_map = mol.GetSubstructMatch(newmol,useChirality=True)
            print("smilesidx, origidx")
            for atm, idx in zip(newmol.GetAtoms(), idx_map):
                print(atm.GetIdx(), idx)
                atm.SetProp('oldidx',str(idx))

            mol = newmol

        except Exception as e:
            print('somthing went wrong with smiles roundtrip:',e)
            sys.exit()


    try:
        if not mol.GetConformer().Is3D():
            print('warning: molecule is not 3d')
    except Exception as e:
        print('error with 3d conformation generation, keep going anyways')
        #sys.exit()


    hflag = False
    for a in mol.GetAtoms():
        if a.GetAtomicNum() == 1:
            hflag = True

    if not hflag:
        print('warning: molecule has no hydrogens') 



    scriptdir = os.path.dirname(os.path.realpath(__file__)) + "/"
    if outputpath:
        if not outputpath.endswith("/"):
            outputpath = outputpath + "/"
    else:
        outputpath = scriptdir  + "results/"


    print ("outputpath = ", outputpath)
    java_scriptdir="%sjava/" % scriptdir
    print ("java_scriptdir = ", java_scriptdir)


    if showstereo or showequiv or canonicalorder or writelabels:
        #(pro)chirality by substituion test
        equiv = getequiv.getequiv(mol)
        chiral, prochiral = getequiv.getchiral(mol, tmploc=outputpath,scriptpath=java_scriptdir)
        stereoequiv = getequiv.findequivalentatoms(mol,chiralmap=(chiral,prochiral),unique=False)
        for i in range(len(stereoequiv)):
            stereoequiv[i] = stereoequiv[i]-1

        #print('original order mapping')
        #print(chiral)
        #print('idx', 'sym', 'oldidx','nmrequiv(idx)', 'chiral(idx)')
        #for a in mol.GetAtoms():
        #    idx = a.GetIdx()
        #    print(a.GetIdx(), a.GetSymbol(), stereoequiv[idx], {c:prochiral[idx][c] for c in prochiral[idx] if c not in chiral or chiral[c] != prochiral[idx][c]})
        #    #print(a.GetIdx(), a.GetSymbol(), a.GetProp('oldidx'), stereoequiv[idx], {c:prochiral[idx][c] for c in prochiral[idx]})

    if canonicalorder:


        if smilesorder:
            newrank = getequiv.getcanonical2(mol, prochiral, chiral)
        else:
            newrank = getequiv.getcanonical(mol, prochiral, chiral)

        #if canonicalsimple:
        #    newrank = list(Chem.CanonicalRankAtoms(mol,includeChirality=False,breakTies=True))

        neworder = [newrank.index(i) for i in range(mol.GetNumAtoms()-1,-1,-1)]
        newmol = Chem.RenumberAtoms(mol, neworder)

        oldnewmap = {old:new for new,old in enumerate(neworder)}
        newchiral = {oldnewmap[i]:chiral[i] for i in chiral}
        newprochiral = {oldnewmap[i]:{oldnewmap[j]:prochiral[i][j] for j in prochiral[i]} for i in prochiral}
        newstereoequiv = []
        for i in range(len(neworder)): #convert to zero-indexed
            newstereoequiv.append(np.array([oldnewmap[a] for a in stereoequiv[neworder[i]]]))

        mol = newmol
        chiral = newchiral
        prochiral = newprochiral
        stereoequiv = newstereoequiv

        print(chiral)
        print('newidx', 'sym', 'oldidx', 'nmrequiv(newidx)', 'prochiral(newidx)')
        for a in mol.GetAtoms():
            idx = a.GetIdx()
            old = int(a.GetProp('oldidx'))

            print(idx, a.GetSymbol(), old, stereoequiv[idx], {c:prochiral[idx][c] for c in prochiral[idx] if c not in chiral or chiral[c] != prochiral[idx][c]})
            #print(idx, a.GetSymbol(), old, stereoequiv[idx], {c:prochiral[idx][c] for c in prochiral[idx]})


    if writemol:
        Chem.rdmolfiles.MolToMolFile(mol, outputpath + outputprefix + "_output.mol")


    atomlabeldic = {"outputidx":{},
                     "inputidx":{},
                          "sym":{},
                      "sym_idx":{},
                  "sym_idx_ABC":{},
                       "chiral":{},
                        "equiv":{},
                    }



    lab = {1:"A",2:"B",3:"C",4:"D"}
    test = {}

    chglabel = {}

    for a in mol.GetAtoms():

        idx = a.GetIdx()
        labidx = idx+1
        sym = a.GetSymbol()
        chg = a.GetFormalCharge()
        if chg:
            if chg > 0:
                chglabel[idx] = "+"+str(chg)
            else:
                chglabel[idx] = str(chg)

        iso = a.GetIsotope()
        if iso and sym != "H": sym = str(iso)+sym
        if iso == 2: sym = "D"
        if iso == 3: sym = "T"

        suff = ""

        if sym in ("H","D","T"):
            neigh = a.GetNeighbors()[0]
            neigh_idx = neigh.GetIdx()
            neigh_H = neigh.GetTotalNumHs(includeNeighbors=True)

            if neigh_idx not in test: test[neigh_idx] = 1
            else: test[neigh_idx] += 1

            if neigh_H > 1: suff = lab[test[neigh_idx]]

            labidx = neigh_idx+1

        if showstereo: 
            if idx in chiral:
                stereo = ["%s"%(chiral[idx])]
            else:
                stereo = []
            tmplist = ["%d%s"%(x+1,prochiral[idx][x]) for x in prochiral[idx] if x not in chiral or (x in chiral and prochiral[idx][x] != chiral[x])]
            stereo.extend(tmplist)
            if stereo:
                stereo = "("+",".join(stereo)+")"
            else:
                stereo = ""
        else:
            stereo = ""

        if showequiv: 
            equivline = stereoequiv[idx]
            for i,line in enumerate(stereoequiv):
                 #print(equivline, line, type(equivline), type(line))
                 if np.array_equal(equivline, line):
                    equiv = "(%s)"%str(i+1)
                    break
            #equiv = "(%d)"%(stereoequiv.index(equivline)+1)
        else:
            equiv = ""
            

        atomlabeldic["outputidx"][idx] = idx+1
        atomlabeldic["inputidx"][idx] = int(a.GetProp('oldidx'))+1
        atomlabeldic["sym"][idx] = sym
        atomlabeldic["sym_idx"][idx] = "%s%d"%(sym,idx+1)
        atomlabeldic["sym_idx_ABC"][idx] = "%s%d%s"%(sym,labidx,suff)
        atomlabeldic["chiral"][idx] = stereo
        #print(stereo)
        #if stereo: atomlabeldic["chiral"][idx] = stereo
        #else: atomlabeldic["chiral"][idx] = atomlabeldic[defaultlabel][idx] #if no stereo
        atomlabeldic["equiv"][idx] = equiv

        print(atomlabeldic["outputidx"][idx], atomlabeldic["inputidx"][idx], atomlabeldic["sym"][idx], atomlabeldic["sym_idx"][idx], atomlabeldic["sym_idx_ABC"][idx],
                atomlabeldic["chiral"][idx], atomlabeldic["equiv"][idx])


    if writelabels: #write labels

        with open(outputpath + outputprefix + "_atomlabels.csv",'w') as w:
            writer = csv.writer(w)
            header = ['outputidx', 'inputidx', 'sym', 'sym_idx', 'sym_idx_ABC', 'chiral', 'equiv']
            writer.writerow(header)
            for a in mol.GetAtoms():
                writer.writerow([atomlabeldic[h][a.GetIdx()] for h in header])


    if not showstereo:
        atomlabeldic["chiral"] = {}
    if not showequiv:
        atomlabeldic["equiv"] = {}

    draw_mol(mol, size, atomdic=atomlabeldic[defaultlabel], stereodic=atomlabeldic["chiral"], equivdic=atomlabeldic["equiv"], outputpath=outputpath, outputprefix=outputprefix, filetype=filetype, write2dmol=write2dmol)


