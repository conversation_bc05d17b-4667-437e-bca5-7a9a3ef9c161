installing for np-mrd:

1) install java runtime

2) install conda or miniconda (recommended)
check that 'conda activate' works

3) install packages (for conda/miniconda):
conda create -n np-mrd python=3.7.7
conda install -n np-mrd numpy scipy matplotlib
conda install -n np-mrd -c rdkit rdkit

Nov 2020, using pynmrstar for bmrb star file parsing:
conda activate np-mrd
pip install pynmrstar

4) for np-mrd app, python_paths.yml should point to python executable, eg:
conda activate np-mrd
which python



