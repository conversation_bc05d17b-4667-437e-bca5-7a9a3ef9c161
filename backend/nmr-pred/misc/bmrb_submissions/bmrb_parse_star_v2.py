import sys
import re
import pynmrstar
import csv
import os

class bmrbparse():

    def __init__(self, bmrbfile):

        self.csvline = makecsvwriter()

        self.bmrbfile = bmrbfile

        entry = pynmrstar.Entry.from_file(bmrbfile)

        print('parsing:', bmrbfile)

        header = entry['entry_information']
        self.entry_id = header['ID'][0] 
        self.entry_date = header['Submission_date'][0] 

        try: self.entry_doi = header['DOI'][0] 
        except: self.entry_doi = ''

        try: 
            self.entry_authors = header['_Entry_author']['Given_name','Middle_initials','Family_name'] 
        except: 
            try: 
                entry_authors = header['_Entry_author']['Family_name'] #required
                self.entry_authors = [[s] for s in entry_authors]
            except: 
                self.entry_authors = []
        for name in self.entry_authors:
            while "?" in name:
                name.remove("?")
            while "." in name:
                name.remove(".")

        try: self.entry_org = header['_Entry_src']['Organization_full_name']
        except: self.entry_org = []

        try: self.entry_update = header['_Release']['Date'][-1]
        except: self.entry_update = entry_date

        entities = entry.get_saveframes_by_category('entity')
        if len(entities) != 1: raise ValueError('zero or more than one molecular entities in star file')
        #required fields
        self.entity_id = entities[0]['ID'][0]
        self.entity_name = entities[0]['Name'][0]
        self.entity_label = entities[0]['Sf_framecode'][0]

        try:
            natural_sources = entry['natural_source']['_Entity_natural_src']['Entity_label','Type']
            self.natural_source = [i[1] for i in natural_sources if i[0][1:] == self.entity_label][0]
        except:
            self.natural_source = ""

        try:
            exp_sources = entry['experimental_source']['_Entity_experimental_src']['Entity_label','Production_method']
            self.exp_source = [i[1] for i in exp_sources if i[0][1:] == self.entity_label][0]
        except:
            self.exp_source = ""


        samples = entry.get_saveframes_by_category('sample')
        #dictionary with entity, solvent, reference, and other; 5 values per entity
        self.sample_list = {}
        for sample in samples:
            self.sample_list[sample['ID'][0]] = {}
            curr_sample = self.sample_list[sample['ID'][0]]

            try:
                sample_loop = sample['_Sample_component']['Entity_label','Mol_common_name','Type','Concentration_val',
                                                      'Concentration_val_units']
                for line in sample_loop:
                    #print("SAMPLE LINE",line)
                    if line[0][1:] == self.entity_label:
                        curr_sample['entity'] = line
                    elif line[2] in ['Solvent','solvent']:
                        curr_sample['solvent'] = line
                        #print("SOLVENT FOUND",line)
                    elif line[2] in ['Reference','reference']:
                        curr_sample['reference'] = line
                    else:
                        if 'other' not in curr_sample:
                            curr_sample['other'] = []
                        curr_sample['other'].append(line)

            except:
                sample_loop = sample['_Sample_component']['Entity_label','Mol_common_name',]
                for line in sample_loop:
                    if line[0][1:] == self.entity_label:
                        curr_sample['entity'] = line + ['Solute','NA','NA']


            if 'entity' not in curr_sample:
                raise ValueError('molecular entity not found in one of the samples')


        #dictionary of lists by condition id, 3 values each
        conditions = entry.get_saveframes_by_category('sample_conditions')
        self.condition_list = {}

        for cond in conditions:
            try:
                self.condition_list[cond['ID'][0]] = cond['_Sample_condition_variable']['Type','Val','Val_units']
            except:
                pass


        #by spec id, 3 values each
        spectrometers = entry.get_saveframes_by_category('NMR_spectrometer')
        self.spec_list = {}
        for spec in spectrometers:
            self.spec_list[spec['ID'][0]] = spec['Manufacturer'][0],spec['Model'][0],spec['Field_strength'][0]




        #by exp id, 5 values each
        #TODO: what to do when sample, condition, spec missing?
        self.exp_list = {}
        try:
            experiments = entry['experiment_list']['_Experiment']['ID','Name','Sample_ID','Sample_condition_list_ID','NMR_spectrometer_ID',]
            for exp in experiments:
                self.exp_list[exp[0]] = exp[1:]
        except:
            experiments = entry['experiment_list']['_Experiment']['ID','Name']
            for exp in experiments:
                self.exp_list[exp[0]] = exp[1:] + ['','','']


        #not always present
        #by id,; 4 values each
        self.exp_files = {}
        try:
            experiment_data = entry['experiment_list']['_Experiment_file']['Experiment_ID','Type','Directory_path','Name']
            for exp in experiment_data:
                if exp[1] == 'text/directory':
                    self.exp_files[exp[0]] = "\'"+os.path.join(exp[2],exp[3])+"\'"
        except:
            pass


        #by id, 4 values each
        ref_compounds = entry.get_saveframes_by_category('chem_shift_reference')
        self.ref_cpd_list = {}
        for ref in ref_compounds:
            self.ref_cpd_list[ref['ID'][0]] = ref['_Chem_shift_ref']['Atom_type','Mol_common_name','Chem_shift_val','Chem_shift_units']



        #ambiguous not handled
        #list of 4 values per assigned atom
        assignment_tables = entry.get_saveframes_by_category('assigned_chemical_shifts')
        self.assign_list = {}
        for table in assignment_tables:
            self.assign_list[table['ID'][0]] = {}
            current_list = self.assign_list[table['ID'][0]]
            current_list['experiments'] = []
            current_list['shifts'] = []
            current_list['conditions'] = table['Sample_condition_list_ID'][0]
            current_list['reference'] = table['Chem_shift_reference_ID'][0]

            try:
                for exp in table['_Chem_shift_experiment']['Experiment_ID','Sample_ID','Experiment_name']:
                    current_list['experiments'].append(exp)
            except:
                for exp in table['_Chem_shift_experiment']['Experiment_ID']:
                    current_list['experiments'].append([exp,'',''])

            for shift in table['_Atom_chem_shift']['Entity_ID','Atom_type','Atom_ID','Val']:
                shift[2] = idx_from_name(shift[2])
                current_list['shifts'].append(shift)



        peak_lists = entry.get_saveframes_by_category('spectral_peak_list')
        self.peak_data = {}
        for exp in peak_lists:
            self.peak_data[exp['ID'][0]] = {}
            curr_list = self.peak_data[exp['ID'][0]]
            curr_list['sample'] = exp['Sample_ID'][0]
            curr_list['conditions'] = exp['Sample_condition_list_ID'][0]
            curr_list['experiment'] = exp['Experiment_ID'][0]
            curr_list['experiment_name'] = exp['Experiment_name'][0]
            curr_list['ndim'] = exp['Number_of_spectral_dimensions'][0]
            curr_list['dim_label'] = exp['_Spectral_dim']['Atom_type']
            curr_list['transitions'] = {}
            curr_list['peaks'] = {}


            #print(curr_list['experiment_name'])
            #transitions may not exist
            try: 
                trans_id = exp['_Spectral_transition']['ID']
                #print("TRANSITIONS FOUND")

                for t in trans_id:
                    #dim1 dim2...
                    curr_list['transitions'][t] = ['NA'] * int(curr_list['ndim'])


                #transition should probably exist
                ppm_id = exp['_Spectral_transition_char']['Spectral_transition_ID']
                ppm_ppm = exp['_Spectral_transition_char']['Chem_shift_val']
                try: ppm_dim = exp['_Spectral_transition_char']['Spectral_dim_ID']
                except: ppm_dim = ['1']*len(ppm_id)
                for i,d,v in zip(ppm_id,ppm_dim,ppm_ppm):
                    if i not in curr_list['transitions']:
                        curr_list['transitions'][i] = ['NA'] * int(curr_list['ndim'])
                    curr_list['transitions'][i][int(d)-1] = v


                #intensities may not exist
                #append columns to end
                try: intens_id = exp['_Spectral_transition_general_char']['Spectral_transition_ID']
                except: intens_id = []
                try: intens_intens = exp['_Spectral_transition_general_char']['Intensity_val']
                except: intens_intens = ['NA']*len(intens_id)
                try: intens_type = exp['_Spectral_transition_general_char']['Measurement_method']
                except: intens_type = ['NA']*len(intens_id)
                for i,v,t in zip(intens_id,intens_intens,intens_type):
                    if i not in curr_list['transitions']:
                        curr_list['transitions'][i] = ['NA'] * int(curr_list['ndim'])
                    curr_list['transitions'][i].extend(['intensity',t,v])


                #transitions can be assigned eg bmse001100
                #loop_
                #   _Assigned_spectral_transition.Spectral_transition_ID
                #   _Assigned_spectral_transition.Spectral_dim_ID
                #   _Assigned_spectral_transition.Atom_ID
                try: assign_id = exp['_Assigned_spectral_transition']['Spectral_transition_ID']
                except: assign_id = []
                try: assign_dim = exp['_Assigned_spectral_transition']['Spectral_dim_ID']
                except: assign_dim = ['1']*len(assign_id)
                try: assign_atm = exp['_Assigned_spectral_transition']['Atom_ID']
                except: assign_atm = ['NA']*len(assign_id)
                for i,d,v in zip(assign_id,assign_dim,assign_atm):
                    if i not in curr_list['transitions']:
                        curr_list['transitions'][i] = ['NA'] * int(curr_list['ndim'])
                    curr_list['transitions'][i].extend(['assignment'])
                    curr_list['transitions'][i].extend(['dim',d,'label',curr_list['dim_label'][int(d)-1],'atom',idx_from_name(v)])
                    

              
            except:
                pass

            #print(curr_list['transitions'])

            #peaks (groups of transitions) may not exist
            try:
                peak_id = exp['_Peak']['ID']

                #print('PEAKS FOUND')

                for p in peak_id:
                    #dim1 dim2...
                    curr_list['peaks'][p] = ['NA'] * int(curr_list['ndim'])


                ppm_id = exp['_Peak_char']['Peak_ID']
                ppm_ppm = exp['_Peak_char']['Chem_shift_val']
                try: ppm_dim = exp['_Peak_char']['Spectral_dim_ID']
                except: ppm_dim = ['1']*len(ppm_id)
                try: ppm_coup = exp['_Peak_char']['Coupling_pattern']
                except: ppm_coup = ['NA']*len(ppm_id)
                for i,d,v,c in zip(ppm_id,ppm_dim,ppm_ppm,ppm_coup):
                    if i not in curr_list['peaks']:
                        curr_list['peaks'][i] = ['NA'] * int(curr_list['ndim'])
                    curr_list['peaks'][i][int(d)-1] = v

                    curr_list['peaks'][i].extend(['multiplicity','dim',d,'mult',c])
                        
                #intensities may not exist
                try: intens_id = exp['_Peak_general_char']['Peak_ID']
                except: intens_id = []
                try: intens_intens = exp['_Peak_general_char']['Intensity_val']

                except: intens_intens = ['NA']*len(intens_id)
                try: intens_type = exp['_Peak_general_char']['Measurement_method']
                except: intens_type = ['NA']*len(intens_id)
                for i,v,m in zip(intens_id,intens_intens,intens_type):
                    if i not in curr_list['peaks']:
                        curr_list['peaks'][i] = ['NA'] * int(curr_list['ndim'])
                    curr_list['peaks'][i].extend(['intensity',m,v])

                #couplings may not exist
                #may be more than one per peak
                try: coup_id = exp['_Peak_coupling']['Peak_ID']
                except: coup_id = []
                try: coup_dim = exp['_Peak_coupling']['Spectral_dim_ID']
                except: coup_dim = ['1']*len(coup_id)
                try: coup_val = exp['_Peak_coupling']['Coupling_val']
                except: coup_val = ['NA']*len(coup_id)
                for i,v,d in zip(coup_id,coup_val,coup_dim):
                    if i not in curr_list['peaks']:
                        curr_list['peaks'][i] = ['NA'] * int(curr_list['ndim'])

                    curr_list['peaks'][i].extend(['coupling','dim',d,'val',v])

                #peaks can be assigned
                #loop_
                #   _Assigned_peak_chem_shift.Peak_ID
                #   _Assigned_peak_chem_shift.Spectral_dim_ID
                #   _Assigned_peak_chem_shift.Atom_ID
                try: assign_id = exp['_Assigned_peak_chem_shift']['Spectral_transition_ID']
                except: assign_id = []
                try: assign_dim = exp['_Assigned_peak_chem_shift']['Spectral_dim_ID']
                except: assign_dim = ['1']*len(assign_id)
                try: assign_atm = exp['_Assigned_peak_chem_shift']['Atom_ID']
                except: assign_atm = ['NA']*len(assign_id)
                for i,d,v in zip(assign_id,assign_dim,assign_atm):
                    if i not in curr_list['peaks']:
                        curr_list['peaks'][i] = ['NA'] * int(curr_list['ndim'])
                    curr_list['peaks'][i].extend(['assign'])
                    curr_list['peaks'][i].extend([d,curr_list['dim_label'][int(d)-1],idx_from_name(v)])


            except:
                pass
            

        print('done parsing:', self.bmrbfile)
        print('number of assignment tables:', len(self.assign_list))
        print('number of experiments:', len(self.exp_list))
        print('number of data files:', len(self.exp_files))
        print('number of samples', len(self.sample_list))
        print('number of experimental conditions', len(self.condition_list))
        print('number of reference compounds', len(self.ref_cpd_list))
        print('number of spectrometers', len(self.spec_list))
        print('number of peak lists', len(self.peak_data))
        print()

        return


    def printheader(self):

        outp = []
        outp.extend(["#entry_id: ", self.entry_id,'\n'])
        outp.extend(['#entry_submission_date: ', self.entry_date,'\n'])
        outp.extend(['#entry_last_updated_date: ', self.entry_update,'\n'])
        outp.extend(['#entry_doi: ', self.entry_doi,'\n'])
        outp.extend(['#entry_authors: ', ", ".join([" ".join(name) for name in self.entry_authors]),'\n'])
        outp.extend(['#entry_author_organization: ', ", ".join(self.entry_org),'\n'])
        outp.extend(['#compound_name: ', self.entity_name,'\n'])
        outp.extend(['#compound_natural_source: ', self.natural_source,'\n'])
        outp.extend(['#compound_experimental_source: ', self.exp_source,'\n'])

        return "".join(outp)


    def printassignments(self):

        

        maxfrq = 0

        #no assignment table
        #print info anyways
        if len(self.assign_list) == 0:

            outp = []

            for e in self.exp_list:
                exp = self.exp_list[e]
                    
                outp.extend(['#experiment_name: ',exp[0],'\n'])

                try:
                    data = self.exp_files[e[0]]
                    outp.extend(["#experiment_data: ",data,'\n'])
                except:
                    pass

                samp = self.sample_list[exp[1]]
                for s in samp:
                    if s == 'entity':
                        outp.extend(['#experiment_sample_concentration_%s: %s'%(samp[s][4],samp[s][3]),'\n'])
                    elif s == 'reference':
                        outp.extend(['#experiment_sample_referece: %s'%(samp[s][1]),'\n'])
                    elif s == 'solvent':
                        outp.extend(['#experiment_sample_solvent: %s'%(samp[s][1]),'\n'])
                    else:
                        for other in samp[s]:
                            outp.extend(['#experiment_sample_other: ',other[1],' ',other[3],' ',other[4],'\n'])

                cond = self.condition_list[exp[2]]
                for c in cond:
                    outp.extend(['#experiment_condition_%s_%s: %s'%(c[0],c[2],c[1]),'\n'])

                    pass
                
                spec = self.spec_list[exp[3]]
                outp.extend(['#experiment_spectrometer: ', spec[0],'\n'])
                outp.extend(['#experiment_frequency_MHz: ', spec[2],'\n'])
                if int(spec[2]) > maxfrq:
                    maxfrq = int(spec[2])

            outp.extend(['#assignment_highest_frequency: ', str(maxfrq),'\n'])

            outp.extend(['#'*80,'\n'])
            outp.extend(['#NO_ASSIGNMENTS'])

            yield "".join(outp)



        i = 0

        for table in self.assign_list:
            
            outp = []

            currtable = self.assign_list[table]

            for c in self.condition_list[currtable['conditions']]:
                outp.extend(['#assignment_condition_%s_%s: %s'%(c[0],c[2],c[1]),'\n'])
            
            for r in self.ref_cpd_list[currtable['reference']]:
                outp.extend(['#assignment_reference_nucleus: ', r[0],'\n'])
                outp.extend(['#assignment_reference_compound: ', r[1],'\n'])
                outp.extend(['#assignment_reference_position_%s: %s'%(r[3],r[2]),'\n'])


            for e in currtable['experiments']:
                exp_name = e[2]
                samp_id = e[1]
                #indexing not correct in eg bmse010497, use name if possible
                exp = self.exp_list[e[0]]
                for id in self.exp_list:
                    if self.exp_list[id][0] == exp_name and self.exp_list[id][2] == samp_id and self.exp_list[id][2] == currtable['conditions']:
                        exp = self.exp_list[id]
                        break
                    
                outp.extend(["#"*80,'\n'])
                outp.extend(['#experiment_name: ',exp[0],'\n'])

                try:
                    data = self.exp_files[e[0]]
                    outp.extend(["#experiment_data: ",data,'\n'])
                except:
                    pass

                samp = self.sample_list[exp[1]]
                for s in samp:
                    if s == 'entity':
                        outp.extend(['#experiment_sample_concentration_%s: %s'%(samp[s][4],samp[s][3]),'\n'])
                    elif s == 'reference':
                        outp.extend(['#experiment_sample_referece: %s'%(samp[s][1]),'\n'])
                    elif s == 'solvent':
                        outp.extend(['#experiment_sample_solvent: %s'%(samp[s][1]),'\n'])
                    else:
                        for other in samp[s]:
                            outp.extend(['#experiment_sample_other: ',other[1],' ',other[3],' ',other[4],'\n'])

                cond = self.condition_list[exp[2]]
                for c in cond:
                    outp.extend(['#experiment_condition_%s_%s: %s'%(c[0],c[2],c[1]),'\n'])
                    pass
                
                spec = self.spec_list[exp[3]]
                outp.extend(['#experiment_spectrometer: ', spec[0],'\n'])
                outp.extend(['#experiment_frequency_MHz: ', spec[2],'\n'])
                if int(spec[2]) > maxfrq:
                    maxfrq = int(spec[2])

            outp.extend(['#'*80,'\n'])
            outp.extend(['#assignment_highest_frequency: ', str(maxfrq),'\n'])
            outp.extend(['#assignment_count: ', str(len(currtable['shifts'])),'\n'])

            outp.extend(['#'*80,'\n'])
            outp.extend(['#ASSIGNMENTS','\n'])
            for s in currtable['shifts']:
                #outp.extend([" ".join(s[1:]),'\n'])
                outp.extend([self.csvline(s[1:])])
                pass

            i += 1

            yield "".join(outp)



    def printpeaklist(self):

        j = 0
        for exp in self.peak_data:

            outp = []

            exp = self.peak_data[exp]

            outp.extend(['#experiment_name: ',exp['experiment_name'],'\n'])
            try:
                data = self.exp_files[exp['experiment']]
                outp.extend(["#experiment_data: ",data,'\n'])
            except:
                pass

            samp = self.sample_list[exp['sample']]
            for s in samp:
                if s == 'entity':
                    outp.extend(['#experiment_sample_concentration_%s: %s'%(samp[s][4],samp[s][3]),'\n'])
                    pass
                elif s == 'reference':
                    outp.extend(['#experiment_sample_referece: %s'%(samp[s][1]),'\n'])
                    pass

                elif s == 'solvent':
                    outp.extend(['#experiment_sample_solvent: %s'%(samp[s][1]),'\n'])
                    pass
                else:
                    for other in samp[s]:
                        outp.extend(['#experiment_sample_other: ',other[1],' ',other[3],' ',other[4],'\n'])

            cond = self.condition_list[exp['conditions']]
            for c in cond:
                outp.extend(['#experiment_condition_%s_%s: %s'%(c[0],c[2],c[1]),'\n'])
                pass

            #spectrometer not specified, need to search
            spec = '1'
            for id in self.exp_list:
                if self.exp_list[id][0] == exp['experiment_name'] and self.exp_list[id][2] == exp['sample'] and self.exp_list[id][2] == exp['conditions']:
                    spec = self.exp_list[id][3]
                    break
            spec = self.spec_list[spec]
            outp.extend(['#experiment_spectrometer: ', spec[0],'\n'])
            outp.extend(['#experiment_frequency_MHz: ', spec[2],'\n'])

                
            outp.extend(['#experiment_dim_count: ',exp['ndim'],'\n'])
            outp.extend(['#experiment_dim_labels: '," ".join(exp['dim_label']),'\n'])
            outp.extend(['#transition_count: ', str(len(exp['transitions'])),'\n'])
            outp.extend(['#peak_count: ', str(len(exp['peaks'])),'\n'])
            outp.extend(['#'*80,'\n'])

            outp.extend(['#TRANSITIONS','\n'])
            for i in sorted(exp['transitions'].keys(),key=lambda x: int(x)):
                #outp.extend([" ".join(exp['transitions'][i]),'\n'])
                outp.extend([self.csvline(exp['transitions'][i])])
            if len(exp['transitions']) == 0:
                outp.extend(['#NO TRANSITIONS','\n'])
            outp.extend(['#'*80,'\n'])

            outp.extend(['#PEAKS','\n'])
            for i in sorted(exp['peaks'].keys(),key=lambda x: int(x)):
                #outp.extend([" ".join(exp['peaks'][i]),'\n'])
                outp.extend([self.csvline(exp['peaks'][i])])
            if len(exp['peaks']) == 0:
                outp.extend(['#NO PEAKS','\n'])
            j += 1


            yield ''.join(outp)

    pass


def idx_from_name(s):
    #atom symbol + numerical index
    m = re.search('\d+$',s)
    if m: return m.group(0)
    else: return "NA"


#import string
#def removepunc(s):
#    return s.translate(str.maketrans(' ', '_', string.punctuation))


#def stripext(s):
#    return s.rfind('.')


#def mapatom(i,mapping):
#    if i in mapping:
#        return mapping[i]
#    else:
#        return "-1"



def makecsvwriter(*args,**kwargs):
    class csvwrapper():
        def __init__(self):
            pass
        def write(self,s):
            return s
    writer = csv.writer(csvwrapper(),*args,**kwargs)
    return writer.writerow


if __name__ == "__main__":


    f = sys.argv[1]
    outputdir = sys.argv[2]
    outputprefix = sys.argv[3]

    print('reading star file', f)
    print('outputdir', outputdir)
    print('outputprefix', outputprefix)

    bmrbdata = bmrbparse(f)

    #print(bmrbdata.printheader())
    #for s in bmrbdata.printassignments():
    #    print(s)
    #for s in bmrbdata.printpeaklist():
    #    print(s)

    for i, s in enumerate(bmrbdata.printassignments()):
        tablepath = os.path.join(outputdir,outputprefix)+'_bmrb_shifts_'+str(i)+".txt"
        print('writing table',i,'to',tablepath)

        with open(tablepath,'w') as assignmentfile:
            assignmentfile.write(bmrbdata.printheader())
            assignmentfile.write(s)


    for i, s in enumerate(bmrbdata.printpeaklist()):
        tablepath = os.path.join(outputdir,outputprefix)+'_bmrb_peaklist_'+str(i)+".txt"
        print('writing table',i,'to',tablepath)

        with open(tablepath,'w') as assignmentfile:
            assignmentfile.write(bmrbdata.printheader())
            assignmentfile.write(s)



