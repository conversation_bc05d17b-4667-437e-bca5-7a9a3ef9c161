import sys, os, csv

"""
takes a csv file with a atom mapping,
and renumbers another csv file (possibly with commented lines)
"""

if __name__ == '__main__':

    shiftlist = sys.argv[1]
    mapcsv = sys.argv[2]

    idxcol = 1

    fromidx = 0
    toidx = 1

    with open(mapcsv) as mapfile:
        mapfile = mapfile.readlines()
        mapping = {}
        for line in mapfile:

            if line.startswith("#"):
                continue

            for row in csv.reader([line.strip()]):
                fromval = row[fromidx]
                toval = row[toidx]
                mapping[fromval] = toval

    with open(shiftlist) as shifts:
        shifts = shifts.readlines()
        shifts_out = []
        #shifts_csv = []
        for line in shifts:

            if line.startswith("#"):
                shifts_out.append(line.strip())
                continue

            for row in csv.reader([line.strip()]):
                if row[idxcol] in mapping:
                    row[idxcol] = mapping[row[idxcol]]
                else:
                    row[idxcol] = "NA"
                shifts_out.append(row)

    writer = csv.writer(sys.stdout)
    #print(shifts_out)
    for line in shifts_out:
        if line[0] == "#":
            print(line)
        else:
            writer.writerow(line)
