import numpy as np
from rdkit.Chem import AllChem as Chem
from rdkit.Chem import rdMolTransforms
from multiprocessing import Pool, freeze_support
from rdkit.ML.Cluster import Butina
import time
import sys

ATOL = 5.0
APOT = 1e5
MAXITER = 1000
DELTA = 6.0
ETOL = 5 #round to nearest 1e-5
RMS_DG = 0.25
RMS_ROT = 0.25
RMS_CNF = 0.10
EQV_DIS = 0.25

def getstereoconf(mol,conf):
    match_tetr = Chem.MolFromSmarts("[X4]")
    match_pyra = Chem.MolFromSmarts("[X3;^3]")
    match_trig = Chem.MolFromSmarts("[X3;^2;!a]=[X3;^2;!a]")
    
    dihed = []
    
    for match in mol.GetSubstructMatches(match_tetr):
        a = mol.GetAtomWithIdx(match[0])
        #if a.GetChiralTag(): continue
        n = a.GetNeighbors()
        ijkl = [a.GetIdx(), n[0].GetIdx(), n[1].GetIdx(), n[2].GetIdx()]
        d = rdMolTransforms.GetDihedralDeg(conf, *ijkl)
        dihed.append(ijkl + [d,"TET"])

    for match in mol.GetSubstructMatches(match_pyra):
        a = mol.GetAtomWithIdx(match[0])
        n = a.GetNeighbors()
        ijkl = [a.GetIdx(), n[0].GetIdx(), n[1].GetIdx(), n[2].GetIdx()]
        d = rdMolTransforms.GetDihedralDeg(conf, *ijkl)
        dihed.append(ijkl + [d,"PYR"])
        
    for match in mol.GetSubstructMatches(match_trig):
        a = mol.GetAtomWithIdx(match[0])
        a2 = mol.GetAtomWithIdx(match[1])
        n = [atm for atm in a.GetNeighbors() if atm.GetIdx() != a2.GetIdx()]
        n2 = [atm for atm in a2.GetNeighbors() if atm.GetIdx() != a.GetIdx()]
        if len(n) < 2 or len(n2) < 2: continue # skip C=C=C
        ijkl = [n[0].GetIdx(),match[0],match[1],n2[0].GetIdx()]
        #ijkl2 = [n[1].GetIdx(),match[0],match[1],n2[1].GetIdx()]
        d = rdMolTransforms.GetDihedralDeg(conf, *ijkl)
        #d2 = rdMolTransforms.GetDihedralDeg(conf, *ijkl2)
        if -90 < d < 90: d = 0
        if -180 < d < -90 or 90 < d < 180: d = 180 
        dihed.append(ijkl + [d,"TRI"])
        #dihed.append(ijkl2 + [d])
        
    return dihed

def matchstereoconf(mol,conf,ref,tol=ATOL):
    flg = True #if ref blank return True
    for line in ref:
        d_ref = line[4]
        d = rdMolTransforms.GetDihedralDeg(conf, *line[:4])
        if abs((d-d_ref+180)%360-180) > tol:
            #print("FAIL",conf.GetId(),line,d)
            flg = False
        else: 
            #print("PASS",conf.GetId(),line,d)
            pass
    return flg


#boltzman weights
def boltzmann(E,T=273.15+30):
    k = 0.0019872041
    E0 = np.min(E)
    E = np.exp(-(E-E0)/k/T)
    P = E/np.sum(E)
    return P


def mmffmin(args):
    mol,c,restraints = args

    ff = Chem.MMFFGetMoleculeForceField(mol, Chem.MMFFGetMoleculeProperties(mol), confId=c)
    for line in restraints:
        ff.MMFFAddTorsionConstraint(line[0],line[1],line[2],line[3],False,line[4]-ATOL,line[4]+ATOL,APOT)
    ff.Initialize()
    result = ff.Minimize(maxIts=MAXITER)
    e = ff.CalcEnergy()

    #result = 0: sucess, result = 1: not converged
    if result == 1: e = float("inf")

    return [int(c),e,mol]

def distmat(a,b):
    mat = np.zeros((a.shape[0],b.shape[0]))
    for i in range(a.shape[0]):
        mat[i,:] = np.sqrt(np.sum((b - a[i:i+1,:])**2,axis=1))
    return mat

def bestrmsmat(mol,confs,removeHs=True):
    l = []
    if removeHs:
        mol = Chem.RemoveHs(mol)
    #1,0; 2,0; 2,1; ...
    for i in range(len(confs)):
        for j in range(len(confs)):
            if j < i:
                l.append(Chem.GetBestRMS(mol,mol,confs[i],confs[j]))
    return l

if __name__ == "__main__":
    freeze_support()
    NPROC = 4
    NCONF = 1000
    #INPUT = None
    INPUT = sys.argv[1]
    #INPUT = '/home/<USER>/projects/notbitbucket/champs/aGlucose.mol'
    PREF = time.time()
    OUTPUT = 'output.sdf'
    OUTPUT2 = 'output_energies.out'
    #OUTPUT3 = 'output_equiv.out'
    #OUTPUT_ALLCONF_PREF = 'output_allconf'
    OUTPUT_ALLCONF_PREF = None
    TEMP = 298.15

    starttime = time.time()

    #start with a 3D molecule, with hydrogens
    print('input: %s'%INPUT)
    mol = Chem.MolFromMolFile(INPUT,removeHs=False)
    #if INPUT:
        #print('input: %s'%INPUT)
        #mol = Chem.MolFromMolFile(INPUT,removeHs=False)
    #else:
        #smiles = '[O-]C(=O)C(C(C)(C))[NH3+]'
        #smiles = '[OH]C(=O)C(Cc1ccc(O)cc1)[NH2]'
        #smiles = "CCO" #doesn't see plane symmetry
        #smiles = "c1ccccc1" #doesn't see rotational symmetry
        #smiles = "CF" #doesn't see CH3 symmetry here
        #print('input: %s'%smiles)
        #mol = Chem.MolFromSmiles(smiles)
        #mol = Chem.AddHs(mol)
        #Chem.EmbedMolecule(mol)

    Chem.AssignAtomChiralTagsFromStructure(mol,replaceExistingTags=True)
    Chem.AssignStereochemistry(mol,force=True)
    #Chem.MolToMolFile(mol,'output_init.mol')

    #save orig conformation and stereo restraints
    inputconf = mol.GetConformer()
    stereoconf = getstereoconf(mol,inputconf)

    print("number of chiral restraints: %s"%len(stereoconf))
    #for line in stereoconf:
    #    print(line)

    #make lots of confs with DG
    print('generating %s conformations'%NCONF)
    params = Chem.ETKDGv2()
    params.useRandomCoords = False
    params.enforceChirality = True
    params.useExpTorsionAnglePrefs = True
    params.useBasicKnowledge = True
    #params.onlyHeavyAtomsForRMS = False
    #params.pruneRmsThresh = RMS_DG
    params.numThreads = NPROC
    cids = Chem.EmbedMultipleConfs(mol,NCONF,params)

    #do MMFF on each conformer with restraints
    print('optimizing %s conformations'%mol.GetNumConformers())
    pool = Pool(NPROC)
    result_pool = pool.map(mmffmin, [(mol, c, stereoconf) for c in cids])

    #update conformers in mol, same order as result_pool
    mol.RemoveAllConformers()
    for i in range(len(result_pool)):
        mol.AddConformer(result_pool[i][2].GetConformer(result_pool[i][0]))
        del result_pool[i][2]
        
    print("%s out of %s structures optimized sucessfully"%(len([e for e in result_pool if e[1] != float('inf')]),len(result_pool)))

    for line in result_pool:
        if not matchstereoconf(mol,mol.GetConformer(line[0]),stereoconf,tol=15,):
            line[1] = float('inf')
    print("%s out of %s structures with correct stereochemistry "%(len([e for e in result_pool if e[1] != float('inf')]),len(result_pool)))
    
    
    #remove high energy conformers
    min_energy = min([line[1] for line in result_pool]) + DELTA
    for i in range(len(result_pool)-1,-1,-1):
        if result_pool[i][1] > min_energy:
            mol.RemoveConformer(result_pool[i][0])
            del result_pool[i]
        else:
            result_pool[i][1] = round(result_pool[i][1],ETOL)
    print("%s structures remaining after energy cutoff of %s kcal/mol"%(len(result_pool),DELTA))

    #align by heavy, cluster by all atom rms -> find unique rotamers
    Chem.AlignMolConformers(mol,atomIds=[a.GetIdx() for a in mol.GetAtoms() if a.GetSymbol != "H"])
    rmsmat = Chem.GetConformerRMSMatrix(mol,atomIds=[a.GetIdx() for a in mol.GetAtoms() if a.GetSymbol != "H"],prealigned=True)
    clusters = Butina.ClusterData(rmsmat, mol.GetNumConformers(), RMS_ROT, isDistData=True, reordering=True)
    print("%s unique conformations found with all-atom rmsd > %s"%(len(clusters),RMS_ROT))

    #mark duplicate molecules
    for n,c in enumerate(clusters):
        c_ener = [result_pool[i][1] for i in c]
        c_minidx = c_ener.index(min(c_ener))
        c_idx = c[c_minidx]
        for i in c:
            if i == c_idx:
                continue
            else:
                result_pool[i][1] = float('inf')

    #remove duplicate conformations
    for i in range(len(result_pool)-1,-1,-1):
        if result_pool[i][1] == float('inf'):
            mol.RemoveConformer(result_pool[i][0])
            del result_pool[i]

    #calc boltzmann populations
    energies = np.array([i[1] for i in result_pool])
    populations = boltzmann(energies,T=298.15)
    for i,p in zip(result_pool,populations):
        i.append(round(float(p),3))

    #cluster by rms (heavy atoms, with permutation) -> find unique conformers, rotamers clustered together
    #doesn't permute eg =[O] and -[O-] in C(=O)[O-]
    #slow
    rmsmat = bestrmsmat(mol,[c.GetId() for c in mol.GetConformers()],removeHs=True)
    clusters = Butina.ClusterData(rmsmat, mol.GetNumConformers(), RMS_CNF, isDistData=True, reordering=True)
    print("%s conformers found with heavy-atom rmsd > %s"%(len(clusters),RMS_CNF))


    w = Chem.SDWriter(OUTPUT)
    final_ener_pop = []
    atom_equiv = {a.GetIdx():{a.GetIdx():a.GetSymbol()} for a in mol.GetAtoms()}
    #Chem.AlignMolConformers(mol,atomIds=[a.GetIdx() for a in mol.GetAtoms() if a.GetSymbol() != "H"])
    for n,c in enumerate(clusters):

        #take minimum energy as representative structure
        ener = np.array([result_pool[i][1] for i in c])
        pop = np.array([result_pool[i][2] for i in c])
        avener = np.min(ener)
        bestidx = np.argmin(np.abs(ener-avener))

        print("conformer %s, %s rotamers, energy %s - %s kcal/mol, population %s"%(n,len(c),np.min(ener),np.max(ener),sum(pop)))

        #write representative struture to file
        w.write(mol,confId=result_pool[c[bestidx]][0])

        #write all conformations to files
        if OUTPUT_ALLCONF_PREF: 
            w_allconf = Chem.SDWriter(OUTPUT_ALLCONF_PREF+"_%s.sdf"%n)
            for i in c:
                w_allconf.write(mol,result_pool[i][0])
            w_allconf.close()

        #try to find equivalent atoms = same coordinates in different rotamers
        #need to align (slow)
        for i in c:
            print(i,c[bestidx],result_pool[i])
            Chem.GetBestRMS(mol,mol,result_pool[i][0],result_pool[c[bestidx]][0])

            confa = mol.GetConformer(result_pool[c[bestidx]][0]).GetPositions()
            confb = mol.GetConformer(result_pool[i][0]).GetPositions()
            dmat = distmat(confa,confb)
            #dmat[dmat > EQV_DIS] = np.inf
            minidx = np.argmin(dmat,axis=1)
            for atmidx in range(minidx.size):
                equividx = int(minidx[atmidx])
                sym = mol.GetAtomWithIdx(equividx).GetSymbol()
                if sym == atom_equiv[atmidx][atmidx]:
                    atom_equiv[atmidx][equividx] = sym

        #add relative population to table
        final_ener_pop.append([])
        for i in c:
            final_ener_pop[n].append(result_pool[i][1:])

    w.close()

    w = open(OUTPUT2,'w')
    print('writing energies and populations')
    for i,line in enumerate(final_ener_pop):

        sumpop = sum([p for e,p in line])
        w.write("conformer %s %s\n"%(i,sumpop))
        
        for i,l in enumerate(line):
            w.write("rotamer %s %s %s\n"%(i,l[0],l[1]))

    w.close()
    
    #equivalent atoms
    #print('writing equivalencies')
    #w = open(OUTPUT3,'w')
    #for a in mol.GetAtoms():
        #idx = a.GetIdx()
        #sym = a.GetSymbol()
        #equ = [i for i in atom_equiv[idx]]
        #print(idx,sym,equ)
        #w.write("%s %s %s\n"%(idx,sym," ".join(map(str,equ))))
    #w.close()
    
    endtime = time.time()
    print("time elapsed, %s"%(endtime-starttime))
