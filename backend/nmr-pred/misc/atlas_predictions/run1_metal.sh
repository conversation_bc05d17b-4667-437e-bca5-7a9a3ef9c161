
#this script is a workaround for atoms with covalently bonded metal atoms
#best way to make the starting file is to
#draw in marvinJS with "clean 3d" plugin 
#(eg the demo version on the offical marvinsketch site)
#nicer geometry could be had using eg xtb (need to clean up charge/bond orders...) 
#or gaussian

#the cdk based nmrshiftdb predictor needs v2000 mol files
#everthing else can use v3000 mol files (may need sanitization depending on what used for)
#inchi requires single bonds (dont bother for now)
#do not know if moldb handles v3000 or not


python_path=/home/<USER>/miniconda3/envs/myenv/bin/python
nmrpred_dir=/home/<USER>/projects/np-mrd/backend/nmr-pred
nmrml_dir=/home/<USER>/projects/np-mrd/backend/nmr_ml

input_path=/home/<USER>/projects/np-mrd/backend/nmr-pred/misc/atlas_predictions/input
output_path=/home/<USER>/projects/np-mrd/backend/nmr-pred/misc/atlas_predictions/output


for f in "$@"; do

    #keep everything after last slash and before dot
    mol_base=$( echo ${f##*/} )
    mol_base=$( echo ${mol_base%.*} )
    echo 'input file' $f
    echo 'base name' $mol_base

    if ! [ -d "$output_path/$mol_base" ]; then
        echo 'making folder' $output_path/$mol_base
        mkdir $output_path/$mol_base
    else
        echo 'folder exists, skipping' $mol_base
        continue
    fi




    #converts structures using rdkit:
    #makes *.mol, *.pdb, *.smi (smiles) *.inchi, *.png
    #these files should be downloadable on the metabocard page
    #
    #the mol file shoould be copied to *_temp_3D.mol on the website
    #
    #would be better to submit mol/smiles to moldb mirror, 
    #which also makes the files using rdkit, and pull the mol file here for use
    #making the mol file more than once could cause inconsistencies
    echo 'making structure files (for moldb)'
    structurepath=$output_path/$mol_base/structures
    newmolname=$structurepath/$mol_base.mol
    newmolnameV2=$structurepath/${mol_base}_v2000.mol
    newmolnameV3=$structurepath/${mol_base}_v3000.mol
    mkdir $structurepath


    $python_path $nmrpred_dir/renumber_metal_mol.py $f $structurepath/${mol_base}_v3000.mol 
    $python_path $nmrpred_dir/renumber_metal_mol.py $structurepath/${mol_base}_v3000.mol $structurepath/${mol_base}_v2000.mol --convertV2000

    $python_path $nmrpred_dir/convert_from_mol.py $structurepath/${mol_base}_v3000.mol $structurepath/${mol_base} --skipprocessing

    #makes 2d versions of molecule
    #makes *_2d.mol, *_2d.png
    #the 2d image is used on the NP-MRD thumbnails (copy to *_temp_3D.png for website use)
    #the 2d mol may be used in the future for nmrml files (and is used run3.sh here)
    echo 'draw 2d images (for npmrd)'
    structurepath2d=$output_path/$mol_base/structures_2d
    mkdir $structurepath2d
    $python_path $nmrpred_dir/draw_mol_simple.py --mol $newmolname \
                                                 --write2dmol \
                                                 --outputpath $structurepath2d \
                                                 --outputprefix $mol_base


    if [ $? -ne 0 ]; then
        echo 'ERROR: structure generation for' $f
        continue
    fi

    echo 'run nmrpred'
    #inital predictions to use later using nmrshiftdb:
    #*_13c_shifts.txt
    #*_1h_formatedcouplings.txt
    #*_1h_couplings.txt
    #*_1h_shifts.txt
    #*_assignmenttable.txt >>> this file can be parsed into the database
    #                          columns: atom_symbol, atom_id, pred_shift, pred_mult, pred_coup
    #
    #solvent has no effect with nmrshiftdb predictions 
    #sfrq has no effect on output
    #
    #other predictors could be used instead or in parallel
    #as long as they output two-column text files
    #with columns: atom_id, predidcted_value
    predictionfolder=$output_path/$mol_base/predictions
    mkdir $predictionfolder
    $python_path $nmrpred_dir/nmrpred.py --mol $newmolnameV2 --nooptmol \
                                         --solvent d2o \
                                         --sfrq 500 \
                                         --outputpath $predictionfolder \
                                         --outputprefix $mol_base \
                                         --nopred1hcoup \
                                         --write1h --write13c \
                                         --deletemol

    $python_path $nmrpred_dir/nmrpred.py --mol $newmolnameV3 \
                                         --solvent d2o \
                                         --sfrq 500 \
                                         --outputpath $predictionfolder \
                                         --outputprefix $mol_base \
                                         --nopred1h \
                                         --nopred13c \
                                         --input1h       $predictionfolder/${mol_base}_1h_shifts.txt \
                                         --input13c      $predictionfolder/${mol_base}_13c_shifts.txt \
                                         --write1hcoup \
                                         --writeassignmenttable \
                                         --deletemol


    echo 'make spectra'
    #makes 1h and 13c 1D spectra from predictions:
    #*_peaklist.txt
    #*_fid.txt
    #*_1d.png
    #*_spectrum.txt
    #*_params.txt
    #could be deposited separately if desired
    #
    #solvent has no effect here
    #sfrq could be pulled from bmrb data
    #
    #these file are used to convert to nmrml
    

    for frq in 100 200 300 400 500 600 700 ************; do

        echo 'calculating for' $frq 'MHz'


        simulationsfolder=$output_path/$mol_base/spectra_${frq}
        mkdir $simulationsfolder

        $python_path $nmrpred_dir/nmrpred.py    --mol $newmolname \
                                                --deletemol \
                                                --noprediction \
                                                --solvent d2o \
                                                --sfrq $frq \
                                                --input1h $predictionfolder/${mol_base}_1h_shifts.txt \
                                                --input1hcoup $predictionfolder/${mol_base}_1h_couplings.txt \
                                                --plot1h \
                                                --input13c $predictionfolder/${mol_base}_13c_shifts.txt \
                                                --plot13c \
                                                --outputpath $simulationsfolder \
                                                --outputprefix $mol_base

        #make nmrml files
        #*.nmrML
        #if the predictions are to be used as a separate deposition
        #one or more of these files should be downloadable from the website
        #(ask mark/manoj which ones)
        #
        #some parameters could be parsed from the bmrb data (solvent, species etc)
        $python_path $nmrml_dir/nmrml_creator.py    -mol $structurepath2d/${mol_base}_2d.mol \
                                                    -pl $simulationsfolder/${mol_base}_1h_peaklist.txt \
                                                    -name ${mol_base} \
                                                    -solvent d2o  \
                                                    -freq $frq \
                                                    -standard DSS \
                                                    -temp 25 \
                                                    -spec_type 2D-1H-13C \
                                                    -param_path $simulationsfolder/${mol_base}_1h_params.txt \
                                                    -fid_path $simulationsfolder/${mol_base}_1h_fid.txt \
                                                    -spec_path $simulationsfolder/${mol_base}_1h_spectrum.txt \
                                                    -output_path $simulationsfolder/${mol_base}.nmrML \
                                                    -13C_pl $simulationsfolder/${mol_base}_13c_peaklist.txt \
                                                    -13C_param_path $simulationsfolder/${mol_base}_13c_params.txt \
                                                    -13C_fid_path $simulationsfolder/${mol_base}_13c_fid.txt \
                                                    -13C_spec_path $simulationsfolder/${mol_base}_c13_spectrum.txt \
                                                    -ref_type BMRB  \
                                                    -ref ${mol_base}  \
                                                    > $simulationsfolder/${mol_base}_creator.log

    rm -f $simulationsfolder/*fid.txt
    rm -f $simulationsfolder/*spectrum.txt
    rm -f $simulationsfolder/13C_1H*.nmrML

    done

done





