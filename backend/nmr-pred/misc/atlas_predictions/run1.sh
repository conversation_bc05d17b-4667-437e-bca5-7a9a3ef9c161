#generates rdkit structure files
#then predicts shifts and couplings
#and generate spectra at multiple frequencies (100,200,300,...1000)
#
#input/
#   structure1.mol
#   structure2.mol
#...etc.

#makes....
#output/
#   structure1/
#   structure2/

#manually find files with charge or metals
#these may need to be done manually
#grep CHG input/*mol
#egrep -v " H | C | N | O | P | S | Cl | Br | I | F " input/*mol | grep 0\.0000 | grep " [A-Z]"

python_path=/home/<USER>/miniconda3/envs/myenv/bin/python
nmrpred_dir=/home/<USER>/projects/np-mrd/backend/nmr-pred
nmrml_dir=/home/<USER>/projects/np-mrd/backend/nmr_ml

input_path=/home/<USER>/projects/np-mrd/backend/nmr-pred/misc/atlas_predictions/input
output_path=/home/<USER>/projects/np-mrd/backend/nmr-pred/misc/atlas_predictions/output


for f in $input_path/*.mol; do

    #keep everything after last slash and before dot
    mol_base=$( echo ${f##*/} )
    mol_base=$( echo ${mol_base%.*} )
    echo 'input file' $f
    echo 'base name' $mol_base

    if ! [ -d "$output_path/$mol_base" ]; then
        echo 'making folder' $output_path/$mol_base
        mkdir $output_path/$mol_base
    #else
    #    echo 'folder exists, skipping' $mol_base
    #    continue
    fi




    #converts structures using rdkit:
    #makes *.mol, *.pdb, *.smi (smiles) *.inchi, *.png
    #these files should be downloadable on the metabocard page
    #
    #the mol file shoould be copied to *_temp_3D.mol on the website
    #
    #would be better to submit mol/smiles to moldb mirror, 
    #which also makes the files using rdkit, and pull the mol file here for use
    #making the mol file more than once could cause inconsistencies
    echo 'making structure files (for moldb)'
    structurepath=$output_path/$mol_base/structures
    newmolname=$structurepath/$mol_base.mol
    if [ -d "$structurepath" ]; then
        echo 'folder exists, skipping making structre'
    else
        mkdir $structurepath
        $python_path $nmrpred_dir/convert_from_mol.py $f $structurepath/$mol_base
    fi


    #makes 2d versions of molecule
    #makes *_2d.mol, *_2d.png
    #the 2d image is used on the NP-MRD thumbnails (copy to *_temp_3D.png for website use)
    #the 2d mol may be used in the future for nmrml files (and is used here)
    echo 'draw 2d images (for npmrd)'
    structurepath2d=$output_path/$mol_base/structures_2d
    if [ -d "$structurepath2d" ]; then
        echo 'folder exists, skipping making 2d structre'
    else
        mkdir $structurepath2d
        $python_path $nmrpred_dir/draw_mol_simple.py --mol $newmolname \
                                                     --write2dmol \
                                                     --outputpath $structurepath2d \
                                                     --outputprefix $mol_base
    fi


    if [ $? -ne 0 ]; then
        echo 'ERROR: structure generation for' $f
        continue
    fi

    echo 'run nmrpred'
    #inital predictions to use later using nmrshiftdb:
    #
    #other predictors could be used instead or in parallel
    #as long as they output two-column text files
    #with columns: atom_id, predidcted_value
    predictionfolder=$output_path/$mol_base/predictions
    mkdir $predictionfolder
    $python_path $nmrpred_dir/nmrpred.py --mol $newmolname \
                                         --solvent d2o \
                                         --sfrq 500 \
                                         --outputpath $predictionfolder \
                                         --outputprefix $mol_base \
                                         --write1h --write13c --write1hcoup \
                                         --writeassignmenttable \
                                         --deletemol



    echo 'make spectra'
    #makes 1h and 13c 1D spectra from predictions:
    #
    #solvent has no effect here
    for frq in 100 200 300 400 500 600 700 800 900 1000; do

        echo 'calculating for' $frq 'MHz'


        simulationsfolder=$output_path/$mol_base/spectra_${frq}
        mkdir $simulationsfolder

        $python_path $nmrpred_dir/nmrpred.py    --mol $newmolname \
                                                --deletemol \
                                                --noprediction \
                                                --solvent d2o \
                                                --sfrq $frq \
                                                --input1h $predictionfolder/${mol_base}_1h_shifts.txt \
                                                --input1hcoup $predictionfolder/${mol_base}_1h_couplings.txt \
                                                --plot1h \
                                                --input13c $predictionfolder/${mol_base}_13c_shifts.txt \
                                                --plot13c \
                                                --outputpath $simulationsfolder \
                                                --outputprefix $mol_base

        #make nmrml files from predictions
        #one or more of these files should be downloadable from the website
        #(ask mark/manoj which ones)
        $python_path $nmrml_dir/nmrml_creator.py    -mol $structurepath2d/${mol_base}_2d.mol \
                                                    -pl $simulationsfolder/${mol_base}_1h_peaklist.txt \
                                                    -name ${mol_base} \
                                                    -solvent d2o  \
                                                    -freq $frq \
                                                    -standard DSS \
                                                    -temp 25 \
                                                    -spec_type 2D-1H-13C \
                                                    -param_path $simulationsfolder/${mol_base}_1h_params.txt \
                                                    -fid_path $simulationsfolder/${mol_base}_1h_fid.txt \
                                                    -spec_path $simulationsfolder/${mol_base}_1h_spectrum.txt \
                                                    -output_path $simulationsfolder/${mol_base}.nmrML \
                                                    -13C_pl $simulationsfolder/${mol_base}_13c_peaklist.txt \
                                                    -13C_param_path $simulationsfolder/${mol_base}_13c_params.txt \
                                                    -13C_fid_path $simulationsfolder/${mol_base}_13c_fid.txt \
                                                    -13C_spec_path $simulationsfolder/${mol_base}_c13_spectrum.txt \
                                                    -ref_type BMRB  \
                                                    -ref ${mol_base}  \
                                                    > $simulationsfolder/${mol_base}_creator.log

    #delete some big files
    rm -f $simulationsfolder/*fid.txt
    rm -f $simulationsfolder/*spectrum.txt
    rm -f $simulationsfolder/13C_1H*.nmrML

    done

done





