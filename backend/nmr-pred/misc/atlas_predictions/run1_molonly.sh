#generates rdkit structure files
#then predicts shifts and couplings
#and generate spectra at multiple frequencies (100,200,300,...1000)
#
#input/
#   structure1.mol
#   structure2.mol
#...etc.

#makes....
#output/
#   structure1/
#   structure2/

#manually find files with charge or metals
#these may need to be done manually
#grep CHG input/*mol
#egrep -v " H | C | N | O | P | S | Cl | Br | I | F " input/*mol | grep 0\.0000 | grep " [A-Z]"

python_path=/home/<USER>/miniconda3/envs/myenv/bin/python
nmrpred_dir=/home/<USER>/projects/np-mrd/backend/nmr-pred
nmrml_dir=/home/<USER>/projects/np-mrd/backend/nmr_ml

input_path=/home/<USER>/projects/np-mrd/backend/nmr-pred/misc/atlas_predictions/input
output_path=/home/<USER>/projects/np-mrd/backend/nmr-pred/misc/atlas_predictions/output


for f in $input_path/*.mol; do

    #keep everything after last slash and before dot
    mol_base=$( echo ${f##*/} )
    mol_base=$( echo ${mol_base%.*} )
    echo 'input file' $f
    echo 'base name' $mol_base

    if ! [ -d "$output_path/$mol_base" ]; then
        echo 'making folder' $output_path/$mol_base
        mkdir $output_path/$mol_base
    fi




    #converts structures using rdkit:
    #makes *.mol, *.pdb, *.smi (smiles) *.inchi, *.png
    #these files should be downloadable on the metabocard page
    #
    #the mol file shoould be copied to *_temp_3D.mol on the website
    #
    #would be better to submit mol/smiles to moldb mirror, 
    #which also makes the files using rdkit, and pull the mol file here for use
    #making the mol file more than once could cause inconsistencies
    echo 'making structure files (for moldb)'
    structurepath=$output_path/$mol_base/structures
    newmolname=$structurepath/$mol_base.mol
    if [ -d "$structurepath" ]; then
        echo 'folder exists, skipping making structre'
    else
        mkdir $structurepath
        $python_path $nmrpred_dir/convert_from_mol.py $f $structurepath/$mol_base
    fi


    #makes 2d versions of molecule
    #makes *_2d.mol, *_2d.png
    #the 2d image is used on the NP-MRD thumbnails (copy to *_temp_3D.png for website use)
    #the 2d mol may be used in the future for nmrml files (and is used here)
    echo 'draw 2d images (for npmrd)'
    structurepath2d=$output_path/$mol_base/structures_2d
    if [ -d "$structurepath2d" ]; then
        echo 'folder exists, skipping making 2d structre'
    else
        mkdir $structurepath2d
        $python_path $nmrpred_dir/draw_mol_simple.py --mol $newmolname \
                                                     --write2dmol \
                                                     --outputpath $structurepath2d \
                                                     --outputprefix $mol_base
    fi


    if [ $? -ne 0 ]; then
        echo 'ERROR: structure generation for' $f
    fi


done





