import xytojcamp
import numpy as np
from scipy.interpolate import interp1d
import sys
import json


"""

python writespectrumdx.py --input example_1h_spectrum.txt --ofrq 600 --nuc 1H > output_file.dx


supported output types (--format): FIX, PAC, SQZ, DIF, DIFDUP

--filetype csv : for comma separated values
--filetype tsv : for whitespace separated values
--filetype json : for magmet/bayesil style json 
                  root:
                      spectrum_xy:
                          x: 1,2,3,...
                          y: 0,0,0,...
--filetype nmrml : reads spectrumDataArray as 'complex': x1,y1,x2,y2,...

--ofrq (observe frequency) and --nuc (observe nucleus) should be specified
--sfrq (spectrometer freq) is optional (may be useful if different than observe freq)

sample information not implemented other than:
--name "text describing sample"


notes on BRUKER TOPSPIN reading of these JCAMPDX files

Topspin plots x axis values 1 point off the intended value
--topsin option will shift the x axis, but will be off for other programs

Topspin reads DIFDUP incorrectly
eg @RU (0,+9x3) interpreted as 0,9,9,9 rather than 0,9,18,27
use different format instead (FIX, PAC, SQZ, or DIF)

"""


def togroup(l):
    s = ""
    for i in l:
        if " " in i or "," in i or ";" in i:
            i = "<" + i + ">"
        if s:
            s += ", "
        s += i
    return s

def readtsv(inputfile):
    x, y = np.loadtxt(inputfile,unpack=True)
    return x, y

def readcsv(inputfile):
    x, y = np.loadtxt(inputfile,delimiter=",",unpack=True)
    return x, y

def readjson(inputfile):
    with open(inputfile) as f:
        j = json.load(f)
    x = j['spectrum_xy']['x']
    y = j['spectrum_xy']['y']
    return np.array(x), np.array(y)

def readnmrml(inputfile):

    matchstr = "\s*<(spectrumDataArray.*?)>(.*)</spectrumDataArray>"
    str1 = "compressed=\"(.*?)\""
    str2 = "encodedLength=\"(.*?)\""
    str3 = "byteFormat=\"(.*?)\""

    x = y = None

    with open(nmrmlfile) as f:

        for line in f:

            match = re.match(matchstr,line)

            if match:

                header = match.group(1)
                data = match.group(2)
                
                compressed = re.search(str1,header).group(1)
                encodedLength = re.search(str2,header).group(1)
                byteFormat = re.search(str3,header).group(1)

                data = base64.b64decode(data)
                dataarr = np.fromstring(data,byteFormat)
                x, y = dataarr.real, dataarr.imag

    return x, y

if __name__ == "__main__":


    header = {"TITLE":"NO TITLE",
              "JCAMP-DX":"5.0",
              "DATA TYPE":"NMR SPECTRUM",
              "DATA CLASS":"XYDATA",
              "OWNER":"UNKNOWN",
              "ORIGIN":"UNKNOWN",
              }

    sampleinfo = {"SAMPLE DESCRIPTION":"",
                  "MP":"",
                  "BP":"",
                  "MW":"",
                  "CONCENTRATIONS":"",
                  # ##CONCENTRATIONS=(NCU)
                  # (<acetic acid>, 100, mM)
                  # (cdcl3, 100, %)
                  # ...
                  ".SOLVENT NAME":"",
                  }

    specinfo = {"SPECTROMETER/DATA SYSTEM":"UNKNOWN",
                ".OBSERVE FREQUENCY":"",
                ".OBSERVE NUCLEUS":"",
                ".SOLVENT REFERENCE":"0.0", #REFERENCE PEAK PPM
                ".SHIFT REFERENCE":["INTERNAL","DSS","NA","0.0"], #INTERNAL/EXTERNAL, COMPOUND NAME, DATA POINT (1 INDEXED), PEAK PPM @ DATA POINT
                ".FIELD":"",
                }

    params = {"XUNITS":"PPM",
              "YUNITS":"ARBITRARY UNITS",
              "XFACTOR":"",
              "YFACTOR":"",
              "FIRSTX":"",
              "LASTX":"",
              "MAXY":"",
              "MINY":"",
              "FIRSTY":"",
              "NPOINTS":"",
              "XYDATA":"",
              }

    footer = {"END":""}

    inputxy = None
    form = "DIF"
    topspin = False
    filetype = 'csv' #csv tsv or json

    for i in range(len(sys.argv)):
        
        if sys.argv[i] == '--input':
            inputxy = sys.argv[i+1]

        if sys.argv[i] == '--format':
            form = sys.argv[i+1]
        if sys.argv[i] == '--topspin':
            topspin = True
        if sys.argv[i] == '--xytype':
            filetype = sys.argv[i+1]
            
        if sys.argv[i] == '--title':
            header["TITLE"] = sys.argv[i+1]
        if sys.argv[i] == '--origin':
            header["ORIGIN"] = sys.argv[i+1]
        if sys.argv[i] == '--owner':
            header["OWNER"] = sys.argv[i+1]

        if sys.argv[i] == '--name':
            sampleinfo["SAMPLE DESCRIPTION"] = sys.argv[i+1]

        if sys.argv[i] == '--spec':
            specinfo["SPECTROMETER/DATA SYSTEM"] = sys.argv[i+1]
        if sys.argv[i] == '--sfrq':
            specinfo[".FIELD"] = sys.argv[i+1]
        if sys.argv[i] == '--nuc':
            specinfo[".OBSERVE NUCLEUS"] = sys.argv[i+1]
        if sys.argv[i] == '--ofrq':
            specinfo[".OBSERVE FREQUENCY"] = sys.argv[i+1]

        if sys.argv[i] == '--reftype':
            specinfo[".SHIFT REFERENCE"][0] = sys.argv[i+1]
        if sys.argv[i] == '--refcpd':
            specinfo[".SHIFT REFERENCE"][1] = sys.argv[i+1]
        if sys.argv[i] == '--refval':
            specinfo[".SHIFT REFERENCE"][3] = sys.argv[i+1]
            specinfo[".SOLVENT REFERENCE"] = sys.argv[i+1]

    if inputxy is None: raise RuntimeError()
    if not specinfo[".OBSERVE NUCLEUS"]: raise RuntimeError()
    if not specinfo[".OBSERVE FREQUENCY"]: raise RuntimeError()

    if filetype == 'tsv':
        x, y = readtsv(inputxy)
    elif filetype == 'csv':
        x, y = readcsv(inputxy)
    if filetype == 'json':
        x, y = readjson(inputxy)
    if filetype == 'nmrml':
        x, y = readjson(inputxy)

    if topspin:
        dx = x[0]-x[1] #0,1,2 -> 0-1=-1
        x += dx

    d, p = xytojcamp.xy_to_dx(x,y,xscale=None,yscale='auto',form=form)
    for i in p:
        if i in params:
            params[i] = p[i]

    ref = float(specinfo[".SHIFT REFERENCE"][3])
    specinfo[".SHIFT REFERENCE"][2] = str(interp1d(x, np.arange(x.size), fill_value="extrapolate")(ref))

    for i in header:
        if not header[i]: continue
        print("##%s=%s"%(i,header[i]))

    for i in sampleinfo:
        if not sampleinfo[i]: continue
        print("##%s=%s"%(i,sampleinfo[i]))


    if topspin:
        print("$$ X axis has been shifted 1 point left for Topspin plotting")

    for i in specinfo:
        if not specinfo[i]: continue
        if i == ".SHIFT REFERENCE":
            specinfo[i] = togroup(specinfo[i])
        print("##%s=%s"%(i,specinfo[i]))

    for i in params:
        if not params[i]: continue
        print("##%s=%s"%(i,params[i]))

    for i in d:
        print(i)

    for i in footer:
        print("##%s=%s"%(i,footer[i]))
