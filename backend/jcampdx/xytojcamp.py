import numpy as np
from copy import deepcopy

DEBUG = False

PARAMS = {
    "$XNAME":"",
    "$YNAME":"",

    "$XSYMBOL":"",
    "$YSYMBOL":"",

    "$XTYPE":"",
    "$YTYPE":"",

    "$XFORM":"",
    "$YFORM":"",

    "XUNITS":"",
    "YUNITS":"",

    "FIRSTX":"",
    "LASTX":"",

    "FIRSTY":"",
    "MINY":"",
    "MAXY":"",

    "XFACTOR":"",
    "YFACTOR":"",

    "NPOINTS":"",

    "XYDATA":"",
    }

SQZ_POS = { "+0":"@",
            "+1":"A",
            "+2":"B",
            "+3":"C",
            "+4":"D",
            "+5":"E",
            "+6":"F",
            "+7":"G",
            "+8":"H",
            "+9":"I",}

SQZ_NEG = { "-0":"@",
            "-1":"a",
            "-2":"b",
            "-3":"c",
            "-4":"d",
            "-5":"e",
            "-6":"f",
            "-7":"g",
            "-8":"h",
            "-9":"i",}


DIF_POS = { "+0":"%",
            "+1":"J",
            "+2":"K",
            "+3":"L",
            "+4":"M",
            "+5":"N",
            "+6":"O",
            "+7":"P",
            "+8":"Q",
            "+9":"R",}

DIF_NEG = { "-0":"%",
            "-1":"j",
            "-2":"k",
            "-3":"l",
            "-4":"m",
            "-5":"n",
            "-6":"o",
            "-7":"p",
            "-8":"q",
            "-9":"r",}

DIF_DUP = { "1":"S",
            "2":"T",
            "3":"U",
            "4":"V",
            "5":"W",
            "6":"X",
            "7":"Y",
            "8":"Z",
            "9":"s",}


def xy_to_dx(x,y,
             xscale=None,yscale=None,
             xprec=5,yprec=0,
             cols=80,ymax=32767,
             form="FIX",
             xname="CHEMICAL SHIFT",yname="INTENSITY",
             xunits="PPM",yunits="ARBITRARY UNITS",
             xsym="X",ysym="Y",
             xtype="INDEPENDENT",ytype="DEPENDENT"):


    params = deepcopy(PARAMS)

    lines = []
    line = []

    if xscale is None:
        xscale = np.abs(x[0]-x[1])
        xprec = 0

    if yscale is None:
        yscale = max(np.max(y),-np.min(y))/ymax
        yprec = 0

    if yscale == 'auto':
        yscale = max(np.max(y),-np.min(y))/ymax
        yprec = 0
        y /= yscale
        yscale = 1

    params["$XNAME"] = xname
    params["$YNAME"]  = yname

    params["$XSYMBOL"] = xsym
    params["$YSYMBOL"]  = ysym

    params["$XTYPE"] = xtype
    params["$YTYPE"] = ytype

    params["$XFORM"] = "AFFN"
    params["$YFORM"] = "AFFN" if form in ["FIXED","PAC"] else "ASDF"

    params["XUNITS"] = xunits
    params["YUNITS"]  = yunits

    params["FIRSTX"] = str(x[0])
    params["LASTX"]  = str(x[-1])

    params["FIRSTY"] = str(y[0])
    params["MINY"] = str(np.min(y))
    params["MAXY"] = str(np.max(y))

    params["XFACTOR"] = str(xscale)
    params["YFACTOR"] = str(yscale)

    params["NPOINTS"] = str(len(x))

    params["XYDATA"] = "({0}++({1}..{1})) $$ {2}".format(xsym,ysym,form)

    newx = x/xscale
    newy = y/yscale


    tmpx = []
    tmpy = []
    tmpd = []

    for i in range(newx.size):

        tmpx.append('{0:0.{1}f}'.format(newx[i], xprec))
        tmpy.append('{0:+0.{1}f}'.format(newy[i], yprec))

        if i == 0:
            tmpd.append(None)
        else:
            tmpd.append('{0:+0.{1}f}'.format( newy[i]-newy[i-1], yprec))

        #print(tmpx[-1],tmpy[-1],tmpd[-1])


    if form == "FIX":

        xsize = len('{0:+0.{1}f}'.format(max(np.max(newx),-np.min(newx)), xprec))
        ysize = len('{0:+0.{1}f}'.format(max(np.max(newy),-np.min(newy)), yprec))

        tmpsize = 0
        tmpline = []
        i = 0

        while i < newx.size:

            #pad with space, x: left align, y: right align
            xi = '{0:<{1}}'.format(tmpx[i],xsize)
            yi = '{0:>{1}}'.format(tmpy[i],ysize)

            if len(tmpline) == 0: #first x,y pair
                tmpline.append(xi)
                tmpline.append(yi)
                tmpsize += len(xi) 
                tmpsize += len(yi) + 1 #space before y value

            elif tmpsize + len(yi) + 1 <= cols:
                tmpline.append(yi)
                tmpsize += len(yi) + 1 #space before y value

            else: #if length >= col, clear line and rewind
                lines.append(" ".join(tmpline))
                tmpsize = 0
                tmpline = []
                i -= 1

            i += 1

        if i == newx.size: #for last point
            lines.append(" ".join(tmpline))
            tmpsize = 0
            tmpline = []


    if form == "PAC":

        tmpsize = 0
        tmpline = []
        i = 0

        while i < newx.size:

            xi = tmpx[i]
            yi = tmpy[i]

            if len(tmpline) == 0: #first x,y pair
                tmpline.append(xi)
                tmpline.append(yi)
                tmpsize += len(xi) 
                tmpsize += len(yi)

            elif tmpsize + len(yi) <= cols:
                tmpline.append(yi)
                tmpsize += len(yi)

            else:
                lines.append("".join(tmpline))
                tmpsize = 0
                tmpline = []
                i -= 1

            i += 1

        if i == newx.size: #for last point
            lines.append("".join(tmpline))
            tmpsize = 0
            tmpline = []



    if form == "SQZ":

        tmpsize = 0
        tmpline = []
        i = 0

        while i < newx.size:

            xi = tmpx[i]
            yi = tmpy[i]

            if   yi[0] == "+": yi = SQZ_POS[yi[:2]] + yi[2:]
            elif yi[0] == "-": yi = SQZ_NEG[yi[:2]] + yi[2:]

            if len(tmpline) == 0: #first x,y pair
                tmpline.append(xi)
                tmpline.append(yi)
                tmpsize += len(xi) 
                tmpsize += len(yi)

            elif tmpsize + len(yi) <= cols:
                tmpline.append(yi)
                tmpsize += len(yi)

            else:
                lines.append("".join(tmpline))
                tmpsize = 0
                tmpline = []
                i -= 1

            i += 1

        if i == newx.size: #for last point
            lines.append("".join(tmpline))
            tmpsize = 0
            tmpline = []

    if form == "DIF":

        tmpsize = 0
        tmpline = []
        i = 0

        while i < newx.size:

            xi = tmpx[i]
            yi = tmpy[i]
            di = tmpd[i]

            if   yi[0] == "+": yi = SQZ_POS[yi[:2]] + yi[2:]
            elif yi[0] == "-": yi = SQZ_NEG[yi[:2]] + yi[2:]

            if i == 0: pass
            elif di[0] == "+": di = DIF_POS[di[:2]] + di[2:]
            elif di[0] == "-": di = DIF_NEG[di[:2]] + di[2:]

            if len(tmpline) == 0: #first x,y pair
                tmpline.append(xi)
                tmpline.append(yi)
                tmpsize += len(xi) 
                tmpsize += len(yi)

            elif tmpsize + len(di) <= cols: # use dif after first y per line
                tmpline.append(di)
                tmpsize += len(di)

            else:
                #add line and clear
                lines.append("".join(tmpline))
                tmpsize = 0
                tmpline = []
                #rewind, Y value calc'd from diff of last line used as checkpoint
                #X1 Y1 D2,1 D3,2 ... DN,N-1
                #XN YN ...
                i -= 2

            i += 1

        if i == newx.size: #for last point
            lines.append("".join(tmpline))
            tmpsize = 0
            tmpline = []

        lines.append( xi + yi + " $$ checkpoint")


    if form == "DIFDUP":

        tmpsize = 0
        tmpline = []
        i = 0

        todif = lambda n: DIF_DUP[str(n)[0]] + str(n)[1:]
        toline = lambda l: l[0]+"".join([s[2] for s in l[1:]])

        while i < newx.size:

            xi = tmpx[i]
            yi = tmpy[i]
            di = tmpd[i]

            if   yi[0] == "+": yi = SQZ_POS[yi[:2]] + yi[2:]
            elif yi[0] == "-": yi = SQZ_NEG[yi[:2]] + yi[2:]

            if i == 0: pass
            elif di[0] == "+": di = DIF_POS[di[:2]] + di[2:]
            elif di[0] == "-": di = DIF_NEG[di[:2]] + di[2:]

            #print(xi, yi, di, tmpline, tmpsize)

            if len(tmpline) == 0: #first x,y pair, y as list for counting

                tmpline.append(xi)
                tmpsize += len(xi)
                tmpline.append([yi,1,yi]) #y string, count, final string
                tmpsize += len(yi)


            elif len(tmpline) == 2 and yi == tmpline[-1][0] and tmpsize - len(tmpline[-1][2]) + len(yi + todif(tmpline[-1][1]+1)) <= cols: #y value same as prev

                #remove from count first
                tmpsize -= len(tmpline[-1][2])
                tmpline[-1][1] += 1
                tmpline[-1][2] = yi + todif(tmpline[-1][1])
                tmpsize += len(tmpline[-1][2])


            elif len(tmpline) == 2 and yi != tmpline[-1][0] and tmpsize + len(di) <= cols: # y is not the same, start using diff

                tmpline.append([di,1,di]) #y string, count, final string
                tmpsize += len(di)

            elif len(tmpline) > 2 and di == tmpline[-1][0] and tmpsize - len(tmpline[-1][2]) + len(di + todif(tmpline[-1][1]+1)) <= cols: # diff value same as prev

                #remove from count first
                tmpsize -= len(tmpline[-1][2])
                tmpline[-1][1] += 1
                tmpline[-1][2] = di + todif(tmpline[-1][1])
                tmpsize += len(tmpline[-1][2])

            elif len(tmpline) > 2 and di != tmpline[-1][0] and tmpsize + len(di) <= cols: # diff value NOT same as prev diff

                tmpline.append([di,1,di]) #y string, count, final string
                tmpsize += len(di)

            else:
                lines.append(toline(tmpline))
                tmpsize = 0
                tmpline = []
                i -= 2

            i += 1

        if i == newx.size: #for last point
            lines.append(toline(tmpline))
            tmpsize = 0
            tmpline = []

        lines.append( xi + yi + " $$ checkpoint")

                    
    return lines, params


if __name__ == "__main__":
    
    x, y = np.loadtxt("example_jcamp_spec.txt",delimiter=",",unpack=True)
    y *= 0.001

    #x = np.array([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20])
    #y = np.array([0,0,0,0,0,1,1,2,2,2,3,4,5,3,3,2,0,0,0,0])

    print(x)
    print(y)

    
    for f in ["FIX","PAC","SQZ","DIF","DIFDUP"]:
        d, params = xy_to_dx(x,y,xscale=None,yscale=0.001,form=f)

        print()
        print(f)

        for p in params:
            if not params[p]: continue
            #print("##%s=%s"%(p,str(params[p])))
        for i in d:
            print(i)


