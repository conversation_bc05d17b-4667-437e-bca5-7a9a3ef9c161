
#-------------------------------------------------------------------------------
# Copyright (c) 2017-2025 <PERSON><PERSON><PERSON> <PERSON>
# All rights reserved
#-------------------------------------------------------------------------------
#####################
# Import global modules
#####################
import os
import sys
import urllib.request, urllib.error, urllib.parse
import copy
# from string import split
# from string import upper
from os import linesep
from types import *
from datetime import datetime
import numpy as np
#####################

def gen_ex(key, var):
    if hasattr(var, 'iteritems'):
        for k, v in var.items():
            if k == key:
                yield v
            if isinstance(v, dict):
                for result in gen_ex(key, v):
                    yield result
            elif isinstance(v, list):
                for d in v:
                    for result in gen_ex(key, d):
                        yield result


def dt_x(data, xmin, xmax):

    if type(data) == np.ndarray:
        data = data
    else:
        data = np.array(data)

    return data[(xmin <= data[:, 0]) & (data[:, 0] <= xmax)]


def moving_window(iterable, size=5):
    i = iter(iterable)
    win = []
    for e in range(0, size):
        win.append(next(i))
    yield win
    for e in i:
        win = win[1:] + [e]
        yield win


def sort_dt(data, column=0):
    data_type = type(data)
    if data_type == np.ndarray:
        np_data = data
    else:
        np_data = np.array(data)
    sorted_data = np_data[np_data[:, column].argsort()]
    return sorted_data


def data_to_np_array(data):

    data_type = type(data)
    if data_type == np.ndarray:
        np_data = data
    else:
        np_data = np.array(data)

    return np_data


def import_csv(filename):
    import csv
    with open(filename) as csvfile:
        reader = csv.reader(csvfile)
        reader_1 = [[str_to_number(r) for r in line] for line in list(reader)]
    return reader_1


def export_result_csv(file_name, result):
    import csv
    with open(file_name, "wb") as f:
        writer = csv.writer(f)
        writer.writerows(result)


def import_file(filename, file_type="CSV"):
    import csv
    import numpy as np

    if file_type == "CSV":
        delimiter = ","
    elif file_type == "TSV":
        delimiter = "\t"

    out = []

    file_open = open(filename, 'r')
    file_lines = file_open.readlines()
    file_open.close()

    for line in file_lines:
        out.append([str_to_number(r) for r in line.split()])
    return out


def str_to_number(str):
    try:
        number = float(str)
        if type(number) == float:
            return number
    except:
        return str


def read_file_mkr(filename):
    out = []
    with open(filename) as file:
        for line in file:
            out.append(line.split())
    return out


def keep_digit(s):

    import re
    return re.sub("\D", "", s)



def natural_key(string_):
    import re
    return [int(s) if s.isdigit() else s for s in re.split(r'(\d+)', string_)]


def unique_result(data):
        unique=[]
        for value in data:
            if value not in unique:
                unique.append(value)
        return unique

def list_product(data):
    result=np.full(len(data[0]),1)
    for r in data:
        result=np.multiply(result,r)
    return result


def union_list(list_of_list):
    combined_list = []
    for l in list_of_list:
        combined_list.extend(l)
    return unique_result(combined_list)

