import sys, os
from os import linesep
from os.path import exists
import time
import numpy as np

import pylab
import matplotlib.pyplot as plt
import matplotlib as mpl


def assignment_score_plot(assignment_score,file):
    col_map = plt.get_cmap('bwr_r')
    score_input = {'Score':[str(assignment_score)+"%",float(assignment_score)]}
    v_l = [list(v) for v in score_input.items()]
    bar_num = len(v_l)
    fig, ax = plt.subplots(figsize = (10,1),dpi = 300)

    bars = ax.barh(range(bar_num),103,height=0.7)
    lim = ax.get_xlim()+ax.get_ylim()
    font_size = 14
    for i in range(bar_num):
        bar = bars[i]
        x,y = bar.get_xy()
        w, h = bar.get_width(),bar.get_height()
        grad = np.atleast_2d(np.linspace(0,100,512))
        grad_lim = [x,x+w,y,y+h]
#         print("grad_lim",grad_lim)
        ax.imshow(grad, extent=grad_lim, aspect="auto",zorder=1,
              cmap =col_map )

        ax.text(x-2,y+h/2,v_l[i][0],va='center', ha='right', fontsize=font_size)
        ax.text(x+w+2,y+h/2,v_l[i][1][0],va='center', ha='left', fontsize=font_size)

        pointer = ax.barh(y+h/2,3,height=h*1.2,left=v_l[i][1][1],color = 'black',zorder=2)
    ax.axis(lim)
    ax.text(lim[0],lim[2]-.2,'Worst',va='center', ha='left', fontsize=font_size)
    ax.text(lim[0]+lim[1]-5,lim[2]-.2,'Best',va='center', ha='right', fontsize=font_size)
    ax.set_axis_off()
    plt.tight_layout()
    plt.savefig(file,transparent=True)
    plt.clf()
    plt.close()


score = 100

if len(sys.argv) > 1:
    for i in range(len(sys.argv)):
        if sys.argv[i] in ["-f"]:  # Done
            file_name = sys.argv[i + 1]
        if sys.argv[i] in ["-s"]:
            score     = sys.argv[i + 1]


assignment_score_plot(score,file_name)