#-------------------------------------------------------------------------------
# Copyright (c) 2017-2025 Man<PERSON><PERSON> <PERSON>out
# All rights reserved
#-------------------------------------------------------------------------------

#####################
# Import global modules
#####################
import sys, os, random, copy, shutil, xmltodict, json, math,glob
from os import linesep
from os.path import exists
from random import shuffle
import time
import pdb
import datetime
import numpy as np
import nmrglue as ng
import numpy as np
import scipy as sp
import os
from standard_functions_3 import *
from standard_functions_mkr_3 import *
from maple_functions_3 import *
#####################

def calc_sigma( y, nregions):
    reg_size = np.floor(y.shape[0]/nregions)
    sig_vals = []
    for reg in range(nregions):
        y_reg = y[int(reg*reg_size): int((reg+1)*reg_size)]
        sig_vals.append(np.std(y_reg))
    return min(sig_vals)

def exp_params():
    p_dict = {}
    p_dict['Instrument']=None
    p_dict['Frequency']=None
    p_dict['Linewidth']=None
    p_dict['S/N']=None
    return p_dict

def clean_files(files):
    for f in files:
        if 'audit' in f:
            if os.path.exists(f):
                os.remove(f)
        else:
            with open(f,'r') as inn:
                lines = inn.readlines()
            with open(f,'w') as out:
                for line in lines:
                    if not (line[:2]=="$$" or 'OWNER' in line):
                        out.write(line)

def remove_security_bruker(sp):
    raw_files = glob.glob(sp + "/*")
    fl_raw = ['acqu','acqu1','acqu1s','acqus']
    raw_files_1 = [r for r in raw_files if os.path.basename(r) in fl_raw]
    clean_files(raw_files_1)
    processed_files = glob.glob(sp +'/pdata'+"/*/*")
    fl = ['proc','proc2','proc2s','procs',
        'meta','meta.txt','clevels','outd',
        'auditp.txt','assocs',]
    processed_files_1 = [r for r in processed_files if os.path.basename(r) in fl]
    clean_files(processed_files_1)

def experiment_axes(expt_type):
    expt_Dict = {'1D-1H': {0:"1H"},
                  '1D-13C': {0:"13C"},
                  '1D-1H-DEPT90': {0:"1H"},
                  '1D-1H-DEPT90': {0:"1H"},
                  '1D-13C-DEPT90':{0:"13C"},
                  '1D-31P': {0:"31P"},
                  '2D-1H-1H-COSY' : {0:"1H", 1:"1H"},
                  '2D-1H-13C-COSY': {0:"1H", 1:"13C"},
                  '2D-1H-13C-COSY': {0:"1H", 1:"13C"},
                  '2D-1H-13C-HSQC': {0:"1H", 1:"13C"},
                  '2D-1H-13C-HMQC': {0:"1H", 1:"13C"},
                  '2D-1H-13C-HMBC': {0:"1H", 1:"13C"},
                  '2D-1H-15N-HMBC': {0:"1H", 1:"15N"},
                  '2D-1H-15N-HSQC': {0:"1H", 1:"15N"},
                  '2D-1H-1H-TOCSY': {0:"1H", 1:"H1"},
                  '2D-1H-1H-ROESY': {0:"1H", 1:"1H"},
                  '2D-13C-13C-INADEQUATE':{0:"13C", 1:"13C"}}
    if expt_type in expt_Dict:
        out = expt_Dict[expt_type]
    else:
        print("experiment_type not in the list. Using '1D-1H' ")
        out = expt_Dict['1D-1H']
    return out

def get_gamma(nuc):
    """
    copied from nmrpipe
    """
    gammaList = {}
    gammaList['H']  = 1.0
    gammaList['1H'] = 1.0
    gammaList['H1'] = 1.0

    gammaList['2H'] = 0.153506088
    gammaList['H2'] = 0.153506088

    gammaList['C']   = 0.251449530
    gammaList['13C'] = 0.251449530
    gammaList['C13'] = 0.251449530

    gammaList['N']   = 0.101329118
    gammaList['15N'] = 0.101329118
    gammaList['N15'] = 0.101329118

    gammaList['P']   = 0.40480864
    gammaList['31P'] = 0.40480864
    gammaList['P31'] = 0.40480864

    gammaList['off']  = 1.000000000
    gammaList['OFF']  = 1.000000000
    gammaList['NONE'] = 1.000000000
    if nuc in gammaList:
        return(gammaList[nuc])
    else:
        return 1.0

def getH20ppm(temp):
    """
    copied from nmrpipe
    temp should be kelvin

    """
    m = -0.009552
    b = 5.011718

    h2oPPM = m *(temp - 273.0) + b
    return h2oPPM

def read_procpar(dicv):
    proc_dict = {}
    out_dict  = {}
    for k in dicv['procpar']:
        proc_dict[dicv['procpar'][k]['name']]=dicv['procpar'][k]['values']
    ndim = 0
    for v in ['np', 'ni', 'ni2', 'ni3']:
        if v in proc_dict:
            ndim += 1
        else:
            break

    frqList = ['sfrq','dfrq','dfrq1','dfrq2','dfrq3','dfrq4','dfrq5','dfrq6']
    nucList = ['tn',  'dn',  'dn1',  'dn2',  'dn3',  'dn4',  'dn5',  'dn6']
    offList = ['tof', 'dof', 'dof1', 'dof2', 'dof3', 'dof4', 'dof5', 'dof6']
    rflList = ['rfl', 'rfl1', 'rfl2','rfl3', 'rfl4', 'rfl5', 'rfl6', 'rfl7']
    rfpList = ['rfp', 'rfp1', 'rfp2','rfp3', 'rfp4', 'rfp5', 'rfp6', 'rfp7']

    swDict = {}
    obsDict = {}
    carDict = {}
    rflDict = {}
    rfpDict = {}

    if not 'temp' in proc_dict:
        proc_dict['temp'] = [0.0]
    else:
        if len(proc_dict['temp'])>1:
            proc_dict['temp_array'] = proc_dict['temp']
            proc_dict['temp']       = proc_dict['temp'][0]


    for i in ['sw','sw1','sw2','sw3','sw4','sw5']:
        if i in proc_dict:
            if proc_dict[i][0]!=0:
                swDict[i] = str_to_number(proc_dict[i][0])
        else:
            swDict[i] = 10000
    out_dict['swDict']=swDict

    for i in rflList:
        if i in proc_dict:
            rflDict[i] = float(proc_dict[i][0])
    out_dict['rflDict']=rflDict

    for i in rfpList:
        if i in proc_dict:
            rfpDict[i] = float(proc_dict[i][0])
    out_dict['rfpDict']=rfpDict

    for i in range(len(frqList)):
        f  = frqList[i]
        if f in proc_dict:
            nuclei = nucList[i]
            nuclei_label = proc_dict[nuclei][0]
            if nuclei_label in ['1H','H1']:
                nuclei_label = '1H'
            elif nuclei_label in ['13C','C13']:
                nuclei_label = '13C'
            elif nuclei_label in ['15N','N15']:
                nuclei_label = '15N'
            if float(proc_dict[f][0])!=0.0:
                obsDict[i]={'obs':float(proc_dict[f][0]),"nuclei":nuclei,"nuclei_label":nuclei_label,"frq":f}

    updateCar = 0
    if 'temp' in proc_dict:
        temperature = str_to_number(proc_dict['temp'][0]) + 273.0
    else:
        temperature = 298.0

    CARx = 'H2O'
    if CARx == "H2O":
        if proc_dict['tn'][0] in ['1H', 'H1']:
            h2oPPM = getH20ppm(temperature)
            reference = "H2O"
        else:
            CARx = 0.0
            h2oPPM = 0.00
            reference = Null

    h2oOBS = str_to_number(proc_dict['sfrq'][0])
    for i in obsDict:
        if proc_dict['tn'][0] not in ['1H', 'H1']:
            break

        frq_val = obsDict[i]['obs']
        nuclei = obsDict[i]['nuclei_label']
        gamma  = get_gamma(nuclei)
        if gamma!=0.0 and frq_val!=0.0:
            car = (frq_val * 10**6 -((h2oOBS*10**6-(h2oOBS*h2oPPM))*gamma))/frq_val
            obsDict[i]['car_ppm'] = car
    out_dict['obsDict']=obsDict
    return out_dict

def var_params(dict1, experiment_type = '1D-1H'):

    experiment_axis_all = experiment_axes(experiment_type)
    proc_param = read_procpar(dict1)
    def search_nuclie_axis(proc_param,nuclei="1H"):
        if nuclei=="1H" or nuclei=="H1":
            nuclei_list = ['1H','H1']
        elif nuclei == "13C" or nuclei=="C13":
            nuclei_list = ["13C",'C13']
        elif nuclei =='15N' or nuclei == 'N15':
            nuclei_list= ['15N','N15']

        for k,v in proc_param['obsDict'].items():
            if v['nuclei_label'] in nuclei_list:
                out = k
        return out

    out_dict = {}
    for axis, n in experiment_axis_all.items():
        out_dict[axis]={}
        if axis==0:
            out_dict[axis]['sw'] = proc_param['swDict']['sw']
            out_dict[axis]['rfl']= proc_param['rflDict']['rfl']
            out_dict[axis]['rfp']= proc_param['rfpDict']['rfp']

        elif axis==1:
            out_dict[axis]['sw'] = proc_param['swDict']['sw1']
            out_dict[axis]['rfl']= proc_param['rflDict']['rfl1']
            out_dict[axis]['rfp']= proc_param['rfpDict']['rfp1']
        axis_i = search_nuclie_axis(proc_param,n)
        out_dict[axis]['obs']= proc_param['obsDict'][axis_i]['obs']
        out_dict[axis]['car_ppm']= proc_param['obsDict'][axis_i]['car_ppm']
        out_dict[axis]['car']= proc_param['obsDict'][axis_i]['car_ppm']*proc_param['obsDict'][axis_i]['obs']
        out_dict[axis]['label']=n
    return out_dict


def update_varp(u,varp):
    for k,v in varp.items():
        if k in u:
            for key in v:
                if key in u[k]:
                    u[k][key]=v[key]
                else:
                    u[k][key]=v[key]
    return u

def read_data3(data_path,data_type = "auto",params_e={},experiment_type = "1D-1H"):
    def decide_type(data_path):
        if not os.path.isdir(data_path):
            raise GeneralException('The input address for raw spectrum should be a directory.')
        if 'procpar' in os.listdir(data_path):
            data_type = 'varian'
            params_e['Instrument'] = 'Varian'
        elif 'acqu' in os.listdir(data_path):
            data_type = 'bruker'
            params_e['Instrument'] = 'Bruker'
        elif 'proc' in os.listdir(data_path):
            data_type = 'bruker_pdata'
        elif 'fid.xy' in os.listdir(data_path):
            data_type = 'xy'
        else:
            data_type = 'unknown'
        return data_type

    def read(data_path):
        input_map = {'varian': read_varian, 'bruker': read_bruker,'bruker_1r': read_bruker_1r,'xy': read_xy,'xy_csv': read_csv}
        decide_type()

    def read_bruker(data_path):
        remove_security_bruker(data_path)
        dicb,datab = ng.bruker.read(dir = data_path)
        datab = ng.bruker.remove_digital_filter(dicb, datab)
        C = ng.convert.converter()
        C.from_bruker(dicb,datab)
        u = ng.bruker.guess_udic(dicb,datab)
        C.from_bruker(dicb,datab,u)
        dic,data = C.to_pipe()
        return ([dic,u,data])

    def read_bruker_1r(data_path):
        dicb, datab = ng.bruker.read_pdata(dir = os.path.join(data_path, 'pdata/1'))
        dicb ,data_un = ng.bruker.read(dir = data_path)
        C = ng.convert.converter()
        C.from_bruker(dicb,datab)
        u = ng.bruker.guess_udic(dicb,datab)
        C.from_bruker(dicb,datab,u)
        dic,data = C.to_pipe()
        return ([dic,u,data])

    def read_varian(data_dir):
        dicv,datav = ng.varian.read(dir = data_dir)
        varp = var_params(dicv,experiment_type=experiment_type)
        u1 = ng.varian.guess_udic(dicv,datav)
        u1 = update_varp(u1,varp)
        C = ng.convert.converter()
        C.from_varian(dicv,datav,u1)
        dic, data = C.to_pipe()
        return ([dic,u1,data])

    def main(data_path):

        if data_type == "auto":
            data_type_current = decide_type(data_path)
        else:
            data_type_current = data_type
        data_t = None
        if data_type_current == "bruker":
            data_t = read_bruker(data_path)
        elif data_type_current == "bruker_pdata":
            data_t = read_bruker_1r(data_path)
        elif data_type_current == "varian":
            data_t = read_varian(data_path)
        elif data_type_current == "xy":
            data_t = import_sp(data_path)
        if data_t:
            return data_t
    return main(data_path)


def update_param(data):
    param = data[1]
    sp_dim = param['ndim']
    raw_data = data[2]
    if sp_dim==1:
        param[0]['size']= len(raw_data)
    else:
        for i in range(sp_dim):
            param[i]['size'] = raw_data.shape[i]
    return[data[0],param,raw_data]

def ppm_scale(data,dim = 0,param_update = True):
    if update_param:
        data  = update_param(data)
    else:
        data
    param = data[1]
    sp_dim = param['ndim']
    if dim > sp_dim:
        return "spectra dimension is less than dim, please recheck the dimension"
    else:
        car = param[dim]['car']
        obs = param[dim]['obs']
        size= param[dim]['size']
        sw  = param[dim]['sw']
        ppm_r = [car/obs - (sw/obs)/2 ,car/obs + (sw/obs)/2]
        ppm_scale = np.linspace(ppm_r[1],ppm_r[0],size)
        return [ppm_r,ppm_scale]

def ppm_2_ind(ppm,p_scale):
    if isinstance(ppm,(list,np.ndarray)):
        ind_list = []
        for p in ppm:
            sel = np.abs(p_scale-p).argmin()
            ind_list.append(sel)
        ind_list = np.array(ind_list)
    else:
        ind_list = np.abs(p_scale-ppm).argmin()
    return ind_list
def ppm_2_pts(ppm,p_scale):
    pts_per_ppm = len(p_scale)/(max(p_scale)-min(p_scale))
    out_pts = pts_per_ppm*ppm
    return out_pts

def calc_spn(data,xmin=-0.5,xmax = -0.2):
    noise_data = dt_x(data,xmin=xmin,xmax=xmax)
    if not len(noise_data)>0:
        noise_data = dt_x(data,xmin = 0.05,xmax=0.08)
        if not len(noise_data)>0:
            min_x = np.min(data[:,0])
            noise_data = dt_x(data,xmin=min_x,xmax=min_x+0.05)

    d1 = copy.deepcopy(noise_data)

    if np.sum(np.abs(d1[:,1]) > np.mean(d1[:,1]) + np.std(d1[:,1])*3)>0:
        cutoff = 3
    elif np.sum(np.abs(d1[:,1]) > np.mean(d1[:,1]) + np.std(d1[:,1])*2)>0:
        cutoff = 2
    elif np.sum(np.abs(d1[:,1]) > np.mean(d1[:,1]) + np.std(d1[:,1]))>0:
        cutoff = 1
    else:
        cutoff = 0.5
    avg_spn = np.mean(np.abs(d1[np.abs(d1[:,1])> np.mean(d1[:,1]) + np.std(d1[:,1])*cutoff][:,1]))

    return avg_spn

def ref_corr (data,xmin = -0.2,xmax = 0.2):
    spn = calc_spn(data,xmin=-0.5,xmax=-0.4)
    ref_dt  = dt_x(data,xmin=-0.3,xmax=0.3)
    ref_h = max(ref_dt[:,1])
    if ref_h/spn >10:
        ref_p   = ref_dt[np.argmax(np.abs(ref_dt[:,1]))][0]
        ppm_scale_c_1 = data[:,0] - ref_p
    else:
        ppm_scale_c_1 = data[:,0]
    return np.column_stack([ppm_scale_c_1,data[:,1]])

def zf(data_t,zf_type  = "auto",zf_value = None,multiplication = 2):
    y = data_t[2]
    data_size = len(y)
    desired_size = None
    if zf_type == "auto":
        k=0
        while 2**k < data_size : k+=1
        digital_size = 2**k
        desired_size = digital_size * multiplication
    elif zf_type == "padding":
        if zf_value:
            desired_size = zf_value + data_size
        else:
            desired_size = data_size
    elif zf_type == "size":
        if zf_value:
            desired_size = zf_value
        else:
            desired_size = data_size * multiplication
    elif zf_type == "multiplication":
        desired_size = data_size * multiplication
    newY = np.zeros(desired_size, dtype = complex)
    newY[0:data_size] = y
    return [data_t[0],data_t[1],newY]

def ft(data_t):
    y = data_t[2]
    _y = np.fft.fft(y)
    _s = _y.shape[0]
    _y = np.append(_y[int(_s/2)::-1], _y[_s:int(_s/2):-1], axis=-1)
    return [data_t[0],data_t[1],_y]

def add_p(data_t):
    import copy
    parameters = copy.deepcopy(data_t[1])
    data       = data_t[2]
    size       = parameters['size']
    if len(data)!= size:
        parameters['size']=len(data)
    parameters['delta_ppm'] = - parameters['sw']/(parameters['size']*parameters['obs'])
    if "origin_pt" not in parameters:
        parameters["origin_pt"] = int(parameters['size']/2.0)
    if "largest_ppm" not in parameters:
        parameters["largest_ppm"] = (float(parameters['sw'])/parameters['obs'])* (float(parameters["origin_pt"])/len(data))
    return([data_t[0],parameters,data])

def unit2pts( unit, value, data_t):
    import copy
    parameters = copy.deepcopy(data_t[1])
    if "delta_ppm" not in parameters:
        parameters =copy.deepcopy(add_p(data_t)[1])
    if 'ppm' in unit:
        return np.floor((value - parameters['largest_ppm'])/parameters['delta_ppm']).astype('int')
    elif 'hz' in unit:
        return (np.floor(((value/parameters['obsfreq'])-parameters['largest_ppm'])/parameters['delta_ppm'])).astype('int')
    elif 'usec' in unit:
        return (np.floor(value*parameters["sw"]*1.e-6)).astype('int')
    elif 'msec' in unit:
        return (np.floor(value*parameters["sw"]*1.e-3)).astype('int')
    elif 'sec' in unit:
        return (np.floor(value*parameters["sw"])).astype('int')
    elif 'pts' in unit:
        return np.int(np.floor(value))



def pts2unit(unit, pts,data_t):
    import copy
    parameters = copy.deepcopy(data_t[1])

    if "delta_ppm" not in parameters:
        parameters =copy.deepcopy(add_p(data_t)[1])
    if 'ppm' in unit:
        return (pts * parameters['delta_ppm']) +  parameters['largest_ppm']
    elif 'hz' in unit:
        return (pts * parameters['delta_ppm'] + parameters['largest_ppm'])* parameters['obsfreq']
    elif 'usec' in unit:
        return pts * 1e6 /parameters["sw"]
    elif 'msec' in unit:
        return pts * 1e3 /parameters["sw"]
    elif 'sec' in unit:
        return pts/parameters["sw"]
    elif 'pts' in unit:
        return pts
    else:
        print('this unit type is not handled!')

def grid(loss, bounds, N1, N2):
    x = np.linspace(bounds[0][0], bounds[0][1], N1)
    y = np.linspace(bounds[1][0], bounds[1][1], N2)
    xv,yv = np.meshgrid(x,y, indexing='ij')
    params = np.vstack((xv.flat, yv.flat)).T
    lossval = loss(params)
    minparam = np.argmin(lossval)
    return params[minparam,:]

def area(y, p_s,norm = None):
    if norm == None:
        norm = p_s['norm_val']
    y[p_s['water_inds'][0]:p_s['water_inds'][1]] = 0
    if p_s['liftit']:
        y = y - np.min(y)
    pind = y> 0
    nind = y< 0
    err =  np.sum(p_s['l_pos'] * y[pind]** norm, axis=-1) +\
            np.sum(p_s['l_neg'] * (-y[nind])** norm, axis=-1)
    return err


def ar_b(Y, p_s,norm=None):
    if norm == None:
        norm = p_s['norm_val']
    Y[:,p_s['water_inds'][0]:p_s['water_inds'][1]] = 0
    if p_s['liftit']:
        Y = Y - np.min(Y, axis=1)[...,np.newaxis]
        err = np.sum(Y**norm, axis=1)
    else:
        pind = Y> 0
        nind = Y< 0
        err =  np.sum((p_s['l_pos'] * Y * pind)**norm, axis=1) +\
                np.sum((p_s['l_neg'] * -Y* nind)**norm, axis=1)
    return err

def ph_s(phi0, phi1, y, return_complex = False):
    phii = phi0 + phi1 * np.arange(y.shape[0], dtype=float)/y.shape[0]
    yp = None
    if return_complex:
        yp = np.zeros_like(y, dtype = complex)
        yp.real = y.real * np.cos(phii) + y.imag * np.sin(phii)
        yp.imag = y.imag * np.cos(phii) + y.real * np.sin(phii)
    else:
        yp = y.real * np.cos(phii) + y.imag * np.sin(phii)
    return yp

def p_s_b(phi0, phi1, y, return_complex = False):
    if phi0.ndim == 0:
        phi0 = np.zeros_like(phi1) + phi0
    elif phi0.shape[0] == 1:
        phi0 = np.zeros_like(phi1) + phi0[0]
    phii = phi0[...,np.newaxis] + phi1[...,np.newaxis] * (np.arange(y.shape[0], dtype=float)/y.shape[0])[np.newaxis, ...]
    if return_complex:
        Yp = np.empty((phii.shape[0],y.shape[0]), dtype = complex)
        Yp.real = y.real[np.newaxis,...] * np.cos(phii) + y.imag[np.newaxis,...] * np.sin(phii)
        Yp.imag = y.imag[np.newaxis,...] * np.cos(phii) + y.real[np.newaxis,...] * np.sin(phii)
    else:
        Yp = y.real[np.newaxis,...] * np.cos(phii) + y.imag[np.newaxis,...] * np.sin(phii)
    return Yp

def p_s_fv(phi0, phi1, y,fv = 0, return_complex = False):
    phii = phi0 + phi1 * np.arange(-fv,-fv+y.shape[0], dtype=float)/y.shape[0]
    yp = None
    if return_complex:
        yp = np.zeros_like(y, dtype = complex)
        yp.real = y.real * np.cos(phii) + y.imag * np.sin(phii)
        yp.imag = y.imag * np.cos(phii) + y.real * np.sin(phii)
    else:
        yp = y.real * np.cos(phii) + y.imag * np.sin(phii)
    return yp

def p_s_b_fv(phi0, phi1, y,fv = 0, return_complex = False):
    if phi0.ndim == 0:
        phi0 = np.zeros_like(phi1) + phi0
    elif phi0.shape[0] == 1:
        phi0 = np.zeros_like(phi1) + phi0[0]
    phii = phi0[...,np.newaxis] + phi1[...,np.newaxis] * \
    (np.arange(-fv,-fv+y.shape[0], dtype=float)/y.shape[0])[np.newaxis, ...]
    if return_complex:
        Yp = np.empty((phii.shape[0],y.shape[0]), dtype = complex)
        Yp.real = y.real[np.newaxis,...] * np.cos(phii) + y.imag[np.newaxis,...] * np.sin(phii)
        Yp.imag = y.imag[np.newaxis,...] * np.cos(phii) + y.real[np.newaxis,...] * np.sin(phii)
    else:
        Yp = y.real[np.newaxis,...] * np.cos(phii) + y.imag[np.newaxis,...] * np.sin(phii)
    return Yp



def smooth(signal, coeff):
    N = (np.size(coeff)-1)/2
    res = np.convolve(signal, coeff)
    return res[N:-N]

def del_h2o(pc_sp,del_h2o_s):
    param     = pc_sp[1]
    yr        = pc_sp[2].real
    delta_ppm = param['delta_ppm']
    inds = unit2pts('ppm', np.array([del_h2o_s['h2o_p']/2.0, - del_h2o_s['h2o_p']/2.0]), pc_sp)
    def find_noise_level(yr):
        reg_size = np.floor(yr.shape[0]/del_h2o_s['nregions'])
        sig_vals = []
        for reg in range(del_h2o_s['nregions']):
            y_reg = yr[int(reg*reg_size): int((reg+1)*reg_size)]
            sig_vals.append(np.std(y_reg))
        if del_h2o_s['use_median']:
            return np.median(sig_vals)
        else:
            return np.min(sig_vals)
    if del_h2o_s['add_gaussian_noise']:
        sigma = find_noise_level(yr)
        yr[inds[0]:inds[1]] = np.random.randn(inds[1] - inds[0]) * sigma
    else:
        yr[inds[0]:inds[1]] = 0
    return [pc_sp[0],pc_sp[1],yr]

def elb(data_f, elb_settings, adj_lb = None, dim=0):
    def determine_decay_coeffs(y):
        x_values = np.array(list(range(len(y))))
        y_values = abs(y.real)
        decay_func = lambda x, c1, c2: c1 * np.exp(-x/float(c2))
        coeffs, _ = curve_fit(decay_func, x_values, y_values, p0=(1.0, len(y)))
        return coeffs

    def decay_function_factory(coeff1, coeff2):
        return (lambda x: coeff1 * np.exp(-x/coeff2))

    param = data_f[1]
    sw    = param[dim]['sw']
    y     = data_f[2]
    size  = param[dim]['size']
    if elb_settings['nmrglueELB']:
        if adj_lb:
            new_y = ng.process.proc_base.em(y, lb=float(adj_lb) / sw)
        else:
            new_y = ng.process.proc_base.em(y, lb=float(elb_settings['LB']) / sw)
        out_spectra = [data_f[0],param,new_y]
    else:
        if elb_settings['automatic']:
            coeff1, coeff2 = determine_decay_coeffs(y)
        else:
            coeff1 = float(elb_settings['coefficient_1'])
            coeff2 = float(elb_settings['coefficient_2']) * size
        decay_func = decay_function_factory(coeff1, coeff2)
        for i in range(len(y)):
            y[i] *= decay_func(i)
        out_spectra = [data_f[0],param,y]
    return out_spectra

def bc1(data_f_pc,settings_all,settings_update_all,bc_sel = "np",dim = 0):
    from scipy.interpolate import interp1d, Rbf, LSQUnivariateSpline,UnivariateSpline
    from scipy.interpolate import pchip
    from scipy.ndimage.filters import gaussian_filter1d
    import scipy.sparse as sparse

    bs = [r for r in settings_all if "bc" in r[0]]
    bs_update = [r for r in settings_update_all if "bc" in r[0]]
    updated_bs = copy.deepcopy(bs[0][1])
    bs_update = [r for r in bs_update if r[1]["BC"] == bc_sel]
    if len(bs_update)>0:
        for r in bs_update[0][1]:
            updated_bs[r] = bs_update[0][1][r]
    else:
        print("baseline settings update not found for BC = ",bc_sel)
        return data_f_pc
    bs          = updated_bs
    param       = copy.deepcopy(data_f_pc[1][dim])

    ppm_scale_c = ppm_scale(data_f_pc,dim=dim)[1]
    pts_p_ppm     = ppm_2_pts(1,ppm_scale_c)

    yr = data_f_pc[2].real
    ydr = np.zeros_like(yr)
    ydr[:-1] = yr[:-1] - yr[1:]
    ydr *= ydr
    exceeds = True
    y_b = np.zeros(ydr.shape[0], dtype=bool)
    prevsize = np.inf
    y_b[:] = True
    while np.sum(y_b) < prevsize:
        prevsize = np.sum(y_b)
        y_b = ydr < (np.mean(ydr[y_b]) + 3 * np.std(ydr[y_b]))
    changes = True
    yb_new = y_b.copy()
    while changes:
        changes = False
        (yb_new[1:-1])[ np.logical_and(np.logical_not(y_b[:-2]),np.logical_not(y_b[2:])) ] = False
        (yb_new[1:-1])[np.logical_and(y_b[:-2],y_b[2:])] = True
        if np.any(yb_new != y_b):
            changes = True
        y_b[:] = yb_new[:]
    y_b[0] = True
    y_b[-1] = True

    diagvals = None
    if not bs['whittaker']:
        p = pchip(ppm_scale[y_b][::-1], yr[y_b][::-1])
        y_fit = p(ppm_scale[::-1])[::-1]
        y_fit_spline = np.column_stack([ppm_scale_c,y_fit])
    else:
        maindiag_vec = np.ones(y_b.shape[0]-1)
        D = sparse.diags([maindiag_vec, -maindiag_vec], [0,1], shape = (y_b.shape[0]-1, y_b.shape[0]))
        D = (D.T).dot(D)
        sigma = calc_sigma(yr.real, bs['n_regions'])
        bandwidth = bs['whittaker_bandwidth_ppm']*pts_p_ppm
        yfiltered = gaussian_filter1d(np.abs(yr.real), sigma=bandwidth)
        diagvals = 1.0/np.maximum(yfiltered, sigma)
        diagvals /= np.max(diagvals)
        diagvals *= (bs['whittaker_smoothness'][1] - bs['whittaker_smoothness'][0])
        diagvals += bs['whittaker_smoothness'][0]
        smoothness = sparse.diags([diagvals], [0], shape = (y_b.shape[0], y_b.shape[0]))
        D = D.dot(smoothness)
        D = D + sparse.diags([y_b.astype(float)], [0])
        y_fit = sparse.linalg.spsolve(D, y_b * yr)
    return [y_fit, yr - y_fit]

def bc2(data_f_pc,settings_all,settings_update_all,bc_sel = "np",ni = 2,dim = 0):
    from scipy.interpolate import interp1d, Rbf, LSQUnivariateSpline,UnivariateSpline
    from scipy.interpolate import pchip
    from scipy.ndimage.filters import gaussian_filter1d
    import scipy.sparse as sparse
    from numpy.lib.stride_tricks import sliding_window_view

    bs = [r for r in settings_all if "bc" in r[0]]
    bs_update = [r for r in settings_update_all if "bc" in r[0]]
    updated_bs = copy.deepcopy(bs[0][1])
    bs_update = [r for r in bs_update if r[1]["BC"] == bc_sel]
    if len(bs_update)>0:
        for r in bs_update[0][1]:
            updated_bs[r] = bs_update[0][1][r]
    else:
        print("baseline settings update not found for BC = ",bc_sel)
        return data_f_pc
    bs          = updated_bs
    param       = copy.deepcopy(data_f_pc[1][dim])
    ppm_scale_c = ppm_scale(data_f_pc,dim=dim)[1]
    pts_p_ppm     = ppm_2_pts(1,ppm_scale_c)

    yr = data_f_pc[2].real
    for r in range(ni):
        ydr = np.zeros_like(yr)
        ydr[:-1] = yr[:-1] - yr[1:]
        ydr *= ydr
        ydr_noise = 2*np.std(ydr[-500:])
        window = 50
        sigma = 1
        if param['label']=='1H':
            len_selection = 200
        else:
            len_selection = 2000
        ybl = np.zeros_like(ydr,dtype=bool)
        ydt_slide_window = sliding_window_view(ydr,window)
        ydt_below_std_fiter =ydt_slide_window < ydr_noise
        ydt_window_sum = np.sum(ydt_below_std_fiter,axis=1)
        ydt_window_sum_noise_mean = np.mean(ydt_window_sum[-len_selection:])
        ydt_window_sum_noise_std  = np.std(ydt_window_sum[-len_selection:])
        peak_cutoff = ydt_window_sum_noise_mean - sigma*ydt_window_sum_noise_std
        ydt_window_sum_test = ydt_window_sum>peak_cutoff
        ybl[window-1:][ydt_window_sum_test]=True

        if r==0:
            y_b = np.zeros(ydr.shape[0], dtype=bool)
            y_b=np.logical_or(y_b,ybl)
        else:
            y_b = np.zeros(ydr.shape[0], dtype=bool)
            y_b=np.logical_or(y_b,ybl)
            yr_n_sel = np.abs(yr) <= np.mean(yr[-200:])+5*np.std(yr[-200:])
            y_b = np.logical_and(y_b,yr_n_sel)

        diagvals = None
        if not bs['whittaker']:
            p = pchip(ppm_scale[y_b][::-1], yr[y_b][::-1])
            y_fit = p(ppm_scale[::-1])[::-1]
            y_fit_spline = np.column_stack([ppm_scale_c,y_fit])
        else:
            maindiag_vec = np.ones(y_b.shape[0]-1)
            D = sparse.diags([maindiag_vec, -maindiag_vec], [0,1], shape = (y_b.shape[0]-1, y_b.shape[0]))
            D = (D.T).dot(D)
            sigma = calc_sigma(yr.real, bs['n_regions'])
            bandwidth = bs['whittaker_bandwidth_ppm']*pts_p_ppm
            yfiltered = gaussian_filter1d(np.abs(yr.real), sigma=bandwidth)
            diagvals = 1.0/np.maximum(yfiltered, sigma)
            diagvals /= np.max(diagvals)
            diagvals *= (bs['whittaker_smoothness'][1] - bs['whittaker_smoothness'][0])
            diagvals += bs['whittaker_smoothness'][0]
            smoothness = sparse.diags([diagvals], [0], shape = (y_b.shape[0], y_b.shape[0]))
            D = D.dot(smoothness)
            D = D + sparse.diags([y_b.astype(float)], [0])
            y_fit = sparse.linalg.spsolve(D, y_b * yr)
        yr = yr - y_fit
        out = yr
    return [y_fit, out]

def pdlocal(data, window=7, min_height=None,thres=0.0001, min_dist=0.0001,noise = None,noise_multi = 10,j_th = 0.001,sel_negative = False,sel_positive = True,smoothen = True):

    from math import ceil
    from scipy.ndimage.filters import uniform_filter1d

    if noise == None:
        average_spn = calc_spn(data)
    else:
        average_spn = noise

    cut_off = average_spn*noise_multi
    data_c = data[np.abs(data[:,1])>np.real(cut_off)]
    data = data_c

    if smoothen:
        data_s = np.column_stack([data[:,0],uniform_filter1d(data[:,1],size = 7)])
    else:
        data_s = data
    pkl = pd_util(data_s,thres=0.0001, min_dist=0.0001,negative_peaks = sel_negative, positive_peaks=sel_positive)
    if len(pkl)>0:
        pkl_g = gr_diff(pkl, j_th*2)
        lo_p_l = []
        for p in pkl_g:
            if len(p)>0:
                p_select = p[np.abs(p[:,1]).argmax()]
                selector = np.abs(data[:,0]-p_select[0]).argmin()
                selector_p = data[selector-3:selector+3]
                if len(selector_p)>0:
                    p_select_1 = selector_p[selector_p[:,1].argmax()]
                    lo_p_l.append(list(p_select_1))
        lo_pks = np.vstack(lo_p_l)
    else:
        lo_pks = np.array([])
    return lo_pks

def lb_proc_sim(data_t,settings_all,settings_update_all,lb=None):
    elb_settings         = [r for r in settings_all if "ExponentialLineBroadening" in r[0]]
    elb_settings_update  = [r for r in settings_update_all if "ExponentialLineBroadening" in r[0]]
    updated_elb_settings = copy.deepcopy(elb_settings[0][1])
    for i in elb_settings_update[0][1]:
        updated_elb_settings[i] = elb_settings_update[0][1][i]
    elb_data   = elb(data_t,updated_elb_settings, adj_lb = lb)
    return elb_data

def pcc(data_f,settings_all,settings_update_all,dim = 0):

    ppm_scale_c = ppm_scale(data_f,dim=dim)[1]
    p_s         = [r for r in settings_all if "pc" in r[0]]
    p_s_update  = [r for r in settings_update_all if "pc" in r[0]]
    p_s_update_sel = [r for r in p_s_update if r[1]['pc_i']=='np']
    updated_p_s = copy.deepcopy(p_s[0][1])
    for i in p_s_update_sel[0][1]:
        updated_p_s[i] = p_s_update_sel[0][1][i]
    y = data_f[2].copy()
    yr_1 = data_f[2].real.copy()
    phi = None
    parameters = copy.deepcopy(data_f[1][dim])
    mu            = np.array(updated_p_s['phi_start'], dtype=float)
    sigma    = np.diag(np.array(updated_p_s['ce_sigma0'], dtype=float))
    phi_bounds    = updated_p_s['phi_bounds']
    df_base      = bc2(data_f,settings_all,settings_update_all,bc_sel = "np",dim = 0)
    df_p          = np.column_stack([ppm_scale_c,df_base[1]])
    spn  = calc_spn(df_p)
    data_f[1][dim]['spn']=spn
    ref_r         = updated_p_s["ref_range"]
    ref_dt        = dt_x(df_p,xmin=min(ref_r),xmax=max(ref_r))
    if not len(ref_dt)>0:
        minx = np.min(df_p[:,0])
        ref_dt = dt_x(df_p,xmin=minx,xmax=minx+0.05)

    df_base_ref_h = max(ref_dt[:,1])
    s_n_base      = df_base_ref_h/spn.real
    if s_n_base > 5:
        ref_p         = ref_dt[np.argmax(np.abs(ref_dt[:,1]))][0]
        ppm_scale_c_1 = ppm_scale_c - ref_p
    else:
        ppm_scale_c_1 = ppm_scale_c

    d_f_p         = np.column_stack([ppm_scale_c_1,y])
    d_p0          = dt_x(d_f_p,xmin=min(ref_r),xmax=max(ref_r))
    y_ref         = d_p0[:,1]
    ref_ind       = ppm_2_ind(0.0, ppm_scale_c_1)
    water_p  = [9,10]
    water_index = ppm_2_ind(water_p,ppm_scale_c)
    updated_p_s['water_inds'] = water_index
    if s_n_base > 50:
        phi_bounds    = [[phi_bounds[0],phi_bounds[1]],[0,0]]
        loss_a_d = lambda phi:  ar_b( p_s_b(phi[:,0], phi[:,1], y_ref), updated_p_s)

        phi_a    = ce(loss_a_d, mu, sigma,updated_p_s['ce_N_area'],updated_p_s['ce_top_area'], updated_p_s['ce_eps_area'], bounds = phi_bounds, batch_loss = True)
        ytmp_1   = ph_s(phi_a[0], phi_a[1], y)

        at_start = np.column_stack([ppm_scale_c_1,y])
        base_sp   = np.column_stack([ppm_scale_c_1, ytmp_1])
        arr_r = updated_p_s["arr_range"]
        ar_ref_dt = dt_x(df_p,xmin=min(arr_r),xmax=max(arr_r))
        ar_ref_h = max(ar_ref_dt[:,1])
        s_n_base_ar = ar_ref_h/spn.real
        if s_n_base_ar>10:
            phi_bounds  = updated_p_s['phi_bounds']
            mu          = np.array(phi_a)
            phi_bounds  = [[mu[0],mu[0]],[phi_bounds[2] *2, phi_bounds[3]*2]]
            y_zero   = np.zeros_like(y)
            ref_ind_r = ppm_2_ind(ref_r,ppm_scale_c_1)
            y_zero[min(ref_ind_r):max(ref_ind_r)] = y[min(ref_ind_r):max(ref_ind_r)]
            arr_ind_r = ppm_2_ind([8.5,9.5],ppm_scale_c_1)
            y_zero[min(arr_ind_r):max(arr_ind_r)] = y[min(arr_ind_r):max(arr_ind_r)]

        else:
            phi_bounds  = updated_p_s['phi_bounds']
            mu          = np.array(phi_a)
            phi_bounds  = [[mu[0],mu[0]],[phi_bounds[2]*2 , phi_bounds[3]*2]]
            y_zero   = np.zeros_like(y)
            pk_tmp   = pdlocal(base_sp,noise_multi = 20,sel_positive=True,sel_negative=False)
            sel_peak = pk_tmp
            for p in sel_peak:
                p_range_ppm = [p[0].real-0.02,p[0].real+0.02]
                p_r_i       = ppm_2_ind(p_range_ppm,ppm_scale_c_1)
                y_zero[min(p_r_i):max(p_r_i)] = y[min(p_r_i):max(p_r_i)]


        y_zero[min(water_index):max(water_index)]=0.0
        loss_a_ar   = lambda phi: ar_b( p_s_b_fv(phi[:,0], phi[:,1],y_zero, fv = ref_ind), updated_p_s)
        phi_ar2      = ce(loss_a_ar, mu, sigma, updated_p_s['ce_N_area'],
                          updated_p_s['ce_top_area'], updated_p_s['ce_eps_area'],
                          bounds = phi_bounds, batch_loss = True)
    else:

        phi_bounds    = [[phi_bounds[0],phi_bounds[1]],[phi_bounds[2] * 3 , phi_bounds[3]*3]]
        y_zero   = np.zeros_like(y)
        base_sp   = np.column_stack([ppm_scale_c_1, y])
        pk_tmp   = pdlocal(base_sp,noise_multi = 2,sel_positive=True,sel_negative=False)
        sel_peak = pk_tmp
        for p in sel_peak:
            p_range_ppm = [p[0].real-0.2,p[0].real+0.2]
            p_r_i       = ppm_2_ind(p_range_ppm,ppm_scale_c_1)
            y_zero[min(p_r_i):max(p_r_i)] = y[min(p_r_i):max(p_r_i)]
        loss_a_ar   = lambda phi: ar_b( p_s_b_fv(phi[:,0], phi[:,1],y_zero, fv = ref_ind), updated_p_s)
        phi_ar2      = ce(loss_a_ar, mu, sigma, updated_p_s['ce_N_area'],
                          updated_p_s['ce_top_area'], updated_p_s['ce_eps_area'],
                          bounds = phi_bounds, batch_loss = True)

    ytmp_3      = p_s_fv(phi_ar2[0], phi_ar2[1], y,fv = ref_ind)
    pc_sp = [data_f[0],data_f[1],ytmp_3]
    return [phi_ar2,pc_sp]


def Lz(x, x0, h, w):
    return (w**2 * h)/(w**2 + 4 * (x - x0)**2)

def fit_p(pd, x_b=[None,None], h_b=[None,None], wd_b=[None,None], it = 10):

    from scipy.optimize import basinhopping
    def f(x):
        return Lz(x_dt,x[0],x[1],x[2])
    def objf(x):
        return sum((y_dt - f(x))**2)

    x_dt = pd[:,0]
    y_dt = pd[:,1]
    x0 = pd[np.argmax(np.abs(pd[:,1]))][0]
    if any(x_b):
        x0_b = x_b
    else:
        x0_b = [min(x_dt),max(x_dt)]

    if any(h_b):
        h_b = h_b
    else:
        h_b = [max(y_dt)*0.75,max(y_dt)*1.25]

    if any(wd_b):
        d_b = wd_b
    else:
        d_b = [0.00001,None]
    bnds = [x0_b, h_b, d_b]
    p0 = [x0, max(y_dt) ,0]
    minimizer_kwargs={ "bounds": bnds,"options":{'maxiter': 1000}}
    hopping1 = basinhopping(objf,x0=p0, minimizer_kwargs = minimizer_kwargs,niter  = it)
    return hopping1.x

def fit_pks(pl,data,xr = 0.01,hr = 0.20,wr = None,f = 700,it = 100):
    fpl=[]
    for p in pl:
        xb = [p[0]-xr, p[0]+xr]
        hb = [p[1]-p[1]*hr,p[1]+p[1]*hr]
        db = [None, None]
        pd = dt_x(data,xmin = min(xb),xmax = max(xb))
        ft_p = fit_p(pd,x_b=xb,h_b=hb,wd_b=db,it = it)
        fx = ft_p[0]
        fh = ft_p[1]
        fd = ft_p[2]*f
        fpl.append([fx,fh,fd])
    return np.array(fpl)

def calc_sn_lw(input_data,xmin=-0.2,xmax = 0.2,nowater = False,frq = 700,n_type = '1H',it=100,params_e=None):

    data = copy.deepcopy(input_data)
    data_r = {}
    if n_type == '1H':
        xr = 0.005
        hr = 0.02
        jth = 0.01
    elif n_type == '13C':
        xr = 0.03
        hr = 0.05
        jth = 1
    else:
        xr = 0.005
        hr = 0.02
        jth = 1

    if nowater:
        water_range = [4,6]
        wi = ppm_2_ind(water_range,data[:,0])
        data[:,1][min(wi):max(wi)]=0
    ref_i = ppm_2_ind([xmin,xmax],data[:,0])

    noise = calc_spn(data,xmin=0.2,xmax=0.5)
    ref_data = dt_x(data,xmin=xmin,xmax=xmax)

    ref_signal = True
    if len(ref_data)>0:
        ref_h = max(ref_data[:,1])
        s_n_ref   = ref_h/noise
        data_r['s/n']=s_n_ref

        if s_n_ref>1000:
            multi = 200
        elif s_n_ref<1000 and s_n_ref>200:
            multi = 100
        elif s_n_ref<200 and s_n_ref>10:
            multi = 3

        elif s_n_ref<10:
            ref_signal = False
            s_n_max    = max(data[:,1])/noise
            data_r['s/n']=s_n_max
            if s_n_max > 500:
                multi = 50
            else:
                multi = 3
        else:
            multi = 3
    else:
        ref_signal = False
        s_n_max    = max(data[:,1])/noise
        data_r['s/n']=s_n_max
        if s_n_max > 500:
            multi = 50
        else:
            multi = 3

    params_e['S/N']= int(data_r['s/n'])
    data_r['reference'] = ref_signal
    params_e['Reference'] = ref_signal
    p_scale = max(data[:,0])-min(data[:,0])
    pk_tmp = pdlocal(data,noise_multi=multi,noise =noise, j_th=jth,window = 5,sel_negative=True,sel_positive=False)
    if len(pk_tmp)>0:
        npk = pk_tmp[pk_tmp[:,1]<0]
    else:
        npk = np.array([])
    if ref_signal:
        pk_tmp = pdlocal(data,noise_multi=multi,noise =noise, j_th=jth,window = 5,sel_negative=False,sel_positive=True)
        all_peaks_s = dt_x(pk_tmp,xmin=xmin,xmax=xmax)
        all_peaks_s_p = all_peaks_s[all_peaks_s[:,1]>0]
        all_fitted_peaks = fit_pks(all_peaks_s_p,data,xr = xr,hr = hr,it=it,f=frq)
    else:
        pk_tmp = pdlocal(data,noise_multi=multi,noise =noise, j_th=jth,window = 5,sel_negative=False,sel_positive=True)
        all_peaks_s = pk_tmp[pk_tmp[:,1]>0]
        all_peak_s_n =  all_peaks_s[:,1]/noise
        all_fitted_peaks = fit_pks(all_peaks_s,data,xr = xr,hr = hr,it=it,f=frq)

    lw_select = all_fitted_peaks[all_fitted_peaks[:,2].argmin()]
    data_r['linewidth']=lw_select[2]
    params_e['Linewidth']=round(lw_select[2],3)
    data_r['Negative Peaks'] = len(npk)
    params_e['Negative Peaks'] = len(npk)
    data_r['Impurities'] = params_e['Impurities']

    return data_r

def find_score(s,db):
    for k,v in db.items():
        if min(v)<=s and s<=max(v):
            return k
    return -1

def spectra_score_1D(data_report):
    lw_l = {4:[0,1.2],
            3:[1.2,2],
            2:[2,3],
            1:[3,5],
            0:[5,20]}

    sn_l = {4:[100000,5000],
            3:[5000,1000],
            2:[1000,100],
            1:[100,10],
            0:[10,0]
           }

    impurity_l = {4:[0,2],
                3:[2,4],
                2:[4,6],
                1:[6,10],
                0:[10,1000]}

    negative_l =  {4:[0,1],
              3:[1,5],
              2:[5,20],
              1:[20,50],
              0:[50,1000]}

    ref_score      = ['Absent','Present']
    ref_plot_score = [0,100]

    lw_score       = ['Not Usable','Very Low','Acceptable','Good','Very Good']
    lw_plot_score  = [0,20,50,70,100]

    sn_score      = ['Not Usable','Very Low','Acceptable','Good','Very Good']
    sn_plot_score = [0,20,50,70,100]

    imp_score      = ['Not Usable','Very High','High','Low','Negligible']
    imp_plot_score = [0,20,50,70,100]

    np_score      = ['Not Usable','Very High','High','Low','Not Present']
    np_plot_score = [0,20,50,70,100]

    score = {}

    lw   = data_report['linewidth']
    sn   = data_report['s/n']
    npk  = data_report['Negative Peaks']
    im_p = data_report['Impurities']

    if data_report['reference']:
        ref_s = 1
    else:
        ref_s = 0
    lw_s  = find_score(lw,lw_l)
    sn_s  = find_score(sn,sn_l)
    imp_s = find_score(im_p,impurity_l)
    np_s  = find_score(npk,negative_l)

    score["Line Width"] = [str(round(lw,3))+' Hz',lw_plot_score[lw_s]]
    score["Signal/Noise"] = [str(int(sn)),sn_plot_score[sn_s]]
    score['Negative Peaks']=[np_score[np_s],np_plot_score[np_s]]
    score['Impurities'] = [imp_score[imp_s],imp_plot_score[imp_s]]
    score["Internal Reference"]  = [ref_score[ref_s],ref_plot_score[ref_s]]

    return score

def score_plots(score_data,file):

    import pylab
    import matplotlib.pyplot as plt
    import matplotlib as mpl

    col_map = plt.get_cmap('bwr_r')
    v_l = [list(v) for v in score_data.items()]
    bar_num = len(v_l)
    fig, ax = plt.subplots(figsize = (8,2),dpi = 300)
    bars = ax.barh(range(bar_num),103,height=0.7)
    lim = ax.get_xlim()+ax.get_ylim()
    col_map = plt.get_cmap('bwr_r')
    font_size = 14
    for i in range(bar_num):
        bar = bars[i]
        x,y = bar.get_xy()
        w, h = bar.get_width(),bar.get_height()
        grad = np.atleast_2d(np.linspace(0,100,512))
        grad_lim = [x,x+w,y,y+h]
        ax.imshow(grad, extent=grad_lim, aspect="auto",zorder=1, cmap =col_map )
        ax.text(x-3,y+h/2,v_l[i][0],va='center', ha='right', fontsize=font_size)
        ax.text(x+w+3,y+h/2,v_l[i][1][0],va='center', ha='left', fontsize=font_size)
        pointer = ax.barh(y+h/2,3,height=h*1.1,left=v_l[i][1][1],color = 'black',zorder=2)
    ax.axis(lim)
    ax.text(lim[0],lim[3]+0.5,'Parameters',va='center', ha='right', fontsize=font_size)
    ax.text(lim[0]+lim[1]-3,lim[3]+0.5,'Values',va='center', ha='left', fontsize=font_size)
    ax.text(lim[0],lim[2]-0.5,'Worst',va='center', ha='left', fontsize=font_size)
    ax.text(lim[0]+lim[1]-5,lim[2]-0.5,'Best',va='center', ha='right', fontsize=font_size)
    ax.set_axis_off()
    plt.tight_layout()
    plt.savefig(file,transparent=True)
    plt.clf()
    plt.close()


def comp_m_imp(lib_det,spks, p_rng_relax_l= None):

    def search_met(met):
        out_dict_m = {}
        clusters = met['clusters']
        m_name   = met['name']
        out_dict_m['name']=m_name
        cl_list_s = []
        for r in clusters.keys():
            cl_number = ['cluster',int(r)]
            cl_peaks = np.array(clusters[r]['peaks'])
            if 'cluster range' in clusters[r].keys():
                cl_range = clusters[r]['cluster range']
            else:
                cl_range = [np.mean(cl_peaks[:,0]),np.mean(cl_peaks[:,0])]
            cl_r     = [cl_number,cl_range,cl_peaks]
            cl_s     = comp_cl_s(spks, cl_r)
            cl_list_s.append(cl_s)
        out_dict_m['clusters'] = cl_list_s
        return out_dict_m

    def chk_detection(out_dict_m):
        clusters = out_dict_m['clusters']
        all_max = np.array([np.max(c[1][:,1]) for c in clusters])
        cutoff = np.max(all_max)*0.1
        cl_filterd = all_max>cutoff
        cl_filterd_count = sum(cl_filterd)
        all_detection = [len(c[2])>0 for c in clusters]
        cl_detection_count = sum(all_detection)
        if cl_detection_count>=cl_filterd_count:
            out = out_dict_m['name']
        else:
            out=None
        return out

    def search_lib(lib_det,spks):
        out_list = []
        for i in lib_det.keys():
            out_s=search_met(lib_det[i])
            chk = chk_detection(out_s)
            if chk !=None:
                out_list.append(chk)
        return out_list
    return search_lib(lib_det,spks)

def det_imp(sp,n_type='1H',solvent = 'D2O',lib_dir = "solvent_lib/"):

    if solvent in ['D2O','D2o',"H2O","H2o",'water',"Water"]:
        solvent = 'D2O'

    elif solvent in ['CH3OH','CD3OD','Methanol','methanol']:
        solvent = 'CD3OD'
    else:
        solvent = solvent

    if solvent not in ['Acetone','CD3OD','CD3CN','CDCl3','D2O','DMSO']:
        solvent = 'DMSO'

    lib_file = lib_dir+solvent+"/"+n_type+"/"+'det.json'
    lib_det = read_json(lib_file)
    if n_type == '1H':
        window = 7
    elif n_type == '13C':
        window = 5
    else:
        window = 7
    sp_pks  = pdlocal(sp,window=window,sel_negative=False, noise_multi=3,sel_positive=True)
    impurities = comp_m_imp(lib_det,sp_pks)
    return [impurities,len(impurities)]


def process_1d(data_in,settings_all,settings_update_all,sol = "D2O",out_name = "out",impurity_lib_dir = 'solvent_lib/',experiment_type="1D-1H"):
    out_dir = os.path.dirname(data_in)
    out_name_sp = out_dir+"/"+out_name+'.json'
    out_name_q  = out_dir+"/"+out_name+'.svg'
    params_e = exp_params()
    params_e['Solvent'] = sol
    data_t = read_data3(data_in,params_e=params_e,experiment_type=experiment_type)
    data_t_em = lb_proc_sim(data_t,settings_all,settings_update_all,lb=0.5)
    parameters = copy.deepcopy(data_t_em[1])
    frq = parameters[0]['obs']
    n_type = parameters[0]['label']
    field_list = np.array([500,600,700,800])
    selector = np.abs(field_list - float(frq)).argmin()
    field = field_list[selector]
    params_e['Frequency']=int(field)
    zf_settings = [r for r in settings_all if "zf" in r[0]]
    zf_settings_update = [r for r in settings_update_all if "zf" in r[0]]
    zf_settings_update_1 = [r for r in zf_settings_update if r[1]["zf_iter"]==2]
    updated_zf_settings = copy.deepcopy(zf_settings[0][1])
    for i in zf_settings_update_1[0][1]:
        updated_zf_settings[i] = zf_settings_update_1[0][1][i]
    zf_data_1 = zf(data_t_em,zf_type  = updated_zf_settings["zf_type"], zf_value = updated_zf_settings["zf_value"])
    data_f_1  = ft(zf_data_1)
    pcs = pcc(data_f_1,settings_all,settings_update_all)
    bc_y = bc2(pcs[1],settings_all,settings_update_all)
    ppm_s_f = ppm_scale(data_f_1)[1]
    df_p = np.column_stack([ppm_s_f,bc_y[1]])
    ref_dt  = dt_x(df_p,xmin=-0.05,xmax=0.05)
    if len(ref_dt)>0:
        ref_h = max(ref_dt[:,1])
        noise = calc_spn(df_p)
        s_n   = ref_h/noise
        if s_n>20:
            ref_p   = ref_dt[np.argmax(np.abs(ref_dt[:,1]))][0]
            ppm_scale_c_1 = ppm_s_f - ref_p
            bc_y_norm = bc_y[1]*10000/ref_h
            norm_sp = np.column_stack([ppm_scale_c_1, bc_y_norm])
        else:
            bc_y_norm = bc_y[1]*10000/max(bc_y[1])
            norm_sp = np.column_stack([ppm_s_f, bc_y_norm])
    else:
        bc_y_norm = bc_y[1]*10000/max(bc_y[1])
        norm_sp = np.column_stack([ppm_s_f, bc_y_norm])

    det_impurities = det_imp(norm_sp, n_type=n_type, solvent=sol, lib_dir=impurity_lib_dir)
    params_e['Impurities'] = det_impurities[1]

    quality_dict   = calc_sn_lw(norm_sp, n_type = n_type, it=100, frq=frq, params_e=params_e)
    sd             = spectra_score_1D(quality_dict)
    score_plots(sd,out_name_q)
    save_jsp(norm_sp,out_name_sp,params = params_e)
    print("processing done")

