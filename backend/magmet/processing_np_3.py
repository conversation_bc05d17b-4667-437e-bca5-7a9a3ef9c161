##################################
# Import
##################################
import sys, os, random, copy, shutil, xmltodict, json, math, glob, re
from os import linesep
from os.path import exists
# from string import split, upper, lower
# from random import shuffle
import time
import datetime
import numpy as np
# from multiprocessing import Pool
from standard_functions_3 import *
from standard_functions_mkr_3 import *
from maple_functions_3 import *
from magmet_process_1d_3 import *
# from maple_main_functions import *
##################################


##################################
spectra_path = ""
##################################

##################################
# out_put name
##################################
out_put_name = ""
d_out_put_name = "out_"
##################################

#################################
# settings files
settings_file = "setting"
settings_update_file = ""
setting_update_file_d = "settings_update_np.txt"

##################################
# Text
##################################
help = """ usage  python maple_main.py options

                        options:-

                        -i  or -sp      spectra
                        -o  or -out     output name
                        -f              field_strength (500,600,700,800)
                        # -b  or -bio     biofluid type. the constrain and wt files will be selected from this.
                        # -dss            path of text file with dss HMDB code
                        -h  or -help    print help
                        -su             settings update file.

            """
##################################
##################################
# Parsing arguments
##################################

if len(sys.argv) > 1:

    for i in range(len(sys.argv)):
        if sys.argv[i] in ["-m"]:  # Done
            magmet_dir = os.path.abspath(sys.argv[i + 1])

        if sys.argv[i] in ["-i"]:
            spectra_path = os.path.abspath(sys.argv[i + 1])

        if sys.argv[i] in ["-o", "-out"]:
            out_name_t = os.path.abspath(sys.argv[i + 1])

        if sys.argv[i] in ["-f"]:
            field_strength = sys.argv[i + 1]

        if sys.argv[i] in ["-ref"]:
            reference = sys.argv[i + 1]

        if sys.argv[i] in ["-sptype"]:
            spectrum_type = sys.argv[i + 1]

        if sys.argv[i] in ["-sol"]:
            solvent = sys.argv[i + 1]


        if sys.argv[i] in ["-h", "-help"]:
            print(help)
            sys.exit()

magmet_dir = magmet_dir+"/"
impurity_lib_dir = magmet_dir+'solvent_lib/'
out_dir = os.path.dirname(spectra_path)
file_name = os.path.basename(spectra_path)
shutil.unpack_archive(spectra_path,out_dir,'zip')
if len(glob.glob(out_dir+"/__MACOSX"))>0:
    shutil.rmtree(out_dir+"/__MACOSX")
out_sp_name = spectrum_type+"_"+solvent+"_"+reference+"_"+field_strength
spectra_list = glob.glob(out_dir+"/*/fid")
if len(spectra_list)>0:
    sp_path = os.path.dirname(spectra_list[0])
    setting = "setting"
    settings_update_file = "settings_update.txt"
    settings_all = read_job_file(magmet_dir+setting)
    settings_update_all = read_job_file(magmet_dir+settings_update_file)
    process_1d(sp_path,settings_all,settings_update_all,out_name = out_sp_name,sol = solvent,impurity_lib_dir=impurity_lib_dir,experiment_type=spectrum_type)
