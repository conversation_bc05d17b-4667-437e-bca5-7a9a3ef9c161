#-------------------------------------------------------------------------------
# Copyright (c) 2017-2025 <PERSON><PERSON><PERSON> <PERSON>
# All rights reserved
#-------------------------------------------------------------------------------

import sys
import os, glob
import random
import copy
import shutil
import xmltodict
import json
import math
from os import linesep
from os.path import exists
from random import shuffle
import time
import datetime
import numpy as np
import nmrglue as ng
from standard_functions_3 import *
from standard_functions_mkr_3 import *

def read_content(content):
    content_out = []
    for line in content:
        line = line.replace(' ', '')
        line = line.replace('\t', '')
        line = line.replace('\n', '')
        parts = line.split('=')
        content_out.append([parts[0],decide_value(parts[1])])
    return content_out

def decide_value(x):
    try:
        if x.find('[') > -1 and x.find(']') > -1:# it's a list of values
            x = x.strip()
            listVars = x[1:-1].split(',')
            x = [decide_value(y) for y in listVars]
            return x
        elif 'true' in x.lower(): return True
        elif 'false' in x.lower(): return False
        elif 'none' in x.lower(): return None
        elif x.isdigit():
            return int(x)
        elif x.replace('.','').replace('e','').replace('-','').replace('+','').isdigit():
            return float(x)
        elif '\"' in x or '\'' in x:
            x = x.replace('\"','').replace('\'','')
            return x
        else:
            return x
    except Exception:
        raise GeneralException('can not decide '+ str(x) + ' type in the setting file')


def read_job_file(job_file):

    f = open(job_file, 'r')
    function_with_settings = []
    use_next_line = False
    while True:
        if not use_next_line:
            try:
                nextline = next(f)
            except StopIteration: break
        else: use_next_line = False

        if nextline.startswith("#") or len(nextline.strip()) == 0:
            continue
        elif nextline.count('=') == 0:
            function_name = nextline.strip()
            content = []
            read_next_line = True
            try:
                nextline = next(f)
            except StopIteration:
                read_next_line = False
                use_next_line = True
            while read_next_line and nextline.count('=') > 0  or\
                len(nextline.strip()) == 0 or nextline.strip().startswith('#'):
                if nextline.count('=') > 0 and not nextline.strip().startswith('#'):
                    if nextline.count('{')+nextline.count('}')+nextline.count('@') > 0:
                        raise GeneralException('The job file seems to contain batch information. Try running Bayesil using --batch parameter.')
                        return
                    content.append(nextline)
                try:
                    nextline = next(f)
                except StopIteration:
                    if nextline.count('=') > 0  and not nextline.strip().startswith('#'):
                        content.append(nextline)
                        use_next_line = True
                    break
            use_next_line = not use_next_line
            content_r = read_content(content)

            settings = {}
            if len(content_r)>0:
                for r in range(len(content_r)):
                    settings[content_r[r][0]]=content_r[r][1]
        function_with_settings.append([function_name, settings])
    return function_with_settings


def ce(loss, muin, sigmain, n, top_n, eps, bounds = None, local_search = False, verbose=False, batch_loss = False):

    np.random.seed(0)
    mu = np.array(muin)
    sigma = np.array(sigmain)
    if local_search:
        import scipy.optimize.lbfgsb as lbfgs
    while(np.max(np.diag(sigma)) > eps):
        samples = np.random.multivariate_normal(mu, sigma, size = n)
        loss_val = np.zeros(n)
        if batch_loss and not local_search:
            if bounds is None:
                loss_val = loss(samples)
            else:
                bounds = np.array(bounds)
                out_of_bound = np.logical_or(samples < bounds[np.newaxis,:,0], samples > bounds[np.newaxis,:,1])
                samples[out_of_bound] = ( (bounds[np.newaxis,:,1] - bounds[np.newaxis,:,0])*np.random.rand(n,muin.shape[0]) + bounds[np.newaxis,:,0] )[out_of_bound]
                loss_val = loss(samples)
        else:
            for i in range(n):
                if bounds == None and not batch_loss:
                    loss_val[i] = loss(samples[i, :])
                else:
                    for j in range(mu.shape[0]):
                        if samples[i,j] < bounds[j][0] or samples[i,j] > bounds[j][1]:
                            samples[i,j] = bounds[j][0] + (bounds[j][1] - bounds[j][0])*np.random.rand()
                    if local_search:
                        samples[i,:],loss_val[i],_ = lbfgs.fmin_l_bfgs_b(func=loss, x0 = samples[i,:],fprime=None,approx_grad = True, bounds = bounds, iprint = -1, epsilon=eps)
                    elif not batch_loss:
                        loss_val[i] = loss(samples[i, :])

        inds = np.argsort(loss_val)
        top_samples = samples[inds[0:top_n], :]
        mu = np.mean(top_samples, axis = 0)

        sigma = (1.0 /(top_n - 1))* np.dot((np.tile(mu, [top_n, 1]) - top_samples).T, (np.tile(mu, [top_n, 1]) - top_samples))

    return mu

def Lz(x, x0, h, w):
    return (w**2 * h)/(w**2 + 4 * (x - x0)**2)


def save_sp(sp_data,file):
    import pandas as pd
    sp_df = pd.DataFrame(sp_data)
    sp_df.to_json(file)

def import_sp(file):
    import pandas as pd
    sp_i = pd.read_json(file)
    xL = sp_i.loc['x']['spectrum_xy']
    yL = sp_i.loc['y']['spectrum_xy']
    sp_np = np.column_stack([xL,yL])
    return sp_np

def save_jsp(r,file,params=None):
    import json
    sp_data = np.array(r)
    all_data = {}
    if params:
        for k,v in params.items():
            all_data[k]=v
    all_data["spectrum_xy"]      = {}
    all_data["spectrum_xy"]["x"] = list(sp_data[:,0])
    all_data["spectrum_xy"]["y"] = list(sp_data[:,1])
    with open(file, 'w') as out:
        json.dump(all_data, out)

def save_json(file,result):
    import json
    with open(file, 'w') as out:
        json.dump(result, out)


def read_json(file):
    import json
    with open(file) as json_file:
        data = json.load(json_file)
    return data

def import_sp_pararms(processed_sp_json):
    sp_data  = read_json(processed_sp_json)
    out_dict = copy.deepcopy(sp_data)
    if 'spectrum_xy' in out_dict:
        del out_dict['spectrum_xy']
    return out_dict


def pd_util(data, thres=0.0001, min_dist=0.0001,positive_peaks=True,negative_peaks=False):
    import peakutils
    if type(data) == np.ndarray:
        data = data
    else:
        data = np.array(data)

    all_peak_indices = []
    if positive_peaks:
        y_data = data[:, 1]
        indexes = peakutils.indexes(y_data, thres=thres, min_dist=min_dist)
        all_peak_indices.append(indexes)
    if negative_peaks:
        y_data = - data[:,1]
        indexes = peakutils.indexes(y_data, thres=thres, min_dist=min_dist)
        all_peak_indices.append(indexes)
    all_peak_indices_np = np.concatenate(all_peak_indices)
    all_peak_indices_u = np.unique(all_peak_indices_np)
    peaks = data[all_peak_indices_u]
    return peaks

def gr(data, column=0, axis=None):
    data = data_to_np_array(data)
    unique_values = np.unique(data[:, column])
    selector = [x for x in range(data.shape[1]) if x != column]
    if data.shape[1] >= 3:
        if axis == None:
            axis = 0
        else:
            axis = axis

    grouped_data = []

    for value in unique_values:
        selected = data[np.where(data[:, column] == value)]
        grouped_data.append([value, selected[:, selector].sum(axis)])

    return np.array(grouped_data)

def gr_3(data, column=0, axis=0):
    data = data_to_np_array(data)
    unique_values = np.unique(data[:, column])
    selector = [x for x in range(data.shape[1]) if x != column]
    if data.shape[1] >= 3:
        if axis == None:
            axis = 0
        else:
            axis = axis
    grouped_data = []
    for value in unique_values:
        selected = data[np.where(data[:, column] == value)]
        grouped_data.append(np.hstack([value, selected[:, selector].sum(axis)]))

    return np.array(grouped_data)

def comp_cl_s(sp_pk_l, cl, p_rng_tol = 0.0005, cl_range = 0.02):
    import itertools
    cl_number = cl[0]
    cl_x      = cl[1]
    cl_pks_db = cl[2]
    cl_bnd_min  = min(cl_x)
    cl_bnd_max  = max(cl_x)
    cl_pks_x  = cl_pks_db[:, 0]
    min_cl    = min(cl_pks_x)
    max_cl    = max(cl_pks_x)

    if cl_bnd_min == cl_bnd_max:
        cl_range = cl_range
        sel_sp_pks = dt_x(sp_pk_l, min_cl - cl_range / 2, max_cl + cl_range / 2)
    else:
        sel_sp_pks = dt_x(sp_pk_l, xmin = cl_bnd_min, xmax = cl_bnd_max )
    sel_sp_pks_x = sel_sp_pks[:, 0]
    dt = cl_pks_x - cl_pks_x[0]
    det_cl_pks = []
    for pk in sel_sp_pks_x:
        test_list     = []
        cl_pks = []
        for d in dt:
            test_list1 = []
            det_pks = []
            for test_pk in sel_sp_pks_x:
                t = pk + d - p_rng_tol <= test_pk <= pk + d + p_rng_tol
                test_list1.append(t)
                if t == True:
                    det_pks.append(test_pk)
            cl_pks.append(det_pks)
            test_list.append(any(test_list1))
        if all(test_list):
            all_cls_c = list(itertools.product(*cl_pks))
            det_cl_pks.append(all_cls_c)
    return [cl_number, cl_pks_db, list(itertools.chain(*det_cl_pks))]

def gr_diff(data,diff=2):
    import numpy as np
    values = np.array(data)
    if len(values.shape)==1:
        values_sorted = sorted(values)
        test_list = values_sorted
    else:
        values_sorted = sort_dt(values)
        test_list = values_sorted[:,0]

    if len(test_list)>1:
        difference_list= np.diff(test_list)
        idx = np.where(difference_list > diff)[0]
        if len(idx)>0:
            index = 0
            group_list = []
            for i in idx:
                group_list.append(values_sorted[index:i+1])
                if i ==idx[-1]:
                    group_list.append(values_sorted[i+1:])
                index = i+1
        else:
            group_list= [data]
    else:
        group_list= [data]
    return np.array(group_list,dtype=object)

def savitzky_golay(y, w_s, order, deriv=0, rate=1):
    """
    smoothening function
    """
    import numpy as np
    from math import factorial

    try:
        w_s = np.abs(np.int(w_s))
        order = np.abs(np.int(order))
    except ValueError as msg:
        raise ValueError("w_s and order have to be of type int")
    if w_s % 2 != 1 or w_s < 1:
        raise TypeError("w_s size must be a positive odd number")
    if w_s < order + 2:
        raise TypeError("w_s is too small for the polynomials order")
    order_range = list(range(order+1))
    half_window = (w_s -1) // 2
    b = np.mat([[k**i for i in order_range] for k in range(-half_window, half_window+1)])
    m = np.linalg.pinv(b).A[deriv] * rate**deriv * factorial(deriv)
    firstvals = y[0] - np.abs( y[1:half_window+1][::-1] - y[0] )
    lastvals = y[-1] + np.abs(y[-half_window-1:-1][::-1] - y[-1])
    y = np.concatenate((firstvals, y, lastvals))
    return np.convolve( m[::-1], y, mode='valid')