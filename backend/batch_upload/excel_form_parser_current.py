import pandas as pd
import sys,os
from standard_functions_py3 import *
import copy

#######################################
#
# Example of file execution:
#
# python excel_form_parser_v2.py -i test_excel_file_with_assignment_and_metadata.xls -p project_name
#
# -i is a flag for input Excel file
#
# -p is a flag for project name
#######################################


project="CSV_files_with_assignments_and_meta"
location=""
env="mac"
#input_file="pandas_image.xlsx"
input_file="NONE"


verbose_level_1=1

# meta_dict={}
# meta_dict["Frequency"]={"triggers":["Frequency"]}
# meta_dict["Compound name"]={"triggers":["Compound name"]}

meta_dict={}
meta_dict["Compound Name"]=""
#meta_dict["NP-MRD ID"]=""
meta_dict["Literature Reference"]=""
meta_dict["Genus"]=""
meta_dict["Species"]=""
meta_dict["Solvent"]=""
meta_dict["Spectrum Type"]=""
meta_dict["Spectrometer Frequency"]=""
meta_dict["Temperature"]=""
meta_dict["Chemical Shift Reference"]=""
meta_dict["Provenance"]=""
meta_dict["Smiles"]=""
meta_dict["Melting Point"]=""
meta_dict["Boiling Point"]=""
meta_dict["Literature Reference Type"]=""
meta_dict["Physical State of Compound"]=""




table_places={}
table_places[0]="Atom"	
table_places[1]="Type"	
table_places[2]="Pred. shifts (ppm)"	
table_places[3]="Exp. shifts (ppm)"	
table_places[4]="Pred. Multiplet"	
table_places[5]="Exp. Multiplet"	
table_places[6]="Pred. J (Hz)"
table_places[7]="Exp. J (Hz)"

columns_numbers_ordered=[0,1,2,3,4,5,6,7]

triggers={}
triggers[0]="Atom"	
triggers[1]="Type"	
triggers[2]="Pred"	
triggers[3]="Exp"	
triggers[4]="Multiplet"	
triggers[5]="Multiplet"	
triggers[6]="Pred"
triggers[7]="Exp"

def parse_excel(input_file,table_places,triggers,meta_dict_global):
	"""
	Parse excel file
	"""
	verbose=1
	if verbose==1:
		print  ('Running function parse_excel()')

	wb = pd.ExcelFile(input_file, engine="openpyxl")
	#xl.sheet_names 

	#print ("wb.__dict__ = ", wb.__dict__) 
	#for i in wb:
	#	print ("i = ", i)
	#sys.exit()

	sheet_names = wb.sheet_names
	#sheet = wb.sheet_by_name(sheet_name)
	print ("sheet_names  =  ", sheet_names )
	sheet_dict={}
	
	for i in sheet_names:
		#print ("i  =  ", i )
		#sheet = wb.parse(i)
		sheet = pd.read_excel(input_file,sheet_name=i,header=None,engine="openpyxl")
		#sheet = wb.sheet_by_name(i)

		#print ("sheet = ", sheet)
		#sys.exit()
		#print ("sheet = ", sheet)
		assignment_found=0
		sheet_dict[i]={}
		sheet_dict[i]["assignment_dict"]={}
		sheet_dict[i]["meta_dict"]={}
		assignment_dict={}
		meta_dict=copy.deepcopy(meta_dict_global)
		#total_rows = wb.book.sheet_by_index(0).nrows
		total_rows = len(sheet.index)
		#print ("total_rows = ", total_rows)
		#sys.exit()

		#for j in range(0,sheet.nrows):
		for j in range(0,total_rows):

			if verbose_level_1>=10: print (j, sheet.row(j))
			#firstcellvalue = sheet.cell_value(j, 0)
			firstcellvalue = sheet.iloc[j][0]
			#print("firstcellvalue= ", firstcellvalue)
			#sys.exit()

			#complete_row=sheet.row(j)
			numnber_of_columns=len(sheet.columns)

			if assignment_found==0:	
				if firstcellvalue in meta_dict:
					#print ("firstcellvalue %s was found in meta_dict" % firstcellvalue )
					cellvalue = sheet.iloc[j][2]

					#cellvalue1 = sheet.iloc[j][1]
					if "Click to select" in cellvalue or cellvalue in ["","Enter_value_here"]:
						cellvalue="NA"

					print (firstcellvalue, "cellvalue = ", cellvalue)
					if pd.isnull(cellvalue)==False:

						meta_dict[firstcellvalue]+=cellvalue

					# for k in range(1,numnber_of_columns):
					# 	#print ("k = ", k)
					# 	#cellvalue = sheet.cell_value(j, k)
					# 	cellvalue = sheet.iloc[j][k]
					# 	#print ("cellvalue = ", cellvalue)
					# 	if pd.isnull(cellvalue)==False:
					# 		meta_dict[firstcellvalue]+=cellvalue

					# 	#print ("complete_row= ", complete_row)
					# 	#print("cellvalue= ", cellvalue)
					# #print ("meta_dict[firstcellvalue] = ", [meta_dict[firstcellvalue]])	
				else:
					if 	assignment_found==0:
						for d in triggers:
							trigger=triggers[d]
							#print("trigger = ", trigger)
							if trigger in "%s" % sheet.iloc[j][d]: #sheet.cell_value(j,d):
								assignment_found=1
								#print ("Trigger %s has been found" % trigger )
							else:
								assignment_found=0
						# if  assignment_found==1:
						# 	print ("Assignment found, Exiting...")	
						# 	sys.exit()	
			else:
				if is_number(firstcellvalue)==1:
					#print("Atom index= ", int(firstcellvalue))
					assignment_dict[j]={}
					for place in table_places:
						parameter_name=table_places[place]
						#print("place = ", place)
						#value = sheet.cell_value(j, place)
						value = sheet.iloc[j][place]
						if pd.isnull(value)==True:
							value="NA"
						#print("value = ", value, value)
						#print("row, parameter_name,place,value = ", j, parameter_name,place,value)
						assignment_dict[j][place]={}
						assignment_dict[j][place]["parameter_name"]=parameter_name
						assignment_dict[j][place]["value"]=value

			sheet_dict[i]["assignment_dict"]=assignment_dict
			sheet_dict[i]["meta_dict"]=meta_dict


	debug=0
	if debug==1:


		print ("Exiting after function parse_excel()")
		sys.exit()						
	return sheet_dict

def output_csv_text(sheet_dict,table_places,columns_numbers_ordered):
	"""
	Write output files
	assignment_dict[j][place][parameter_name]=value
	"""
	verbose=1
	if verbose==1:
		print  ('Running function output_csv_text()')

	for i in sheet_dict:	
		assignment_dict=sheet_dict[i]["assignment_dict"]
		meta_dict=sheet_dict[i]["meta_dict"]
		csv_text=""
		meta_text=""

		header=""
		
		for place in columns_numbers_ordered:
			parameter_name=table_places[place]
			header+="%s," % parameter_name
		if header[-1]==",":
			header=header[:-1]		
		header+=linesep	

		for j in assignment_dict:
			csv_text_local=""
			#print ("j = ", j,assignment_dict[j] )
			for place in columns_numbers_ordered:
				parameter_name=assignment_dict[j][place]["parameter_name"]
				value=assignment_dict[j][place]["value"]

				csv_text_local+="%s," % value
			if csv_text_local[-1]==",":
				csv_text_local=csv_text_local[:-1]
			csv_text+=csv_text_local + linesep		



		csv_text=header + csv_text

		for param_name in meta_dict:
			param_value=meta_dict[param_name]
			meta_text+="%s, %s" % (param_name, param_value) + linesep

		sheet_dict[i]["meta_text"]=meta_text
		sheet_dict[i]["assignment_text"]=csv_text

	debug=0
	if debug==1:
		print ("Exiting after function output_csv_text()")
		sys.exit()	
	return 

def write_csv_files(sheet_dict,project_dir,project):
	"""
	Write CSV files
	"""
	verbose=1
	if verbose==1:
		print  ('Running function write_csv_files()')

	make_dir_no_remove(project_dir)
	file_text=""
	list_of_files="%s/files_to_import.csv" % project_dir
	count=1
	for i in sheet_dict:
		i=i.replace(" ","_")
		i=i.replace(",","")
		dirname=i
		sheet_dir="%s/%s" % (project_dir,dirname)
		make_dir_no_remove(sheet_dir)
		file_with_assigment="%s/%s_assignment.csv" % (sheet_dir,dirname)
		file_with_meta="%s/%s_meta.csv" % (sheet_dir,dirname)
		file_text+="%s,%s,%s,%s" % (i,dirname,file_with_assigment,file_with_meta) + linesep
		meta_text=sheet_dict[i]["meta_text"]
		assignment_text=sheet_dict[i]["assignment_text"]
		write_file(file_with_assigment,assignment_text)
		write_file(file_with_meta,meta_text)
		count+=1

	write_file(list_of_files,file_text)
	debug=1
	if debug==1:
		print ("Exiting after function  write_csv_files()")
		sys.exit()		
	return


def main(input_file,table_places,triggers,columns_numbers_ordered,project_dir,project,meta_dict):
	"""
	Main program
	"""
	sheet_dict =parse_excel(input_file,table_places,triggers,meta_dict)
	output_csv_text(sheet_dict,table_places,columns_numbers_ordered)
	write_csv_files(sheet_dict,project_dir,project)
	return



####################################
# Arguments
####################################

command_text="python "
i=0
if len(sys.argv)>1:
	for item in sys.argv:
		##############################
		# Project/file management
		##############################
		if item in ["-l"]:
			location  = os.path.abspath(sys.argv[i + 1])
		if item in ["-p"]:
			project  = sys.argv[i + 1]
		if item in ["-v1"]:
			verbose_level_1  = int(sys.argv[i + 1])
		if item in ["-e", "-env"]:
			env  = sys.argv[i + 1]
		##############################
		# Inputoptions
		##############################
		if item in ["-i"]:
			input_file  = os.path.abspath(sys.argv[i + 1])
		i+=1
	command_text+=" %s " % item

#######################################################
#  Processing general arguments
#######################################################							
this_script_full_path=os.path.abspath(sys.argv[0])
this_script_name=os.path.basename(sys.argv[0])
script_location=os.path.dirname(this_script_full_path)
if verbose_level_1>0:
	print ("this_script_full_path=", this_script_full_path)
	print ("this_script_name=", this_script_name)
	print ("script_location=", script_location)
cur_dir=os.getcwd()
if location=="":
	location=cur_dir
project_dir="%s/%s" % (location,project)
print ("Project directory is %s " % project_dir)


# debug=0
# if debug==1:
# 	print ("project_dir = ", project_dir)
# 	sys.exit()
# make_dir_no_remove(project_dir)

main(input_file,table_places,triggers,columns_numbers_ordered,project_dir,project,meta_dict)
