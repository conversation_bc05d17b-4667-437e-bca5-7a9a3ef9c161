import pandas as pd
import csv
from standard_functions_py3 import *


######################################
# Example of execution
#
# python excel_form_generation_v3.py -i List_of_input_files_for_excel_generator.csv -p project_name -l location_where_project_directory_should_be_created
#######################################

#Initializing variables
csv_file_with_inputs="List_of_input_files_for_excel_generator.csv"
image_file=""
meta_assignment_file=""
meta_file=""
assignment_file  = ""
verbose_level_1=1
location=""
project="test"

column_for_index="Atom"
sheet_name="Sheet1"
excel_start_row=4
excel_start_column=0

#Scaling down structure images
image_x_scale = 0.3
image_y_scale = 0.3

required_meta_fields=[]
required_meta_fields+=["Spectrometer Frequency",]
required_meta_fields+=["Compound Name"]
required_meta_fields+=["Provenance"]



meta_dict={}


#1
meta_dict["Compound Name"]={} #Common/IUPAC name 1
meta_dict["Compound Name"]["keywords"]=["Compound","Name"] 
meta_dict["Compound Name"]["allowed_entries"]=[] #Anything is allowed
meta_dict["Compound Name"]["required"]=1 #Required
meta_dict["Compound Name"]["order"]=1
 

#2
meta_dict["NP-MRD ID"]={} #2
meta_dict["NP-MRD ID"]["keywords"]=["NP-MRD","ID"] #2
meta_dict["NP-MRD ID"]["allowed_entries"]=[] #2
meta_dict["NP-MRD ID"]["required"]=1 #Required
meta_dict["NP-MRD ID"]["order"]=2


#3 What about InChi and InChi key
meta_dict["Smiles"]={} #3
meta_dict["Smiles"]["keywords"]=["Smiles"]
meta_dict["Smiles"]["allowed_entries"]=[] 
meta_dict["Smiles"]["required"]=1 
meta_dict["Smiles"]["order"]=3



#3
meta_dict["Provenance"]={} # 3
meta_dict["Provenance"]["keywords"]=["Provenance"] 
meta_dict["Provenance"]["allowed_entries"]=[] 
meta_dict["Provenance"]["allowed_entries"]+=["Isolated-from-a-field-sample"]  
meta_dict["Provenance"]["allowed_entries"]+=["Isolated-from-a-cultered-organism"]    
meta_dict["Provenance"]["allowed_entries"]+=["Isolated-from-a-combinational-source"]   
meta_dict["Provenance"]["allowed_entries"]+=["Isolated-from-a-community"]   
meta_dict["Provenance"]["allowed_entries"]+=["eDNA-screening"]    
meta_dict["Provenance"]["allowed_entries"]+=["Heterologous-biosynthesis"]   
meta_dict["Provenance"]["allowed_entries"]+=["Shunt-products"]    
meta_dict["Provenance"]["allowed_entries"]+=["Biotransformation"]   
meta_dict["Provenance"]["allowed_entries"]+=["Commercial"]    
meta_dict["Provenance"]["allowed_entries"]+=["Chemical-synthesis"]    
meta_dict["Provenance"]["allowed_entries"]+=["Other"]  
meta_dict["Provenance"]["required"]=1 # Required
meta_dict["Provenance"]["order"]=4

#4
meta_dict["Genus"]={} #Organism Genus Name 4
meta_dict["Genus"]["keywords"]=["Genus"] #Organism Genus Name 4
meta_dict["Genus"]["allowed_entries"]=[] #Organism Genus Name 4
meta_dict["Genus"]["required"]=0 # Required
meta_dict["Genus"]["order"]=5


meta_dict["Species"]={} #Species name 5
meta_dict["Species"]["keywords"]=["Species"] #Species name 5
meta_dict["Species"]["allowed_entries"]=[] #Species name 5
meta_dict["Species"]["required"]=0 # Required
meta_dict["Species"]["order"]=6


#Done
meta_dict["Physical State of Compound"]={} #15
meta_dict["Physical State of Compound"]["keywords"]=["Physical","State"] #15
meta_dict["Physical State of Compound"]["allowed_entries"]=["Solid","Liquid","Gas"] #15
meta_dict["Physical State of Compound"]["required"]=0 # Required
meta_dict["Physical State of Compound"]["order"]=7


meta_dict["Literature Reference Type"]={} #14
meta_dict["Literature Reference Type"]["keywords"]=["Literature","Type"] #14
meta_dict["Literature Reference Type"]["allowed_entries"]=["PMID","DOI","Book"] #14
meta_dict["Literature Reference Type"]["required"]=1 # Required
meta_dict["Literature Reference Type"]["order"]=8


meta_dict["Literature Reference"]={} #3
meta_dict["Literature Reference"]["keywords"]=["Literature","Reference"] #3
meta_dict["Literature Reference"]["allowed_entries"]=[] #3
meta_dict["Literature Reference"]["required"]=1 # Required
meta_dict["Literature Reference"]["order"]=9


meta_dict["Melting Point"]={} #6
meta_dict["Melting Point"]["keywords"]=["Melting"] #6
meta_dict["Melting Point"]["allowed_entries"]=[] #6
meta_dict["Melting Point"]["required"]=0 # Required
meta_dict["Melting Point"]["order"]=10


meta_dict["Boiling Point"]={}  #7
meta_dict["Boiling Point"]["keywords"]=["Boiling"] #7
meta_dict["Boiling Point"]["allowed_entries"]=[] #7
meta_dict["Boiling Point"]["required"]=0 # Required
meta_dict["Boiling Point"]["order"]=11


meta_dict["Solvent"]={} #8
meta_dict["Solvent"]["keywords"]=["Solvent"] #8
meta_dict["Solvent"]["allowed_entries"]=["H2O"] #8
meta_dict["Solvent"]["allowed_entries"]+=["CDCl3"] #8
meta_dict["Solvent"]["allowed_entries"]+=["DMSO"] #8
meta_dict["Solvent"]["allowed_entries"]+=["Methanol"] 
meta_dict["Solvent"]["allowed_entries"]+=["Acetone"] 
meta_dict["Solvent"]["allowed_entries"]+=["Pyridine"]
meta_dict["Solvent"]["required"]=1 # Required
meta_dict["Solvent"]["order"]=12



meta_dict["Spectrum Type"]={} #9
meta_dict["Spectrum Type"]["keywords"]=["Spectrum","Type"] #9
meta_dict["Spectrum Type"]["allowed_entries"]=["1D-1H"]
meta_dict["Spectrum Type"]["allowed_entries"]+=["1D-13C"] #9
meta_dict["Spectrum Type"]["allowed_entries"]+=["2D-1H-13C"] #9
meta_dict["Spectrum Type"]["required"]=1 # Required
meta_dict["Spectrum Type"]["order"]=13


meta_dict["Spectrometer Frequency"]={} #10
meta_dict["Spectrometer Frequency"]["keywords"]=["Spectrometer","Frequency"] #10
meta_dict["Spectrometer Frequency"]["allowed_entries"]=["300MHz"] 
meta_dict["Spectrometer Frequency"]["allowed_entries"]+=["400MHz"] 
meta_dict["Spectrometer Frequency"]["allowed_entries"]+=["500MHz"]  
meta_dict["Spectrometer Frequency"]["allowed_entries"]+=["600MHz"]  
meta_dict["Spectrometer Frequency"]["allowed_entries"]+=["700MHz"]  
meta_dict["Spectrometer Frequency"]["allowed_entries"]+=["800MHz"]  
meta_dict["Spectrometer Frequency"]["allowed_entries"]+=["850MHz"]  
meta_dict["Spectrometer Frequency"]["allowed_entries"]+=["900Mz"]  
meta_dict["Spectrometer Frequency"]["required"]=1 # Required
meta_dict["Spectrometer Frequency"]["order"]=14


meta_dict["Temperature"]={} #11
meta_dict["Temperature"]["keywords"]=["Temperature"] #11
meta_dict["Temperature"]["allowed_entries"]=[] #11
meta_dict["Temperature"]["required"]=0 # Required
meta_dict["Temperature"]["order"]=15

meta_dict["Chemical Shift Reference"]={} #12
meta_dict["Chemical Shift Reference"]["keywords"]=["Chemical","Shift","Reference"] #12
meta_dict["Chemical Shift Reference"]["allowed_entries"]=["TMS"] 
meta_dict["Chemical Shift Reference"]["allowed_entries"]+=["DSS"] 
meta_dict["Chemical Shift Reference"]["allowed_entries"]+=["TSP"] 
meta_dict["Chemical Shift Reference"]["allowed_entries"]+=["H2O(Residual-Peak)"] 
meta_dict["Chemical Shift Reference"]["allowed_entries"]+=["CHCl3(Residual-Peak)"] 
meta_dict["Chemical Shift Reference"]["allowed_entries"]+=["Ethanol(Residual-Peak)"] 
meta_dict["Chemical Shift Reference"]["allowed_entries"]+=["Methanol(Residual-Peak)"] 
meta_dict["Chemical Shift Reference"]["required"]=1 # Required
meta_dict["Chemical Shift Reference"]["order"]=16




meta_order_in_excel=[]
meta_order_in_excel+=["Compound Name"] #1
# meta_order_in_excel+=["NP-MRD ID"] #2
meta_order_in_excel+=["Smiles"] #3
meta_order_in_excel+=["Provenance"] #4
meta_order_in_excel+=["Genus"] #5
meta_order_in_excel+=["Species"] #6
meta_order_in_excel+=["Solvent"] #7
meta_order_in_excel+=["Spectrum Type"] #8
meta_order_in_excel+=["Spectrometer Frequency"] #9
meta_order_in_excel+=["Temperature"] #10
meta_order_in_excel+=["Chemical Shift Reference"] #11
meta_order_in_excel+=["Melting Point"] #12
meta_order_in_excel+=["Boiling Point"] #13
meta_order_in_excel+=["Literature Reference Type"] #14
meta_order_in_excel+=["Literature Reference"] #15
meta_order_in_excel+=["Physical State of Compound"] #16



meta_cs_triggers={}
meta_cs_triggers[0]="Atom"	
meta_cs_triggers[1]="No"	
meta_cs_triggers[2]="ppm"	
meta_cs_triggers[3]="Shift"	
meta_cs_triggers[4]="Multiplet"	
meta_cs_triggers[5]="Actual"	
meta_cs_triggers[6]="Predicted"	
meta_cs_triggers[7]="coupling"

triggers={}
triggers[0]="AtomIdx"	
triggers[1]="AtomSymbol"	
triggers[2]="ChemicalShift"	
triggers[3]="MultipletStructure"	
triggers[4]="J_Couplings"	


columns={}
columns[0]={"npmrd":"Atom No","excel":"Atom","place":0}
columns[1]={"npmrd":"Atom Type","excel":"Type","place":1}	
columns[2]={"npmrd":"Predicted Shift(ppm)","excel":"Predicted shifts (ppm)","place":2}
columns[3]={"npmrd":"Actual Shift(ppm)","excel":"Exp. shifts (ppm)","place":None}	
columns[4]={"npmrd":"Predicted Multiplet Type","excel":"Pred. Multiplet","place":3}
columns[5]={"npmrd":"Actual Multiplet Type","excel":"Exp. Multiplet","place":None}	
columns[6]={"npmrd":"Predicted J coupling(Hz)","excel":"Pred. J (Hz)","place":4}	
columns[7]={"npmrd":"Actual J coupling(Hz)","excel":"Exp. J (Hz)","place":None} 

y="""
i =  Atom Type,Atom No,Predicted Shift(ppm),Actual Shift(ppm),Predicted Multiplet Type,Actual Multiplet Type,Predicted J coupling(Hz),Actual J coupling(Hz)

i =  H,5,1.96,1.96,s,s,NA,0

i =  H,6,1.96,1.96,s,s,NA,0

i =  H,7,1.96,1.96,s,s,NA,0

i =  H,8,NA,1.96,NA,NA,NA,0
"""

# output_dict={}
# output_dict["Atom"]=[5,6,7,8]
# output_dict["Type"]=["H","H","H","H"]

# output_dict["Pred. shifts (ppm)"]=[1.96, 1.96,1.96,"NA"]
# output_dict["Exp. shifts (ppm)"]=["?","?","?","?"]
# output_dict["Pred. Multiplet"]=["s","s","s","NA"]
# output_dict["Exp. Multiplet"]=["?","?","?","?"]
# output_dict["Pred. J"]=["NA", "NA","NA","NA"]
# output_dict["Exp. J"]=["?","?","?","?"]



# text1="Acetic acid"
# text2="700Mhz"






# # Create a Pandas dataframe from some data.
# df = pd.DataFrame(output_dict)
# df = df.set_index("Atom")    

# # Create a Pandas Excel writer using XlsxWriter as the engine.
# writer = pd.ExcelWriter('pandas_image.xlsx', engine='xlsxwriter')

# # Convert the dataframe to an XlsxWriter Excel object.
# df.to_excel(writer, sheet_name='Sheet1', startrow=4, startcol=0)
# #df.to_excel(writer, startrow=4, startcol=0)

# # Get the xlsxwriter workbook and worksheet objects.
# workbook  = writer.book
# #wrap_format = workbook.add_format({'text_wrap': True})
# worksheet = writer.sheets['Sheet1']
# worksheet.write(0, 0, text1)
# worksheet.write(1, 0, text2)

# x_scale = 0.3
# y_scale = 0.3
# # Insert an image.
# worksheet.insert_image('J3', '86_NP0000174_12_temp_3D.png',
# 						{'x_scale': x_scale, 'y_scale': y_scale})
# #worksheet.insert_image('D3', '86_NP0000174_12_draw_mol_2d.png')
# #worksheet.insert_image('D3', '86_NP0000174_12_draw_mol_equiv.png')
# #worksheet.insert_image('D3', '86_NP0000174_12_draw_mol_stereo.png')

# # Close the Pandas Excel writer and output the Excel file.

# worksheet.set_column('C:F', 17)


# writer.save()



def parse_assignment_file(assignment_file,
								triggers,
								columns
						):
	"""
	Parse assignment file
	AtomIdx,AtomSymbol,ChemicalShift,MultipletStructure,J_Couplings
	"""	
	verbose=0
	if verbose==1:
		print  ('Running function parse_assignment_file()')
		print ("Triggers : ", triggers)

		print ("columns : ", columns)
		#sys.exit()


	#file_lines=read_file(assignment_file)

	assignment_dict={}
	excel_dict={}
	assignment_found=0
	atom_count=0

	with open(assignment_file) as csv_file:
		csv_reader = csv.reader(csv_file, delimiter=',')
		for k in csv_reader:
			# if verbose_level_1>1: print ("i = ", i)
			# i=i.strip()
			if assignment_found==0:
				#k=i.split(",")
				if len(k)==len(triggers):
					for d in triggers:
						trigger =triggers[d]
						column_title=k[d]
						# print("trigger = ", trigger )
						# print("column_title = ", column_title )

						if trigger.lower() in column_title.lower():
							assignment_found=1
							if verbose_level_1>10: print ("Trigger %s has been found" % trigger )
						else:
							assignment_found=0

			else:
				#k=i.split(",")
				if verbose_level_1>=10: print ("k = ", k)
				
				atom_count+=1	
				assignment_dict[atom_count]={}
				for m in columns:
					column_name=columns[m]["npmrd"]
					excel_column_name=columns[m]["excel"]
					column_value="Enter_value_here"
					place=columns[m]["place"]
					if place!=None:
						column_value=k[place]
					assignment_dict[atom_count][column_name]=column_value
					if excel_column_name not in excel_dict:
						excel_dict[excel_column_name]=[]
					excel_dict[excel_column_name]+=[column_value]	
	debug=0
	if debug==1:
		for atom_count in assignment_dict:
			for column_name in assignment_dict[atom_count]:
				column_value=assignment_dict[atom_count][column_name]
				if verbose_level_1>10: print ("assignment_dict: Atom # %s, Parameter %s, value %s" % (atom_count,column_name,column_value))

		for param in excel_dict:
			value_list=excel_dict[param]	
			if verbose_level_1>1: print ("Excel: Parameter %s, value list: " % (param),value_list)

		#if verbose_level_1>1: print ("Number of missing parameters is", len(missing_parameters))
		if verbose_level_1>1: print ("Exiting after function parse_assignment_file()")
		sys.exit()
	return	excel_dict





def parse_meta_assignment_file(meta_assignment_file,
								meta_dict,
								triggers,
								columns):
	"""
	Parse meta assignment file
	Atom Type,Atom No,Predicted Shift(ppm),Actual Shift(ppm),Predicted Multiplet Type,Actual Multiplet Type,Predicted J coupling(Hz),Actual J coupling(Hz)
	"""	
	verbose=0
	if verbose==1:
		print  ('Running function parse_meta_assignment_file()')

	file_lines=read_file(meta_assignment_file)
	missing_parameters=[]	
	meta_dict={}
	assignment_dict={}
	excel_dict={}

	assignment_found=0
	atom_count=0

	for i in file_lines:
		if verbose_level_1>1: print ("i = ", i)
		i=i.strip()
		if assignment_found==0:
			if ":" not in i:
				k=i.split(",")
				if len(k)==len(triggers):
					for d in triggers:
						trigger =triggers[d]
						column_title=k[d]
						#print("trigger = ", trigger, )

						if trigger.lower() in column_title.lower():
							assignment_found=1
							#print ("Trigger %s has been found" % trigger )
						else:
							assignment_found=0

			else:
				j=i.split(":")
				if len(j)>0:
					for parameter in meta_dict:
						found=0
						found_list=[]

						for keywords in meta_dict[parameter]:
							if keywords.lower() in i.lower():
								found_list+=[1]
								found=1
							else:
								found_list+=[0]	
						if 0 in found_list:
							found=0
						value="NONE"	
						if found==1:
							if len(j)>1:
								value=j[1]
								if parameter in meta_dict:
									print ("ERROR!!!! parameter %s is already  in meta_dict and it is value is %s " % (parameter,meta_dict[parameter]))
									print ("Exiting...")
									sys.exit()
								else:	
									meta_dict[parameter]=value 
		else:
			k=i.split(",")
			if len(k)!=len(columns):
				print ("ERROR!!!! The number of parameters %s is not equal to the number of columns %s" % (len(k),len(columns)))
				print ("k = ", k)
				print ("Exiting...")
				sys.exit()		
			else:			
				atom_count+=1	
				assignment_dict[atom_count]={}
				for place in columns:
					column_name=columns[place]["npmrd"]
					excel_column_name=columns[place]["excel"]
					column_value=k[place]
					assignment_dict[atom_count][column_name]=column_value
					if excel_column_name not in excel_dict:
						excel_dict[excel_column_name]=[]
					else:
						excel_dict[excel_column_name]+=[column_value]	

	for parameter in meta_dict:
		if parameter not in meta_dict:
			missing_parameters+=[parameter]	
			meta_dict[parameter]=""

	debug=0
	if debug==1:
		for i in meta_dict:
			i_value=meta_dict[i]
			if verbose_level_1>1: print ("meta_dict: Parameter %s, value %s" % (i,i_value))

		for atom_count in assignment_dict:
			for column_name in assignment_dict[atom_count]:
				column_value=assignment_dict[atom_count][column_name]
				if verbose_level_1>1: print ("assignment_dict: Atom # %s, Parameter %s, value %s" % (atom_count,column_name,column_value))

		for param in excel_dict:
			value_list=excel_dict[param]	
			if verbose_level_1>1: print ("Excel: Parameter %s, value list: " % (param),value_list)

		#if verbose_level_1>1: print ("Number of missing parameters is", len(missing_parameters))
		if verbose_level_1>1: print ("Exiting after function parse_meta_assignment_file()")
		sys.exit()
	return	meta_dict, assignment_dict, excel_dict

def parse_csv_file_with_inputs_old(csv_file_with_inputs):
	"""
	Parse CSV file with inputs
	"""
	verbose=0
	if verbose==1:
		print ('Running function parse_csv_file_with_inputs()')
		print ("For filename  ", csv_file_with_inputs) 

	files_with_inputs_dict={}
	file_lines=read_file(csv_file_with_inputs)
	for i in file_lines:
		if verbose_level_1>1: print ("i = ", i)
		i=i.strip()
		i=i.split(",")
		entry_id=i[0]
		meta_assignment_file=i[1]
		image_1=i[2]
		if entry_id in files_with_inputs_dict:
			print ("ERROR!!!  entry_id  %s is already in files_with_inputs_dict " % i)
			print ("Exiting...")
			sys.exit()
		else:
			files_with_inputs_dict[entry_id]={}	
			files_with_inputs_dict[entry_id]["meta_assignment_file"]=meta_assignment_file.strip()
			files_with_inputs_dict[entry_id]["image_1"]=image_1.strip()

	debug=0
	if debug==1:
		for entry_id in  files_with_inputs_dict:
			meta_assignment_file=files_with_inputs_dict[entry_id]["meta_assignment_file"]
			image_1	=files_with_inputs_dict[entry_id]["image_1"]		
			print (f"{entry_id}, {meta_assignment_file}, {image_1}")
		print ("Exiting after function parse_csv_file_with_inputs()")
		sys.exit()	
	return files_with_inputs_dict	



def parse_csv_file_with_inputs(csv_file_with_inputs):
	"""
	Parse CSV file with inputs
	"""
	verbose=0
	if verbose==1:
		print ('Running function parse_csv_file_with_inputs()')
		print ("For filename  ", csv_file_with_inputs) 

	files_with_inputs_dict={}
	file_lines=read_file(csv_file_with_inputs)
	for i in file_lines:
		if verbose_level_1>1: print ("i = ", i)
		i=i.strip()
		i=i.split(",")
		entry_id=i[0]
		meta_assignment_file=i[1]
		image_1=i[2]
		compound_name="NONE"
		smiles="NONE"

		try:
			compound_name=i[3]
		except:		
			print ("Warning!!! No compound name has been found in the following entry:")	
			print (i, linesep)	

		try:
			smiles=i[4]
		except:		
			print ("Warning!!! No Smiles string has been found in the following entry:")	
			print (i, linesep)	


		if entry_id in files_with_inputs_dict:
			print ("ERROR!!!  entry_id  %s is already in files_with_inputs_dict " % i)
			print ("Exiting...")
			sys.exit()
		else:
			files_with_inputs_dict[entry_id]={}	
			files_with_inputs_dict[entry_id]["meta_assignment_file"]=meta_assignment_file.strip()
			files_with_inputs_dict[entry_id]["image_1"]=image_1.strip()
			files_with_inputs_dict[entry_id]["compound_name"]=compound_name.strip()
			files_with_inputs_dict[entry_id]["smiles"]=smiles.strip()

	debug=0
	if debug==1:
		for entry_id in  files_with_inputs_dict:
			meta_assignment_file=files_with_inputs_dict[entry_id]["meta_assignment_file"]
			image_1	=files_with_inputs_dict[entry_id]["image_1"]		
			print (f"{entry_id}, {meta_assignment_file}, {image_1}")
		print ("Exiting after function parse_csv_file_with_inputs()")
		sys.exit()	
	return files_with_inputs_dict	




def main(csv_file_with_inputs,
	image_file,
	meta_assignment_file,
	meta_file,
	assignment_file,
	project_dir,
	location,
	project,
	meta_dict,
	triggers,
	columns,
	column_for_index,
	sheet_name,
	excel_start_row,
	excel_start_column,
	meta_order_in_excel,
	image_x_scale,
	image_y_scale,	
	required_meta_fields,	
	):
	"""
	Main program
	"""
	verbose=0
	if verbose==1:
		print  ('Running function main()')
		print ("meta_assignment_file = ", meta_assignment_file)
		print ("image_file = ", image_file)
		#sys.exit()

	files_with_inputs_dict=parse_csv_file_with_inputs(csv_file_with_inputs)	
	excel_filename="%s/%s.xlsx" % (project_dir, project)
	skpipped_entries_filename="%s/%s_skipped_entries.csv" % (project_dir, project)

	if len(files_with_inputs_dict)==0:
		print ("ERROR!!! CSV file %s either misformatted or empty" % csv_file_with_inputs)
	else:	
		create_multi_sheet_excel_file(
									files_with_inputs_dict,
									excel_filename,
									triggers,
									columns,
									meta_order_in_excel,
									image_x_scale,
									image_y_scale,
									image_file,
									column_for_index,
									excel_start_row,
									excel_start_column,
									required_meta_fields,
									skpipped_entries_filename,
									meta_dict,
									)
	debug=1
	if debug==1:
		print ("Exiting after function main()")
		sys.exit()				
	return	



def create_multi_sheet_excel_file(
							files_with_inputs_dict,
							excel_filename,
							triggers,
							columns,
							meta_order_in_excel,
							image_x_scale,
							image_y_scale,
							image_file,
							column_for_index,
							excel_start_row,
							excel_start_column,
							required_meta_fields,
							skpipped_entries_filename,
							meta_dict,
							):
	"""
	Create multi-sheet Excel file
	"""
	verbose=0
	if verbose==1:
		print ('Running function create_multi_sheet_excel_file()')
		print ("column_for_index = ", column_for_index)
		print ("excel_filename = ", excel_filename)
		#sys.exit()

	excel_start_row=len(meta_order_in_excel)+1
	skipped_entries_text=""
	
	# Create a Pandas Excel writer using XlsxWriter as the engine.
	writer = pd.ExcelWriter(excel_filename, engine='xlsxwriter')
	#writer = pd.ExcelWriter(excel_filename, engine='openpyxl')


	for entry_id in files_with_inputs_dict:
		sheet_name=entry_id
		meta_assignment_file=files_with_inputs_dict[entry_id]["meta_assignment_file"]
		image_file=files_with_inputs_dict[entry_id]["image_1"]
		compound_name=files_with_inputs_dict[entry_id]["compound_name"]
		smiles=files_with_inputs_dict[entry_id]["smiles"]
		reason_2_skip=""

		if file_exists(meta_assignment_file)==0:
			reason_2_skip="File with %s NMR predictions does not exist" % meta_assignment_file
		else:	
			output_dict=parse_assignment_file(
											meta_assignment_file,
											triggers,
											columns)
			if len(output_dict)==0:
				reason_2_skip="File with %s NMR predictions is empty" % meta_assignment_file
			else:

				#print ("output_dict = ", output_dict)																


				# Create a Pandas dataframe from data.
				df = pd.DataFrame(output_dict)
				df = df.set_index(column_for_index)
				#print ("df = ", df)    



				df.to_excel(writer, sheet_name=sheet_name, startrow=excel_start_row+1, startcol=excel_start_column)

				# Convert the dataframe to an XlsxWriter Excel object.
				# #df.to_excel(writer, startrow=4, startcol=0)

				# Get the xlsxwriter workbook and worksheet objects.
				workbook  = writer.book
				# #wrap_format = workbook.add_format({'text_wrap': True})
				worksheet = writer.sheets[sheet_name]

				counter=0


				cell_format_bold = workbook.add_format({'bold': True, 'size': '11'})
				cell_format_bold.set_align("center")

				cell_format = workbook.add_format({'size': '11'})
				cell_format.set_text_wrap()
				#cell_format.set_border(1)
				cell_format.set_align("vcenter")


				cell_format_center = workbook.add_format({'size': '11'})
				cell_format_center.set_text_wrap()
				#cell_format_center.set_border(1)
				cell_format_center.set_align("center")
				cell_format_center.set_align("vcenter")




				cell_format_dropdown = workbook.add_format({'bold': True,'size': '11'})
				cell_format_dropdown.set_text_wrap()
				cell_format_dropdown.set_border(1)
				cell_format_dropdown.set_align("center")
				cell_format_dropdown.set_align("vcenter")
				#cell_format_dropdown.set_pattern(4)  
				#cell_format_dropdown.set_bg_color('white')

				worksheet.write(counter, 0, "Parameter",cell_format_bold)
				worksheet.write(counter, 1, "Required?",cell_format_bold)
				worksheet.write(counter, 2, "Value",cell_format_bold)
				worksheet.write(counter, 3, "Allowed entries",cell_format_bold)	
				counter=1

				for meta_param in meta_order_in_excel:

					required_or_optional_word="No"
					# if meta_param in required_meta_fields:
					# 	print ("meta_param %s has been found in required_meta_fields"  % meta_param)
					# 	required_or_optional_word="Required"
					# 	#sys.exit()

				
					meta_param_value="Enter_value_here"
					if meta_param=="Compound Name" and compound_name not in ["","NONE"]:
						meta_param_value=compound_name

					if meta_param=="Smiles" and smiles not in ["","NONE"]:
						meta_param_value=smiles

					if meta_param not in meta_dict:
						print ("ERROR!!!!  meta_param %s  not in meta_dict" %  meta_param)	
						print ("Exiting...")
						sys.exit()
					else:
						required_or_optional=meta_dict[meta_param]["required"]
						if required_or_optional==1:	
							required_or_optional_word="Yes"

					allowed_entries=meta_dict[meta_param]["allowed_entries"]
						#allowed_entries_text=""
						# for i in allowed_entries:
						# 	allowed_entries_text+="%s, " % i	

						# if allowed_entries_text=="":
						# 	allowed_entries_text="Any"
						# else:	
						# 	allowed_entries_text=allowed_entries_text[:-2]	

					

					worksheet.write(counter, 0, meta_param, cell_format)
					worksheet.write(counter, 1, required_or_optional_word, cell_format_center)
					if len(allowed_entries)==0:
						worksheet.write(counter, 2, meta_param_value, cell_format)
					else:
						worksheet.write(counter, 2, "\N{CHECK MARK}"+"Click to select:",cell_format_dropdown)
						worksheet.data_validation(counter, 2,counter, 2, {'validate': 'list',
									'input_title': 'Click on the arrow for options',
									'input_message': 'Click on the down arrow above to see options',
                                  	'source': allowed_entries})


					# count=0
					# for i in allowed_entries:
					# 	worksheet.write(counter, 3+count, i, cell_format_center)
					# 	count+=1

					# # worksheet.write(counter, 1, meta_param_value)
					counter+=1

				# # Insert an image.
				worksheet.insert_image('J%s' % counter, 
									image_file,
									{'x_scale': image_x_scale, 'y_scale': image_y_scale},
									)
				# Set column width
				worksheet.set_column('A:B',25)
				worksheet.set_column('C:AA',17)

				# # Close the Pandas Excel writer and output the Excel file.
		if reason_2_skip!="":
			skipped_entries_text+="%s," % entry_id
			skipped_entries_text+="%s," % compound_name
			skipped_entries_text+="%s," % smiles
			skipped_entries_text+="%s" % reason_2_skip + linesep

	writer.save()	

	if 	skipped_entries_text!="":
		write_file(skpipped_entries_filename,skipped_entries_text)						

	print ("Excel file %s has been created" % excel_filename)
	debug=1
	if debug==1:
		print ("Exiting after function create_multi_sheet_excel_file()")
		sys.exit()	
	return								

####################################
# Arguments
####################################

command_text="python "
i=0
if len(sys.argv)>1:
	for item in sys.argv:
		##############################
		# Project/file management
		##############################
		if item in ["-l"]:
			location  = os.path.abspath(sys.argv[i + 1])
		if item in ["-p"]:
			project  = sys.argv[i + 1]
		if item in ["-v1"]:
			verbose_level_1  = int(sys.argv[i + 1])
		if item in ["-e", "-env"]:
			env  = sys.argv[i + 1]

		##############################
		# Input options
		##############################
		if item in ["-i"]:
			csv_file_with_inputs  = os.path.abspath(sys.argv[i + 1])
		if item in ["-img"]:
			image_file  = os.path.abspath(sys.argv[i + 1])
		if item in ["-meta_assign"]:
			meta_assignment_file  = os.path.abspath(sys.argv[i + 1])
		if item in ["-meta"]:
			meta_file  = os.path.abspath(sys.argv[i + 1])
		if item in ["-assign"]:
			assignment_file  = os.path.abspath(sys.argv[i + 1])
		i+=1
	command_text+=" %s " % item

#######################################################
#  Processing general arguments
#######################################################							
this_script_full_path=os.path.abspath(sys.argv[0])
this_script_name=os.path.basename(sys.argv[0])
script_location=os.path.dirname(this_script_full_path)
if verbose_level_1>0:
	print ("this_script_full_path=", this_script_full_path)
	print ("this_script_name=", this_script_name)
	print ("script_location=", script_location)
cur_dir=os.getcwd()
if location=="":
	location=cur_dir
project_dir="%s/%s" % (location,project)


debug=0
if debug==1:
	print ("project_dir = ", project_dir)
	sys.exit()
make_dir_no_remove(project_dir)

main(csv_file_with_inputs,
	image_file,
	meta_assignment_file,
	meta_file,
	assignment_file,
	project_dir,
	location,
	project,
	meta_dict,
	triggers,
	columns,
	column_for_index,
	sheet_name,
	excel_start_row,
	excel_start_column,
	meta_order_in_excel,
	image_x_scale,
	image_y_scale,
	required_meta_fields,
	)		