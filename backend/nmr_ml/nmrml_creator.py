
############################################
# Importing modules
############################################
import sys, os, random, copy, shutil, json, struct, zlib, array,numpy
from os import linesep
from os.path import exists
from random import shuffle
import time, base64, cmath
import datetime
from standard_functions_py3 import *

############################################
# End of importing modules
############################################

############################################
# Command examples
############################################
#python nmrml_creator_August_12th_2020.py -solvent H2O -standard DSS -freq 700MHz -spec_type 1D-1H
############################################
# End of command examples
############################################


############################################
# Default settings
############################################
output_path="NONE"
project="project"
location=""
verbose_level_1=1
#npmrd_cs_abth_path="NP0001510_12_142.csv"
#npmrd_cs_abth_path="NP0001510_12_144.csv"
npmrd_cs_abth_path=""
#sdf_abth_path="/Users/<USER>/Dropbox/Macbook_Pro/NMR_auto_assignment/test_files/Isovalerylglycine_HMDB0000678.sdf"
#sdf_abth_path="/Users/<USER>/Research/np-mrd/public/downloads/313_NP0000174_12/nmrpred_output.mol"
sdf_abth_path="nmrpred_output.mol"
#nmrpred_peak_list_abth_path="/Users/<USER>/Research/np-mrd/public/downloads/313_NP0000174_12/nmrpred_1h_peaklist.txt"
nmrpred_peak_list_abth_path="nmrpred_1h_peaklist.txt"
param_path="nmrpred_1h_params.txt"
fid_path="nmrpred_1h_fid.txt"
spectrum_path="nmrpred_1h_spectrum.txt"
byteFormat="complex128"
compressed="true"
intensity_scaling_coefficient=10000.0
signal_width_scaling_coefficient=2.0

genus=None
solvent=None
species=None
spectrometer_frequency=None
literature_reference=None
chemical_shift_standard=None
temperature=None
spectrum_type=None
npmrdb_id=None
npmrdb_session_id=None
npmrdb_submission_id=None
npmrdb_user_id=None
npmrdb_user_defined_name=None
literature_reference_type = None
physical_state_of_compound = None
melting_point = None
boiling_point = None
cluster_count_1H=0
cluster_count_13C=0
cluster_count_13C_1H=0

spectrumAnnotationList_tag="spectrumAnnotationList"
spectrumRef_parameter="spectrumRef"
atomAssignment_tag="atomAssignment"
spectrumRef_value="spectrum1"
opened_tag="<"
closed_slash_tag="/>"
closed_tag=">"
slash="/"
default_peak_width=550

chemicalCompound_tag="chemicalCompound"
identifierList_tag="identifierList"
identifier_tag="identifier"
cvRef_parameter="cvRef"
accession_parameter="accession"
name_parameter="name"
output_1H_for_JSviewer="NONE"
output_13C_for_JSviewer="NONE"
output_13C_1H="NONE"


atomAssignmentList_tag="atomAssignmentList"
multiplet_tag="multiplet"
center_parameter = "center" 
atoms_tag = "atoms" 
atomRefs_parameter = "atomRefs"
multiplicity_tag="multiplicity"


peakList_tag="peakList"
peak_tag="peak"
amplitude_parameter="amplitude"
width_parameter="width"

structure_tag="structure"
atomList_tag="atomList"
atom_parameter="atomList"
elementType_parameter="elementType"
x_parameter="x"
y_parameter="y"

bondList_tag="bondList"
bond_taf="bond"
atomRefs_parameter="atomRefs"
order_parameter="order"

spectrumList_tag="spectrumList"


identifierList_identifier_cvRef_parameter="cvRef"
identifierList_identifier_cvRef_value="NMRCV"
identifierList_identifier_accession_parameter="accession"
identifierList_identifier_accession_value=None
identifierList_identifier_name_parameter="name"
identifierList_identifier_name_value=None

nmrpred_13c_peak_list_abth_path  = "NONE"
nmrpred_13c_param_path   = "NONE"
nmrpred_13c_fid_abs_path  = "NONE"
nmrpred_13c_spectrum_abs_path   = "NONE"
############################################
# Dictionaries
############################################
multiplet_structure_dict={}
multiplet_structure_dict["s"]="singlet"
multiplet_structure_dict["q"]="quartet"
multiplet_structure_dict["t"]="triplet"
multiplet_structure_dict["dd"]="doublet of doublets"
multiplet_structure_dict["ddd"]="doublet of doublet of doublets"
multiplet_structure_dict["m"]="multiplet"
multiplet_structure_dict["td"]="triplet of doublets"
multiplet_structure_dict["dt"]="doublet of triplets"
multiplet_structure_dict["dddd"]="doublet of doublet of doublet of doublets"
multiplet_structure_dict["tt"]="triplet of triplets"
multiplet_structure_dict["dq"]="doublet of quartets"
multiplet_structure_dict["ddt"]="doublet of Doublet of Triplets"
multiplet_structure_dict["dtd"]="doublet of Triplet of Doublets"
multiplet_structure_dict["tdd"]="triplet of Doublet of Doublets"
multiplet_structure_dict["dtd"]="doublet of Triplet of Doublets"


############################################
# Examples of internal structures
############################################

chemical_shift_dict={}
chemical_shift_dict[1]={}
chemical_shift_dict[1]["multiplet_center"]="0.925"
chemical_shift_dict[1]["atoms_atomRefs"]=["a18","a19","a20","a15","a16","a17"]
chemical_shift_dict[1]["multiplicity_accession"]="1000184"
chemical_shift_dict[1]["multiplicity_name"]="doublet"


chemical_shift_dict[1]["peak_dict"]={}
chemical_shift_dict[1]["peak_dict"][1]={}
chemical_shift_dict[1]["peak_dict"][1]["peak_center"]=0.919
chemical_shift_dict[1]["peak_dict"][1]["amplitude"]=0.9831
chemical_shift_dict[1]["peak_dict"][1]["width"]=551.1

chemical_shift_dict[1]["peak_dict"][2]={}
chemical_shift_dict[1]["peak_dict"][2]["peak_center"]=0.930
chemical_shift_dict[1]["peak_dict"][2]["amplitude"]=1.0000
chemical_shift_dict[1]["peak_dict"][2]["width"]=557.82


chemical_shift_dict[2]={}
chemical_shift_dict[2]["multiplet_center"]="1.992"
chemical_shift_dict[2]["atoms_atomRefs"]=["a12"]
chemical_shift_dict[2]["multiplicity_accession"]="1400305"
chemical_shift_dict[2]["multiplicity_name"]="multiplet"

chemical_shift_dict[2]["peak_dict"]={}
chemical_shift_dict[2]["peak_dict"][1]={}
chemical_shift_dict[2]["peak_dict"][1]["peak_center"]=0.919
chemical_shift_dict[2]["peak_dict"][1]["amplitude"]=0.9831
chemical_shift_dict[2]["peak_dict"][1]["width"]=551.1

chemical_shift_dict[2]["peak_dict"][2]={}
chemical_shift_dict[2]["peak_dict"][2]["peak_center"]=0.930
chemical_shift_dict[2]["peak_dict"][2]["amplitude"]=1.0000
chemical_shift_dict[2]["peak_dict"][2]["width"]=557.82



coordinate_dict={}
coordinate_dict["a1"]={}
coordinate_dict["a1"]["elementType"]="O"
coordinate_dict["a1"]["x"]="-2.2321"
coordinate_dict["a1"]["y"]="-1.866"
coordinate_dict["a1"]["z"]=""


coordinate_dict["a2"]={}
coordinate_dict["a2"]["elementType"]="O"
coordinate_dict["a2"]["x"]="-1.366"
coordinate_dict["a2"]["y"]="-2.366"
coordinate_dict["a2"]["z"]=""


bond_dict={}
bond_dict[1]={}
bond_dict[1]["atom_1"]="a1"
bond_dict[1]["atom_2"]="a9"
bond_dict[1]["order"]="2"

bond_dict[2]={}
bond_dict[2]["atom_1"]="a2"
bond_dict[2]["atom_2"]="a11"
bond_dict[2]["order"]="1"


############################################
# End of default settings
############################################

############################################
# Functions
############################################


def add_xml_version(text=""):
	"""
	Add <?xml version="1.0" encoding="UTF-8" standalone="yes"?>
	"""
	verbose=1
	if verbose==1:
		print  ('Running function add_xml_version()')

	default_text="""<?xml version="1.0" encoding="UTF-8" standalone="yes"?>""" + linesep  
	if text =="":
		text=default_text

	debug=0
	if debug==1:
		print ("Exiting after function add_xml_version()")
		sys.exit()      
	return text



def open_nmrML_tag(text=""):
	"""
	Open nmrML tag
	"""
	verbose=0
	if verbose==1:
		print  ('Running function open_nmrML_tag())')

	default_text="""<nmrML xmlns="http://nmrml.org/schema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="1.0.rc1" xsi:schemaLocation="http://nmrml.org/schema http://nmrml.org/schema/v1.0.rc1/nmrML.xsd">
""" + linesep  
	if text =="":
		text=default_text

	debug=0
	if debug==1:
		print ("Exiting after function open_nmrML_tag()")
		sys.exit()      
	return text


def close_nmrML_tag(text=""):
	"""
	Close nmrML tag    
	"""
	verbose=0
	if verbose==1:
		print  ('Running function close_nmrML_tag())')

	default_text="""</nmrML>""" + linesep  
	if text =="":
		text=default_text

	debug=0
	if debug==1:
		print ("Exiting after function close_nmrML_tag()")
		sys.exit()      
	return text    


def add_linseps(number):
	"""
	Add line separations
	"""
	verbose=0
	if verbose==1:
		print  ('Running function add_linseps())')

	text=linesep*number

	debug=0
	if debug==1:
		print ("Exiting after function add_linseps()")
		sys.exit()      
	return text    


def open_tag(text):
	"""
	Open a nmrML tag
	"""
	verbose=0
	if verbose==1:
		print  ('Running function open_tag())')

	text=text+linesep

	debug=0
	if debug==1:
		print ("Exiting after function open_tag()")
		sys.exit()      
	return text


def close_tag(text):
	"""
	Close a nmrML tag
	"""
	verbose=0
	if verbose==1:
		print  ('Running function close_tag())')

	text=text+linesep

	debug=0
	if debug==1:
		print ("Exiting after function close_tag()")
		sys.exit()      
	return text


def create_parameter_string(parameter=None,value=None):
	"""
	Create parameter text
	"""
	text=""
	if parameter!=None and value!=None:
		text='%s="%s"' % (parameter,value)
	return text


def cv_id_NMRCV(opened_tag=opened_tag,
				closed_slash_tag=closed_tag,
				l_id=None,
				fullName=None,
				version=None,
				URI=None,
				):
	"""
	Add cv id NMRCV  field
	"""

	verbose=0
	if verbose==1:
		print ('Running function cv_id_NMRCV())')

	example="""<cv id="NMRCV" fullName="Nuclear Magnetic Resonance CV" version="1.1.0" URI="http://nmrml.org/cv/v1.1.0/nmrCV.owl"/>""" + linesep  


	id_parameter="id"
	default_id="NMRCV"

	fullName_parameter="fullName"
	default_fullName="Nuclear Magnetic Resonance CV"

	default_version="1.1.0"
	version_parameter="version"

	URI_parameter="URI"
	default_URI="http://nmrml.org/cv/v1.1.0/nmrCV.owl"

	if URI==None: URI=default_URI
	if version==None: version=default_version
	if fullName==None: fullName=fullName_parameter
	if l_id==None: l_id=default_id


	id_string=create_parameter_string(parameter=id_parameter,value=default_id)
	fullName_string=create_parameter_string(parameter=fullName_parameter,value=default_fullName)
	version_string=create_parameter_string(parameter=version_parameter,value=default_version)
	URI_string=create_parameter_string(parameter=URI_parameter,value=URI)
	tag="cv"

	space=" "

	text=""
	text+=opened_tag
	text+=tag
	text+=space   
	text+=id_string
	text+=space   

	text+=fullName_string
	text+=space 

	text+=version_string
	text+=space 
   
	text+=URI_string
   
	text+=closed_slash_tag
	text+=add_linseps(1)

	debug=0
	if debug==1:
		print ("example = ", example)
		print (add_linseps(2))        
		print ("text = ", text)
		print (add_linseps(2))                
		print ("Exiting after function cv_id_NMRCV()")
		sys.exit()      
	return text



def add_text_between_tags(text,tag,indent=0):
	"""
	Add text between tags
	"""
	verbose=0
	if verbose==1:
		print ('Running function  add_text_between_tags())')

	total_text="" 
	total_text+=add_tabs(indent)     
	total_text+="<%s>" % tag + linesep
	total_text+=text + linesep
	total_text+=add_tabs(indent)         
	total_text+="</%s>" % tag+ linesep

	debug=0
	if debug==1:           
		print ("Exiting after function add_text_between_tags()")
		sys.exit()    
	return total_text




def create_coordinates_text(coordinate_dict,indent=0):
	"""
	Creating multiplet text

  <atomList>
		<atom id="a1" elementType="O" x="-2.2321" y="-1.866"/>
		<atom id="a2" elementType="O" x="-1.366" y="-2.366"/>
		<atom id="a3" elementType="O" x="0.366" y="-2.366"/>
		<atom id="a4" elementType="N" x="-1.366" y="-0.366"/>
		<atom id="a5" elementType="C" x="-3.0981" y="0.634"/>
		<atom id="a6" elementType="C" x="-3.0981" y="-0.366"/>
		<atom id="a7" elementType="C" x="-4.0981" y="0.634"/>
		<atom id="a8" elementType="C" x="-3.0981" y="1.634"/>
		<atom id="a9" elementType="C" x="-2.2321" y="-0.866"/>
		<atom id="a10" elementType="C" x="-0.5" y="-0.866"/>
		<atom id="a11" elementType="C" x="-0.5" y="-1.866"/>
		<atom id="a12" elementType="H" x="-2.0981" y="0.634"/>
		<atom id="a13" elementType="H" x="-4.0981" y="-0.366"/>
		<atom id="a14" elementType="H" x="-3.5981" y="-1.2321"/>
		<atom id="a15" elementType="H" x="-4.9641" y="1.134"/>
		<atom id="a16" elementType="H" x="-4.0981" y="1.634"/>
		<atom id="a17" elementType="H" x="-4.5981" y="-0.2321"/>
		<atom id="a18" elementType="H" x="-2.5981" y="2.5"/>
		<atom id="a19" elementType="H" x="-2.0981" y="1.634"/>
		<atom id="a20" elementType="H" x="-3.9641" y="2.134"/>
		<atom id="a21" elementType="H" x="-1.366" y="0.634"/>
		<atom id="a22" elementType="H" x="0.5" y="-0.866"/>
		<atom id="a23" elementType="H" x="0.0" y="-0.0"/>
		<atom id="a24" elementType="H" x="-1.366" y="-3.366"/>
	</atomList>
coordinate_dict={}
coordinate_dict["a1"]={}
coordinate_dict["a1"]["elementType"]="O"
coordinate_dict["a1"]["x"]="-2.2321"
coordinate_dict["a1"]["y"]="-1.866"
coordinate_dict["a1"]["z"]=""


coordinate_dict["a2"]={}
coordinate_dict["a2"]["elementType"]="O"
coordinate_dict["a2"]["x"]="-1.366"
coordinate_dict["a2"]["y"]="-2.366"
coordinate_dict["a2"]["z"]=""


	"""
	verbose=1
	if verbose==1:
		print  ('Running function create_coordinates_text)')

	text=""   
	for atom in coordinate_dict:

		elementType=coordinate_dict[atom]["elementType"]
		x=coordinate_dict[atom]["x"]
		y=coordinate_dict[atom]["y"]
		z=coordinate_dict[atom]["z"]

		text+=add_tabs(1+indent) 
		text+='<atom id="%s" '  % atom
		text+='elementType="%s" ' % elementType 
		text+='x="%s" ' % x
		text+='y="%s" ' % y
		if z not in [None,"","NONE"," "]:
			text+='z="%s"' % z
		text+='/>' + linesep

	debug=0
	if debug==1:
	   # I stopped here
		print ("text = ", text)
		print ('Example:  <atom id="a1" elementType="O" x="-2.2321" y="-1.866"/>')
		print ("Exiting after function create_coordinates_text)")
		sys.exit()
	return text


def create_bond_text(bond_dict,indent=0):
	"""
	Create bond text
	 <bondList>
		<bond atomRefs="a1 a9" order="2"/>
		<bond atomRefs="a2 a11" order="1"/>
		<bond atomRefs="a2 a24" order="1"/>
		<bond atomRefs="a3 a11" order="2"/>
		<bond atomRefs="a4 a9" order="1"/>
		<bond atomRefs="a4 a10" order="1"/>
		<bond atomRefs="a4 a21" order="1"/>
		<bond atomRefs="a5 a6" order="1"/>
		<bond atomRefs="a5 a7" order="1"/>
		<bond atomRefs="a5 a8" order="1"/>
		<bond atomRefs="a5 a12" order="1"/>
		<bond atomRefs="a6 a9" order="1"/>
		<bond atomRefs="a6 a13" order="1"/>
		<bond atomRefs="a6 a14" order="1"/>
		<bond atomRefs="a7 a15" order="1"/>
		<bond atomRefs="a7 a16" order="1"/>
		<bond atomRefs="a7 a17" order="1"/>
		<bond atomRefs="a8 a18" order="1"/>
		<bond atomRefs="a8 a19" order="1"/>
		<bond atomRefs="a8 a20" order="1"/>
		<bond atomRefs="a10 a11" order="1"/>
		<bond atomRefs="a10 a22" order="1"/>
		<bond atomRefs="a10 a23" order="1"/>
	</bondList>

	bond_dict={}
bond_dict[1]={}
bond_dict[1][atom_1]="a1"
bond_dict[1][atom_2]="a9"
bond_dict[1][order]="2"

bond_dict[2]={}
bond_dict[2][atom_1]="a2"
bond_dict[2][atom_2]="a11"
bond_dict[2][order]="1"
	"""
	verbose=1
	if verbose==1:
		print  ('Running function create_bond_text()')
	text=""
	for i in bond_dict:
		atom_1=bond_dict[i]["atom_1"]
		atom_2=bond_dict[i]["atom_2"]  
		order=bond_dict[i]["order"]
		text+=add_tabs(1+indent) 
		text+='<bond atomRefs="%s %s" order="%s"/>"' % (atom_1,atom_2,order) + linesep

	debug=0
	if debug==1:
	   # I stopped here
		print ("text = ", text)
		print ('Example:  <bond atomRefs="a1 a9" order="2"/>')
		print ("Exiting after function create_bond_text()")
		sys.exit()
	return text   






def make_spectrumAnnotationList_text(atomAssignment_tag,
									spectrumRef_parameter,
									spectrumRef_value,
									chemicalCompound_tag,
									identifierList_tag,
									identifier_tag,
									cvRef_parameter,
									accession_parameter,
									name_parameter,
									opened_tag,
									closed_tag,
									):
	"""
	Make spectrumAnnotationList text
	"""
	verbose=1
	if verbose==1:
		print  ('Running function make_spectrumAnnotationList_text()')
	text="" 
	text+=open_atomAssignment(atomAssignment_tag=atomAssignment_tag,
						spectrumRef_parameter=spectrumRef_parameter,
						spectrumRef_value=spectrumRef_value,
						opened_tag=opened_tag,
						closed_tag=closed_tag,
						)   
	text+="         <%s>" % chemicalCompound_tag + linesep

	debug=0
	if debug==1:
		print ("Exiting after function make_spectrumAnnotationList_text()")
		sys.exit()    
	return text


def open_atomAssignment(atomAssignment_tag=atomAssignment_tag,
						spectrumRef_parameter=spectrumRef_parameter,
						spectrumRef_value=spectrumRef_value,
						opened_tag=opened_tag,
						closed_tag=closed_tag,
						):
	"""
	Open atomAssignment tag
	<atomAssignment spectrumRef="spectrum1">
	"""
	verbose=1
	if verbose==1:
		print  ('Running function open_atomAssignment()')
	example="""<atomAssignment spectrumRef="spectrum1">"""      
	text=""
	text+=opened_tag
	text+=atomAssignment_tag    
	text+=" "
	text+=create_parameter_string(parameter=spectrumRef_parameter,value=spectrumRef_value)
	text+=closed_tag
	text+=linesep
	   
	debug=0
	if debug==1:
		print ("Example = ", example)
		print ("text = ", text)
		print ("Exiting after function open_atomAssignment()")
		sys.exit()          
	return text



def create_identifierList(identifierList_tag,
						identifier_tag,
						identifierList_identifier_cvRef_parameter,
						identifierList_identifier_cvRef_value,
						identifierList_identifier_accession_parameter,
						identifierList_identifier_accession_value,
						identifierList_identifier_name_parameter,
						identifierList_identifier_name_value,
						npmrdb_id=None,
						npmrdb_session_id=None,
						npmrdb_submission_id=None,
						npmrdb_user_id=None,
						npmrdb_genus=None,
						npmrdb_species=None,
						npmrdb_reference=None,
						npmrdb_user_defined_name=None,
						literature_reference_type=None,
						physical_state_of_compound=None,
						melting_point=None,
						boiling_point=None,					
						):
	"""
	Create identifierList
	"""
	verbose=1
	if verbose==1:
		print ('Running function create_identifierList()')    
	example="""
	<identifierList>
		<identifier cvRef="NMRCV" accession="NMR:1002000" name="(3-methylbutanamido)acetic acid"/>
	</identifierList>
	"""
	text=""

	if identifierList_identifier_name_value not in ["","NONE",None,"NA"]:

		text+="<%s>" % (identifierList_tag)

		text+=linesep

		text+="<%s " % identifier_tag

		text+="%s " % create_parameter_string(parameter=identifierList_identifier_cvRef_parameter,
												value=identifierList_identifier_cvRef_value)

		text+="%s " % create_parameter_string(parameter=identifierList_identifier_accession_parameter,
												value=identifierList_identifier_accession_value)

		text+="%s " % create_parameter_string(parameter=identifierList_identifier_name_parameter,
												value=identifierList_identifier_name_value)
		text+="/>" 
	  
		text+=linesep

		if npmrdb_user_defined_name!=None:
			text+="""<NPMRD_USER_DEFINED_NAME value="%s" />""" % npmrdb_user_defined_name + linesep
		if npmrdb_id!=None:
			text+="""<NPMRD_ID value="%s" />""" % npmrdb_id + linesep
		if npmrdb_session_id!=None:
			text+="""<NPMRD_SESSION_ID value="%s"  />""" % npmrdb_session_id + linesep
		if npmrdb_submission_id!=None:
			text+="""<NPMRD_SUBMISSION_ID value="%s"  />""" % npmrdb_submission_id + linesep
		if npmrdb_user_id!=None:
			text+="""<NPMRD_USER_ID value="%s" />""" % npmrdb_user_id + linesep
		if npmrdb_genus!=None:
			text+="""<NPMRD_GENUS value="%s" />""" % npmrdb_genus + linesep
		if npmrdb_species!=None:
			text+="""<NPMRD_SPECIES value="%s" />""" % npmrdb_species + linesep

		if physical_state_of_compound!=None:
			text+="""<NPMRD_PHYSICAL_STATE value="%s" />""" % physical_state_of_compound + linesep

		if melting_point!=None:
			text+="""<NPMRD_MELTING_POINT value="%sC" />""" % melting_point + linesep

		if boiling_point!=None:
			text+="""<NPMRD_BOILING_POINT value="%sC" />""" % boiling_point + linesep		

		if npmrdb_reference!=None:
			text+="""<NPMRD_REFERENCE value="%s" />""" % npmrdb_reference + linesep

		if literature_reference_type!=None:
			text+="""<NPMRD_REFERENCE_TYPE value="%s" />""" % literature_reference_type + linesep

		text+="</%s>" % (identifierList_tag)+linesep

	debug=0

	if debug==1:

		identifierList_identifier_cvRef_parameter="cvRef"
		identifierList_identifier_cvRef_value="NMRCV"
		identifierList_identifier_accession_parameter="accession"
		identifierList_identifier_accession_value="NMR:1002000"
		identifierList_identifier_name_parameter="name"
		identifierList_identifier_name_value="(3-methylbutanamido)acetic acid"
						
		text=""

		text+="<%s>" % (identifierList_tag)

		text+=linesep

		text+="         <%s " % identifier_tag

		text+="%s " % create_parameter_string(parameter=identifierList_identifier_cvRef_parameter,
												value=identifierList_identifier_cvRef_value)

		text+="%s " % create_parameter_string(parameter=identifierList_identifier_accession_parameter,
												value=identifierList_identifier_accession_value)

		text+="%s " % create_parameter_string(parameter=identifierList_identifier_name_parameter,
												value=identifierList_identifier_name_value)
		text+="/>" 
	  
		text+=linesep

		text+="</%s>" % (identifierList_tag)+linesep

		print ("Example =",  example)
		print ("text = ", text)

		print ("Exiting... after function  create_identifierList()")
		sys.exit()
			   
	return  text


def add_space(number):
	text=" "*number
	return text


def add_tabs(number):
	text="\t"*number
	return text


def create_multiplet_text(chemical_shift_dict,indent=0):
	"""
	Creating multiplet text
	"""
	verbose=1
	if verbose==1:
		print  ('Running function chemical_shift_dict = ', chemical_shift_dict)
		print  ('Running function create_multiplet_text()')

	for i in chemical_shift_dict:
		print ("1) i, chemical_shift_dict[i] = ", i,chemical_shift_dict[i])
		multiplet_center = chemical_shift_dict[i]["multiplet_center"]
		print ("1) multiplet_center = ",multiplet_center)

	#print( "Exiting...")
	#sys.exit()	



	text=""   
	for i in chemical_shift_dict:
		print ("2) i = ", i)
		print ("2) i  chemical_shift_dict[i] = ", chemical_shift_dict[i])

		multiplet_center = chemical_shift_dict[i]["multiplet_center"] 
		atoms_atomRefs = chemical_shift_dict[i]["atoms_atomRefs"] 
		multiplicity_accession = chemical_shift_dict[i]["multiplicity_accession"] 
		multiplet_center = chemical_shift_dict[i]["multiplet_center"] 
		multiplet = chemical_shift_dict[i]["multiplicity_name"] 
		peak_dict=chemical_shift_dict[i]["peak_dict"]
		atoms_atomRefs_string=""
		for atom in atoms_atomRefs:
			atoms_atomRefs_string+="%s " % atom
		atoms_atomRefs_string=atoms_atomRefs_string.strip()
		text+=add_tabs(1+indent) 
		text+='<multiplet center="%s">' %  multiplet_center + linesep
		text+=add_tabs(2+indent) 
		text+="""<atoms atomRefs="%s"/>""" %  atoms_atomRefs_string + linesep
		text+=add_tabs(2+indent) 
		text+="""<multiplicity cvRef="NMRCV" accession="NMR:%s" name="%s feature"/>""" % (multiplicity_accession,multiplet) + linesep

		text+=add_tabs(2+indent) 
		text+="""<peakList>""" + linesep
		for j in  peak_dict:
			peak_center=peak_dict[j]["peak_center"]
			peak_amplitude=peak_dict[j]["amplitude"]
			peak_amplitude*=intensity_scaling_coefficient
			peak_width=peak_dict[j]["width"]
			text+=add_tabs(3+indent) 
			text+="""<peak center="%s" amplitude="%s" width="%s"/>""" % (peak_center,peak_amplitude,peak_width) + linesep

		text+=add_tabs(2+indent) 
		text+="</peakList>" + linesep
		text+=add_tabs(1+indent) 
		text+="</multiplet>" + linesep

	debug=0
	if debug==1:
		print ("text = ", text)
		print ("Exiting after function create_multiplet_text()")
		sys.exit()
	return text



def create_xml_text(
		literature_reference_type,
		physical_state_of_compound,
		melting_point,
		boiling_point,	
		chemical_shift_dict_13C,
		chemical_shift_dict_13C_1H,
		chemical_shift_dict,
		bond_dict,
		coordinate_dict,
		output_path,
		output_1H_for_JSviewer,
		output_13C_for_JSviewer,
		output_13C_1H,		
		genus,
		solvent,
		species,
		spectrometer_frequency,
		literature_reference,
		chemical_shift_standard,
		temperature,
		spectrum_type,
		encoded_length,	
		npmrdb_id,
		npmrdb_session_id,
		npmrdb_submission_id,
		npmrdb_user_id,
		npmrdb_user_defined_name,
		x_y_interleaved_length,
		x_y_interleaved_byte_string,
		x_y_interleaved_compressed_bytes,
		x_y_interleaved_compressed_encoded_spectrum,
		x_y_interleaved_byte_string_encoded_spectrum,	
		x_y_interleaved_byte_string_encoded_spectrum_13c,		
		y_encoded_spectrum="",
		x_encoded_spectrum="",
		spectrumDataArray_byteFormat=None,
		spectrumDataArray_compressed=None,		
	):
	"""
	Create XML text
	"""
	verbose=1
	if verbose==1:
		print  ('Running function create_xml_text)')
		print  ("genus = " ,genus)
		print  ("solvent = " ,solvent )
		print  ("species = " ,species)
		print  ("spectrometer_frequency = " ,spectrometer_frequency)
		print  ("literature_reference = " ,literature_reference)
		print  ("chemical_shift_standard = " ,chemical_shift_standard)
		print  ("temperature = " ,temperature)
		print  ("spectrum_type = " ,	spectrum_type)	
		#print ("y_encoded_spectrum = ", y_encoded_spectrum)
		print ("encoded_length = ", encoded_length)	
		#print ("x_y_interleaved_byte_string_encoded_spectrum_13c = ", x_y_interleaved_byte_string_encoded_spectrum_13c)	

	if spectrometer_frequency!=None:
		spectrometer_frequency=spectrometer_frequency.replace("Mz","")

	if spectrum_type in ["1D-1H","2D-1H-13C"]:
		spectrum_type_2_use="1D-1H"

		spectrumList_text_1D_1H=make_spectrumList_text(
		spectrum_type_2_use,
		x_y_interleaved_length,
		x_y_interleaved_byte_string,
		x_y_interleaved_compressed_bytes,
		x_y_interleaved_compressed_encoded_spectrum,
		x_y_interleaved_byte_string_encoded_spectrum,
		x_y_interleaved_byte_string_encoded_spectrum_13c,				
		y_encoded_spectrum=y_encoded_spectrum,
		x_encoded_spectrum=x_encoded_spectrum,
		spectrumDataArray_encoded_length=encoded_length,
		spectrumDataArray_compressed=spectrumDataArray_compressed,
		spectrumDataArray_byteFormat=spectrumDataArray_byteFormat,
		spectrum1D_id="1D-1H",
		)	

		acquisition_text_1D_1H=make_acquisition_text(
			irradiationFrequency=None,
			acquisitionParameterSet_numberOfSteadyStateScans=None,
			acquisitionParameterSet_numberOfScans=None,
			acquisitionNucleus_cvRef=None,
			acquisitionNucleus_accession=None,
			acquisitionNucleus_name=None,
			effectiveExcitationField_value=spectrometer_frequency,
			effectiveExcitationField_unitAccession=None,
			effectiveExcitationField_unitName=None,
			effectiveExcitationField_unitCvRef=None,
			spectrum_type=spectrum_type_2_use,
			sampleAcquisitionTemperature_value=temperature,
			sampleAcquisitionTemperature_unitAccession=None,
			sampleAcquisitionTemperature_unitName=None,
			sampleAcquisitionTemperature_unitCvRef=None,
			)



	if spectrum_type in ["1D-13C","2D-1H-13C"]:

		spectrum_type_2_use="1D-13C"

		spectrumList_text_1D_13C=make_spectrumList_text(
		spectrum_type_2_use,
		x_y_interleaved_length,
		x_y_interleaved_byte_string,
		x_y_interleaved_compressed_bytes,
		x_y_interleaved_compressed_encoded_spectrum,
		x_y_interleaved_byte_string_encoded_spectrum,
		x_y_interleaved_byte_string_encoded_spectrum_13c,				
		y_encoded_spectrum=y_encoded_spectrum,
		x_encoded_spectrum=x_encoded_spectrum,
		spectrumDataArray_encoded_length=encoded_length,
		spectrumDataArray_compressed=spectrumDataArray_compressed,
		spectrumDataArray_byteFormat=spectrumDataArray_byteFormat,
		spectrum1D_id="1D-13C",
		)	



		acquisition_text_1D_13C=make_acquisition_text(
			irradiationFrequency=None,
			acquisitionParameterSet_numberOfSteadyStateScans=None,
			acquisitionParameterSet_numberOfScans=None,
			acquisitionNucleus_cvRef=None,
			acquisitionNucleus_accession=None,
			acquisitionNucleus_name=None,
			effectiveExcitationField_value=spectrometer_frequency,
			effectiveExcitationField_unitAccession=None,
			effectiveExcitationField_unitName=None,
			effectiveExcitationField_unitCvRef=None,
			spectrum_type=spectrum_type_2_use,
			sampleAcquisitionTemperature_value=temperature,
			sampleAcquisitionTemperature_unitAccession=None,
			sampleAcquisitionTemperature_unitName=None,
			sampleAcquisitionTemperature_unitCvRef=None,
			)





	sample_text=create_sample_info(
	originalBiologicalSampleReference=None,
	postBufferpH=None,		
	fieldFrequencyLockName=solvent,
	ChemicalShiftStandard_accession=None,
	ChemicalShiftStandard_cvRef=None,
	ChemicalShiftStandard_name=chemical_shift_standard,
	ChemicalShiftStandard_value=None,
	)

	spectrumAnnotationList_text=make_spectrumAnnotationList_text(
									atomAssignment_tag,
									spectrumRef_parameter,
									spectrumRef_value,
									chemicalCompound_tag,
									identifierList_tag,
									identifier_tag,
									cvRef_parameter,
									accession_parameter,
									name_parameter,
									opened_tag,
									closed_tag,
									)


	identifierList_text=create_identifierList(identifierList_tag,
						identifier_tag,
						identifierList_identifier_cvRef_parameter,
						identifierList_identifier_cvRef_value,
						identifierList_identifier_accession_parameter,
						identifierList_identifier_accession_value,
						identifierList_identifier_name_parameter,
						identifierList_identifier_name_value,
						npmrdb_id=npmrdb_id,
						npmrdb_session_id=npmrdb_session_id,
						npmrdb_submission_id=npmrdb_submission_id,
						npmrdb_user_id=npmrdb_user_id,
						npmrdb_genus=genus,
						npmrdb_species=species,
						npmrdb_reference=literature_reference,
						npmrdb_user_defined_name=npmrdb_user_defined_name,
						literature_reference_type=literature_reference_type,
						physical_state_of_compound=physical_state_of_compound,
						melting_point=melting_point,
						boiling_point=boiling_point,						
						)


	structure_text=""
	coordinates_text=create_coordinates_text(coordinate_dict,indent=1)
	structure_text+=add_text_between_tags(coordinates_text,atomList_tag,indent=1)
	bond_text=create_bond_text(bond_dict,indent=1)
	structure_text+=add_text_between_tags(bond_text,bondList_tag,indent=1)

	multiplet_text_text_1H=""
	multiplet_text_text_13C=""
	multiplet_text_text_13C_1H=""
	if len(chemical_shift_dict_13C_1H)>0:
		multiplet_text_13C_1H=create_multiplet_text(chemical_shift_dict_13C_1H,indent=2)
		multiplet_text_text_13C_1H=add_text_between_tags(multiplet_text_13C_1H,atomAssignmentList_tag,indent=2)		
	if len(chemical_shift_dict_13C)>0:
		multiplet_text_13C=create_multiplet_text(chemical_shift_dict_13C,indent=2)
		multiplet_text_text_13C=add_text_between_tags(multiplet_text_13C,atomAssignmentList_tag,indent=2)
	if len(chemical_shift_dict)>0:
		multiplet_text_1H=create_multiplet_text(chemical_shift_dict,indent=2)
		multiplet_text_text_1H=add_text_between_tags(multiplet_text_1H,atomAssignmentList_tag,indent=2)




	text=""     
	text_13C_1H="" 
	text_1H_for_JSviewer="" 
	text_13C_for_JSviewer="" 

	text+=add_xml_version()    
	text+=open_nmrML_tag()
	text+=open_tag("<cvList>")
	text+=cv_id_NMRCV()    
	text+=close_tag("</cvList>") 
	multiplet_text=""
	text+=add_linseps(1)
	text+=sample_text

	text_13C_1H=text
	text_1H_for_JSviewer=text
	text_13C_for_JSviewer=text

	if spectrum_type in ["1D-1H","2D-1H-13C"]:
		text+=acquisition_text_1D_1H
		text_13C_1H+=acquisition_text_1D_1H
		text_1H_for_JSviewer+=acquisition_text_1D_1H

	if  spectrum_type in ["1D-13C","2D-1H-13C"]:	
		text+=acquisition_text_1D_13C
		text_13C_1H+=acquisition_text_1D_13C
		text_13C_for_JSviewer+=acquisition_text_1D_13C


	text+=add_linseps(1)
	text+="		<%s>" % spectrumList_tag + linesep

	text_13C_1H+=add_linseps(1)
	text_13C_1H+="		<%s>" % spectrumList_tag + linesep

	text_1H_for_JSviewer+=add_linseps(1)
	text_1H_for_JSviewer+="		<%s>" % spectrumList_tag + linesep

	text_13C_for_JSviewer+=add_linseps(1)
	text_13C_for_JSviewer+="		<%s>" % spectrumList_tag + linesep





	if spectrum_type in ["1D-1H"]:
		text+=spectrumList_text_1D_1H
		text_1H_for_JSviewer+=spectrumList_text_1D_1H
		text_13C_1H+=spectrumList_text_1D_1H

	elif  spectrum_type in ["1D-13C"]:
		text+=spectrumList_text_1D_13C
		text_13C_for_JSviewer+=spectrumList_text_1D_13C
		text_13C_1H+=spectrumList_text_1D_13C

	elif spectrum_type in ["2D-1H-13C"]:	
		text+=spectrumList_text_1D_13C
		text+=spectrumList_text_1D_1H

		text_13C_for_JSviewer+=spectrumList_text_1D_13C
		text_1H_for_JSviewer+=spectrumList_text_1D_1H

		text_13C_1H+=spectrumList_text_1D_1H
		text_13C_1H+=spectrumList_text_1D_13C


	text+="		</%s>" % spectrumList_tag + linesep
	text+="		<%s>" % spectrumAnnotationList_tag + linesep
	text+='		<%s %s="%s">' % (atomAssignment_tag,spectrumRef_parameter,spectrumRef_value) + linesep
	text+="		<%s>" % chemicalCompound_tag + linesep

	text_13C_1H+="		</%s>" % spectrumList_tag + linesep
	text_13C_1H+="		<%s>" % spectrumAnnotationList_tag + linesep
	text_13C_1H+='		<%s %s="%s">' % (atomAssignment_tag,spectrumRef_parameter,spectrumRef_value) + linesep
	text_13C_1H+="		<%s>" % chemicalCompound_tag + linesep

	text_1H_for_JSviewer+="		</%s>" % spectrumList_tag + linesep
	text_1H_for_JSviewer+="		<%s>" % spectrumAnnotationList_tag + linesep
	text_1H_for_JSviewer+='		<%s %s="%s">' % (atomAssignment_tag,spectrumRef_parameter,spectrumRef_value) + linesep
	text_1H_for_JSviewer+="		<%s>" % chemicalCompound_tag + linesep

	text_13C_for_JSviewer+="		</%s>" % spectrumList_tag + linesep
	text_13C_for_JSviewer+="		<%s>" % spectrumAnnotationList_tag + linesep
	text_13C_for_JSviewer+='		<%s %s="%s">' % (atomAssignment_tag,spectrumRef_parameter,spectrumRef_value) + linesep
	text_13C_for_JSviewer+="		<%s>" % chemicalCompound_tag + linesep


	text+=identifierList_text
	text_13C_1H+=identifierList_text
	text_1H_for_JSviewer+=identifierList_text
	text_13C_for_JSviewer+=identifierList_text


	text+=add_text_between_tags(structure_text,structure_tag,indent=1)
	text+="		</%s>" % chemicalCompound_tag + linesep
	#text+=add_text_between_tags(spectrumAnnotationList_text,spectrumAnnotationList_tag)

	text_13C_1H+=add_text_between_tags(structure_text,structure_tag,indent=1)
	text_13C_1H+="		</%s>" % chemicalCompound_tag + linesep

	text_1H_for_JSviewer+=add_text_between_tags(structure_text,structure_tag,indent=1)
	text_1H_for_JSviewer+="		</%s>" % chemicalCompound_tag + linesep

	text_13C_for_JSviewer+=add_text_between_tags(structure_text,structure_tag,indent=1)
	text_13C_for_JSviewer+="		</%s>" % chemicalCompound_tag + linesep



	if spectrum_type in ["1D-1H"]:
		text+=multiplet_text_text_1H
		text_1H_for_JSviewer+=multiplet_text_text_1H
		text_13C_1H+=multiplet_text_text_1H

	elif spectrum_type in ["1D-13C"]:
		text+=multiplet_text_text_13C
		text_13C_for_JSviewer+=multiplet_text_text_13C
		text_13C_1H+=multiplet_text_text_13C

	elif spectrum_type in ["2D-1H-13C"]:
		text+=multiplet_text_text_13C_1H
		text_13C_1H+=text_13C_1H
		text_13C_for_JSviewer+=multiplet_text_text_13C
		text_1H_for_JSviewer+=multiplet_text_text_1H



	text+="		</%s>" % atomAssignment_tag + linesep
	text+="		</%s>" % spectrumAnnotationList_tag + linesep
	text+=close_nmrML_tag()

	text_13C_1H+="		</%s>" % atomAssignment_tag + linesep
	text_13C_1H+="		</%s>" % spectrumAnnotationList_tag + linesep
	text_13C_1H+=close_nmrML_tag()


	text_1H_for_JSviewer+="		</%s>" % atomAssignment_tag + linesep
	text_1H_for_JSviewer+="		</%s>" % spectrumAnnotationList_tag + linesep
	text_1H_for_JSviewer+=close_nmrML_tag()

	text_13C_for_JSviewer+="		</%s>" % atomAssignment_tag + linesep
	text_13C_for_JSviewer+="		</%s>" % spectrumAnnotationList_tag + linesep
	text_13C_for_JSviewer+=close_nmrML_tag()

	if spectrum_type in ["2D-1H-13C"]:
		write_file(output_path,text_1H_for_JSviewer)
	else:
		write_file(output_path,text)	

	write_file(output_1H_for_JSviewer,text_1H_for_JSviewer)
	write_file(output_13C_for_JSviewer,text_13C_for_JSviewer)
	write_file(output_13C_1H,text_13C_1H)

	
	debug=0
	if debug==1:
		print ("text = ", text)
		#print ("x_y_interleaved_byte_string_encoded_spectrum_13c = ", x_y_interleaved_byte_string_encoded_spectrum_13c)			
		print ("Exiting after function create_xml_text()")
		sys.exit()        
	return  text


def create_processed_spectrum_entry(
		spectrum_type,
		x_y_interleaved_length,
		x_y_interleaved_byte_string,
		x_y_interleaved_compressed_bytes,
		x_y_interleaved_compressed_encoded_spectrum,
		x_y_interleaved_byte_string_encoded_spectrum,
		x_y_interleaved_byte_string_encoded_spectrum_13c,				
		y_encoded_spectrum=None,
		x_encoded_spectrum=None,
		spectrumDataArray_encoded_length=None,
		spectrumDataArray_compressed=None,
		spectrumDataArray_byteFormat=None,
		):
	"""
	Create processed spectrum entry
	"""
	verbose=1
	if verbose==1:
		print  ('Running function create_processed_spectrum_entry()')
		#print  ('x_y_interleaved_byte_string_encoded_spectrum_13c=', x_y_interleaved_byte_string_encoded_spectrum_13c)	

	spectrumDataArray_text=""

	spectrumDataArray_compressed="false"
	#spectrumDataArray_compressed="true"
	spectrumDataArray_byteFormat="complex128"
	#spectrumDataArray_encoded_length=len(x_y_interleaved_byte_string_encoded_spectrum)
	#data_2_use=x_y_interleaved_compressed_encoded_spectrum
	data_2_use=""
	if spectrum_type=="1D-1H":
		data_2_use=x_y_interleaved_byte_string_encoded_spectrum
	elif spectrum_type=="1D-13C":	
		data_2_use=x_y_interleaved_byte_string_encoded_spectrum_13c

	spectrumDataArray_encoded_length=len(data_2_use)

	if y_encoded_spectrum!="":
		spectrumDataArray_text+="		<spectrumDataArray "	
		if spectrumDataArray_compressed!=None:
			spectrumDataArray_text+=""" compressed="%s" """ % spectrumDataArray_compressed
		if spectrumDataArray_compressed!=None:
			spectrumDataArray_text+=""" encodedLength="%s" """ % spectrumDataArray_encoded_length
		if spectrumDataArray_compressed!=None:
			spectrumDataArray_text+='byteFormat="%s"' % spectrumDataArray_byteFormat
		spectrumDataArray_text+=">"
		#y_encoded_spectrum_text="".join(map(chr, y_encoded_spectrum))
		x_y_interleaved_byte_string_encoded_spectrum_text="".join(map(chr, data_2_use))
	
		#spectrumDataArray_text+=y_encoded_spectrum_text
		spectrumDataArray_text+=x_y_interleaved_byte_string_encoded_spectrum_text
		
		spectrumDataArray_text+="</spectrumDataArray>" + linesep



	debug=0
	if debug==1:
		#print ("x_y_interleaved_byte_string_encoded_spectrum_text = ", x_y_interleaved_byte_string_encoded_spectrum_text)
		write_file("tmp.txt", spectrumDataArray_text)	
		print ("spectrumDataArray_text = ", spectrumDataArray_text)
		print ("Exiting after function  create_processed_spectrum_entry()")
		sys.exit() 
	return spectrumDataArray_text


def make_spectrumList_text(
		spectrum_type,
		x_y_interleaved_length,
		x_y_interleaved_byte_string,
		x_y_interleaved_compressed_bytes,
		x_y_interleaved_compressed_encoded_spectrum,
		x_y_interleaved_byte_string_encoded_spectrum,
		x_y_interleaved_byte_string_encoded_spectrum_13c,	
		y_encoded_spectrum=None,
		x_encoded_spectrum=None,
		spectrumDataArray_encoded_length=None,
		spectrumDataArray_compressed=None,
		spectrumDataArray_byteFormat=None,
		spectrum1D_id="spectrum1",
	):
	verbose=1
	if verbose==1:
		#print ("y_encoded_spectrum = ", y_encoded_spectrum)
		print  ('Running function make_spectrumList_text()')
	spectrumList=""
	spectrum1D_text=""
	if y_encoded_spectrum!="":
		spectrum1D_text=make_spectrum1D_text(
			spectrum_type,
			x_y_interleaved_length,
			x_y_interleaved_byte_string,
			x_y_interleaved_compressed_bytes,
			x_y_interleaved_compressed_encoded_spectrum,
			x_y_interleaved_byte_string_encoded_spectrum,
			x_y_interleaved_byte_string_encoded_spectrum_13c,			
			y_encoded_spectrum=y_encoded_spectrum,
			x_encoded_spectrum=x_encoded_spectrum,
			spectrumDataArray_encoded_length=spectrumDataArray_encoded_length,
			spectrumDataArray_compressed=spectrumDataArray_compressed,
			spectrumDataArray_byteFormat=spectrumDataArray_byteFormat,
			spectrum1D_id=spectrum1D_id,
			)

	if spectrum1D_text!="": 
		spectrumList+="<spectrumList>" + linesep
		spectrumList+=spectrum1D_text
		spectrumList+="</spectrumList>" + linesep
		

	debug=0
	if debug==1:
		print ("spectrum1D_text = ", spectrum1D_text)
		print ("Exiting after function  make_spectrumList_text()")
		sys.exit()
	return	spectrum1D_text


def make_spectrum1D_text(
		spectrum_type,
		x_y_interleaved_length,
		x_y_interleaved_byte_string,
		x_y_interleaved_compressed_bytes,
		x_y_interleaved_compressed_encoded_spectrum,
		x_y_interleaved_byte_string_encoded_spectrum,
		x_y_interleaved_byte_string_encoded_spectrum_13c,		
		y_encoded_spectrum=None,
		x_encoded_spectrum=None,
		spectrumDataArray_encoded_length=None,
		spectrumDataArray_compressed=None,
		spectrumDataArray_byteFormat=None,
		spectrum1D_id="spectrum1",
	):
	"""
	Make spectrum 1D text
	"""
	verbose=1
	if verbose==1:
		print  ('Running function make_spectrum1D_text()')	
	spectrum1D_text=""

	processed_spectrum_text=create_processed_spectrum_entry(
		spectrum_type,
		x_y_interleaved_length,
		x_y_interleaved_byte_string,
		x_y_interleaved_compressed_bytes,
		x_y_interleaved_compressed_encoded_spectrum,
		x_y_interleaved_byte_string_encoded_spectrum,
		x_y_interleaved_byte_string_encoded_spectrum_13c,				
		y_encoded_spectrum=y_encoded_spectrum,
		x_encoded_spectrum=x_encoded_spectrum,
		spectrumDataArray_encoded_length=spectrumDataArray_encoded_length,
		spectrumDataArray_compressed=spectrumDataArray_compressed,
		spectrumDataArray_byteFormat=spectrumDataArray_byteFormat,
		)

	if 	processed_spectrum_text!="":
		spectrum1D_text+="""		
<spectrum1D numberOfDataPoints= "%s"  id="%s" >""" % (spectrumDataArray_encoded_length,spectrum1D_id) + linesep
		spectrum1D_text+=processed_spectrum_text
		spectrum1D_text+="</spectrum1D>" + linesep

	debug=0
	if debug==1:
		print ("spectrum1D_text = ", spectrum1D_text)
		print ("Exiting after function  make_spectrum1D_text()")
		sys.exit() 
	return spectrum1D_text



def create_sample_info(
	originalBiologicalSampleReference=None,
	postBufferpH=None,
	fieldFrequencyLockName=None,
	ChemicalShiftStandard_accession=None,
	ChemicalShiftStandard_cvRef=None,
	ChemicalShiftStandard_name=None,
	ChemicalShiftStandard_value=None,
	):
	"""
	Create sample nmrML entry
   """

	text=""
	local_text=""
	text+="   <sampleList>" + linesep
	text+="      <sample "
	if originalBiologicalSampleReference!=None:
		text+=""" originalBiologicalSampleReference= "%s" """ % originalBiologicalSampleReference
	text+=">" + linesep

	if postBufferpH!=None:
		local_text+="         <postBufferpH>%s</postBufferpH>" %  postBufferpH + linesep	

	if fieldFrequencyLockName!=None:
		local_text+="""         <fieldFrequencyLock fieldFrequencyLockName="%s"/>""" %  fieldFrequencyLockName + linesep

	if ChemicalShiftStandard_name!=None:
		local_text+="""         <chemicalShiftStandard """
		if ChemicalShiftStandard_accession!=None:
			local_text+=""" accession="%s" """ %  ChemicalShiftStandard_accession

		if ChemicalShiftStandard_cvRef!=None:
			local_text+=""" cvRef="%s" """ %  ChemicalShiftStandard_cvRef

		if ChemicalShiftStandard_name!=None:
			local_text+=""" name="%s" """ %  ChemicalShiftStandard_name


		if ChemicalShiftStandard_value!=None:
			local_text+=""" value="%s" """ %  ChemicalShiftStandard_value
		local_text+=""" </chemicalShiftStandard>""" + linesep

	text+=local_text
	text+="      </sample >"+ linesep
	text+="   </sampleList>" + linesep

	if local_text=="":
		text=""

	debug=0
	if debug==1:
		print ("text = ", text)
		print ("Exiting after function create_xml_text()")
		sys.exit()  
	return text



def make_acquisition_text(
			irradiationFrequency=None,
			acquisitionParameterSet_numberOfSteadyStateScans=None,
			acquisitionParameterSet_numberOfScans=None,
			acquisitionNucleus_cvRef=None,
			acquisitionNucleus_accession=None,
			acquisitionNucleus_name=None,
			effectiveExcitationField_value=None,
			effectiveExcitationField_unitAccession=None,
			effectiveExcitationField_unitName=None,
			effectiveExcitationField_unitCvRef=None,
			spectrum_type=None,
			sampleAcquisitionTemperature_value=None,
			sampleAcquisitionTemperature_unitAccession=None,
			sampleAcquisitionTemperature_unitName=None,
			sampleAcquisitionTemperature_unitCvRef=None,
			irradiationFrequency_value=None,
			irradiationFrequency_unitAccession=None,
			irradiationFrequency_unitName=None,
			irradiationFrequency_unitCvRef=None,		
			):
	"""
	Make acquisition text
	"""
	verbose=1
	if verbose==1:
		print  ('Running function make_acquisition_text()')

	text=""	
	local_acquisition_text=""

	effectiveExcitationField_unitName="megaHertz"
	sampleAcquisitionTemperature_unitName="kelvin"

	if spectrum_type!=None and "1D-1H" in spectrum_type:
		acquisitionNucleus_name="hydrogen atom"
	if spectrum_type!=None and "1D-13C" in spectrum_type:
		acquisitionNucleus_name="carbon atom"	

		local_acquisition_text=make_acquisition_1D(
					irradiationFrequency=irradiationFrequency,
					acquisitionParameterSet_numberOfSteadyStateScans=acquisitionParameterSet_numberOfSteadyStateScans,
					acquisitionParameterSet_numberOfScans=acquisitionParameterSet_numberOfScans,
					acquisitionNucleus_cvRef=acquisitionNucleus_cvRef,
					acquisitionNucleus_accession=acquisitionNucleus_accession,
					acquisitionNucleus_name=acquisitionNucleus_name,
					effectiveExcitationField_value=effectiveExcitationField_value,
					effectiveExcitationField_unitAccession=effectiveExcitationField_unitAccession,
					effectiveExcitationField_unitName=effectiveExcitationField_unitName,
					effectiveExcitationField_unitCvRef=effectiveExcitationField_unitCvRef,
					sampleAcquisitionTemperature_value=sampleAcquisitionTemperature_value,
					sampleAcquisitionTemperature_unitAccession=sampleAcquisitionTemperature_unitAccession,
					sampleAcquisitionTemperature_unitName=sampleAcquisitionTemperature_unitName,
					sampleAcquisitionTemperature_unitCvRef=sampleAcquisitionTemperature_unitCvRef,
					irradiationFrequency_value=irradiationFrequency_value,
					irradiationFrequency_unitAccession=irradiationFrequency_unitAccession,
					irradiationFrequency_unitName=irradiationFrequency_unitName,
					irradiationFrequency_unitCvRef=irradiationFrequency_unitCvRef,
					
					)

	if local_acquisition_text!="":
		text=" <acquisition>" + linesep		
		text+=local_acquisition_text
		text+="	</acquisition>"	 + linesep		
	debug=0
	if debug==1:
		print ("text = ", text)
		print ("Exiting after function  make_acquisition_text()")
		sys.exit() 				
	return text

def make_acquisition_1D(
			irradiationFrequency=None,
			acquisitionParameterSet_numberOfSteadyStateScans=None,
			acquisitionParameterSet_numberOfScans=None,
			acquisitionNucleus_cvRef=None,
			acquisitionNucleus_accession=None,
			acquisitionNucleus_name=None,
			effectiveExcitationField_value=None,
			effectiveExcitationField_unitAccession=None,
			effectiveExcitationField_unitName=None,
			effectiveExcitationField_unitCvRef=None,
			sampleAcquisitionTemperature_value=None,
			sampleAcquisitionTemperature_unitAccession=None,
			sampleAcquisitionTemperature_unitName=None,
			sampleAcquisitionTemperature_unitCvRef=None,
			irradiationFrequency_value=None,
			irradiationFrequency_unitAccession=None,
			irradiationFrequency_unitName=None,
			irradiationFrequency_unitCvRef=None,
			DirectDimensionParameterSet_decoupled=None, 
			DirectDimensionParameterSet_numberOfDataPoints=None,
			):
	"""
	Make acquisition1D text
	"""	
	verbose=1
	if verbose==1:
		print  ('Running function make_acquisition_1D()')	
	text=""
	local_text=""
	DirectDimensionParameterSet_text=""
	temperature_text=""
	DirectDimensionParameterSet_total_text=""

	text+="        <acquisition1D>" + linesep
	text+="        <acquisitionParameterSet"

	if acquisitionParameterSet_numberOfSteadyStateScans!=None:
		text+=""" numberOfSteadyStateScans="%s" """ % acquisitionParameterSet_numberOfSteadyStateScans
	if acquisitionParameterSet_numberOfScans!=None:
		text+=""" numberOfScans="%s" """ % acquisitionParameterSet_numberOfScans
	text+=">" + linesep

	if sampleAcquisitionTemperature_value!=None: 
		temperature_text+="""                <sampleAcquisitionTemperature value="%s" """ % sampleAcquisitionTemperature_value
		if sampleAcquisitionTemperature_unitAccession!=None:
			temperature_text+=""" unitAccession="%s" """ % (sampleAcquisitionTemperature_unitAccession)	
		if sampleAcquisitionTemperature_unitName!=None:
			temperature_text+=""" unitName="%s" """ % (sampleAcquisitionTemperature_unitName)
		if sampleAcquisitionTemperature_unitCvRef!=None:
			temperature_text+=""" unitCvRef="%s" """ % (sampleAcquisitionTemperature_unitCvRef)
		temperature_text+="/>"	+ linesep
	local_text+=temperature_text


	if acquisitionNucleus_name!=None:
		DirectDimensionParameterSet_text+="                    <acquisitionNucleus "
		if acquisitionNucleus_cvRef!=None:
			DirectDimensionParameterSet_text+=""" cvRef="%s" """ %  acquisitionNucleus_cvRef 
		if acquisitionNucleus_accession!=None:	  
			DirectDimensionParameterSet_text+=""" accession="%s" """ %  acquisitionNucleus_accession
		DirectDimensionParameterSet_text+="""name="%s" />""" % acquisitionNucleus_name + linesep

	if effectiveExcitationField_value!=None:
		DirectDimensionParameterSet_text+="""                    <effectiveExcitationField value="%s" """ % effectiveExcitationField_value
		if effectiveExcitationField_unitAccession!=None:
			DirectDimensionParameterSet_text+=""" unitAccession="%s" """ % effectiveExcitationField_unitAccession

		if effectiveExcitationField_unitName!=None:
			DirectDimensionParameterSet_text+=""" unitName="%s" """ % effectiveExcitationField_unitName

		if effectiveExcitationField_unitCvRef!=None:
			DirectDimensionParameterSet_text+=""" unitCvRef="%s" """ % effectiveExcitationField_unitCvRef

		DirectDimensionParameterSet_text+=" />" + linesep

	if DirectDimensionParameterSet_text!="":
		DirectDimensionParameterSet_total_text+="                <DirectDimensionParameterSet "
		if DirectDimensionParameterSet_decoupled!=None:
			DirectDimensionParameterSet_total_text+=""" decoupled="%s"  """ % DirectDimensionParameterSet_decoupled
		if DirectDimensionParameterSet_numberOfDataPoints!=None:
			DirectDimensionParameterSet_total_text+=""" numberOfDataPoints="%s"  """ % DirectDimensionParameterSet_numberOfDataPoints
		DirectDimensionParameterSet_total_text+=" >" + linesep
	
		DirectDimensionParameterSet_total_text+=DirectDimensionParameterSet_text
		DirectDimensionParameterSet_total_text+="                </DirectDimensionParameterSet> "+ linesep

	local_text+=DirectDimensionParameterSet_total_text

	text+=local_text		
	text+="        </acquisitionParameterSet>"  + linesep
	text+="        </acquisition1D>" + linesep


	if local_text=="":
		text=""  	

	debug=0
	if debug==1:
		print ("text = ", text)
		print ("Exiting after function  make_acquisition_1D()")
		sys.exit() 
	return text

def main(project_dir,
	sdf_abth_path,
	npmrd_cs_abth_path,
	nmrpred_peak_list_abth_path,
	multiplet_structure_dict,
	default_peak_width,
	output_path,
	output_1H_for_JSviewer,
	output_13C_for_JSviewer,
	output_13C_1H,
	param_path,
	fid_path,
	spectrum_path,
	genus,
	solvent,
	species,
	spectrometer_frequency,
	literature_reference,
	chemical_shift_standard,
	temperature,
	spectrum_type,
	npmrdb_id,
	npmrdb_session_id,
	npmrdb_submission_id,
	npmrdb_user_id,
	npmrdb_user_defined_name,
	byteFormat,
	compressed,
	nmrpred_13c_peak_list_abth_path,
	nmrpred_13c_param_path,
	nmrpred_13c_fid_abs_path,
	nmrpred_13c_spectrum_abs_path,
	literature_reference_type,
	physical_state_of_compound,
	melting_point,
	boiling_point,					
	):
	"""
	Main program
	"""
	verbose=1
	if verbose==1:
		print  ("Running function main()")
		print  ("project_dir = " ,project_dir)
		print  ("sdf_abth_path = " ,sdf_abth_path)
		print  ("npmrd_cs_abth_path = " ,npmrd_cs_abth_path )
		print  ("nmrpred_peak_list_abth_path = " ,nmrpred_peak_list_abth_path)
		print  ("multiplet_structure_dict = " ,multiplet_structure_dict)
		print  ("default_peak_width = " ,default_peak_width)
		print  ("output_path = " ,output_path)
		print  ("param_path = " ,param_path)
		print  ("fid_path = " ,fid_path)
		print  ("spectrum_path = " ,spectrum_path )
		print  ("genus = " ,genus)
		print  ("solvent = " ,solvent )
		print  ("species = " ,species)
		print  ("spectrometer_frequency = " ,spectrometer_frequency)
		print  ("literature_reference = " ,literature_reference)
		print  ("chemical_shift_standard = " ,chemical_shift_standard)
		print  ("temperature = " ,temperature)
		print  ("spectrum_type = " ,	spectrum_type)
		print  ("nmrpred_13c_peak_list_abth_path = " ,	nmrpred_13c_peak_list_abth_path)
		print  ("spectrum_type = " ,	spectrum_type)
		print  ("literature_reference_type = " ,	literature_reference_type)
		print  ("physical_state_of_compound = " ,	physical_state_of_compound)
		print  ("melting_point = " ,	melting_point)
		print  ("boiling_point = " ,	boiling_point)

	cluster_count_1H=0
	cluster_count_13C=0
	cluster_count_13C_1H=0

	bond_dict={}
	coordinate_dict={}
	param_dict={}

	peak_dict={}
	cluster_dict={}

	peak_dict_13C={}
	cluster_dict_13C={}

	peak_dict_13C_1H={}
	cluster_dict_13C_1H={}

	y_encoded_spectrum_text=""
	x_encoded_spectrum_text=""
	encoded_length=None
	

	y_appened_to_x_13c=[]
	x_y_interleaved_13c=[]
	spectrum_list_13c=[]
	y_list_13c=[]
	x_list_13c=[]
	file_lines_13c=[]
	x_y_list_13c=[]
	spectrum_text_13c=""

	y_appened_to_x=[]
	x_y_interleaved=[]
	spectrum_list=[]
	y_list=[]
	x_list=[]
	file_lines=[]
	x_y_list=[]
	spectrum_text=""


	y_encoded_spectrum_text=""
	x_encoded_spectrum_text=""
	encoded_length="NONE"
	x_y_interleaved_length="NONE"
	x_y_interleaved_byte_string="NONE"
	x_y_interleaved_compressed_bytes="NONE"
	x_y_interleaved_compressed_encoded_spectrum=""
	x_y_interleaved_byte_string_encoded_spectrum=""


	y_encoded_spectrum_text_13c=""
	x_encoded_spectrum_text_13c=""
	encoded_length_13c="NONE"
	x_y_interleaved_length_13c="NONE"
	x_y_interleaved_byte_string_13c="NONE"
	x_y_interleaved_compressed_bytes_13c="NONE"
	x_y_interleaved_compressed_encoded_spectrum_13c=""
	x_y_interleaved_byte_string_encoded_spectrum_13c=""

	if file_exists(nmrpred_13c_peak_list_abth_path)==1: 
		if spectrum_type=='2D-1H-13C':
			print ("Place 1")
			cluster_count_13C_1H=parse_nmrpred_peak_file(cluster_count_13C_1H,peak_dict_13C_1H,cluster_dict_13C_1H,nmrpred_13c_peak_list_abth_path,multiplet_structure_dict,default_peak_width)
		if spectrum_type in ["1D-13C","2D-1H-13C"]:
			print ("Place 2")
			parse_nmrpred_peak_file(cluster_count_13C,peak_dict_13C,cluster_dict_13C,nmrpred_13c_peak_list_abth_path,multiplet_structure_dict,default_peak_width)

	#print ("nmrpred_13c_spectrum_abs_path = ", nmrpred_13c_spectrum_abs_path)
	#print ("Exiting...")
	#sys.exit()

	if file_exists(nmrpred_13c_spectrum_abs_path)==1:
		y_appened_to_x_13c,\
		x_y_interleaved_13c,\
		spectrum_list_13c,\
		y_list_13c,\
		x_list_13c,\
		file_lines_13c,\
		x_y_list_13c,\
		spectrum_text_13c=parse_spectrum(nmrpred_13c_spectrum_abs_path)


	if file_exists(spectrum_path)==1:
		y_appened_to_x,\
		x_y_interleaved,\
		spectrum_list,\
		y_list,\
		x_list,\
		file_lines,\
		x_y_list,\
		spectrum_text=parse_spectrum(spectrum_path)



	if len(x_y_interleaved_13c)>0:
		y_encoded_spectrum_text_13c,\
		x_encoded_spectrum_text_13c,\
		encoded_length_13c,\
		x_y_interleaved_length_13c,\
		x_y_interleaved_byte_string_13c,\
		x_y_interleaved_compressed_bytes_13c,\
		x_y_interleaved_compressed_encoded_spectrum_13c,\
		x_y_interleaved_byte_string_encoded_spectrum_13c=encode_spectrum_list(
						y_appened_to_x_13c,
						x_y_interleaved_13c,
						spectrum_list_13c,
						y_list_13c,
						x_list_13c,
						file_lines_13c,
						x_y_list_13c,
						spectrum_text_13c)
		
	#print ("x_y_interleaved_13c =", x_y_interleaved_13c)
	#print ("x_y_interleaved_byte_string_encoded_spectrum_13c=", x_y_interleaved_byte_string_encoded_spectrum_13c)
	#print ("Exiting...")
	#sys.exit()




	if len(x_y_interleaved)>0:
		y_encoded_spectrum_text,\
		x_encoded_spectrum_text,\
		encoded_length,\
		x_y_interleaved_length,\
		x_y_interleaved_byte_string,\
		x_y_interleaved_compressed_bytes,\
		x_y_interleaved_compressed_encoded_spectrum,\
		x_y_interleaved_byte_string_encoded_spectrum=encode_spectrum_list(
						y_appened_to_x,
						x_y_interleaved,
						spectrum_list,
						y_list,
						x_list,
						file_lines,
						x_y_list,
						spectrum_text)
		
	if 	file_exists(fid_path):
		fid_text=parse_fid(fid_path)

	if file_exists(param_path):
		param_dict=parse_param(param_path)

	debug=1
	if debug==1:
		print ("cluster_dict_13C_1H = ", cluster_dict_13C_1H)
		for i in cluster_dict_13C_1H:
			print (linesep, "1) i,cluster_dict_13C_1H[i]=",i,cluster_dict_13C_1H[i],linesep) 
			multiplet_center=cluster_dict_13C_1H[i]["multiplet_center"]
			print (linesep, "1) multiplet_center=",multiplet_center,linesep) 

		#print ("Exiting...")
		#sys.exit()

	if file_exists(nmrpred_peak_list_abth_path):
		parse_nmrpred_peak_file(cluster_count_1H,peak_dict,cluster_dict,nmrpred_peak_list_abth_path,multiplet_structure_dict,default_peak_width)
		parse_nmrpred_peak_file(cluster_count_13C_1H,peak_dict_13C_1H,cluster_dict_13C_1H,nmrpred_peak_list_abth_path,multiplet_structure_dict,default_peak_width)


	debug=0
	if debug==1:
		print ("cluster_dict_13C_1H = ", cluster_dict_13C_1H)
		for i in cluster_dict_13C_1H:
			print (linesep, "2) i,cluster_dict_13C_1H[i]=",i,cluster_dict_13C_1H[i],linesep) 
			multiplet_center=cluster_dict_13C_1H[i]["multiplet_center"]
			print (linesep, "2) multiplet_center=",multiplet_center,linesep) 

		print ("Exiting 1...")
		sys.exit()
		#print ("Exiting...")
		#sys.exit()
		#cluster_dict=find_clusters(peak_dict) 
		  
	if file_exists(npmrd_cs_abth_path):
		parse_npmrd_cs(npmrd_cs_abth_path)    

	if file_exists(sdf_abth_path):
		bond_dict,coordinate_dict=parse_sdf(sdf_abth_path)

	create_xml_text(
		literature_reference_type,
		physical_state_of_compound,
		melting_point,
		boiling_point,		
		cluster_dict_13C,
		cluster_dict_13C_1H,
		cluster_dict,
		bond_dict,
		coordinate_dict,
		output_path,
		output_1H_for_JSviewer,
		output_13C_for_JSviewer,
		output_13C_1H,		
		genus,
		solvent,
		species,
		spectrometer_frequency,
		literature_reference,
		chemical_shift_standard,
		temperature,
		spectrum_type,
		encoded_length,
		npmrdb_id,
		npmrdb_session_id,
		npmrdb_submission_id,
		npmrdb_user_id,
		npmrdb_user_defined_name,
		x_y_interleaved_length,
		x_y_interleaved_byte_string,
		x_y_interleaved_compressed_bytes,
		x_y_interleaved_compressed_encoded_spectrum,
		x_y_interleaved_byte_string_encoded_spectrum,
		x_y_interleaved_byte_string_encoded_spectrum_13c,	
		y_encoded_spectrum=y_encoded_spectrum_text,
		x_encoded_spectrum=x_encoded_spectrum_text,
		spectrumDataArray_byteFormat=byteFormat,
		spectrumDataArray_compressed=compressed,					
		)


	debug=1
	if debug==1:
		print ("Exiting after function main()")
		sys.exit()     
	return



def parse_npmrd_cs(npmrd_cs_abth_path):
	"""
	Parse CS files submitted by the user to NP-MRD
	"""
	verbose=1
	if verbose==1:
		print  ('Running function parse_npmrd_cs()')

	file_lines=read_file(npmrd_cs_abth_path)

	header_found=0 
	header_keywords=["atom"]
	entry_dictionary={}
	column_list=[]
	column_dict={}
	cs_dict={}
	atom_count=0

	for i in file_lines:
		i=i.rstrip()
		#print ("i = ", i) 
		i_lowcase=i.lower()
		for j in header_keywords:
			if j in i_lowcase:
				header_found=1 
		if header_found==0 and ":" in i:
			i_split=i.split(":")
			#print ("i_split = ", i_split)
			if len(i_split)>1:
				paramter=i_split[0]
				value=i_split[1]
				entry_dictionary[paramter]=value
		else:  
			i_split_2=i.split(",")
			column_count=0
			if len(column_list)==0:
				for k in  i_split_2:
				   column_list+=[k] 
				   column_dict[column_count]=[k]
				   column_count+=1
			else:
				atom_count+=1
				column_count=0
				cs_dict[atom_count]={}
				for k in  i_split_2:
					paramter=column_list[column_count]
					value=k
					cs_dict[atom_count][paramter]=value
					column_count+=1

			  
	debug=0
	if debug==1:
		for i in  entry_dictionary:
			print (i,entry_dictionary[i])
		for i in  column_list:
			print (i)  
		for i in  cs_dict:
			for paramter in cs_dict[i]:
				value=   cs_dict[i][paramter] 
				print ("i, paramter, value = ", i, paramter, value)
		print ('Exiting after function parse_npmrd_cs()')
		sys.exit()  
	return cs_dict,column_list,column_dict  



def parse_nmrpred_peak_file(cluster_count,peak_dict,cluster_dict,nmrpred_peak_file,multiplet_structure_dict,default_peak_width):
	"""
	Parse peak file
	"""

	verbose=1

	if verbose>=1:
		print  ('Running function parse_parse_nmrpred_peak_file() for file ', [nmrpred_peak_file])	
		print ("cluster_count = ", cluster_count)

		debug=1
		if debug==1:
			for i in cluster_dict:
				print (linesep, "3 i,cluster_dict_13C_1H[i]=",i,cluster_dict[i],linesep) 		
	#peak_dict={}
	#cluster_dict={}
	old_multiplet_center=None
	old_multiplet_structure=None

	#cluster_count=0
	if file_exists(nmrpred_peak_file)==0:
		print ("Error!!! nmrpred_peak_file = %s does not exist " % nmrpred_peak_file)
		print ("Exiting...")
		sys.exit()
	else:	

		file_lines=read_file(nmrpred_peak_file)
		for i in file_lines:
			#print (i)
			i=i.rstrip()
			i=i.split(",")
			atom_id = int(float(i[0]))
			cluster_center = float(i[1])
			multiplet_structure = i[2]
			peak_center = float(i[3])
			peak_intensity = float(i[4])

			if verbose>=1:print ("i = ", i)
			if atom_id  not in peak_dict:
				peak_dict[atom_id]={"clusters":{}}


			if is_number(cluster_center)==0:
				print ("ERROR!!! The cluster_center for atom ID %s is not numeric and is " 	% atom_id, [cluster_center])
			else:
				if cluster_center not in peak_dict[atom_id]["clusters"]:
					peak_dict[atom_id]["clusters"][cluster_center]={"peaks":{},"multiplicity_accession":""}
				#if "peaks" not in peak_dict[atom_id]["clusters"][cluster_center]:
				#	peak_dict[atom_id]["clusters"][cluster_center]["peaks"]={}	
				peak_dict[atom_id]["clusters"][cluster_center]["peaks"][peak_center]=peak_intensity	
				peak_dict[atom_id]["clusters"][cluster_center]["multiplet"]=multiplet_structure



				if cluster_center!=old_multiplet_center or multiplet_structure!=old_multiplet_structure:
					cluster_count+=1
					print ("INCREMENTING")
					old_multiplet_center=cluster_center
					old_multiplet_structure=multiplet_structure
					peak_counter=0   


				if cluster_count not in cluster_dict:
					print ("ADDING 2 cluster_count = ", cluster_count)
			
					cluster_dict[cluster_count]={"atoms_atomRefs":[],"multiplicity_accession":"","peak_dict":{}} 
					cluster_dict[cluster_count]["multiplet_center"]=cluster_center           
					if  multiplet_structure not in multiplet_structure_dict:
						multiplicity_name="Undefined" 
					else:
						multiplicity_name=multiplet_structure_dict[multiplet_structure]      
					cluster_dict[cluster_count]["multiplicity_name"]=multiplicity_name 

				print ("ADDING cluster_count = ", cluster_count)
				cluster_dict[cluster_count]["atoms_atomRefs"]+=[atom_id]
				peak_counter+=1 
				cluster_dict[cluster_count]["peak_dict"][peak_counter]={}
				cluster_dict[cluster_count]["peak_dict"][peak_counter]["peak_center"]=peak_center
				cluster_dict[cluster_count]["peak_dict"][peak_counter]["amplitude"]=peak_intensity
				cluster_dict[cluster_count]["peak_dict"][peak_counter]["width"]=default_peak_width
	debug=1
	if debug==1:
		for i in cluster_dict:
			print (linesep, "4 i,cluster_dict_13C_1H[i]=",i,cluster_dict[i],linesep)
			multiplet_center=cluster_dict[i]["multiplet_center"] 
			print( "multiplet_center =", multiplet_center)

	debug=0
	if debug==1:
		#print ("peak_dict = ", peak_dict)
		#print ("cluster_dict = ", cluster_dict)
		print ("Exiting after function parse_nmrpred_peak_file()")
		sys.exit()		
	return	cluster_count
	

def parse_sdf(sdf_abth_path):
	"""
	Parse SDF
	"""
	verbose=1
	if verbose==1:
		print  ('Running function parse_sdf()')
	file_lines=read_file(sdf_abth_path)	
	start_coordinates_detected=0
	end_coordinates_detected=0

	bonds_start_detected=0
	bonds_end_detected=0
	coordinates_done=0
	bonds_done=0
	bond_dict={}
	coordinate_dict={}
	bond_counter=0
	atom_counter=0

	for i in file_lines:
		i=i.rstrip()
		i_split=i.split()
		i_len=len(i_split)
		#print (i_len,i_split)
		if i_len>12 and start_coordinates_detected==0:
			start_coordinates_detected=1
		if i_len>12 and coordinates_done==0 and bonds_done==0 and start_coordinates_detected==1 and end_coordinates_detected==0 and bonds_start_detected==0:
			x=i_split[0]
			y=i_split[1]
			z=i_split[2]
			atom=i_split[3]

			atom_counter+=1
			coordinate_dict[atom_counter]={}
			coordinate_dict[atom_counter]["elementType"]=atom
			coordinate_dict[atom_counter]["x"]=x
			coordinate_dict[atom_counter]["y"]=y
			coordinate_dict[atom_counter]["z"]=z


		if 	i_len<12 and i_len>2 and start_coordinates_detected==1 and bonds_done==0 and coordinates_done==0 and end_coordinates_detected==0  and bonds_start_detected==0:
			coordinates_done=1
			end_coordinates_detected=1
			bonds_start_detected=1
		if 	i_len<12 and i_len>2 and start_coordinates_detected==1 and bonds_done==0 and coordinates_done==1 and end_coordinates_detected==1 and bonds_start_detected==1:
			atom_1=	i_split[0]
			atom_2=	i_split[1]
			bond=i_split[2]
			bond_counter+=1

			bond_dict[bond_counter]={}
			bond_dict[bond_counter]["atom_1"]=atom_1
			bond_dict[bond_counter]["atom_2"]=atom_2
			bond_dict[bond_counter]["order"]=bond	
		if 	i_len<3 and start_coordinates_detected==1 and bonds_done==0 and coordinates_done==1 and end_coordinates_detected==1 and bonds_start_detected==1:
			bonds_done=1

	debug=0
	if debug==1:
		print ("bond_dict = ", bond_dict)
		print ("coordinate_dict  = ", coordinate_dict) 

		print ('Exiting after function parse_sdf()')
		sys.exit()  
	return bond_dict,coordinate_dict  

def parse_param(param_path):	
	"""
	Parse NMRpred param file

	"""
	verbose=1
	if verbose==1:
		print  ('Running function parse_param() for file ', param_path)
	file_lines=read_file(param_path)
	param_dict={}
	for i in file_lines:
		i=i.strip()
		#print ("i = ", i)
		i_split=i.split()
		if len(i_split)>1:
			#print ("i_split = ", i_split)
			param_name=i_split[0]
			param_value=i_split[1]
			param_dict[param_name]=param_value


	debug=0
	if debug==1:
		print ("param_dict = ", param_dict)
		print ('Exiting after function param_path()')
		sys.exit()  
	return	param_dict

def parse_fid(fid_path):	
	"""
	Parse NMRpred FID file
	"""
	verbose=1
	if verbose==1:
		print  ('Running function parse_fid() for file ', fid_path)
	file_lines=read_file(fid_path)
	for i in file_lines:
		i=i.strip()
		#print ("i = ", i)
		i_split=i.split()
		if len(i_split)>2:
			time=i_split[0]
			real=i_split[1]
			imaginary=i_split[2]


	debug=0
	if debug==1:
		#print ("param_dict = ", param_dict)
		print ('Exiting after function parse_fid()')
		sys.exit()  
	return	









def parse_spectrum(spectrum_path):	
	"""
	Parse NMRpred predicted spectrum file
	"""
	verbose=1
	if verbose==1:
		print  ('Running function parse_spectrum() for file ', spectrum_path)
	spectrum_list=[]
	file_lines=read_file(spectrum_path)
	y_list=[]	
	x_list=[]

	#myarray = array.array('f')
	
	spectrum_text=""
	x_y_list=[]
	x_y_interleaved=[]
	y_appened_to_x=[]
	counter=0
	for i in file_lines:
		counter+=1
		if counter==5:
			spectrum_text+=i
			i=i.strip()
			#print ("i = ", i)
			i_split=i.split(",")
			x=i_split[0]
			y=i_split[1]
			x_float=float(x)
			y_float=float(y)
			y_float*=intensity_scaling_coefficient
			#print (" x_float,y_float = ", x_float,y_float)
			spectrum_list+=[[x_float,y_float]]
			x_y_interleaved+=[x_float,y_float]	
			y_list+=[y_float]	
			x_list+=[x_float]
			complex_point=complex(x_float,y_float)
			x_y_list+=[complex_point]
			#spectrum_text+="%s,%s"
			#myarray	+=[[x_float,y_float]]
			counter=0

	y_appened_to_x=x_list+y_list
	debug=0
	if debug==1:
		print ("x_y_interleaved = ", x_y_interleaved)
		print ('Exiting after function spectrum_path())')
		sys.exit()  
	return	y_appened_to_x,x_y_interleaved, spectrum_list,y_list,x_list,file_lines,x_y_list,spectrum_text



def encode_spectrum_list(y_appened_to_x,
						x_y_interleaved,
						spectrum_list,
						y_list,
						x_list,
						file_lines,
						x_y_list,
						spectrum_text):	
	"""
	Encode spectrum list
	"""	
	verbose=1
	if verbose==1:
		print  ('Running function encode_spectrum()')	
	spectrum_text=""


	# Pack the array into a byte string, need to make sure
	# to use '<' so that the bits will be little-endian
	filename="tmp.txt"

	debug=0
	if debug==1:
		x_y_interleaved=[]
		x_y_interleaved+=[0,10.0]
		x_y_interleaved+=[1.0,0.0]
		x_y_interleaved+=[2.0,3.0]
		x_y_interleaved+=[3.0,5.0]
		x_y_interleaved+=[4.0,0.0]
		x_y_interleaved+=[5.0,3.0]
		x_y_interleaved+=[6.0,0.0]
		x_y_interleaved+=[7.0,4.0]

	x_y_interleaved=numpy.array(x_y_interleaved)


	x_y_interleaved_length=len(x_y_interleaved)


	#@ - native
	#- - native
	#< - little-endian
	#> big-endian
	#! network
	#print ("x_y_interleaved_length=", x_y_interleaved_length)
	#x_y_interleaved_byte_string = struct.pack( '<'+str(x_y_interleaved_length)+'f',*x_y_interleaved)
	x_y_interleaved_byte_string = struct.pack( '<'+str(x_y_interleaved_length)+'d',*x_y_interleaved)
	
	#print ("x_y_interleaved_byte_string= ", x_y_interleaved_byte_string)
	#print ("x_y_interleaved_byte_string_length = " , len(x_y_interleaved_byte_string))
	#print ("Writing temporary file ", filename)
	#write_file(filename,x_y_interleaved_byte_string)
	#x_y_interleaved_text=""
	#for i in x_y_interleaved:
	#	x_y_interleaved_text+="%s," % i



	x_y_interleaved_compressed_bytes = zlib.compress(x_y_interleaved_byte_string)
	#print ("x_y_interleaved_compressed_bytes_length = " , len(x_y_interleaved_compressed_bytes))
	#print ("Writing temporary file ", filename)
	#write_file(filename,x_y_interleaved_byte_string)

	x_y_interleaved_compressed_encoded_spectrum=base64.b64encode(x_y_interleaved_compressed_bytes)
	#print ("x_y_interleaved_compressed_encoded_spectrum_length = " , len(x_y_interleaved_compressed_encoded_spectrum))

	#x_y_interleaved_byte_string_encoded_spectrum=base64.encodestring(x_y_interleaved_byte_string)
	x_y_interleaved_byte_string_encoded_spectrum=base64.b64encode(x_y_interleaved_byte_string)

	#x_y_interleaved_text_encoded_spectrum=base64.encodestring(x_y_interleaved_text)


	#print ("x_y_interleaved_byte_string_encoded_spectrum_length = " , len(x_y_interleaved_byte_string_encoded_spectrum))
	#print ("Writing temporary file ", filename)
	#write_binary_file(filename,x_y_interleaved_byte_string_encoded_spectrum)
	#for i in x_y_interleaved_byte_string_encoded_spectrum:
	#	print (i)
		
	#x_y_interleaved_byte_string_encoded_spectrum_text="".join(map(chr, x_y_interleaved_byte_string_encoded_spectrum))








	y_list_length = len(y_list)
	y_byte_string = struct.pack( '<'+str(y_list_length)+'f',*y_list)
	y_compressed_bytes = zlib.compress(y_byte_string)
	y_encoded_spectrum_text=base64.encodestring(y_compressed_bytes)

	x_list_length = len(x_list)
	x_byte_string = struct.pack( '<'+str(x_list_length)+'f',*x_list)
	x_compressed_bytes = zlib.compress(x_byte_string)
	x_encoded_spectrum_text=base64.encodestring(x_compressed_bytes)

	#byte_string = struct.pack( '<'+str(list_length)+'c',*x_y_list)
	#if verbose==1: print  ('byte_string is  %s ' % byte_string )
	#unpacked_y_list=struct.unpack( '<'+str(list_length)+'f', byte_string)	
	#print ("compressed_bytes = ", compressed_bytes)
	# Decoding for debugging
	#l_spectrum_code=spectrum_code.replace(os.linesep,"")
	#TypeError: expected bytes-like object, not str
	#decoded_spectrum_text=base64.b64decode(spectrum_code)
	#dt = numpy.dtype('<'+str(list_length)+'f')  
	#dt = numpy.dtype('<f')
	#decoded_spectrum=numpy.loadtxt(byte_string,dtype=dt)
	#decoded_spectrum=numpy.genfromtxt(byte_string,dtype=dt)
	#print ("decoded_spectrum_text = ", decoded_spectrum_text)
	#decoded_spectrum_text=base64.decodestring(spectrum_code)
	#decoded_spectrum_text=base64.decodestring(encoded_spectrum_text)
	#print ("decoded_spectrum_text = ", decoded_spectrum_text)
	#uncompressed_bytes = zlib.decompress(decoded_spectrum_text)
	#uncompressed_bytes = zlib.decompress(decoded_spectrum_text)
	#unpacked_y_list=struct.unpack( '<c', decoded_spectrum_text)	
	#unpacked_y_list=struct.unpack( '<'+str(list_length)+'f', uncompressed_bytes)	
	#print ("unpacked_y_list = ", unpacked_y_list)
	#base64.encodestring(byte_string).replace("\n","")
	#data_bytes = data_string.encode("utf-8")


	debug=0
	if debug==1:
		#print ("x_y_interleaved_byte_string_encoded_spectrum = ", x_y_interleaved_byte_string_encoded_spectrum)

		print ('Exiting after function  encode_spectrum()')
		sys.exit()  
	return (y_encoded_spectrum_text, 
			x_encoded_spectrum_text,
			y_list_length,
			x_y_interleaved_length,
			x_y_interleaved_byte_string,
			x_y_interleaved_compressed_bytes,
			x_y_interleaved_compressed_encoded_spectrum,
			x_y_interleaved_byte_string_encoded_spectrum,
			)


####################################
# Arguments
####################################

command_dict={}
command_text="python "
i=0
if len(sys.argv)>1:
	for item in sys.argv:
		##############################
		# Project/file management
		##############################
		if item in ["-l"]:
			location  = os.path.abspath(sys.argv[i + 1])
			command_dict["location"]=location
		if item in ["-p"]:
			project  = sys.argv[i + 1]
			command_dict["project"]=project
		if item in ["-v1"]:
			verbose_level_1  = int(sys.argv[i + 1])
		if item in ["-e", "-env"]:
			env  = sys.argv[i + 1]
		if item in ["-sdf","-mol"]:
			sdf_abth_path  = os.path.abspath(sys.argv[i + 1])
		if item in ["-cs"]:
			npmrd_cs_abth_path  = os.path.abspath(sys.argv[i + 1])    
		if item in ["-pl"]:
			nmrpred_peak_list_abth_path  = os.path.abspath(sys.argv[i + 1])  
		if item in ["-name"]:
			identifierList_identifier_name_value =sys.argv[i + 1] 
		if item in ["-output_path"]:
			output_path  = os.path.abspath(sys.argv[i + 1])
		if item in ["-output_1H_for_JSviewer_path"]:
			output_1H_for_JSviewer= os.path.abspath(sys.argv[i + 1])
		if item in ["-output_13C_for_JSviewer_path"]:
			output_13C_for_JSviewer= os.path.abspath(sys.argv[i + 1])
		if item in ["-output_13C_1H"]:
			output_13C_1H= os.path.abspath(sys.argv[i + 1])
		if item in ["-param_path"]:
			param_path  = os.path.abspath(sys.argv[i + 1]) 	
		if item in ["-fid_path"]:
			fid_path  = os.path.abspath(sys.argv[i + 1])
		if item in ["-13C_pl"]:
			nmrpred_13c_peak_list_abth_path  = os.path.abspath(sys.argv[i + 1])	
		if item in ["-13C_param_path"]:
			nmrpred_13c_param_path  = os.path.abspath(sys.argv[i + 1])
		if item in ["-13C_fid_path"]:
			nmrpred_13c_fid_abs_path  = os.path.abspath(sys.argv[i + 1])
		if item in ["-13C_spec_path"]:
			nmrpred_13c_spectrum_abs_path  = os.path.abspath(sys.argv[i + 1])



		if item in ["-spec_path"]:
			spectrum_path  = os.path.abspath(sys.argv[i + 1])	
		if item in ["-genus"]:
			genus  = sys.argv[i + 1]
		if item in ["-solvent"]:
			solvent  = sys.argv[i + 1]
		if item in ["-species"]:
			species  = sys.argv[i + 1]		
		if item in ["-freq"]:
			spectrometer_frequency  = sys.argv[i + 1]				
		if item in ["-ref"]:
			literature_reference  = sys.argv[i + 1]	
		if item in ["-standard"]:
			chemical_shift_standard = sys.argv[i + 1]	
		if item in ["-temp"]:
			temperature = sys.argv[i + 1]
		if item in ["-spec_type"]:
			spectrum_type = sys.argv[i + 1]	
		if item in ["-npmrdb_id"]:
			npmrdb_id = sys.argv[i + 1]	
		if item in ["-npmrdb_session_id"]:
			npmrdb_session_id = sys.argv[i + 1]	
		if item in ["-npmrdb_submission_id"]:
			npmrdb_submission_id = sys.argv[i + 1]	
		if item in ["-npmrdb_user_id"]:
			npmrdb_user_id = sys.argv[i + 1]
		if item in ["-npmrdb_user_defined_name"]:
			npmrdb_user_defined_name = sys.argv[i + 1]	

		if item in ["-ref_type"]:
			literature_reference_type = sys.argv[i + 1]
		if item in ["-phys_state"]:
			physical_state_of_compound = sys.argv[i + 1]
		if item in ["-melt_point"]:
			melting_point = sys.argv[i + 1]
		if item in ["-boil_point"]:
			boiling_point = sys.argv[i + 1]			

		i+=1
	command_text+=" %s " % item

#######################################################
#  Processing general arguments
#######################################################                         
this_script_full_path=os.path.abspath(sys.argv[0])
this_script_name=os.path.basename(sys.argv[0])
script_location=os.path.dirname(this_script_full_path)
if verbose_level_1>0:
	print ("this_script_full_path=", this_script_full_path)
	print ("this_script_name=", this_script_name)
	print ("script_location=", script_location)
cur_dir=os.getcwd()
if location=="":
	location=cur_dir
project_dir="%s/%s" % (location,project)


debug=0
if debug==1:
	print ("project_dir = ", project_dir)
	sys.exit()
make_dir_no_remove(project_dir)
if output_path=="NONE":
	output_path="%s/%s.nmrML" % (project_dir,project)
	if output_1H_for_JSviewer=="NONE":
		output_1H_for_JSviewer="%s/1H_for_JSviewer_%s.nmrML" % (project_dir,project)
	if output_13C_for_JSviewer=="NONE":	
		output_13C_for_JSviewer="%s/13C_for_JSviewer_%s.nmrML" % (project_dir,project)
	if output_13C_1H=="NONE":
		output_13C_1H="%s/13C_1H_%s.nmrML" % (project_dir,project)
else:
	output_basename=os.path.basename(output_path)
	output_dirname=os.path.dirname(output_path)
	if output_1H_for_JSviewer=="NONE":
		output_1H_for_JSviewer="%s/1H_for_JSviewer_%s" % (output_dirname,output_basename)
	if output_13C_for_JSviewer=="NONE":
		output_13C_for_JSviewer="%s/13C_for_JSviewer_%s" % (output_dirname,output_basename)
	if output_13C_1H=="NONE":
		output_13C_1H="%s/13C_1H_%s" % (output_dirname,output_basename)		

#######################################################
# End of  processing general arguments
#######################################################

#######################################################
# Main program
#######################################################

main(project_dir,
	sdf_abth_path,
	npmrd_cs_abth_path,
	nmrpred_peak_list_abth_path,
	multiplet_structure_dict,
	default_peak_width,
	output_path,
	output_1H_for_JSviewer,
	output_13C_for_JSviewer,
	output_13C_1H,
	param_path,
	fid_path,
	spectrum_path,
	genus,
	solvent,
	species,
	spectrometer_frequency,
	literature_reference,
	chemical_shift_standard,
	temperature,
	spectrum_type,
	npmrdb_id,
	npmrdb_session_id,
	npmrdb_submission_id,
	npmrdb_user_id,
	npmrdb_user_defined_name,
	byteFormat,
	compressed,
	nmrpred_13c_peak_list_abth_path,
	nmrpred_13c_param_path,
	nmrpred_13c_fid_abs_path,
	nmrpred_13c_spectrum_abs_path,
	literature_reference_type,
	physical_state_of_compound,
	melting_point,
	boiling_point,
	)
